import type { Config } from 'tailwindcss';
import path from 'path';

interface ThemeObjectType {
  [key: string]: any;
}

export const THEME_FONT_SIZES: ThemeObjectType = {
  t0: '5.375rem', // 86px
  t1: '4.25rem', // 68px
  t2: '3.375rem', // 54px
  t3: '2.75rem', // 44px
  t4: '2.188rem', // 35px
  t5: '1.75rem', // 28px
  t6: '1.375rem', // 22px
  body: '1.125rem', // 18px
  remark: '0.938rem', // 15px
  footnote: '0.813rem', // 13px
  mini: '0.688rem', // 11px
};

export const THEME_FONT_COLORS: ThemeObjectType = {
  primary: {
    100: '#fff5da',
    200: '#ffe7b6',
    300: '#ffd792',
    400: '#ffc777',
    500: '#ffac4a',
    600: '#ff964a',
    700: '#ff813a',
    800: '#fe6d1b',
    900: '#e65300',
    1000: '#f9d17b',
    DEFAULT: '#ffac4a',
  },
  secondary: {
    100: '#ffffff',
    500: '#30302f',
    900: '#000000',
    DEFAULT: '#30302f',
  },
  tertiary: {
    100: '#ccf9ff',
    200: '#99edff',
    300: '#66daff',
    400: '#3fc5ff',
    500: '#00a3ff',
    600: '#007edb',
    700: '#005eb7',
    800: '#004393',
    900: '#002f7a',
    DEFAULT: '#00a3ff',
  },
  grey: {
    50: '#f0f0f0',
    100: '#f2f2f2',
    200: '#dedede',
    300: '#c6c6c6',
    400: '#acacac',
    500: '#949494',
    600: '#636363',
    700: '#313131',
    DEFAULT: '#949494 ',
  },
  common: {
    black: '#000000',
    divider: '#DEDEDE',
    white: '#ffffff',
    bg: '#f7f7f7',
    disable: '#f0f0f0',
    DEFAULT: '#000000',
  },
  status: {
    danger: '#ff271c',
    info: '#0075ff',
    success: '#1cc500',
    warning: '#ffe600',
    connected: '#57B62D',
  },
  other: {
    orange: '#FCA235',
  },
};

// To make sure the theme style work for as much as possible cases of "dynamic classes" (e.g. in the style-guide page)
// We use a safelist technique to whitelist all the possible classes that can be generated by the theme style:
// Ref: https://stackoverflow.com/questions/69687530/dynamically-build-classnames-in-tailwindcss/73057959#73057959
const safelist = [];

// interaction portal
safelist.push('grid-rows-4');
safelist.push('overflow-y-auto');

// Generate font size safelist
for (const key in THEME_FONT_SIZES) {
  safelist.push(`text-${key}`);
  safelist.push(`font-${key}`);
}

// Generate color safelist
for (const key in ['transparent', 'current', 'black', 'white']) {
  safelist.push(`text-${key}`);
  safelist.push(`bg-${key}`);
  safelist.push(`border-${key}`);
  safelist.push(`fill-${key}`);
  safelist.push(`stroke-${key}`);
}
for (const key in THEME_FONT_COLORS) {
  // Single color
  safelist.push(`text-${key}`);
  safelist.push(`bg-${key}`);
  safelist.push(`border-${key}`);
  safelist.push(`fill-${key}`);
  safelist.push(`stroke-${key}`);
  if (typeof THEME_FONT_COLORS[key] !== 'string') {
    // Color shades
    for (const shade in THEME_FONT_COLORS[key]) {
      safelist.push(`text-${key}-${shade}`);
      safelist.push(`bg-${key}-${shade}`);
      safelist.push(`border-${key}-${shade}`);
    }
  }
}

['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl'].forEach((size) => {
  safelist.push(`shadow-${size}`);
});

// Generate size safelist
new Array(60).fill(0).forEach((_, index) => {
  safelist.push(`w-${index}`);
  safelist.push(`h-${index}`);
  safelist.push(`size-${index}`);
  safelist.push(`mb-${index}`);
  safelist.push(`-mb-${index}`);
});

const tailwindClasses = [
  // Layout & Positioning
  'flex',
  'grid',
  'fixed',
  'absolute',
  'inline',
  'flex-1',
  'flex-col',
  'flex-row',
  'flex-none',
  'items-start',
  'items-center',
  'justify-between',
  'justify-center',
  'justify-around',
  'justify-items-center',
  'left-0',
  'top-0',
  'right-5',
  'right-0',
  'bottom-5',
  'bottom-4',
  'z-[999]',
  'z-[99999]',
  'z-2',
  // Grid
  'grid-cols-2',
  'grid-cols-3',
  'auto-rows-fr',
  'grid-rows-2',
  // Spacing & Gaps
  'gap-1',
  'gap-2',
  'gap-3',
  'gap-4',
  'gap-6',
  'gap-8',
  'gap-x-3',
  'gap-x-4',
  'gap-y-3',
  'gap-y-4',
  'grid',
  'sm:gap-2',
  'sm:gap-3',
  'md:gap-3',
  // Margin
  'mt-2',
  'mb-4',
  'ml-2',
  'mx-auto',
  // Padding
  'p-1',
  'p-1.5',
  'p-2',
  'p-3',
  'pb-4',
  'px-2',
  'px-3',
  'py-1',
  'py-1.5',
  'py-2',
  'pt-2',
  'pr-1',
  'pr-2',
  'pr-8',
  'pl-2',
  'pl-3',
  'sm:p-2',
  'sm:pl-3',
  'md:p-2.5',
  'lg:p-2',
  'lg:pt-2',
  'pt-1',
  'pt-2',
  'pt-3',
  // Width & Height
  'w-4',
  'w-5',
  'w-6',
  'w-7',
  'w-8',
  'w-10',
  'w-12',
  'w-14',
  'w-16',
  'w-full',
  'w-auto',
  'w-max',
  'w-min',
  'w-[30%]',
  'w-[50%]',
  'w-[70%]',
  'w-[300px]',
  'h-0',
  'h-4',
  'h-5',
  'h-6',
  'h-7',
  'h-8',
  'h-10',
  'h-12',
  'h-14',
  'h-16',
  'h-full',
  'min-w-0',
  'lg:w-6',
  'lg:h-6',
  'lg:w-14',
  'lg:h-14',
  'lg:w-16',
  'lg:h-16',
  // Typography
  'text-left',
  'text-center',
  'text-sm',
  'text-base',
  'text-lg',
  'text-xl',
  'text-2xl',
  'text-[20px]',
  'text-[24px]',
  'text-body',
  'text-end',
  'text-t6',
  'sm:text-base',
  'sm:text-lg',
  'md:text-lg',
  'md:text-xl',
  'lg:text-l',
  'lg:text-sm',
  'lg:text-xl',
  'lg:text-2xl',
  'font-medium',
  'font-semibold',
  'font-bold',
  'truncate',
  // Colors
  'text-white',
  'text-black',
  'text-gray-500',
  'text-gray-600',
  'text-red-500',
  'text-green-500',
  'text-yellow-500',
  'text-orange-500',
  'text-blue-500',
  'text-primary',
  'text-remark',
  'text-[#1CC500]',
  'text-[#949494]',
  'text-[#DEDEDE]',
  'text-[#FF271C]',
  'bg-white',
  'bg-gray-100',
  'bg-gray-200',
  'bg-gray-500',
  'bg-green-500',
  'bg-red-500',
  'bg-yellow-500',
  'bg-orange-400',
  'bg-orange-500',
  'bg-blue-500',
  'active:bg-grey-400',
  'bg-[#1CC500]',
  'bg-[#D9D9D9]',
  'bg-[#DEDEDE]',
  'bg-[#FF271C]',
  'bg-[#FFAC4A]',
  'bg-[#F8F8F8]',
  'bg-primary-100',
  'bg-primary-300',
  'bg-status-info',
  'bg-status-success',
  // Borders & Rounded
  'border-t-2',
  'border-t-gray-300',
  'rounded-lg',
  'rounded-md',
  'rounded-full',
  'rounded-2xl',
  'border-2',
  'border-b',
  'border-b-2',
  'border-grey-200',
  'border-none',
  'border-x',
  'border-zinc-300',
  'box-border',
  // Effects & Display
  'shadow',
  'shadow-sm',
  'hidden',
  'overflow-clip',
  'object-cover',
  // Hover States
  'hover:bg-gray-100',
  'hover:bg-gray-800',
  'hover:bg-grey-300',
  'hover:border-none',
  'hover:text-primary',
  // Container
  'container',
  // Specific combinations
  'flex-1',
  'overflow-auto',
  'p-2',
  'bg-gray-50',
  'mx-auto',
  'flex',
  'items-center',
  'justify-between',
  'p-1',
  'flex',
  'items-center',
  'gap-2',
  'pr-2',
  'flex',
  'items-center',
  'gap-3',
  'h-full',
  'grid',
  'grid-cols-2',
  'gap-4',
  'auto-rows-fr',
  'flex-col',
  'justify-center',
  'text-center',
  'fill-white',
  'fill-black',
  'fill-primary',
  '!fill-primary',
  '-bottom-3.5',
  '-bottom-6',
  'col-end-3',
  'col-end-4',
  'cursor-not-allowed',
  'disabled:bg-grey-400',
  'focus:shadow-none',
  'left-[25%]',
  'row-span-full',
  'shrink-0',
  'size-8',
  'size-[10px]',
  'hover:fill-current',
];

for (let i = 0; i < tailwindClasses.length; i++) {
  safelist.push(tailwindClasses[i]);
}

// const manualQueueTailwindClasses = [
//   'w-[35%]',
//   'grid-cols-[80px,1fr]',
//   'text-[14px]',
//   'relative',
//   'z-50',
//   'bg-white',
//   'rounded-sm',
//   'shadow-lg',
//   'border',
//   'border-grey-200',
//   'min-w-[8rem]',
//   'max-w-[600px]',
//   'max-h-96',
//   'overflow-hidden',
//   'data-[state=open]:animate-in',
//   'data-[state=closed]:animate-out',
//   'data-[state=closed]:fade-out-0',
//   'data-[state=open]:fade-in-0',
//   'flex',
//   'items-center',
//   'gap-2',
//   'p-2',
//   'border-b',
//   'border-b-2',
//   'border-primary-600',
//   'text-base',
//   'font-medium',
//   'tracking-wider',
//   'text-sm',
//   'text-body',
//   'text-lg',
//   'text-xl',
//   'text-2xl',
//   'text-4xl',
//   'text-left',
//   'text-center',
//   'text-gray-500',
//   'text-gray-600',
//   'text-red-500',
//   'text-green-500',
//   'text-yellow-500',
//   'text-orange-500',
//   'text-blue-500',
//   'bg-grey-100',
//   'bg-grey-200',
//   'bg-green-500',
//   'bg-red-500',
//   'bg-yellow-500',
//   'bg-orange-400',
//   'bg-orange-500',
//   'bg-blue-500',
//   'border-t-2',
//   'border-t-grey-300',
//   'rounded-lg',
//   'rounded-md',
//   'rounded-full',
//   'shadow-sm',
//   'hidden',
//   'overflow-auto',
//   'overflow-clip',
//   'object-cover',
//   'w-full',
//   'h-full',
//   'min-w-0',
//   'lg:w-6',
//   'lg:h-6',
//   'lg:w-14',
//   'lg:h-14',
//   'lg:w-16',
//   'lg:h-16',
//   'w-1/2',
//   'py-6',
//   'text-3xl',
//   'bg-[#1CC500]',
//   'text-[#ffffff]',
//   'py-2',
//   'px-80',
//   'justify-center',
//   'hover:bg-primary-300',
//   'font-bold',
//   'pr-200',
//   'pl-200',
//   'flex-col',
//   'mt-8',
//   'mx-auto',
//   'justify-between',
//   'p-4',
//   'border-r',
//   'sm:max-w-[800px]',
//   'pb-16',
//   'shadow-md',
//   'h-screen',
//   'overflow-y-auto',
//   'space-y-4',
//   'space-y-6',
//   'min-h-screen',
//   'grid-cols-1',
//   'cursor-pointer',
//   'cursor-not-allowed',
//   'opacity-50',
//   'z-[9999]',
//   'px-4',
//   'px-6',
//   'px-8',
//   'pt-16',
//   'pb-8',
//   'px-3',
//   'py-4',
//   'pl-8',
//   'py-1.5',
//   'pl-2',
//   'mb-auto',
//   'mb-6',
//   'tracking-wider',
//   'text-grey-400',
//   'text-grey-500',
//   'text-grey-600',
//   'text-status-danger',
//   'text-status-success',
//   '!fill-white',
//   'border-blue-500',
//   'border-blue-600',
//   'border-gray-100',
//   'border-gray-200',
//   'hover:bg-gray-50',
//   'bg-primary-100',
//   'hover:bg-primary-200',
//   'flex-wrap',
//   'items-stretch',
//   'gap-1',
//   'gap-3',
//   'space-x-2',
//   'space-x-3',
//   'transition-colors',
//   'animate-spin',
//   'hover:bg-gray-800',
//   'focus:outline-none',
//   'focus:ring-2',
//   'focus:ring-offset-2',
//   'select-none',
//   'last:border-b-0',
//   'active:bg-gray-200',
//   'disabled:opacity-50',
//   'disabled:cursor-not-allowed',
//   'outline-none',
//   'bg-transparent',
//   'border-gray-200',
//   'border-l-none',
//   'hover:cursor-pointer',
//   'data-[state=open]:animate-in',
//   'data-[state=closed]:animate-out',
//   'shadow-lg',
//   'bg-white/30',
//   'max-w-sm',
//   'text-gray-300',
//   'text-gray-700',
//   'text-gray-900',
//   'font-semibold',
//   'bg-orange-100',
//   'rounded-full',
//   'shadow-xl',
//   'hover:text-gray-600',
//   'space-y-3',
//   'focus:ring-blue-500',
//   'mx-4',
//   'w-9',
//   'transition',
//   'pointer-events-none',
//   'whitespace-nowrap',
//   'prose',
//   'max-w-none',
//   'bg-opacity-50',
//   'w-[1000px]',
//   'max-w-[90vw]',
//   'bottom-24',
//   'right-24',
//   'transition-opacity',
//   'opacity-50',
//   'cursor-not-allowed',
//   'rounded-l-none',
//   'inline-flex',
//   'border-[#DEDEDE]',
//   'h-[23px]',
//   'min-w-[58px]',
//   'font-mono',
//   'sm:w-[90%]',
//   'md:w-[85%]',
//   'lg:w-[80%]',
//   'xl:w-[75%]',
//   'h-3',
//   'h-px',
//   'min-h-0',
//   'w-3',
//   'mb-8',
//   'mr-1',
//   'mx-1',
//   'py-0.5',
//   'space-y-1',
//   'break-all',
//   'bg-amber-50',
//   'border-l',
//   'col-span-1',
//   'col-span-11',
//   'grid-cols-12',
//   'grid-cols-4',
//   'block',
//   'disabled:bg-gray-400',
//   'hover:bg-gray-200',
//   'hover:border-gray-500',
//   'inline-block',
//   'top-full',
//   'space-y-3',
//   'shadow-xl',
//   'hover:text-gray-600',
//   'focus:ring-blue-500',
//   'mx-4',
//   'w-9',
//   'transition',
//   'pointer-events-none',
//   'whitespace-nowrap',
//   'prose',
//   'max-w-none',
//   'bg-opacity-50',
//   'relative',
//   'absolute',
//   'fixed',
//   'inset-0',
//   'bottom-full',
//   'right-0',
//   'z-50',
//
//   // 动画相关
//   'animate-spin',
//   'animate-in',
//   'animate-out',
//   'fade-in-0',
//   'fade-out-0',
//
//   // 其他交互状态
//   'hover:opacity-50',
//   'focus:outline-none',
//   'disabled:opacity-50',
//   'disabled:cursor-not-allowed',
//   'group-hover:visible',
//   'group-hover:opacity-100',
//   // ChatManager 新增样式
//   'w-[60%]',
//   'w-[70%]',
//   'lg:w-[65%]',
//   'xl:w-[70%]',
//   'min-w-[400px]',
//   'min-w-[250px]',
//   'lg:min-w-[280px]',
//   'xl:min-w-[300px]',
//   'lg:gap-3',
//   'xl:gap-4',
//   '2xl:gap-6',
//   'lg:p-3',
//   'xl:p-4',
//   'transition-all',
//   'duration-200',
//   'h-full',
//   'w-full',
//   'flex-col',
//   'max-h-[80vh]',
//   'w-[65%]',
//   'min-h-[120px]',
//   'max-w-[200px]',
//   'size-48',
//   'grid-cols-12',
//   'col-span-1',
//   'col-span-11',
//   'focus:ring-blue-500',
//   'hover:bg-gray-800',
//   'bg-blue-100',
//   'bg-amber-50',
//   'text-blue-700',
//   'focus:ring-2',
//   'focus:ring-blue-500',
//   'border-dashed',
//   'rounded-[4px]',
//   'min-w-[120px]',
//   'min-w-[100px]',
//   'max-w-none',
//   'whitespace-pre-line',
//   'hover:border-gray-500',
//   'text-red-800',
//   'bg-red-100',
//   'flex-wrap',
//   'relative',
//   'sticky',
//   'top-full',
//   'bottom-full',
//   'inset-0',
//   'z-10',
//   'z-20',
//   'z-[100]',
//   '-translate-x-1/2',
//   '-translate-y-1/2',
//   'transform',
//   'min-h-[120px]',
//   'min-h-[150px]',
//   'min-w-[100px]',
//   'min-w-[120px]',
//   'max-h-32',
//   'max-h-80',
//   'max-h-96',
//   'max-h-[80vh]',
//   'max-w-sm',
//   'max-w-md',
//   'max-w-lg',
//   'max-w-xl',
//   'max-w-3xl',
//   'max-w-4xl',
//   'max-w-[90vw]',
//   'max-w-[200px]',
//   'max-w-[600px]',
//   'w-80',
//   'w-96',
//   'h-20',
//   'h-24',
//   'h-40',
//   'h-48',
//   'm-2',
//   'm-3',
//   'mb-8',
//   'mt-0.5',
//   'mt-1',
//   'mt-6',
//   'mx-1',
//   'mx-4',
//   'p-8',
//   'py-0.5',
//   'py-4',
//   'py-6',
//   'border-l-none',
//   'border-black',
//   'border-gray-300',
//   'border-green-500',
//   'border-red-500',
//   'border-dashed',
//   'rounded-[4px]',
//   'rounded-t-2xl',
//   'rounded-b-none',
//   'rounded-xl',
//   'bg-white/30',
//   'bg-opacity-40',
//   'bg-opacity-50',
//   'bg-amber-50',
//   'bg-amber-50/50',
//   'bg-blue-50',
//   'bg-green-50',
//   'bg-orange-50',
//   'bg-orange-50/50',
//   'bg-orange-100',
//   'bg-red-50',
//   'bg-[#F8A04A]',
//   'text-gray-300',
//   'text-gray-700',
//   'text-gray-800',
//   'text-gray-900',
//   'text-blue-600',
//   'text-blue-700',
//   'text-orange-400',
//   'text-orange-500',
//   'text-red-800',
//   'shadow-lg',
//   'shadow-xl',
//   'opacity-0',
//   'transition',
//   'transition-opacity',
//   'duration-150',
//   'whitespace-pre-line',
//   'whitespace-pre-wrap',
//   'whitespace-nowrap',
//   'font-mono',
//   'leading-tight',
//   'leading-relaxed',
//   'break-all',
//   'break-words',
//   'hover:bg-orange-50',
//   'hover:bg-orange-400',
//   'hover:bg-orange-500',
//   'hover:text-orange-500',
//   'hover:text-gray-600',
//   'hover:border-gray-500',
//   'hover:opacity-50',
//   'hover:scale-105',
//   'active:scale-95',
//   'focus:ring-1',
//   'focus:ring-orange-400',
//   'placeholder:text-gray-400',
//   'placeholder:italic',
//   'group-hover:visible',
//   'group-hover:opacity-100',
//   'inline-block',
//   'inline-flex',
//   'invisible',
//   'pointer-events-none',
//   'scroll-smooth',
//   'resize-none',
//   'data-[state=open]:animate-in',
//   'data-[state=closed]:animate-out',
//   'data-[state=closed]:fade-out-0',
//   'data-[state=open]:fade-in-0',
//   'hover:cursor-pointer',
//   'disabled:bg-gray-300',
//   'disabled:hover:bg-gray-300',
//   'disabled:text-gray-400',
//   'min-h-20',
//   '-right-1.5',
//   '-top-1.5',
//   'bottom-6',
//   'right-6',
//   'h-9',
//   'h-3',
//   'h-2',
//   'h-px',
//   'w-24',
//   'w-2',
//   'p-2.5',
//   'p-0.5',
//   'ml-2',
//   'mr-2',
//   'hover:bg-blue-50',
//   'active:bg-blue-500',
//   'bottom-24',
//   'right-24',
//   'origin-top-right',
//   'origin-bottom-left',
//   'leading-6',
//   'sm:w-[90%]',
//   'md:w-[85%]',
//   'lg:w-[80%]',
//   'xl:w-[75%]',
//   'w-[80%]',
//   'w-[20%]',
//   'max-h-[80vh]',
//   'min-h-[120px]',
//   'break-all',
//   'break-words',
//   'whitespace-pre-wrap',
//   'overflow-hidden',
//   'text-ellipsis',
// ];
// for (let i = 0; i < manualQueueTailwindClasses.length; i++) {
//   safelist.push(manualQueueTailwindClasses[i]);
// }

// const messageTailwindClasses = [
//   'flex',
//   'flex-col',
//   'flex-row',
//   'flex-grow',
//   'flex-shrink-0',
//   'flex-1',
//   'flex-wrap',
//   'items-start',
//   'items-end',
//   'items-center',
//   'items-stretch',
//   'justify-start',
//   'justify-end',
//   'justify-center',
//   'justify-between',
//   'self-start',
//   'self-end',
//   'relative',
//   'absolute',
//   'fixed',
//   'inset-0',
//   'left-1/2',
//   'left-0',
//   'right-2',
//   'top-1/2',
//   'top-0',
//   'transform',
//   '-translate-x-1/2',
//   '-translate-y-1/2',
//   'translate-x-0',
//   'translate-y-0',
//   'w-px',
//   'w-1',
//   'w-4',
//   'w-5',
//   'w-6',
//   'w-7',
//   'w-8',
//   'w-10',
//   'w-12',
//   'w-14',
//   'w-16',
//   'w-1/2',
//   'w-full',
//   'w-auto',
//   'w-max',
//   'w-[30%]',
//   'w-[40%]',
//   'w-[50%]',
//   'w-[70%]',
//   'h-4',
//   'h-5',
//   'h-6',
//   'h-7',
//   'h-8',
//   'h-10',
//   'h-12',
//   'h-14',
//   'h-16',
//   'h-48',
//   'h-full',
//   'h-screen',
//   'h-[10%]',
//   'h-[40%]',
//   'h-[60%]',
//   'h-[90%]',
//   'h-[700px]',
//   'h-[calc(100%-88px)]',
//   'min-w-0',
//   'min-w-[8rem]',
//   'min-w-[20rem]',
//   'max-w-xs',
//   'max-w-[34rem]',
//   'max-w-[600px]',
//   'sm:max-w-[800px]',
//   'max-h-48',
//   'max-h-96',
//   'min-h-screen',
//   'gap-1',
//   'gap-2',
//   'gap-3',
//   'gap-4',
//   'gap-6',
//   'gap-8',
//   'gap-x-3',
//   'gap-x-4',
//   'gap-y-3',
//   'gap-y-4',
//   'space-x-2',
//   'space-x-3',
//   'space-x-4',
//   'space-y-2',
//   'space-y-4',
//   'space-y-6',
//   'space-y-8',
//   'p-1',
//   'p-1.5',
//   'p-2',
//   'p-3',
//   'p-4',
//   'px-1',
//   'px-2',
//   'px-3',
//   'px-4',
//   'px-6',
//   'px-8',
//   'px-80',
//   'py-1',
//   'py-1.5',
//   'py-2',
//   'py-4',
//   'py-6',
//   'pt-1',
//   'pt-2',
//   'pt-3',
//   'pt-16',
//   'pb-2',
//   'pb-4',
//   'pb-8',
//   'pb-16',
//   'pl-2',
//   'pl-3',
//   'pl-4',
//   'pl-8',
//   'pl-200',
//   'pr-1',
//   'pr-2',
//   'pr-3',
//   'pr-4',
//   'pr-8',
//   'pr-200',
//   'mt-1',
//   'mt-2',
//   'mt-4',
//   'mt-5',
//   'mt-8',
//   'mb-1',
//   'mb-2',
//   'mb-4',
//   'mb-6',
//   'mb-auto',
//   'mx-3',
//   'mx-auto',
//   'sm:p-2',
//   'sm:pl-3',
//   'md:p-2.5',
//   'lg:p-2',
//   'lg:pt-2',
//   'bg-white',
//   'bg-black',
//   'bg-gray-50',
//   'bg-gray-100',
//   'bg-gray-200',
//   'bg-gray-500',
//   'bg-grey-100',
//   'bg-grey-200',
//   'bg-green-500',
//   'bg-red-500',
//   'bg-yellow-500',
//   'bg-orange-400',
//   'bg-orange-500',
//   'bg-blue-500',
//   'bg-primary-100',
//   'bg-primary-300',
//   'bg-status-info',
//   'bg-status-success',
//   'bg-[#1CC500]',
//   'bg-[#D9D9D9]',
//   'bg-[#DEDEDE]',
//   'bg-[#FF271C]',
//   'bg-[#FFAC4A]',
//   'bg-[#F8F8F8]',
//   'text-white',
//   'text-black',
//   'text-black-200',
//   'text-black-600',
//   'text-gray-400',
//   'text-gray-500',
//   'text-gray-600',
//   'text-gray-700',
//   'text-grey-400',
//   'text-grey-500',
//   'text-grey-600',
//   'text-red-500',
//   'text-green-500',
//   'text-yellow-500',
//   'text-orange-500',
//   'text-blue-500',
//   'text-primary',
//   'text-remark',
//   'text-status-danger',
//   'text-status-success',
//   'text-[#1CC500]',
//   'text-[#949494]',
//   'text-[#DEDEDE]',
//   'text-[#FF271C]',
//   'text-[#ffffff]',
//   'text-[14px]',
//   'text-[20px]',
//   'text-[24px]',
//   'text-xs',
//   'text-sm',
//   'text-base',
//   'text-lg',
//   'text-xl',
//   'text-2xl',
//   'text-3xl',
//   'text-4xl',
//   'text-body',
//   'text-t6',
//   'text-left',
//   'text-center',
//   'text-end',
//   'sm:text-base',
//   'sm:text-lg',
//   'md:text-lg',
//   'md:text-xl',
//   'lg:text-l',
//   'lg:text-sm',
//   'lg:text-xl',
//   'lg:text-2xl',
//   'border',
//   'border-0',
//   'border-2',
//   'border-t',
//   'border-b',
//   'border-r',
//   'border-x',
//   'border-t-2',
//   'border-b-2',
//   'border-orange-500',
//   'border-blue-500',
//   'border-blue-600',
//   'border-gray-100',
//   'border-gray-200',
//   'border-grey-200',
//   'border-zinc-300',
//   'border-primary-600',
//   'border-t-gray-300',
//   'border-t-grey-300',
//   'border-none',
//   'rounded',
//   'rounded-sm',
//   'rounded-md',
//   'rounded-lg',
//   'rounded-full',
//   'rounded-2xl',
//   'box-border',
//   'shadow',
//   'shadow-sm',
//   'shadow-md',
//   'shadow-lg',
//   'shadow-2xl',
//   'shadow-button-primary',
//   'shadow-button-secondary',
//   'shadow-button-common',
//   'shadow-chat-window-input',
//   'opacity-0',
//   'opacity-50',
//   'opacity-70',
//   'opacity-100',
//   'transition-all',
//   'transition-colors',
//   'transition-transform',
//   'transition-opacity',
//   'duration-200',
//   'duration-300',
//   'animate-spin',
//   'data-[state=open]:animate-in',
//   'data-[state=closed]:animate-out',
//   'data-[state=closed]:fade-out-0',
//   'data-[state=open]:fade-in-0',
//   'container',
//   'grid',
//   'grid-cols-1',
//   'grid-cols-2',
//   'grid-cols-3',
//   'auto-rows-fr',
//   'grid-rows-2',
//   'row-span-full',
//   'col-end-3',
//   'col-end-4',
//   'object-cover',
//   'object-contain',
//   'overflow-hidden',
//   'overflow-auto',
//   'overflow-y-auto',
//   'overflow-x-hidden',
//   'overflow-clip',
//   'scroll-smooth',
//   'scrollbar-hide',
//   'whitespace-pre-wrap',
//   'whitespace-normal',
//   'break-words',
//   'truncate',
//   'italic',
//   'font-medium',
//   'font-semibold',
//   'font-bold',
//   'capitalize',
//   'tracking-wider',
//   'leading-relaxed',
//   'cursor-pointer',
//   'cursor-not-allowed',
//   'select-none',
//   'outline-none',
//   'fill-white',
//   'fill-black',
//   '!fill-white',
//   'shrink-0',
//   'size-8',
//   'size-[10px]',
//   'z-2',
//   'z-50',
//   'z-[999]',
//   'z-[9999]',
//   'z-[99999]',
//   '-bottom-3.5',
//   '-bottom-6',
//   'left-[25%]',
//   'hover:bg-gray-50',
//   'hover:bg-gray-100',
//   'hover:bg-gray-800',
//   'hover:bg-grey-300',
//   'hover:bg-orange-400',
//   'hover:bg-primary-200',
//   'hover:bg-primary-300',
//   'hover:text-primary',
//   'hover:text-black-200',
//   'hover:border-none',
//   'hover:border-orange-400',
//   'hover:scale-105',
//   'active:bg-gray-200',
//   'active:bg-grey-400',
//   'active:border-orange-500',
//   'active:scale-95',
//   'focus:border-orange-500',
//   'focus:ring-orange-500',
//   'focus:outline-none',
//   'focus:ring-2',
//   'focus:ring-offset-2',
//   'focus:shadow-none',
//   'disabled:bg-gray-200',
//   'disabled:bg-grey-400',
//   'disabled:text-gray-400',
//   'disabled:opacity-50',
//   'disabled:cursor-not-allowed',
//   'group-hover:visible',
//   'group-hover:opacity-100',
//   'placeholder:text-gray-400',
//   'placeholder:italic',
//   'last:border-b-0',
//   'group',
//   'w-[28rem]',
//   'max-w-3xl',
//   'bg-[#F0F0F0]',
//   'bg-white-100',
//   'h-[calc(100vh_-_64px)]',
//   'w-[520px]',
//   'h-40',
//   'pl-9',
//   'p-2.5',
//   'bg-[#f8a04a]',
//   'bg-opacity-40',
//   'text-orange-400',
//   'text-gray-800',
//   'text-blue-600',
//   'border-dashed',
//   'border-gray-50',
//   'hover:bg-orange-50',
//   'hover:text-orange-500',
//   'focus:ring-1',
//   'focus:ring-orange-400',
//   'duration-150',
//   'inset-y-0',
//   'z-10',
//   'hidden',
//   'max-w-sm',
//   'h-9',
//   'h-2',
//   'w-2',
//   'w-24',
//   'w-80',
//   'max-w-4xl',
//   'max-h-80',
//   'p-8',
//   'm-2',
//   'mt-6',
//   'text-right',
//   'text-gray-100',
//   'text-gray-200',
//   'text-gray-300',
//   'bg-red-50',
//   'bg-blue-50',
//   'bg-white/30',
//   'border-b-0',
//   'border-t-transparent',
//   'border-yellow-500',
//   'overflow-x-auto',
//   'sticky',
//   'static',
//   'left-2',
//   'right-0',
//   'bottom-0',
//   'z-9999',
//   'translate-x-50%',
//   'hover:bg-red-50',
//   'w-96',
//   'rounded-t-2xl',
//   'rounded-b-none',
//   '-right-1.5',
//   '-top-1.5',
//   'bg-orange-50/50',
//   'bottom-6',
//   'right-6',
//   'p-2 rounded-full',
//   'hover:bg-orange-500',
//   'max-h-32',
//   'min-h-20',
//   'left-50%',
//   'z-20',
//   'h-20',
//   'h-24',
//   'w-36',
//   'p-0.5',
//   'm-3',
//   'mt-0.5',
//   'ml-2',
//   'mr-2',
//   'border-black',
//   'border-gray-300',
//   'border-green-500',
//   'border-red-500',
//   'rounded-xl',
//   'bg-gray-300',
//   'bg-gray-800',
//   'bg-green-50',
//   'bg-orange-50',
//   'bg-orange-100',
//   'bg-[#F8A04A]',
//   'text-gray-900',
//   'leading-tight',
//   'hover:cursor-pointer',
//   'disabled:bg-gray-300',
//   'disabled:hover:bg-gray-300',
//   'translateX(-50%)',
//   'resize-none',
// ];
// for (let i = 0; i < messageTailwindClasses.length; i++) {
//   safelist.push(messageTailwindClasses[i]);
// }

export const GLOBAL_TAILWIND_CONFIG: Config = {
  content: [
    'libs/design-system/**/*.{js,ts,jsx,tsx}',
    path.join(__dirname, './pages/**/*.{js,ts,jsx,tsx}'),
    path.join(__dirname, './components/**/*.{js,ts,jsx,tsx}'),
    path.join(__dirname, './app/**/*.{js,ts,jsx,tsx}'),
  ],
  safelist,
  theme: {
    extend: {
      screens: {
        '3xl': '1601px',
        '4xl': '1681px',
      },
      height: {
        field: '51px',
      },
      fontSize: THEME_FONT_SIZES,
      colors: { ...THEME_FONT_COLORS },
      boxShadow: {
        'button-primary': '4px 4px 24px 0px rgba(255, 172, 74, 0.40)',
        'button-secondary': '4px 4px 24px 0px rgba(0, 163, 255, 0.20)',
        'button-blank': '4px 4px 24px 0px rgba(0, 0, 0, 0.20)',
        'button-common': '0px 0px 8px 0px rgba(0, 0, 0, 0.16)',
        'button-callButton': '0px 3px 3px 0px rgba(241, 229, 196, 1)',
        card: '0px 4px 32px 0px rgba(0, 0, 0, 0.12)',
        dot: '0px 0px 8px 0px rgba(0, 0, 0, 0.16)',
        'contact-window': '4px 0px 40px 0px rgba(0, 0, 0, 0.08)',
        'contact-item': '0px 0px 16px 0px rgba(255, 172, 74, 0.40)',
        'chat-window-input': '0px -4px 16px 0px rgba(0, 0, 0, 0.24)',
        field: '0px 0px 24px 0px rgba(230, 83, 0, 0.12)',
        'tab-selected': '0px -2px 0px 0px inset rgba(255, 172, 74)',
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic':
          'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
      },
      animation: {
        'scroll-down': 'scroll-down 1s infinite',
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        flashing: 'flashing 2s ease-in-out infinite',
        inbound: 'inbound 2s ease-in-out infinite',
        'bounce-hr': 'bounce-hr 1s infinite',
      },
      keyframes: {
        'bounce-hr': {
          '0%, 100%': {
            transform: 'translateX(0)',
          },
          '50%': {
            transform: 'translateX(5px)',
          },
        },
        flashing: {
          '0%, 100%': {
            background: '#1cc500',
            color: 'white',
            border: '1px solid #1cc500',
          },
          '50%': {
            background: 'white',
            color: '#1cc500',
            border: '1px solid #1cc500',
          },
        },
        inbound: {
          '0%, 100%': {
            background: '#1cc500',
            color: 'white',
            borderBottom: '1px solid #1cc500',
          },
          '50%': {
            background: 'white',
            color: '#1cc500',
            borderBottom: '1px solid #1cc500',
          },
        },
      },
    },
  },
  plugins: [
    require('tailwindcss-animate'),
    require('tailwind-scrollbar-hide'),
    require('@tailwindcss/container-queries'),
  ],
};

export default GLOBAL_TAILWIND_CONFIG;
