/* eslint-disable react-hooks/rules-of-hooks */
import { useState } from 'react';
import useWebSocket from 'react-use-websocket';
import { v4 as uuidv4 } from 'uuid';

const demoEvent =
  '{"event":"conversation.fc493628-3033-4912-b15d-c39c4d311f4e.calls.alerting","eventData":{"data":{"agent":[{"address":"sip:6632f4bda6d94f1bd20ec1b9+ctil.orgspan.com;tgrp=f650bf31-c0c7-40f2-8b92-5f2bb5b63def;trunk-context=null@localhost","attributes":{},"confined":false,"direction":"inbound","held":false,"id":"7611151a-4a10-4ec1-b722-b2adc05f4576","initialState":"alerting","mediaRoles":["full"],"muted":false,"peer":"506ddc6b-7f4c-45c7-ba38-7327e6b09e7c","provider":"Edge","purpose":"user","recording":false,"recordingState":"none","securePause":false,"state":"alerting","user":{"id":"68da5409-fda3-48d3-8c42-5c7e98a0431d"},"wrapupRequired":false}],"customer":[{"address":"tel:+85262373810","attributes":{},"confined":false,"direction":"inbound","externalContact":{"id":"9fe834c0-**************-2fa83c634660"},"held":false,"id":"5b1451b6-4de3-4393-9555-fe1fa944b9e7","initialState":"offering","mediaRoles":["full"],"muted":false,"name":"Mobile Number, Hong Kong","provider":"Edge","purpose":"external","recording":false,"recordingState":"none","securePause":false,"state":"offering","wrapupRequired":false}]},"deviceId":"b08d3a39-a9e8-4eb6-a672-958fb880b9b1"}}';

const webSocketClient = () => {
  // const { onChangeInteraction } = useToolbarContext();

  const [isConnected, setIsConnected] = useState(false);
  const deviceId = localStorage.getItem('deviceId') || '';

  const { sendMessage, lastMessage, readyState } = useWebSocket(
    `ws://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com/ctint/cdss-ws/ws?deviceId=${deviceId}`,
    {
      share: true,
      retryOnError: false,
      shouldReconnect: (e) => false,
      reconnectInterval: 4,
      reconnectAttempts: 2,
      onOpen: (event) => {
        sendMessage(
          JSON.stringify({
            traceId: uuidv4(),
            tenant: localStorage.getItem('tenant') || 'ctint',
            sourceId: 'ctint-bff-cdss',
            previousId: 'ctint-mf-cpp',
            deviceId,
            'cdss-authorization':
              'Bearer ' + localStorage.getItem('cdss-auth-token'),
          })
        );
        setIsConnected(true);
        // onChangeInteraction(JSON.parse(demoEvent));
      },
      onMessage: (event) => {
        console.log(4444, event);
        if (event.data === 'ping') {
          sendMessage('pong');
        }
      },
      onClose: () => {
        console.log('disconnected');
        setIsConnected(false);
      },
      onError: (event) => console.error('WebSocket error:', event),
    }
  );

  return {
    isConnected,
    sendMessage,
    lastMessage,
    readyState,
  };
};

export default webSocketClient;
