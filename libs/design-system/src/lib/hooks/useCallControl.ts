import { handleBlocking, toast, useBlocking } from '@cdss-modules/design-system';
import {
  fireCallControlAction,
  fireMakeCall,
  fireTransferCall,
  fireConsultCall,
  fireConsultCancel,
  fireConsultDisconnect,
  fireConferenceCall,
  fireIVRConferenceCall,
} from '@cdss-modules/design-system/lib/api';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import {
  CallControlRequestBody,
  MakeCallRequestBody,
  CallBlockingResponse,
} from '@cdss-modules/design-system/@types';
import { useOpenBlockingContext } from '@cdss-modules/design-system/context/BlockingContext';

export const useCallControl = () => {
  const { basePath } = useRouteHandler();
  const {handleTriggerBlocking}=useBlocking()
  const {
    stationContext: { station },
  } = useTbarContext();
  
  const call = (data: MakeCallRequestBody): Promise<string> => {
    return new Promise((resolve, reject) => {
      handleTriggerBlocking(data?.phoneNumber,"phone").then((res)=>{
        if(res){
          return
        }
        fireMakeCall(data, basePath)
        .then((res) => {
          if (res?.data?.isSuccess) {
            resolve(res?.data?.data);
          } else {
            reject('call error');
          }
        })
        .catch((error) => {
          toast({
            variant: 'error',
            title: 'Error',
            description: `${error.response.data.error}`,
          });
          reject(error);
        });
      })
      
    });
  };

  //  "hold" | "unhold" | "mute" | "unmute" | "pickup" | "disconnect"
  const callConverSationControl = (data: CallControlRequestBody) => {
    if (station?.type === 'inin_webrtc_softphone' && data.type == 'pickup') {
      const payload = {
        type: 'updateInteractionState',
        data: {
          action: data.type,
          id: data.conversationId,
        },
      };
      const softphoneElem = document.getElementById('softphone') as any;
      if (softphoneElem) {
        softphoneElem.contentWindow.postMessage(JSON.stringify(payload), '*');
      } else {
        toast({
          variant: 'error',
          title: 'Error in ' + data.type,
          description: 'Softphone not found',
        });
      }
    } else {
      fireCallControlAction(
        data.conversationId,
        data.agentParticipantId,
        data.type,
        basePath
      );
    }
  };
  const transfer = (
    conversationId: string,
    agentParticipantId: string,
    payload: {
      destinationUserId?: string;
      destinationAddress?: string;
      destinationQueueId?: string;
    }
  ) => {
    fireTransferCall(
      conversationId,
      agentParticipantId,
      payload,
      basePath
    ).catch((error) => {
      return toast({
        variant: 'error',
        title: 'Error',
        description: `${error.response.data.error}`,
      });
    });
  };
  const consult = (
    conversationId: string,
    customerParticipantId: string,
    payload: {
      destinationUserId?: string;
      destinationAddress?: string;
      destinationQueueId?: string;
    }
  ) => {
    fireConsultCall(conversationId, customerParticipantId, payload, basePath)
      .then((res) => {
        if (res?.data?.isSuccess) {
        }
      })
      .catch((error) => {
        return toast({
          variant: 'error',
          title: 'Error',
          description: `${error.response.data.error}`,
        });
      });
  };

  // Disconnect consultant
  const consultCompleted = (
    conversationId: string,
    customerParticipantId: string
  ) => {
    fireConsultCancel(conversationId, customerParticipantId, basePath);
  };

  // After consultation, transfer a call to consultant
  const consultToTransfer = (
    state: string,
    conversationId: string,
    agentParticipantId: string
  ) => {
    fireConsultDisconnect(state, conversationId, agentParticipantId, basePath);
  };

  const conference = (
    speakTo: string,
    conversationId: string,
    customerParticipantId: string
  ) => {
    fireConferenceCall(
      speakTo,
      conversationId,
      customerParticipantId,
      basePath
    );
  };

  const IVRConference = (
    conversationId: string,
    destinationAddress: string
  ) => {
    fireIVRConferenceCall(conversationId, destinationAddress, basePath);
  };
  return {
    call,
    callConverSationControl,
    transfer,
    consult,
    consultCompleted,
    consultToTransfer,
    conference,
    IVRConference,
  };
};
