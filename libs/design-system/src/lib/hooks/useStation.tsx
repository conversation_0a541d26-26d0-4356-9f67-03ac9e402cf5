import { useEffect, useState } from 'react';
import { useQuery, useInfiniteQuery } from '@tanstack/react-query';
import { useDebounce } from '@cdss-modules/design-system/lib/hooks/useDebounce';
import { toast, useRole, useRouteHandler } from '@cdss-modules/design-system';
import {
  fireGetAllStations,
  fireGetCurrentStation,
  fireGetUpdateStation,
  fireDeleteCurrentStation,
} from '@cdss-modules/design-system/lib/api';
import { TStation } from '@cdss-modules/design-system/@types/station';
export const useStation = () => {
  const { basePath } = useRouteHandler();
  const { userConfig,globalConfig } = useRole();
  const [station, setStation] = useState<TStation | undefined>(undefined);
  const [stationSearchKeyword, setStationSearchKeyword] = useState<string>('');
  const debounceStationSearchKeyword = useDebounce(stationSearchKeyword);

 const microfrontendsConfig = globalConfig?.microfrontends
 const selectStation = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableSelectStation"];

  const stationSearchHandler = useQuery({
    queryKey: ['call-control-stations-search', debounceStationSearchKeyword],
    queryFn: async () => {
      const res = await fireGetAllStations(
        basePath,
        undefined,
        debounceStationSearchKeyword
      );
      const filterWebRtcList = res?.data?.data?.data?.filter((v: TStation) => {
        if (v?.type == 'inin_webrtc_softphone') {
          return v.webrtcUserId == userConfig?.id;
        }
        return v;
      });
      res.data.data.data = filterWebRtcList;
      return res?.data?.data;
    },
    enabled: !!debounceStationSearchKeyword,
  });

  const stationHandler = useQuery({
    queryKey: ['call-control-stations-all'],
    queryFn: async () => {
      const res = await fireGetAllStations(basePath);
      const filterWebRtcList = res?.data?.data?.data?.filter((v: TStation) => {
        if (v?.type == 'inin_webrtc_softphone') {
          return v.webrtcUserId == userConfig?.id;
        }
        return v;
      });
      res.data.data.data = filterWebRtcList;
      return res?.data?.data || [];
    },
    enabled: selectStation,
  });

  const getCurrentStation = () => {
    try {
      const userId = userConfig?.id || '';
      fireGetCurrentStation(userId, basePath).then((res) => {
        const targetData = res?.data?.data?.associatedStation;
        if (targetData) {
          setStation(targetData);
        }
      });
    } catch (error) {
      console.log(error);
    }
  };
  const updateCurrentStation = async (stationId: string) => {
    try {
      const userId = userConfig?.id || '';
      const res = await fireGetUpdateStation(
        userId,
        stationId,
        station?.id,
        basePath
      );
      if (res?.data?.isSuccess) {
        // ge no event,need manual call api
        if (localStorage.getItem('loginPlatform') === 'pure-engage') {
          getCurrentStation();
        }
      }
    } catch (error: any) {
      toast({
        variant: 'error',
        title: 'Error Getting Station',
        description: (
          <div>
            <p>
              Selected {error?.response?.data?.error}. Please click{' '}
              <button
                className="font-bold"
                onClick={() => getCurrentStation()}
              >
                Refresh
              </button>{' '}
              to get latest station
            </p>
          </div>
        ),
      });
    }
  };

  const deleteCurrentStation = (stationId: string) => {
    try {
      const userId = userConfig?.id || '';
      fireDeleteCurrentStation(userId, stationId, basePath).then((res) => {
        if (res?.data?.isSuccess) {
          if (localStorage.getItem('loginPlatform') === 'pure-engage') {
          }
          setStation(undefined);
        }
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleEvenStation = (formattedEventData: any) => {
    // only gc have station event
    if (formattedEventData?.associatedStation) {
      const allStations = stationHandler?.data?.data;
      const station = allStations?.find(
        (v: TStation) => v.id == formattedEventData.associatedStation.id
      );
      formattedEventData.associatedStation.name = station?.name;
      formattedEventData.associatedStation.type = station?.type;
      setStation({
        ...formattedEventData?.associatedStation,
      });
    } else {
      setStation(undefined);
    }
  };
  return {
    station,
    stationSearchKeyword,
    setStationSearchKeyword,
    getCurrentStation,
    stationSearchHandler,
    stationHandler,
    updateCurrentStation,
    deleteCurrentStation,
    handleEvenStation,
  };
};
