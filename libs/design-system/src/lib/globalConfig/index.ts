// Logic to load global config (server side only)
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import _ from 'lodash';

export const loadGlobalConfig = (mfName: string) => {
  const yamlEnv = process?.env?.['CDSS_PUBLIC_ENVIRONMENT'] || 'build';
  const yamlConfig = process?.env?.['GLOBAL_CONFIG_FILE'] || 'null';

  console.log('yamlConfig:', yamlConfig);
  let yamlPath = path.join(
    process.cwd(),
    `apps/${mfName}/public/config/${yamlConfig}`
  );

  console.log('yamlPath1:', yamlPath);
  if (!fs.existsSync(yamlPath)) {
    yamlPath = path.join(process.cwd(), `public/config/${yamlConfig}`);
  }
  console.log('yamlPath2:', yamlPath);
  if (!fs.existsSync(yamlPath)) {
    yamlPath = path.join(
      process.cwd(),
      `public/config/ctint-global-config-${yamlEnv}.yaml`
    );
    if (!fs.existsSync(yamlPath)) {
      throw new Error(`Configuration file not found: ${yamlPath}`);
    }
  }
  const fileContents = fs.readFileSync(yamlPath, 'utf8');
  const configData = yaml.load(fileContents);
  return configData;
};

export const getGlobalConfigValue = (mfName: string, valuePath: string) => {
  const globalConfig = loadGlobalConfig(mfName);
  return _.get(globalConfig, valuePath) ?? '';
};

export default loadGlobalConfig;
