import { v4 as uuidv4 } from 'uuid';
import pino from 'pino';
import { AxiosInstance } from 'axios';

const logger = pino();

export type MFLogEntry = any;

const logWithStorage = (
  level: 'fatal' | 'info' | 'error' | 'trace' | 'debug',
  data: MFLogEntry
) => {
  if (typeof window === 'undefined') return;
  const user = JSON.parse(localStorage.getItem('session-user') || '{}');
  const logEntry = {
    ...data,
    level,
    userId: user?.id ?? 'anonymous',
    traceId: data?.traceId || uuidv4(),
    time: new Date().toISOString(),
  };
  //logger[level](logEntry); // Log to console

  // Store log in localStorage
  if (localStorage) {
    const logs = JSON.parse(localStorage.getItem('mf-logging') || '[]');
    logs.push(logEntry);
    // TODO: disable now prevent localStorage overflow - need to implement a way to clear logs
    // localStorage.setItem('mf-logging', JSON.stringify(logs));
  }
};

const getLogFromStorage = (traceId: string) => {
  if (typeof window === 'undefined') return;
  const logs = JSON.parse(localStorage.getItem('mf-logging') || '[]');
  return logs.find((log: any) => log.traceId === traceId);
};

export const mfLogger = {
  api: (type: 'request' | 'response' | 'error', data: MFLogEntry) => {
    const logLevel = type === 'error' ? 'error' : 'info';
    logWithStorage(logLevel, {
      ...data,
      logType: `api-${type}`,
    });
  },
  fatal: (data: MFLogEntry) => logWithStorage('fatal', data),
  error: (data: MFLogEntry) => logWithStorage('error', data),
  info: (data: MFLogEntry) => logWithStorage('info', data),
  trace: (data: MFLogEntry) => logWithStorage('trace', data),
  debug: (data: MFLogEntry) => logWithStorage('debug', data),
  log: (data: MFLogEntry) => logWithStorage('debug', data),
  get: (traceId: string) => getLogFromStorage(traceId),
};

export const addLoggingToAxios = (axiosInstance: AxiosInstance) => {
  axiosInstance.interceptors.request.use(
    (request) => {
      const reqUrl = request.url;
      const headers = request?.headers as any;
      mfLogger.api('request', {
        logType: 'api-request',
        traceId: headers?.traceId,
        sourceId: headers?.sourceId,
        previousId: headers?.previousId,
        message: `Request to ${reqUrl}`,
        detail: {
          ...request,
        },
      });
      return request;
    },
    (error) => {
      mfLogger.api('error', {
        logType: 'api-request',
        traceId: error?.config?.headers?.traceId,
        sourceId: error?.config?.headers?.sourceId,
        previousId: error?.config?.headers?.previousId,
        time: new Date().toISOString(),
        message: `Request (error) from ${error.config.url || error}`,
        detail: {
          ...error,
        },
      });
      return Promise.reject(error);
    }
  );

  axiosInstance.interceptors.response.use(
    (response) => {
      const headers = response?.headers as any;
      mfLogger.api('response', {
        logType: 'api-response',
        traceId: headers?.traceId,
        sourceId: headers?.sourceId,
        previousId: headers?.previousId,
        time: new Date().toISOString(),
        message: `Response from ${response.request.url}`,
        detail: {
          ...response,
        },
      });
      return response;
    },
    (error) => {
      mfLogger.api('error', {
        logType: 'api-response',
        traceId: error?.config?.headers?.traceId,
        sourceId: error?.config?.headers?.sourceId,
        previousId: error?.config?.headers?.previousId,
        time: new Date().toISOString(),
        message: `Response (error) from ${error?.config?.url || error}`,
        detail: {
          ...error,
        },
      });
      return Promise.reject(error);
    }
  );
};
