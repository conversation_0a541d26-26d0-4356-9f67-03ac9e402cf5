import { type ClassValue, clsx } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import duration from 'dayjs/plugin/duration';

import { fireLogout } from './api';
import { GLOBAL_DATE_FORMAT } from './constants';
import DOMPurify from 'dompurify';
dayjs.extend(relativeTime);
dayjs.extend(duration);

interface StatusColorMap {
  [key: string]: string;
}

interface RoutingStatusMap {
  [key: string]: string;
}

const twMerge = extendTailwindMerge({
  extend: {
    classGroups: {
      'font-size': [
        'text-t0',
        'text-t1',
        'text-t2',
        'text-t3',
        'text-t4',
        'text-t5',
        'text-t6',
        'text-body',
        'text-remark',
        'text-footnote',
        'text-mini',
      ],
      h: ['field'],
    },
  },
});

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const replaceMdPattern = (input: string): string => {
  return input.replace(/\.md\)/g, ')');
};

export const addIdsToMarkdownHeadings = (markdown: string): string => {
  const headingRegex = /^(#+) (.*)$/gm;

  return markdown.replace(headingRegex, (match, hashes, headingText) => {
    // Slugify the heading text. You might want a more sophisticated slugify function for complex cases.
    const slug = headingText
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-')
      // eslint-disable-next-line no-useless-escape
      .replace(/[^\w\-]+/g, '');
    // Return the modified heading with an ID in markdown format.
    // Markdown doesn't officially support IDs in the heading directly, so we need to use inline HTML.
    return `\n${hashes} ${headingText} <a id="${slug}" class="anchor-heading"></a>\n`;
  });
};

export const replaceClassWithClassName = (input: string): string => {
  return input.replace(/class="/g, 'className="');
};

export const formatMD = (input: string): string => {
  let result = input;
  result = replaceMdPattern(result);
  result = addIdsToMarkdownHeadings(result);
  result = replaceClassWithClassName(result);
  return result;
};

export const camelCaseToWords = (str: string) => {
  if (typeof str !== 'string') {
    return str;
  }
  return str
    .replace(/([a-z0-9])([A-Z])/g, '$1 $2') // insert a space before a capital letter if it's preceded by a lowercase letter or a digit
    .replace(/^./, function (str) {
      return str.toUpperCase();
    }) // uppercase the first character
    .trim(); // remove any leading or trailing spaces
};

export const formatAudioTime = (seconds: number) => {
  if (seconds === Infinity) {
    return '--';
  }
  const floored = Math.floor(seconds);
  let from = 14;
  let length = 5;
  // Display hours only if necessary.
  if (floored >= 3600) {
    from = 11;
    length = 8;
  }

  return new Date(floored * 1000).toISOString().substr(from, length);
};

export const getOptionLabelFromValue = (
  options: any[],
  value: string | number
) => {
  return options.find((option) => option?.value === value)?.label;
};

export const secondsToTimeDisplay = (
  seconds?: number,
  labels?: [string, string, string]
) => {
  if (!seconds) return '';
  const defaultLabels = ['h', 'm', 's'];
  const hrLabel = labels?.[0] ?? defaultLabels[0];
  const minLabel = labels?.[1] ?? defaultLabels[1];
  const secLabel = labels?.[2] ?? defaultLabels[2];
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = Math.floor(seconds % 60);

  let result = '';
  if (hours > 0) {
    result += `${hours}${hrLabel} `;
  }
  if (minutes > 0) {
    result += `${minutes}${minLabel} `;
  }
  result += `${remainingSeconds}${secLabel}`;

  return result;
};

export const secondsToFormat = (seconds: number, format = 'mm:ss') => {
  return dayjs.duration(seconds, 'seconds').format(format);
};

export const downloadFileFromUrl = (url: string, fileName: string) => {
  if (!url) return;
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', fileName);
  document.body.appendChild(link);
  link.click();
  link.remove();
};

export const removeTrailingSlash = (url: string | undefined): string => {
  if (!url) return '';
  if (url === '/') return url;
  if (url.endsWith('/')) {
    return url.slice(0, -1);
  }
  return url;
};

export const removeDeviceId = () => {
  return localStorage.removeItem('deviceId');
};

export const getLoginUrl = (basePath?: string) => {
  const port = window?.location?.port ? `:${window.location.port}` : '';
  const redirectUri = window
    ? `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/login`
    : '';
  return redirectUri;
};

export const logout = async (basePath?: string) => {
  try {
    await fireLogout(basePath);
    localStorage.removeItem('cdss-auth-token');
    localStorage.removeItem('gc-access-token');
    localStorage.removeItem('permissions');
    localStorage.removeItem('userName');
    window.location.href = `${basePath}/login`;
  } catch (error) {
    console.error('Error logging out', error);
    // remove token in local storage even if logout api failed
    localStorage.removeItem('cdss-auth-token');
    localStorage.removeItem('gc-access-token');
    localStorage.removeItem('permissions');
    localStorage.removeItem('userName');
    window.location.href = `${basePath}/login`;
  }
};

export const extractErrorMessage = (error: any) => {
  return (
    error?.response?.data?.error ??
    error?.response?.data?.message ??
    error?.message ??
    'Uncatgorized error'
  );
};

export const agentStatusColorMap: StatusColorMap = {
  away: '#ffe600',
  break: '#ffe600',
  available: '#1cc500',
  busy: '#ff271c',
  meal: '#ffe600',
  idle: '#1cc500',
  meeting: '#ff271c',
  offline: '#ff271c',
  training: '#ffe600',
  onqueue: '#1cc500',
  'on queue': '#21c0f6',
};

export const routingStatusMap: RoutingStatusMap = {
  idle: '#1cc500',
  off_queue: '#ff271c',
  interacting: '#ffe600',
  communicating: '#ffe600',
  not_responding: '#ff271c',
};

export const extractIdandStatus = (
  event: string
):
  | {
      eventType:
        | 'conversation'
        | 'agent'
        | 'station'
        | 'queues'
        | 'disconnect'
        | 'user'
        | 'conversations';
      conversationId?: string;
      eventSubType?: string; // 'state' | 'station' | 'conversation'
      monitor?: string; // 'observe | 'aggregate'
      status: string;
    }
  | undefined => {
  const parts = event?.split('.');
  if (parts?.[0] === 'conversation' || parts?.[0] === 'conversations') {
    const conversationIdPart = parts?.[1];
    const statusPart = parts[parts.length - 1];

    const conversationIdRegex = /([a-zA-Z0-9-]+)/;
    const statusRegex = /([a-zA-Z]+)/;

    const conversationIdMatch = conversationIdPart.match(conversationIdRegex);
    const statusMatch = statusPart.match(statusRegex);

    if (conversationIdMatch && statusMatch) {
      return {
        eventType: parts?.[0],
        conversationId: conversationIdMatch[1],
        status: statusMatch[1],
      };
    } else {
      throw new Error('Invalid input event');
    }
  } else if (parts?.[0] === 'agent') {
    return {
      eventType: 'agent',
      eventSubType: parts?.[1],
      monitor: parts?.[2],
      status: parts[parts.length - 1],
    };
  } else if (parts?.[0] === 'queues' || parts?.[0] === 'user') {
    return {
      eventType: parts?.[0],
      eventSubType: parts?.[2],
      monitor: parts?.[3],
      status: parts[4],
    };
  } else {
    return undefined;
  }
};

export const validateGlobalPhoneNumber = (phoneNumber: string): boolean => {
  // Regular expression pattern to match global phone numbers
  // allow pattern 6xxxxxxx | +852 6xxxxxxx | +8526xxxxxxx
  const phoneRegex = /^(\+\d{1,3}[\s]?\d{4,15}|\+\d{1,3}\d{4,15}|\d{4,15})$/;

  return phoneRegex.test(phoneNumber);
};

export const removePhoneNumberSpaces = (phoneNumber: string): string => {
  return phoneNumber?.replace(/\s/g, '');
};

export const formatInteractionTiming = (
  startTime: string,
  endTime: string
): string => {
  let result = '';
  if (!startTime && !endTime) return '';
  if (!startTime) {
    const now = dayjs();
    const end = dayjs(endTime);
    // Calculate startAt
    let endAt: string;
    if (end.isSame(now, 'day')) {
      endAt = end.format('h:mm A');
    } else if (end.isSame(now.subtract(1, 'day'), 'day')) {
      endAt = 'yesterday';
    } else {
      endAt = end.from(now);
    }

    const resultObj = { startAt: `${endAt}`, duration: '' };
    result = `${resultObj.startAt}`;
  } else {
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const now = dayjs();

    // Calculate startAt
    let startAt: string;
    if (start.isSame(now, 'day')) {
      startAt = start.format('h:mm A');
    } else if (start.isSame(now.subtract(1, 'day'), 'day')) {
      startAt = 'yesterday';
    } else {
      // startAt = start.from(now);
      startAt = start.format(GLOBAL_DATE_FORMAT);
    }

    // Calculate duration
    const interactionDuration = dayjs.duration(end.diff(start));
    let duration: string;
    if (interactionDuration.asSeconds() < 60) {
      duration = `${Math.round(interactionDuration.asSeconds())} seconds`;
    } else {
      duration = interactionDuration.humanize();
    }
    if (!endTime) duration = 'a few seconds';

    const resultObj = { startAt, duration };
    result = `${resultObj.startAt} (${resultObj.duration})`;
  }
  return result;
};

export const get2LettersFromName = (name: string): string => {
  if (!name) return '--';
  const rgx = new RegExp(/(\p{L}{1})\p{L}+/u, 'gu');

  const initials = [...name.matchAll(rgx)];

  return (
    (initials.shift()?.[1] || '') + (initials.pop()?.[1] || '')
  ).toUpperCase();
};
/**
 * Given a time string in the format "HH:MM:SS.SSS", where:
 * - HH: hours
 * - MM: minutes
 * - SS: seconds
 * - SSS: milliseconds
 *
 * Returns the time in seconds as a number.
 *
 * @param {string} timeString - the time string to convert
 * @returns {number} the time in seconds
 */
export const timeStringToSeconds = (timeString: string): number => {
  const [hours, minutes, secondsWithMs] = timeString.split(':');
  const [seconds, milliseconds] = secondsWithMs.split('.');

  let totalSeconds =
    parseInt(hours, 10) * 3600 + // 转换小时为秒
    parseInt(minutes, 10) * 60 + // 转换分钟为秒
    parseInt(seconds, 10); // 秒数
  const millisecondsInNumber = parseInt(milliseconds, 10);
  if (milliseconds && isNaN(millisecondsInNumber)) {
    totalSeconds += millisecondsInNumber / 1000; // 毫秒转
  }

  if (isNaN(totalSeconds)) return 0;
  return totalSeconds;
};
export const formatInteractionDuration = (
  startTime: string,
  endTime: string
): string => {
  let result = '';
  let duration = '';
  if (!startTime && !endTime) return '';
  if (!startTime) {
    const now = dayjs();
    const end = dayjs(endTime);
    // Calculate startAt
    let endAt: string;
    if (end.isSame(now, 'day')) {
      endAt = end.format('h:mm A');
    } else if (end.isSame(now.subtract(1, 'day'), 'day')) {
      endAt = 'yesterday';
    } else {
      endAt = end.from(now);
    }

    const resultObj = { startAt: `${endAt}`, duration: '' };
    result = `${resultObj.startAt}`;
  } else {
    const start = dayjs(startTime);
    const end = dayjs(endTime);
    const now = dayjs();
    // Calculate startAt
    let startAt: string;
    if (start.isSame(now, 'day')) {
      startAt = start.format('h:mm A');
    } else if (start.isSame(now.subtract(1, 'day'), 'day')) {
      startAt = 'yesterday';
    } else {
      // startAt = start.from(now);
      startAt = start.format(GLOBAL_DATE_FORMAT);
    }

    // Calculate duration
    const interactionDuration = dayjs.duration(end.diff(start));

    // let duration: string;
    if (interactionDuration.asSeconds() < 60) {
      duration = `${Math.round(interactionDuration.asSeconds())} seconds`;
    } else {
      const minutes = Math.floor(interactionDuration.asSeconds() / 60);
      const seconds = Math.floor(interactionDuration.asSeconds() % 60);
      duration = minutes + ':' + seconds;
    }
    if (!endTime) duration = '00:00';

    const resultObj = { startAt, duration };

    result = `${resultObj.startAt} (${resultObj.duration})`;
  }

  return duration;
};

const createResultMap = (prefix: string): Record<string, string> => ({
  pass: `${prefix}.passed`,
  passed: `${prefix}.passed`,
  fail: `${prefix}.failed`,
  failed: `${prefix}.failed`,
  'to be reviewed': `${prefix}.toBeReviewed`,
  'not evaluated': `${prefix}.notEvaluated`,
});

/**
 * Get the translation key for a given evaluation result
 * @param result - The evaluation result
 * @param prefix - The prefix for the translation keys
 * @returns The corresponding translation key or the original result if not found
 */
export const getTranslatedResult = (result: string, prefix: string): string => {
  const resultMap = createResultMap(prefix);
  const normalizedResult = result.toLowerCase();
  return resultMap[normalizedResult] || result;
};

export function replaceEmailInlineAttachments(
  emailBody: string | undefined | null,
  attachments: Array<{ isInLine?: boolean; contentId?: string; url?: string }>
): string {
  if (!emailBody) return '';

  // Get inline attachments
  const inlineAttachments = attachments.filter((item) => !!item.isInLine);

  // Process HTML content, replace inline attachment src
  let processedContent = emailBody;
  inlineAttachments.forEach((attachment) => {
    // Only process if contentId exists to avoid regex issues
    if (attachment.contentId && attachment.url) {
      processedContent = processedContent.replace(
        new RegExp(`cid:${attachment.contentId}`, 'g'),
        attachment.url
      );
    }
  });

  return processedContent;
}

/**
 * 净化HTML内容并确保所有链接在新标签页中打开
 * @param htmlContent 需要净化的HTML内容
 * @returns 净化后的HTML内容，所有链接都设置为在新标签页打开
 */
export function sanitizeEmailBody(htmlContent: string): string {
  // 首先使用DOMPurify对HTML内容进行净化
  const sanitizedContent = DOMPurify.sanitize(htmlContent, {
    FORBID_TAGS: [
      'script', // 禁止script标签，防止执行恶意JavaScript代码
      'style', // 禁止style标签，防止CSS注入攻击
      'iframe', // 禁止iframe标签，防止跨站点攻击和点击劫持
      'object', // 禁止object标签，防止加载恶意插件或文件
      'embed', // 禁止embed标签，防止加载恶意插件或媒体文件
      'form', // 禁止form标签，防止表单钓鱼攻击
    ],
    FORBID_ATTR: [
      'srcset', // 禁止srcset属性，防止加载未授权图像资源
      'xlink:href', // 禁止xlink:href属性，防止SVG中的XSS攻击
      'onerror', // 禁止onerror事件处理器，防止错误触发时执行恶意代码
      'onload', // 禁止onload事件处理器，防止加载完成时执行恶意代码
      'onclick', // 禁止onclick事件处理器，防止点击时执行恶意代码
      'onmouseover', // 禁止onmouseover事件处理器，防止鼠标悬停时执行恶意代码
    ],
    ALLOW_DATA_ATTR: false, // 不允许使用data-*属性，防止通过这些属性注入恶意数据或代码
    ADD_ATTR: ['target', 'rel'], // 允许target和rel属性，这是为了支持在新标签中打开链接
  });

  // 创建一个临时DOM元素来修改所有的锚点标签
  const tempDiv = document.createElement('div');
  tempDiv.innerHTML = sanitizedContent;

  // 查找所有锚点标签并添加target="_blank"和rel="noopener noreferrer"属性
  const anchorTags = tempDiv.querySelectorAll('a');
  anchorTags.forEach((anchor) => {
    // 设置在新标签页中打开，而不是在当前页面导航
    anchor.setAttribute('target', '_blank');
    // 添加安全属性，防止新页面访问window.opener，保护原页面免受潜在的钓鱼攻击
    anchor.setAttribute('rel', 'noopener noreferrer');
    anchor.classList.add('text-blue-600', 'underline', 'hover:text-blue-800');
  });

  // 返回修改后的HTML内容
  return tempDiv.innerHTML;
}

// 自定义CSS样式
export const emailEditorStyles = `
/* 编辑器内容基本样式 */
.ProseMirror {
  min-height: 150px;
  outline: none;
}

/* 链接样式 */
.ProseMirror a.email-editor-link {
  color: #0066cc !important;
  text-decoration: underline !important;
  cursor: pointer !important;
}

.ProseMirror a.email-editor-link:hover {
  color: #0056b3 !important;
}

/* 图片样式 - 确保维持原始尺寸 */
.ProseMirror img {
  max-width: 100%;
  height: auto;
  cursor: default;
  display: inline-block;
  position: relative; /* 确保可以进行绝对定位 */
}

/* 被选中图片的样式 */
.ProseMirror img.ProseMirror-selectednode {
  outline: 3px solid #68CEF8;
  border-radius: 2px;
}

/* 可调整大小的图片样式 */
.ProseMirror .resizable-image {
  position: relative;
  display: inline-block;
  margin: 0;
}

.ProseMirror .resizable-image img {
  display: block;
}

/* 图片右下角调整大小的控制柄 */
.ProseMirror img::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 15px;
  height: 15px;
  cursor: nwse-resize; /* 对角线调整大小的鼠标指针 */
  opacity: 0; /* 默认隐藏 */
  transition: opacity 0.2s ease, background-color 0.2s ease;
}

/* 鼠标悬停时显示调整大小的控制柄 */
.ProseMirror img:hover::after {
  opacity: 1;
  background-color: rgba(104, 206, 248, 0.6);
  border-radius: 50%;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

/* 为调整大小创建更大的点击区域 */
.ProseMirror img::before {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  cursor: nwse-resize;
}

/* 被选中图片的增强型调整大小控制柄 */
.ProseMirror .resizable-image.ProseMirror-selectednode::after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: -6px;
  width: 12px;
  height: 12px;
  background-color: #68CEF8;
  border: 2px solid white;
  border-radius: 50%;
  cursor: nwse-resize;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.3);
  opacity: 1;
}

/* 正在调整大小时的全局鼠标样式 */
body.resizing-image,
body.resizing-image * {
  cursor: nwse-resize !important;
}

/* 完整的邮件表格样式处理 */
/* 表格基本样式 */
.ProseMirror table {
  border-collapse: separate;
  margin: 0;
  overflow: hidden;
  table-layout: auto;
  width: auto;
}

.ProseMirror .email-table {
  border-collapse: inherit !important;
  border-spacing: inherit !important;
  width: inherit !important;
  table-layout: inherit !important;
}

/* 表格单元格基本样式 */
.ProseMirror td, .ProseMirror th {
  border: 1px solid #ddd;
  box-sizing: border-box;
  min-width: 1em;
  padding: 3px 5px;
  position: relative;
  vertical-align: top;
}

/* 背景色处理 */
.ProseMirror table[bgcolor],
.ProseMirror tr[bgcolor],
.ProseMirror td[bgcolor],
.ProseMirror th[bgcolor] {
  background-color: var(--table-bg-color, inherit) !important;
}

.ProseMirror [data-original-bgcolor] {
  background-color: var(--original-bgcolor, inherit) !important;
}

/* 宽度处理 */
.ProseMirror table[width],
.ProseMirror td[width],
.ProseMirror th[width] {
  width: var(--table-width, inherit) !important;
}

/* 边框处理 */
.ProseMirror table[border],
.ProseMirror td[border],
.ProseMirror th[border] {
  border-width: var(--border-width, 1px) !important;
  border-style: solid !important;
}

.ProseMirror table[bordercolor],
.ProseMirror td[bordercolor],
.ProseMirror th[bordercolor] {
  border-color: var(--border-color, #ddd) !important;
}

/* 对齐方式处理 */
.ProseMirror table[align],
.ProseMirror td[align],
.ProseMirror th[align] {
  text-align: var(--text-align, left) !important;
}

.ProseMirror table[valign],
.ProseMirror td[valign],
.ProseMirror th[valign] {
  vertical-align: var(--vert-align, top) !important;
}

/* Teams邮件特殊处理 */
.ProseMirror .teams-table {
  border-collapse: separate !important;
  border-spacing: 0 !important;
  table-layout: fixed !important;
  width: 100% !important;
}

.ProseMirror .teams-bg-cell {
  background-color: inherit !important;
}

/* 常见的Teams邮件背景色硬编码 */
.ProseMirror .teams-bg-cell[bgcolor="#A6A6A6"],
.ProseMirror td[bgcolor="#A6A6A6"],
.ProseMirror th[bgcolor="#A6A6A6"] {
  background-color: #A6A6A6 !important;
}

.ProseMirror .teams-bg-cell[bgcolor="#EAEAEA"],
.ProseMirror td[bgcolor="#EAEAEA"],
.ProseMirror th[bgcolor="#EAEAEA"] {
  background-color: #EAEAEA !important;
}

.ProseMirror .teams-bg-cell[bgcolor="#f7f7f7"],
.ProseMirror td[bgcolor="#f7f7f7"],
.ProseMirror th[bgcolor="#f7f7f7"] {
  background-color: #f7f7f7 !important;
}

.ProseMirror .teams-bg-cell[bgcolor="#f8f8f8"],
.ProseMirror td[bgcolor="#f8f8f8"],
.ProseMirror th[bgcolor="#f8f8f8"] {
  background-color: #f8f8f8 !important;
}

/* Teams按钮处理 */
.ProseMirror td[bgcolor="#6264a7"],
.ProseMirror table[bgcolor="#6264a7"] {
  background-color: #6264a7 !important;
}

/* 类属性选择器处理 */
.ProseMirror .has-bgcolor {
  background-color: inherit !important;
}

.ProseMirror .has-width {
  width: inherit !important;
}

.ProseMirror .has-border {
  border-width: inherit !important;
  border-style: inherit !important;
}

.ProseMirror .has-bordercolor {
  border-color: inherit !important;
}

.ProseMirror .has-align {
  text-align: inherit !important;
}

.ProseMirror .has-valign {
  vertical-align: inherit !important;
}

/* 特殊边框处理 */
.ProseMirror .has-custom-border {
  border: inherit !important;
}

/* 确保内联样式正确显示 */
.ProseMirror [style*="background-color"] {
  background-color: inherit !important;
}

.ProseMirror [style*="border"] {
  border: inherit !important;
}

.ProseMirror [style*="width"] {
  width: inherit !important;
}

.ProseMirror [style*="text-align"] {
  text-align: inherit !important;
}

.ProseMirror [style*="vertical-align"] {
  vertical-align: inherit !important;
}

/* 表格背景色处理 - 原始代码 */
.ProseMirror table[data-original-bgcolor],
.ProseMirror table[bgcolor] {
  background-color: var(--table-bg-color, inherit) !important;
}

.ProseMirror table[bgcolor] {
  --table-bg-color: attr(bgcolor);
}

.ProseMirror table[style*="background-color"] {
  background-color: inherit !important;
}

/* 单元格背景色处理 - 原始代码 */
.ProseMirror td[data-original-bgcolor],
.ProseMirror td[bgcolor],
.ProseMirror th[data-original-bgcolor],
.ProseMirror th[bgcolor] {
  background-color: var(--cell-bg-color, inherit) !important;
}

.ProseMirror td[bgcolor], .ProseMirror th[bgcolor] {
  --cell-bg-color: attr(bgcolor);
}

.ProseMirror td[style*="background-color"],
.ProseMirror th[style*="background-color"] {
  background-color: inherit !important;
}

/* 优化表格结构，避免Tiptap覆盖关键样式 */
.ProseMirror table.custom-table {
  border-collapse: inherit !important;
  border-spacing: inherit !important;
  background-color: inherit !important;
}

/* 确保表格背景色继承 */
.ProseMirror table.custom-table td,
.ProseMirror table.custom-table th {
  background-color: inherit !important;
}

/* 特殊处理有bgcolor数据属性的元素 */
.ProseMirror [data-bgcolor] {
  background-color: attr(data-bgcolor) !important;
}

/* 确保data-original-bgcolor属性正常工作 */
.ProseMirror [data-original-bgcolor] {
  background-color: attr(data-original-bgcolor) !important;
}

/* Microsoft特定样式的处理 */
.ProseMirror [style*="mso-"] {
  font-family: 'Calibri', 'Segoe UI', Arial, sans-serif !important;
}

/* 带有title属性的图片（通常是内联图片）特殊处理 */
.ProseMirror img[title] {
  display: inline-block;
}

/* 列表样式 */
.ProseMirror ol {
  list-style-type: decimal;
  padding-left: 2em;
}

.ProseMirror ul {
  list-style-type: disc;
  padding-left: 2em;
}

.ProseMirror li {
  margin-bottom: 0.5em;
}

/* 字体大小样式 - 修复字体大小不生效的问题 */
.ProseMirror [style*="font-size"] {
  font-size: unset;
}

/* 确保文本样式生效 */
.ProseMirror [style] {
  color: inherit;
  font-family: inherit;
}

/* 确保textStyle标记不显示背景色 */
.ProseMirror mark[data-type="textStyle"] {
  background-color: transparent;
}

/* 但是当有明确的背景色属性时，使用指定的颜色 */
.ProseMirror mark[data-type="textStyle"][style*="background-color"] {
  background-color: inherit !important;
}

/* 确保textStyle标记中的背景色能够正确显示 */
.ProseMirror mark[data-type="textStyle"][data-original-bgcolor] {
  background-color: attr(data-original-bgcolor) !important;
}

/* 预定义的字体大小类 */
.ProseMirror .text-small {
  font-size: 0.8em;
}

.ProseMirror .text-large {
  font-size: 1.4em;
}

.ProseMirror .text-xl {
  font-size: 1.8em;
}

/* 修复标题样式 */
.ProseMirror h1 {
  font-size: 2em;
  margin-top: 0.67em;
  margin-bottom: 0.67em;
  font-weight: bold;
}

.ProseMirror h2 {
  font-size: 1.5em;
  margin-top: 0.83em;
  margin-bottom: 0.83em;
  font-weight: bold;
}

.ProseMirror h3 {
  font-size: 1.17em;
  margin-top: 1em;
  margin-bottom: 1em;
  font-weight: bold;
}

/* 确保带有bgcolor的元素显示背景色 */
.ProseMirror [bgcolor] {
  background-color: attr(bgcolor) !important;
}

/* 确保有宽高属性的图片使用属性值 */
.ProseMirror img[width][height] {
  height: auto;
}

.ProseMirror .email-content {
  --bgcolor-fix-enabled: true;
}

/* 强化表格背景色处理 */
.ProseMirror table,
.ProseMirror td,
.ProseMirror th,
.ProseMirror tr {
  background-color: inherit;
}

/* 强制应用bgcolor属性 */
.ProseMirror table[bgcolor] {
  background-color: var(--bg-color, inherit) !important;
}
.ProseMirror td[bgcolor],
.ProseMirror th[bgcolor] {
  background-color: var(--bg-color, inherit) !important;
}
.ProseMirror tr[bgcolor] {
  background-color: var(--bg-color, inherit) !important;
}

/* 处理data属性背景色 */
.ProseMirror [data-original-bgcolor] {
  background-color: var(--original-bgcolor, inherit) !important;
}

/* 修复Teams邮件特殊样式 */
.ProseMirror [style*="revert"] {
  all: revert;
  background-color: inherit;
}

/* 确保Teams表格正确显示 */
.ProseMirror table[style*="background-color"] {
  background-color: inherit !important;
}

/* 不覆盖带有明确背景色的元素 */
.ProseMirror .has-bgcolor {
  background-color: inherit !important;
}

/* 对于很顽固的表格，使用特殊处理 */
.ProseMirror table[bgcolor="#A6A6A6"],
.ProseMirror td[bgcolor="#A6A6A6"] {
  background-color: #A6A6A6 !important;
}

.ProseMirror table[bgcolor="#EAEAEA"],
.ProseMirror td[bgcolor="#EAEAEA"] {
  background-color: #EAEAEA !important;
}

/* 新增：文本对齐强化规则 */
/* 确保文本对齐样式能够正确应用并覆盖其他样式 */
.ProseMirror p[style*="text-align: left"],
.ProseMirror h1[style*="text-align: left"],
.ProseMirror h2[style*="text-align: left"],
.ProseMirror h3[style*="text-align: left"],
.ProseMirror div[style*="text-align: left"],
.ProseMirror li[style*="text-align: left"],
.ProseMirror blockquote[style*="text-align: left"] {
  text-align: left !important;
}

.ProseMirror p[style*="text-align: center"],
.ProseMirror h1[style*="text-align: center"],
.ProseMirror h2[style*="text-align: center"],
.ProseMirror h3[style*="text-align: center"],
.ProseMirror div[style*="text-align: center"],
.ProseMirror li[style*="text-align: center"],
.ProseMirror blockquote[style*="text-align: center"] {
  text-align: center !important;
}

.ProseMirror p[style*="text-align: right"],
.ProseMirror h1[style*="text-align: right"],
.ProseMirror h2[style*="text-align: right"],
.ProseMirror h3[style*="text-align: right"],
.ProseMirror div[style*="text-align: right"],
.ProseMirror li[style*="text-align: right"],
.ProseMirror blockquote[style*="text-align: right"] {
  text-align: right !important;
}

/* 处理align属性的对齐方式 */
.ProseMirror p[align="left"],
.ProseMirror h1[align="left"],
.ProseMirror h2[align="left"],
.ProseMirror h3[align="left"],
.ProseMirror div[align="left"],
.ProseMirror li[align="left"],
.ProseMirror blockquote[align="left"] {
  text-align: left !important;
}

.ProseMirror p[align="center"],
.ProseMirror h1[align="center"],
.ProseMirror h2[align="center"],
.ProseMirror h3[align="center"],
.ProseMirror div[align="center"],
.ProseMirror li[align="center"],
.ProseMirror blockquote[align="center"] {
  text-align: center !important;
}

.ProseMirror p[align="right"],
.ProseMirror h1[align="right"],
.ProseMirror h2[align="right"],
.ProseMirror h3[align="right"],
.ProseMirror div[align="right"],
.ProseMirror li[align="right"],
.ProseMirror blockquote[align="right"] {
  text-align: right !important;
}

/* 处理包含TextAlign标记的段落 */
.ProseMirror .has-text-align-left {
  text-align: left !important;
}

.ProseMirror .has-text-align-center {
  text-align: center !important;
}

.ProseMirror .has-text-align-right {
  text-align: right !important;
}

/* 覆盖Tiptap默认样式 */
.ProseMirror .is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 确保段落和其他块级元素的对齐方式正确应用 */
.ProseMirror p[data-text-align="left"],
.ProseMirror h1[data-text-align="left"],
.ProseMirror h2[data-text-align="left"],
.ProseMirror h3[data-text-align="left"],
.ProseMirror div[data-text-align="left"],
.ProseMirror blockquote[data-text-align="left"] {
  text-align: left !important;
}

.ProseMirror p[data-text-align="center"],
.ProseMirror h1[data-text-align="center"],
.ProseMirror h2[data-text-align="center"],
.ProseMirror h3[data-text-align="center"],
.ProseMirror div[data-text-align="center"],
.ProseMirror blockquote[data-text-align="center"] {
  text-align: center !important;
}

.ProseMirror p[data-text-align="right"],
.ProseMirror h1[data-text-align="right"],
.ProseMirror h2[data-text-align="right"],
.ProseMirror h3[data-text-align="right"],
.ProseMirror div[data-text-align="right"],
.ProseMirror blockquote[data-text-align="right"] {
  text-align: right !important;
}

/* 新增和修改：文本背景颜色处理 - 修复选中文本设置背景色问题 */
/* 确保背景颜色能够正确应用到所有文本类型 */
.ProseMirror mark[data-type="textStyle"] {
  background-color: transparent; /* 基础状态为透明 */
}

/* 确保带有背景色的textStyle标记正确显示背景色 */
.ProseMirror mark[data-type="textStyle"][style*="background-color"] {
  background-color: inherit !important; /* 使用inherit来保持背景色 */
  padding: 0 1px; /* 添加少量内边距以确保背景色显示完整 */
  -webkit-box-decoration-break: clone; /* 确保跨行元素的背景色正确显示 */
  box-decoration-break: clone;
}

/* 增强处理文本背景色确保可见 */
.ProseMirror .has-bg-color,
.ProseMirror [data-bg-color] {
  background-color: inherit !important;
  padding: 0 1px;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/* 强调显示指定背景色的元素 */
.ProseMirror span[style*="background-color"],
.ProseMirror mark[style*="background-color"] {
  background-color: inherit !important;
  display: inline;
  padding: 0 1px;
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
}

/* 确保所有文本容器元素能够正确继承和显示背景色 */
.ProseMirror p,
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror li,
.ProseMirror div,
.ProseMirror span,
.ProseMirror a,
.ProseMirror strong,
.ProseMirror em,
.ProseMirror mark {
  background-color: inherit; /* 确保背景色能够正确继承 */
}

/* 防止透明背景色覆盖特定文本元素 */
.ProseMirror mark.text-highlight,
.ProseMirror span.text-highlight {
  background-color: inherit !important;
  padding: 0 1px;
}

/* 确保内联样式优先于编辑器默认样式 */
.ProseMirror *[style*="background-color"] {
  background-color: inherit !important;
}

/* 增强文本背景颜色在选择状态下的显示 */
.ProseMirror .ProseMirror-selectednode mark[data-type="textStyle"],
.ProseMirror .ProseMirror-selectednode span[style*="background-color"] {
  /* 高亮所选节点的背景颜色,确保可见 */
  background-color: inherit !important;
  outline: 1px dashed rgba(0, 0, 0, 0.2);
}

/* 空段落保持最小高度以显示换行效果 */
.ProseMirror p:empty {
  min-height: 1.2em !important;
  margin: 0.3em 0 !important;
}
`;
