// 模板参数接口
interface TemplateParameter {
  text?: string;
  type: 'text' | 'image';
  image?: any;
}

// 模板组件接口
interface TemplateComponent {
  parameters: TemplateParameter[];
  type: 'body' | 'header' | 'button';
  index?: number;
  sub_type?: 'url';
}

// 语言接口
interface Language {
  code: string;
}

// 发送负载模板接口
interface SendPayloadTemplate {
  components: TemplateComponent[];
  language: Language;
  name: string;
}

// 发送负载接口
interface SendPayload {
  biz_opaque_callback_data: string;
  template: SendPayloadTemplate;
  to: string;
  type: 'template';
}

// 按钮接口
interface Button {
  phone_number?: string;
  text: string;
  type: 'PHONE_NUMBER' | 'URL' | 'QUICK_REPLY';
  url?: string;
  example?: string[];
}

// 模板JSON组件接口
interface TemplateJsonComponent {
  example?: {
    body_text?: string[][];
    header_text?: string[];
  };
  text?: string;
  type: 'BODY' | 'HEADER' | 'FOOTER' | 'BUTTONS';
  format?: 'TEXT';
  buttons?: Button[];
}

// 用户信息接口
interface UserInfo {
  user_id: string;
  user_name: string;
}

// 模板JSON接口
interface TemplateJson {
  category: string;
  components: TemplateJsonComponent[];
  created_at: string;
  created_by: UserInfo;
  id: string;
  language: string;
  modified_at: string;
  modified_by: UserInfo;
  name: string;
  status: string;
}

// 响应数据接口
interface ResponseData {
  sendPlayLoad: SendPayload;
  templateJson: TemplateJson;
  CampaignName: string;
}

// API响应接口
interface BroadcastApiResponse {
  data: ResponseData | null;
  error: string;
  isSuccess: boolean;
}

// 导出所有接口
export type {
  TemplateParameter,
  TemplateComponent,
  Language,
  SendPayloadTemplate,
  SendPayload,
  Button,
  TemplateJsonComponent,
  UserInfo,
  TemplateJson,
  ResponseData,
  BroadcastApiResponse,
};
