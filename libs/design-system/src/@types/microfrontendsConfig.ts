// eslint-disable-next-line unused-imports/no-unused-imports
export interface microfrontends {
  'ctint-mf-cdss': CtintMF;
  'ctint-mf-cpp': CtintMF;
  'ctint-mf-user-admin': CtintMFUserAdmin;
  'ctint-mf-tts': CtintMF;
  'ctint-mf-wap': CtintMF;
  'ctint-mf-info': CtintMF;
  'ctint-mf-manual-queue': CtintMFManualQueue;
}

export interface CtintMF {
  auditTabNames: never[];
  host: string;
  basepath: null | string;
}

export interface CtintMFManualQueue {
  host: string;
  basepath: string;
  'manual-tab-names': ManualQueueTabName[];
}

export interface ManualQueueTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}

export interface CtintMFUserAdmin {
  host: string;
  basepath: string;
  'user-tab-names': UserTabName[];
  'audit-tab-names': UserAuditTabName[];
}

export interface UserTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}
export interface UserAuditTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  sort: boolean;
}
export interface ShowColumn {
  key: string;
  value: string;
}

export interface TAdminRolesDataResp {
  data: TAdminRole[];
  error: string;
  isSuccess: boolean;
}

export interface TAdminRole {
  name: string;
  code: string;
  state: string;
  description: string;
  platform: string;
  tenant: string;
  createTime: Date;
  updateTime: Date;
  createBy: string;
  updateBy: string;
}

export interface TAdminUserGroupDataResp {
  data: TAdminUserGroup[];
  error: string;
  isSuccess: boolean;
}

export interface TAdminUserGroup {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  state: string;
  tenant: string;
  platform: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface CombinedRoleNGroupData {
  roleNames: TAdminRole[];
  groupNames: TAdminUserGroup[];
}

export interface userGroup {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  state: string;
  tenant: string;
  platform: string;
  createTime: Date;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface userRole {
  name: string;
  code: string;
  platform: string;
  tenant: string;
  state: string;
  description: string;
  createTime: Date;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface PrimaryContactInfo {
  address: string;
  mediaType: string;
  type: string;
}

export type ManualQueueData = {
  id?: string;
  name?: string;
  email?: string;
  divisionId?: string;
  divisionName?: string;
  state?: string;
  description?: string;
  type?: string;
  platform?: string;
  tenant?: string;
  organization?: string;
  primaryContactInfo?: PrimaryContactInfo[];
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  isAllowEdit?: boolean;
  roles?: userRole[];
  roleNames?: string;
  groups?: userGroup[];
  groupNames?: string;
  conversationId?: string;
  conversationEnd?: string;
  conversationDuration?: string;
  from?: string;
  mp3URL?: string;
  wrapupRequired?: boolean;
  selfQueueId?: string;
  selfParticipantId?: string;
  queueName?: string;
};

export type ManualQueueDataResp = {
  data: {
    list: ManualQueueData[];
    total: number;
    page_size: number;
    current_page: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
    next_page: number;
    prev_page: number;
    from: number;
    to: number;
  };
  isSuccess: boolean;
};

export interface UserSearchReq {
  keyword?: string;
}

export interface AssignReq {
  isAssignToMe?: boolean;
  conversationId?: string;
  agentId?: string;
}

export interface ConversationCallBack {
  data: ManualQueueData;
  isSuccess: boolean;
}

export interface TransferReq {
  transferType?: string;
  conversationId?: string;
  id?: string;
  selfQueueId?: string;
  selfParticipantId?: string;
  state?: string;
}

export type VoiceMailInfo = {
  conversationId?: string;
  from?: string;
  mp3URL?: string;
  selfParticipantId?: string;
  selfParticipantState?: string;
  selfQueueId?: string;
  voiceMailId?: string;
  selfParticipantStartTime?: string;
  selfParticipantEndTime?: string;
  selfParticipantWrapupRequired?: boolean;
  selfParticipantWrapupPrompt?: string;
  type?: string;
};

export type User = {
  id: string;
  name: string;
  phone?: string;
  presence?: {
    presenceDefinition?: {
      systemPresence: string;
    };
  };
  primaryContactInfo?: PrimaryContactInfo[];
};
export interface PrimaryContactInfo {
  address: string;
  mediaType: string;
  type: string;
}
