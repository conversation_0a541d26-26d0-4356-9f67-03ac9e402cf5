import { BasicPermissionChe<PERSON> } from './BasicPermission';

export class CommonPermission extends BasicPermissionChecker {
  // check if the user has permission to visit the QM page
  checkPermission: () => boolean = () => false;

  /**
   * Check if the permission is enabled for the given module, function and permission name
   * @param moduleName - The module name
   * @param functionName - The function name
   * @param permissionName - The permission name
   * @returns boolean
   */
  isPermissionEnabled: (
    moduleName: string,
    functionName: string,
    permissionName: string
  ) => boolean = (moduleName, functionName, permissionName) => {
    if (this.globalConfig?.permissionRelease) return true;

    return (
      this.permissions?.includes(
        `${moduleName}.${functionName}.${permissionName}`
      ) ?? false
    );
  };
}
