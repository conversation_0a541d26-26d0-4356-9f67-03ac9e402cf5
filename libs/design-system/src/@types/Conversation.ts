export type ConversationType =
  | 'chat' //openMessage聊天
  | 'email'
  | 'voice' // full voice
  | 'message' //sms
  | 'callback'; //voice mail ?

// 定义基础对话项目接口
export interface BaseConversationItem {
  id: string;
  userName: string;
  startTime: string;
  endTime: string;
  isActive: boolean;
  type: string;
  borderProgress: number;
  bgProgress: number;
  icon: string;
  queueId: string;
  mediaType?: string;
  connectedTime?: string;
  state?: string;
  phoneNumber?: string;
  agentId?: string;
  userAddress?: string;
}

export interface ChatConvItem extends BaseConversationItem {
  type: 'chat';
}

export interface EmailConvItem extends BaseConversationItem {
  type: 'email';
}

export interface VoiceConvItem extends BaseConversationItem {
  type: 'voice';
}

export interface CallConvItem extends BaseConversationItem {
  type: 'voice';
}

export interface MessageConvItem extends BaseConversationItem {
  type: 'message';
  integrationId: string;
  originatingDirection: string;
  hasUnread: boolean;
  latestMessage?: string;
  latestMessageTime?: string;
}

export interface CallbackConvItem extends BaseConversationItem {
  type: 'callback';
}
export interface OutboundConvItem extends BaseConversationItem {
  type: 'outbound';
}
export interface CallsConvItem extends BaseConversationItem {
  type: 'call';
}

export type ConversationItem =
  | ChatConvItem
  | EmailConvItem
  | VoiceConvItem
  | MessageConvItem
  | CallbackConvItem
  | CallsConvItem
  | OutboundConvItem;

// export {
//   ChatConvItem,
//   EmailConvItem,
//   VoiceConvItem,
//   MessageConvItem,
//   CallbackConvItem,
// };
