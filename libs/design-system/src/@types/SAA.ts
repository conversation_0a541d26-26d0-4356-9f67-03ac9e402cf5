interface SAAContentItem {
  title: string;
  content: string;
  editable: boolean;
}
interface SAAContentSection {
  timestamp?: Date;
  rawTextBody?: string;
  keywords?: string[];
  messageId?: string;
  SAAContentList: SAAContentItem[];
}

interface SAAData {
  totalCount: number;
  contents: SAAContentItem[];
}

interface SAAResponse {
  data: SAAData;
  error: string;
  isSuccess: boolean;
}
interface SAASearchTerm {
  searchTerm: string;
  triggerSearch?: boolean;
  relatedMessageContent?: {
    timestamp?: Date;
    rawTextBody?: string;
    keywords?: string[];
    messageId?: string;
  };
}
export type {
  SAAContentItem,
  SAAData,
  SAAResponse,
  SAAContentSection,
  SAASearchTerm,
};
