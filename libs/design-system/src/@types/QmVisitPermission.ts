import { BasicPermissionChecker } from './BasicPermission';

export class QmVisitPermission extends BasicPermissionChecker {
  // check if the user has permission to visit the QM page
  checkPermission: () => boolean = () => {
    return (
      this.globalConfig?.permissions?.['ctint-mf-interaction']?.qm?.visit ??
      false
    );
  };

  isPermissionEnabled: (
    moduleName: string,
    functionName: string,
    permissionName: string
  ) => boolean = (
    moduleName = 'ctint-mf-interaction',
    functionName = 'qm',
    permissionName = 'visit'
  ) => {
    // console.log('this.permissions', this.permissions);
    if (this.globalConfig?.permissionRelease) return true;
    return (
      this.permissions?.includes(
        `${moduleName}.${functionName}.${permissionName}`
      ) ?? false
    );
  };
}
