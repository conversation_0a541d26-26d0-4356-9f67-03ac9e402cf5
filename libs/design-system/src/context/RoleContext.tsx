import { useState, useContext, useEffect, ReactNode, useCallback } from 'react';
// import { useSearchParams } from 'next/navigation';
// import _ from 'lodash';
import { createRegisteredContext } from 'react-singleton-context';
import { LoadingBlock } from '../components/_ui/Loader';
import { fireGetSession } from '../lib/api';

export type TglobalConfig = {
  portals?: string[];
  services?: any;
  recording?: any;
  microfrontends?: any;
  permissions?: any;
  permissionRelease?: boolean;
  hasSidebar?: boolean;
};
export type TUserConfig = {
  firstName?: string;
  userName?: string;
  id?: string;
  roles?: string[];
  permissions: string[];
  authenticationInfo?: AuthenticationInfo;
};
type AuthenticationInfo = {
  greeting?: string;
  greetingEn?: string;
};

type TRoleContextType = {
  globalConfig?: TglobalConfig;
  userConfig?: TUserConfig;
  loading: boolean;
  devMode?: boolean;
  noHeaderMode?: boolean;
  noSiderMode?: boolean;
  updateDevMode: (value: boolean) => void;
  updateNoHeaderMode: (value: boolean) => void;
  refreshUserConfig?: () => void;
};

const RoleContext = createRegisteredContext<TRoleContextType>('RoleContext', {
  globalConfig: undefined,
  userConfig: undefined,
  loading: false,
  devMode: false,
  noHeaderMode: false,
  noSiderMode: false,
  updateDevMode: () => null,
  updateNoHeaderMode: () => null,
  refreshUserConfig: () => null,
});

export const RoleProvider = ({
  children,
  basePath = '',
}: {
  children: ReactNode;
  basePath?: string;
}) => {
  const [globalConfigLoading, setGlobalConfigLoading] = useState<boolean>(true);
  const [userConfigLoading, setUserConfigLoading] = useState<boolean>(true);
  const [globalConfig, setGlobalConfig] = useState<TglobalConfig | undefined>();
  const [userConfig, setUserConfig] = useState<TUserConfig | undefined>();
  const [devMode, setDevMode] = useState<boolean>(false);
  const [noHeaderMode, setNoHeaderMode] = useState<boolean>(
    window.location.pathname.includes('demo-toolbar')
  );
  const [noSiderMode, setNoSiderMode] = useState<boolean>(
    window.location.pathname.includes('demo-toolbar')
  );

  const getGlobalConfig = useCallback(async () => {
    try {
      const response = await fetch(`${basePath}/api/bff/ctint-auth/config`);
      const data = await response.json();
      setGlobalConfig(data?.data);
    } catch (error) {
      console.error(error);
    }
    setGlobalConfigLoading(false);
  }, []);

  const getUserConfig = useCallback(async () => {
    try {
      const haveCDSSToken = localStorage.getItem('cdss-auth-token');

      if (haveCDSSToken) {
        // TODO: use session API
        const deviceId = localStorage.getItem('deviceId') as string;
        const sessionAPIRes = await fireGetSession(deviceId, basePath); // updated to use deviceId as a key of userSession
        const sessionUserData =
          sessionAPIRes?.data?.data?.['userSession'] || {};

        setUserConfig({
          ...sessionUserData,
          id: sessionUserData?.userId,
          // permissions: [
          //   'ctint-mf-cpp.application.visit',
          //   // 'ctint-mf-cpp.recording.download',
          //   'ctint-mf-tts.application.visit',
          //   'ctint-mf-wap.application.visit',
          //   'ctint-mf-template.application.visit',
          // ]
        });
      }
    } catch (error) {
      console.error(error);
    }
    setUserConfigLoading(false);
  }, []);

  useEffect(() => {
    if (globalConfigLoading) getGlobalConfig();
  }, [getGlobalConfig, globalConfigLoading]);

  useEffect(() => {
    if (userConfigLoading) getUserConfig();
  }, [getUserConfig, userConfigLoading]);

  const loading = globalConfigLoading || userConfigLoading;

  if (loading)
    return (
      <div className="w-full h-screen flex items-center justify-center">
        <LoadingBlock />
      </div>
    );

  return (
    <RoleContext.Provider
      value={{
        globalConfig,
        userConfig,
        loading,
        devMode,
        noHeaderMode,
        noSiderMode,
        updateDevMode: (value: boolean) => setDevMode(value),
        updateNoHeaderMode: (value: boolean) => setNoHeaderMode(value),
        refreshUserConfig: getUserConfig,
      }}
    >
      {children}
    </RoleContext.Provider>
  );
};

export const useRole = () => useContext(RoleContext);
