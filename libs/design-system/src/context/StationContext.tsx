import { ReactNode, useContext, useState } from "react";
import { createRegisteredContext } from "react-singleton-context";
type TStationContextType = {
    open: boolean,
    setOpen: (open: boolean) => void
    openOption: boolean,
    setOpenOption: (open: boolean) => void
}
const StationContext = createRegisteredContext<TStationContextType>('StationContext', {
    open: false,
    setOpen: () => null,
    openOption: false,
    setOpenOption: () => null
});
export const OpenStationProvider = ({ children }: { children: ReactNode }) => {
    const [open, setOpen] = useState<boolean>(false);
    const [openOption, setOpenOption] = useState(false);
    return (
        <StationContext.Provider
            value={{
                open,
                setOpen,
                openOption,
                setOpenOption
            }}
        >
            {children}
        </StationContext.Provider>
    );
};
export const useOpenStationContext = () => useContext(StationContext);