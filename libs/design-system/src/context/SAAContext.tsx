import React, { createContext, useContext, useState, useCallback } from 'react';
import { SAASearchTerm, SAAContentSection } from '../@types/SAA';
/*此context已弃用 请勿使用*/
interface SAAContextType {
  // 手动搜索词记录 - 以键值对形式存储
  manualSAASearchTerm: Record<string, SAASearchTerm>;
  // 获取特定键对应的手动搜索词
  getManualSearchTerm: (key: string) => SAASearchTerm | undefined;
  // 设置特定键对应的手动搜索词
  setManualSearchTerm: (key: string, term: SAASearchTerm) => void;
  // 清除特定键对应的手动搜索词
  clearManualSearchTerm: (key: string) => void;

  // 搜索结果记录
  SAAResult: Record<string, SAAContentSection[]>;
  // 获取特定键对应的搜索结果
  getSAAResult: (key: string) => SAAContentSection[] | undefined;
  // 设置特定键对应的搜索结果
  setSAAResult: (key: string, result: SAAContentSection[]) => void;
  // 清除特定键对应的搜索结果
  clearSAAResult: (key: string) => void;
}

// 创建带有有意义默认值的Context，避免空方法
const defaultContext: SAAContextType = {
  manualSAASearchTerm: {},
  getManualSearchTerm: (key: string) => {
    console.warn(
      `getManualSearchTerm('${key}') 被调用，但不在 SAAProvider 内部`
    );
    return undefined;
  },
  setManualSearchTerm: (key: string, term: SAASearchTerm) => {
    console.warn(
      `setManualSearchTerm('${key}') 被调用，但不在 SAAProvider 内部`,
      term
    );
  },
  clearManualSearchTerm: (key: string) => {
    console.warn(
      `clearManualSearchTerm('${key}') 被调用，但不在 SAAProvider 内部`
    );
  },

  SAAResult: {},
  getSAAResult: (key: string) => {
    console.warn(`getSAAResult('${key}') 被调用，但不在 SAAProvider 内部`);
    return undefined;
  },
  setSAAResult: (key: string, result: SAAContentSection[]) => {
    console.warn(
      `setSAAResult('${key}') 被调用，但不在 SAAProvider 内部`,
      result
    );
  },
  clearSAAResult: (key: string) => {
    console.warn(`clearSAAResult('${key}') 被调用，但不在 SAAProvider 内部`);
  },
};

export const SAAContext = createContext<SAAContextType>(defaultContext);

export const useSAAContext = () => {
  const context = useContext(SAAContext);
  if (context === defaultContext) {
    throw new Error('useSAAContext must be used within a SAAProvider');
  }
  return context;
};

interface SAAProviderProps {
  children: React.ReactNode;
  // 可选的回调函数，用于通知外部状态变化
  onManualSearchTermUpdate?: (
    key: string,
    term: SAASearchTerm | undefined
  ) => void;
  onSAAResultUpdate?: (
    key: string,
    result: SAAContentSection[] | undefined
  ) => void;
}

export const SAAProvider: React.FC<SAAProviderProps> = ({
  children,
  onManualSearchTermUpdate,
  onSAAResultUpdate,
}) => {
  // 管理手动搜索词状态
  const [manualSearchTerms, setManualSearchTerms] = useState<
    Record<string, SAASearchTerm>
  >({});

  // 管理搜索结果状态
  const [saaResults, setSaaResults] = useState<
    Record<string, SAAContentSection[]>
  >({});

  // 手动搜索词的操作方法
  const getManualSearchTerm = useCallback(
    (key: string) => {
      return manualSearchTerms[key];
    },
    [manualSearchTerms]
  );

  const setManualSearchTerm = useCallback(
    (key: string, term: SAASearchTerm) => {
      setManualSearchTerms((prev) => {
        const updated = { ...prev, [key]: term };
        // 通知外部状态变化（如果提供回调）
        onManualSearchTermUpdate?.(key, term);
        return updated;
      });
    },
    [onManualSearchTermUpdate]
  );

  const clearManualSearchTerm = useCallback(
    (key: string) => {
      setManualSearchTerms((prev) => {
        const updated = { ...prev };
        delete updated[key];
        // 通知外部状态变化（如果提供回调）
        onManualSearchTermUpdate?.(key, undefined);
        return updated;
      });
    },
    [onManualSearchTermUpdate]
  );

  // 搜索结果的操作方法
  const getSAAResult = useCallback(
    (key: string) => {
      return saaResults[key];
    },
    [saaResults]
  );

  const setSAAResult = useCallback(
    (key: string, result: SAAContentSection[]) => {
      setSaaResults((prev) => {
        const updated = { ...prev, [key]: result };
        // 通知外部状态变化（如果提供回调）
        onSAAResultUpdate?.(key, result);
        return updated;
      });
    },
    [onSAAResultUpdate]
  );

  const clearSAAResult = useCallback(
    (key: string) => {
      setSaaResults((prev) => {
        const updated = { ...prev };
        delete updated[key];
        // 通知外部状态变化（如果提供回调）
        onSAAResultUpdate?.(key, undefined);
        return updated;
      });
    },
    [onSAAResultUpdate]
  );

  return (
    <SAAContext.Provider
      value={{
        manualSAASearchTerm: manualSearchTerms,
        getManualSearchTerm,
        setManualSearchTerm,
        clearManualSearchTerm,

        SAAResult: saaResults,
        getSAAResult,
        setSAAResult,
        clearSAAResult,
      }}
    >
      {children}
    </SAAContext.Provider>
  );
};
