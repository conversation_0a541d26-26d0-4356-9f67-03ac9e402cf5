import { ReactNode, useContext, useState } from 'react';
import { createRegisteredContext } from 'react-singleton-context';

export type TUserData = {
  conversationId: string;
  name: string;
  phoneNumber: string;
};

export type TPopupContent = {
  title: string | undefined;
  popupTo: string | undefined;
};

type TAgentEventProps = {
  address: string;
  attributes: object;
  confined: boolean;
  direction: string;
  held: boolean;
  id: string;
  initialState: string;
  mediaRoles: string[];
  muted: boolean;
  peer: string;
  provider: string;
  purpose: string;
  recording: boolean;
  recordingState: string;
  securePause: boolean;
  state: string;
  user: { id: string };
  wrapupRequired: boolean;
};

type TCustomerProps = {
  address: string;
  attributes: object;
  confined: boolean;
  direction: string;
  externalContact: { id: string };
  held: boolean;
  id: string;
  initialState: string;
  mediaRoles: string[];
  muted: boolean;
  name: string;
  provider: string;
  purpose: string;
  recording: boolean;
  recordingState: string;
  securePause: boolean;
  state: string;
  wrapupRequired: boolean;
};

export type TEventProps = {
  event: string;
  eventData: {
    data: {
      agent: TAgentEventProps[];
      customer: TCustomerProps[];
    };
    deviceId: string;
  };
};

type TToolbarContextType = {
  panelStatus: string;
  callStatus: CallStatus;
  isOnQueue: boolean;
  agentStatus: string;
  selectedWrapup: string[];
  secondCallStatus: string | null;
  workgroup: string | null;
  userData: TUserData | undefined;
  secondUserData: TUserData | undefined;
  popupContent: TPopupContent;
  interaction: TEventProps[];
  onChangePanelStatus: (
    value: PanelStatus,
    title?: string | undefined,
    popupTo?: string | undefined
  ) => void;
  onChangeCallStatus: (value: CallStatus) => void;
  onChangeQueue: () => void;
  onChangeAgentStatus: (status: string) => void;
  onChangeSelectedWrapup: (item: string[]) => void;
  onChangeSecondCallStatus: (value: string) => void;
  onDisconnect: (number: string) => void;
  onChangeWorkgroup: (workgroup: string) => void;
  onChangeUserData: (userData: TUserData) => void;
  onChangeSecondUserData: (userData: TUserData) => void;
  removeOneWrapup: (item: string) => void;
};

export type CallStatus =
  | 'Dialing'
  | 'In Call'
  | 'Conference'
  | 'IVR'
  | undefined;

export type PanelStatus =
  | 'outbound'
  | 'inbound'
  | 'openWallboard'
  | 'openWorkgrounp'
  | 'openDirectoty'
  | 'transfer'
  | 'calling'
  | 'conference'
  | 'wrapup'
  | 'agentStatusList'
  | 'openWrapup'
  | 'wrapupList'
  | 'secondCall'
  | 'popup'
  | 'ivr';

const ToolbarContext = createRegisteredContext<TToolbarContextType>(
  'ToolbarContext',
  {
    panelStatus: 'outbound',
    callStatus: undefined,
    isOnQueue: false,
    agentStatus: 'Available',
    selectedWrapup: [],
    secondCallStatus: null,
    workgroup: null,
    userData: {
      conversationId: '',
      name: '',
      phoneNumber: '',
    },
    secondUserData: {
      conversationId: '',
      name: '',
      phoneNumber: '',
    },
    popupContent: {
      title: '',
      popupTo: '',
    },
    interaction: [],
    onChangePanelStatus: () => undefined,
    onChangeCallStatus: () => null,
    onChangeQueue: () => null,
    onChangeAgentStatus: () => null,
    onChangeSelectedWrapup: () => null,
    onChangeSecondCallStatus: () => null,
    onChangeWorkgroup: () => null,
    onChangeUserData: () => null,
    onChangeSecondUserData: () => null,
    removeOneWrapup: () => null,
    onDisconnect: () => null,
  }
);

export const ToolbarProvider = ({ children }: { children: ReactNode }) => {
  const [panelStatus, setPanelStatus] = useState<PanelStatus>('outbound');
  const [callStatus, setCallStatus] = useState<CallStatus>(undefined);
  const [isOnQueue, setIsOnQueue] = useState(false);
  const [agentStatus, setAgentStatus] = useState('Available');
  const [selectedWrapup, setSelectedWrapup] = useState<string[]>([]);
  const [secondCallStatus, setSecondCallStatus] = useState<string | null>(null);
  const [workgroup, setWorkgroup] = useState<string | null>(null);
  const [userData, setUserData] = useState<TUserData | undefined>(undefined);
  const [secondUserData, setSecondUserData] = useState<TUserData | undefined>(
    undefined
  );
  const [popupContent, setPopupContent] = useState<TPopupContent>({
    title: '',
    popupTo: '',
  });

  const [interaction, setInteraction] = useState<any[]>([]);

  const onChangePanelStatus = (
    value: PanelStatus,
    title?: string,
    popupTo?: string
  ) => {
    setPanelStatus(value);
    setPopupContent({
      ...popupContent,
      title: title,
      popupTo: popupTo,
    });
  };

  const onChangeCallStatus = (status: CallStatus) => {
    setCallStatus(status);
  };

  const onChangeSecondCallStatus = (status: string) => {
    setSecondCallStatus(status);
  };

  const onChangeQueue = () => {
    setIsOnQueue(!isOnQueue);
  };

  const onChangeAgentStatus = (status: string) => {
    setAgentStatus(status);
  };

  const onChangeSelectedWrapup = (items: string[]) => {
    setSelectedWrapup([...selectedWrapup, ...items]);
  };

  const removeOneWrapup = (item: string) => {
    const updatedList = selectedWrapup.filter((selected) => selected !== item);
    console.log('updatedList', updatedList);
    setSelectedWrapup(updatedList);
  };

  const onChangeWorkgroup = (workgroup: string) => {
    setWorkgroup(workgroup);
  };

  const onChangeUserData = (user: TUserData) => {
    setUserData(user);
  };

  const onChangeSecondUserData = (user: TUserData) => {
    setSecondUserData(user);
  };

  const onDisconnect = (number: string) => {
    const firstCall = localStorage.getItem('phoneNumber');
    // const secondCall = localStorage.getItem('secondCallNumber');

    if (number === firstCall) {
      setCallStatus(undefined);
    } else {
      setSecondCallStatus(null);
    }
  };

  return (
    <ToolbarContext.Provider
      value={{
        panelStatus,
        callStatus,
        isOnQueue,
        agentStatus,
        selectedWrapup,
        secondCallStatus,
        workgroup,
        userData,
        secondUserData,
        popupContent,
        interaction,
        onChangePanelStatus,
        onChangeCallStatus,
        onChangeQueue,
        onChangeAgentStatus,
        onChangeSelectedWrapup,
        onChangeSecondCallStatus,
        onChangeWorkgroup,
        onChangeUserData,
        onChangeSecondUserData,
        removeOneWrapup,
        onDisconnect,
      }}
    >
      {children}
    </ToolbarContext.Provider>
  );
};

export const useToolbarContext = () => useContext(ToolbarContext);
