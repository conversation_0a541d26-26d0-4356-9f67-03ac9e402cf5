import React from 'react';
import * as ContextMenu from '@radix-ui/react-context-menu';
import { CDSSMessage } from '../@types/Message';

interface MessageContextMenuProps {
  message: CDSSMessage;
  setReferenceMessage?: (referenceMessage: CDSSMessage) => void;
  children: React.ReactNode;
  disabled?: boolean;
}

const MessageContextMenu: React.FC<MessageContextMenuProps> = ({
  message,
  setReferenceMessage,
  children,
  disabled = false,
}) => {
  // 如果禁用右键菜单，直接返回children
  if (disabled) {
    return <>{children}</>;
  }

  // 处理回复按钮点击
  const handleReplyClick = () => {
    console.log('Reply button clicked for message:', message.id);

    if (setReferenceMessage) {
      // 创建一个新的消息对象，清除 reference 和 referenceId
      const cleanedMessage: CDSSMessage = {
        ...message,
        reference: undefined,
        referenceId: undefined,
      };

      setReferenceMessage(cleanedMessage);
      console.log('Reference message set:', cleanedMessage);
    }
  };

  // 根据 message.status 动态判断是否禁用 Reply 按钮
  const isReplyDisabled: boolean =
    message.status === 'queued' ||
    message.status === 'failed' ||
    !message.externalMessageId;
  return (
    <ContextMenu.Root>
      <ContextMenu.Trigger asChild>{children}</ContextMenu.Trigger>

      <ContextMenu.Portal>
        <ContextMenu.Content
          className="min-w-[120px] bg-white rounded-md shadow-lg border border-gray-200 p-1 z-50"
          // sideOffset={5}
        >
          {!isReplyDisabled ? (
            <ContextMenu.Item
              className="flex items-center px-3 py-2 text-sm rounded-sm outline-none text-gray-700 hover:bg-gray-100 cursor-pointer"
              onClick={handleReplyClick}
            >
              Reply
            </ContextMenu.Item>
          ) : (
            <div className="flex items-center px-3 py-2 text-sm rounded-sm text-gray-400 cursor-not-allowed">
              Reply
            </div>
          )}
        </ContextMenu.Content>
      </ContextMenu.Portal>
    </ContextMenu.Root>
  );
};

export default MessageContextMenu;
