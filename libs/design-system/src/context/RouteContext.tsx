import React, { useState, useContext, useEffect, ReactNode } from 'react';
import { createRegisteredContext } from 'react-singleton-context';
import {
  BrowserRouter,
  useLocation,
  useSearchParams,
  useNavigate,
} from 'react-router-dom';

export interface IRoute {
  path: string;
  group?: string;
  component: JSX.Element;
  subroutes?: IRoute[];
}

type TLastVisited = {
  [group: string]: string;
};

// Create the context.
const RouteContext = createRegisteredContext<{
  lastVisited: TLastVisited;
  browsingHisotry: string[];
  toScreen: (path: string) => void;
  toGroup: (group: string) => void;
  routes: Array<IRoute>;
  pathname?: string;
  basePath?: string;
}>('RouteContext', {
  lastVisited: {},
  browsingHisotry: [],
  toScreen: () => null,
  toGroup: () => null,
  routes: [],
  pathname: '',
  basePath: '',
});

export type TRouteProviderProps = {
  routes: IRoute[];
  children: ReactNode;
  basePath?: string;
};

// Context Provider Component.
export const RouteCoreProvider: React.FC<TRouteProviderProps> = ({
  children,
  routes,
  basePath,
}) => {
  const [browsingHisotry, setBrowsingHisotry] = useState<string[]>([]);
  const [lastVisited, setLastVisited] = useState<TLastVisited>({});
  const navigate = useNavigate();

  useEffect(() => {
    const initialLastVisited = routes.reduce((acc: any, route) => {
      if (route.group) {
        acc[route.group] = route.path;
      }
      return acc;
    }, {});
    setLastVisited(() => ({
      ...initialLastVisited,
    }));
  }, [routes]);

  const toScreen = (path: string) => {
    let target = basePath ? `${basePath}${path}` : path;
    if (path === '/' && basePath) target = basePath;
    navigate(target);
  };

  const toGroup = (group: string) => {
    const lastVisitedPath = lastVisited[group];
    if (lastVisitedPath) {
      toScreen(lastVisitedPath);
    }
  };
  const [searchParams] = useSearchParams();
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;
    const currentGroup = flattenRoutes(routes).find((route) => {
      const routePattern = route.path.replace(/:[^/]+/g, '[^/]+');
      const regex = new RegExp(`^${routePattern}$`);
      return regex.test(location.pathname);
    })?.group;

    if (currentGroup) {
      setLastVisited((prevState) => ({
        ...prevState,
        [currentGroup]: currentPath,
      }));
    }
    setBrowsingHisotry((prevState) => [...prevState, currentPath]);
  }, [routes, location]);

  return (
    <RouteContext.Provider
      value={{
        pathname: location.pathname,
        toScreen,
        toGroup,
        routes,
        lastVisited,
        browsingHisotry,
        basePath,
      }}
    >
      {children}
    </RouteContext.Provider>
  );
};

export const RouteProvider = (props: TRouteProviderProps) => {
  if (typeof window === 'undefined') {
    return props.children as JSX.Element;
  }
  return (
    <BrowserRouter>
      <RouteCoreProvider {...props} />
    </BrowserRouter>
  );
};

// Helper function for flatting routes
const flattenRoutes = (
  routes: IRoute[],
  parentPath = '',
  parentGroup?: string
): IRoute[] => {
  let flatRoutes: IRoute[] = [];

  routes.forEach((route) => {
    const fullPath =
      parentPath.endsWith('/') && route.path.startsWith('/')
        ? parentPath + route.path.slice(1)
        : parentPath + route.path;
    const group = parentGroup || route.group;

    // Add the current route to the flatRoutes array
    flatRoutes.push({
      path: fullPath,
      group: group,
      component: route.component,
    });

    // If there are subroutes, recursively flatten them
    if (route.subroutes) {
      const subroutes = flattenRoutes(route.subroutes, fullPath, group);
      flatRoutes = flatRoutes.concat(subroutes);
    }
  });

  return flatRoutes;
};

// Custom hook to use the route handler.
export const useRouteHandler = () => {
  const [searchParams] = useSearchParams();
  const {
    pathname,
    toScreen,
    toGroup,
    routes,
    lastVisited,
    browsingHisotry,
    basePath,
  } = useContext(RouteContext);
  const allRoutes = flattenRoutes(routes);

  const toPath = (path: string) => {
    toScreen(path);
  };

  return {
    toPath,
    activePath: pathname,
    toGroup,
    routes,
    allRoutes,
    lastVisited,
    browsingHisotry,
    searchParams,
    basePath,
  };
};
