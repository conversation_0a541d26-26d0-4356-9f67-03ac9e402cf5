import { ReactNode, useContext, useMemo, useState } from 'react';
import { createRegisteredContext } from 'react-singleton-context';

type TTabContextType = {
    selected: any[];
    onChangeTab: (value: any[]) => void;
    onChangeDefaultMiniWallboardView?: (value: string|null|undefined) => void;
    defaultMiniWallboardView?:string|null|undefined
};

const TabsContext = createRegisteredContext<TTabContextType>('TabsContext', {
    selected: [],
    onChangeTab: () => null,
    onChangeDefaultMiniWallboardView: () => null,
    defaultMiniWallboardView:null
});

export const TabsProvider = ({ children }: { children: ReactNode }) => {
    const [selected, setSelected] = useState<any[]>([]);
    const [defaultMiniWallboardView, setdefaultMiniWallboardView] = useState<string|null|undefined>(null);

    const onChangeTab = (value: any[]) => {
        setSelected(value);
    };
    const onChangeDefaultMiniWallboardView=(value:string|null|undefined)=>{
        setdefaultMiniWallboardView(value)
    }
    return (
        <TabsContext.Provider value={{ selected, onChangeTab,onChangeDefaultMiniWallboardView ,defaultMiniWallboardView}}>
            {children}
        </TabsContext.Provider>
    );
};

export const useTabsContext = () => useContext(TabsContext);
