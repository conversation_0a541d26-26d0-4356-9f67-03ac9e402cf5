import { ReactNode, useCallback, useContext, useMemo, useState } from 'react';
import { createRegisteredContext } from 'react-singleton-context';
import useWebSocket, { ReadyState, SendMessage } from 'react-use-websocket';
import { v4 as uuidv4 } from 'uuid';
import { extractIdandStatus, logout } from '../lib/utils';
import { Button, Popup, PopupContent, toast, useRole, useRouteHandler } from '@cdss-modules/design-system';
import { on } from 'events';

export type TInteractionParticipant = {
  address: string;
  attributes: object;
  confined: boolean;
  connectedTime?: string;
  direction: string;
  externalContact?: { id: string };
  endTime?: string;
  held: boolean;
  id: string;
  initialState: string;
  mediaRoles: string[];
  muted: boolean;
  peer?: string;
  name?: string;
  provider: string;
  purpose: string;
  recording: boolean;
  recordingState: string;
  securePause: boolean;
  state: string;
  user?: { id: string };
  wrapupRequired: boolean;
  consultParticipantId?: string;
};
export type TEventProps = {
  event: string;
  eventData: {
    data: {
      agent: TInteractionParticipant[];
      customer: TInteractionParticipant[];
    };
    deviceId: string;
  };
};

export type TInteractionContextType = {
  events: TEventProps[];
  isConnected: boolean;
  lastMessage: MessageEvent<any> | null;
  readyState: ReadyState;
  sendMessage: SendMessage;
  eventData: any;
};

const InteractionContext = createRegisteredContext<TInteractionContextType>(
  'InteractionContext',
  {
    events: [],
    isConnected: false,
    lastMessage: null,
    readyState: ReadyState.CLOSED,
    sendMessage: () => null,
    eventData: null,
  }
);

const RETRY_LIMIT = 3;

export const InteractionProvider = ({ children }: { children: ReactNode }) => {
  const { globalConfig } = useRole();
  const { basePath } = useRouteHandler();
  const [events, setEvents] = useState<TEventProps[]>([]);
  const [eventOrderIncrement, setEventOrderIncrement] = useState<number>(0);
  const [wsConnectedTime, setWsConnectedTime] = useState<Date | undefined>();
  const [retryCount, setRetryCount] = useState(0);
  const [isConnected, setIsConnected] = useState(false);
  const [eventData, setEventData] = useState<any>(null);
  const [errorPopupOpen, setErrorPopupOpen] = useState(false);
  const onErrorOpenChange = (open: boolean) => {
    setErrorPopupOpen(open);
  };

  const deviceId = localStorage.getItem('deviceId') || '';
  const eventHandler = useCallback((formattedEvent: any, isWS?: boolean) => {
    const eventId = formattedEvent?.eventData?.eventId;
    const { eventType } = extractIdandStatus(formattedEvent.event) || {};
    if (isWS) {
      setEvents((eve: any) => [...eve, formattedEvent]);
      sendMessage(
        JSON.stringify({
          event: 'mf-received',
          eventId,
          wsConnectedTime: wsConnectedTime
            ? wsConnectedTime?.toISOString()
            : 'undefined',
          receivedTime: new Date().toISOString(),
          receivedOrder: eventOrderIncrement,
        })
      );
      setEventOrderIncrement((prev) => prev + 1);
    }
    if (
      eventType === 'conversation' ||
      eventType === 'agent' ||
      eventType === 'queues' ||
      eventType === 'user' ||
      eventType === 'conversations'
    ) {
      setEventData(formattedEvent);
    }
    if (eventType === 'disconnect') {
      window.location.reload();
    }
  }, []);
  // WebSocket (Recieve events from provider - e.g. GC / Engage)
  let cdssUrl =
    globalConfig?.services['ctint-cdss-ws'].host +
    globalConfig?.services['ctint-cdss-ws'].basepath;

  const wsProtocol = window.location.protocol === 'http:' ? 'ws' : 'wss';
  if (cdssUrl.includes('https')) {
    cdssUrl = cdssUrl.replace('https', wsProtocol);
  } else if (cdssUrl.includes('http')) {
    cdssUrl = cdssUrl.replace('http', wsProtocol);
  }
  const { lastMessage, readyState, sendMessage, sendJsonMessage } =
    useWebSocket(`${cdssUrl}/ws?deviceId=${deviceId}`, {
      share: true,
      retryOnError: true,
      shouldReconnect: (e) => {
        return retryCount <= RETRY_LIMIT;
      },
      reconnectInterval: 5000,
      reconnectAttempts: RETRY_LIMIT,
      onOpen: (event) => {
        sendMessage(
          JSON.stringify({
            traceId: uuidv4(),
            tenant: globalConfig?.microfrontends?.tenant,
            sourceId: 'ctint-bff-cdss',
            previousId: 'ctint-mf-cpp',
            deviceId,
            cdss_authorization:
              'Bearer ' + localStorage.getItem('cdss-auth-token'),
            gc_authorization:
              'Bearer ' + localStorage.getItem('gc-access-token'),
          })
        );
        // sendMessage("test error"); // test code
        setIsConnected(true);
        setRetryCount(0);
        setWsConnectedTime(new Date());
      },
      onMessage: (event) => {
        if (event.data === 'ping') {
          console.log('Sending pong');
          return sendMessage('pong');
        }
        if (event.data) {
          const formattedEvent = JSON.parse(event.data);
          eventHandler(formattedEvent, true);
        } else {
          sendMessage(
            // Case when I receive an non-pingpong event with no data
            JSON.stringify({
              event: 'mf-received',
              eventId: 'no-event-data',
              wsConnectedTime: wsConnectedTime
                ? wsConnectedTime?.toISOString()
                : 'undefined',
              receivedTime: new Date().toISOString(),
              receivedOrder: eventOrderIncrement,
            })
          );
        }
      },
      onClose: (e) => {
        console.log('websocket close event', e);
        setIsConnected(false);
        setRetryCount(retryCount + 1);
        if (retryCount >= RETRY_LIMIT) {
          onErrorOpenChange(true);
          // console.log('ON CLOSE => onErrorOpenChange(true);');
        }
      },
      onError: (event) => {
        sendJsonMessage({ error: 'why error' });
        setRetryCount(retryCount + 1);
        console.error('WebSocket error:', event);
        if (retryCount >= RETRY_LIMIT) {
          onErrorOpenChange(true);
          // console.log('ON ERROR => onErrorOpenChange(true);');
        }
      },
    });
  const value = useMemo(
    () => ({
      eventData,
      sendMessage,
      events,
      isConnected,
      lastMessage,
      readyState,
    }),
    [eventData, lastMessage]
  );
  return (
    <InteractionContext.Provider value={value}>
      {children}
      <Popup
        open={!!errorPopupOpen}
        onOpenChange={onErrorOpenChange}

      >
        <PopupContent
          className="w-4/5 max-w-[500px] shadow-md"
          title="WebSocket Connection Error"
          hideCloseButton
          onInteractOutside={(e) => {
            e.preventDefault();
          }}
        >
          <div className="p-8 text-center">
            <p className="mb-4">There is an error in the web socket connection. Please refresh or re-login.</p>
            <div className="flex justify-center gap-4">
              <Button
                size="mini"
                onClick={() => {
                  window.location.reload();
                }}>
                Refresh Now
              </Button>
              <Button
                size="mini" onClick={() => {
                  logout(basePath);
                }}>
                Logout Now
              </Button>
            </div>
          </div>
        </PopupContent>
      </Popup>
    </InteractionContext.Provider>
  );
};

export const useInteractionContext = () => useContext(InteractionContext);
