import { useCallback, useEffect, useState } from 'react';
import { Button, useRole } from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';

type TWebRtcBlockBody = {
  className?: string;
};

export const WebRtcBlockBody = ({ className }: TWebRtcBlockBody) => {
  const { globalConfig } = useRole();
  const iframeSrc =
    globalConfig?.microfrontends['gc-webrtc-iframe-url'] ||
    'https://apps.mypurecloud.com.au/crm/embeddableFramework.html?enableFrameworkClientId=true';

  // State variables to store the entire message for each type
  const [screenPopMessage, setScreenPopMessage] = useState<any>(undefined);
  const [processCallLogMessage, setProcessCallLogMessage] =
    useState<any>(undefined);
  const [openCallLogMessage, setOpenCallLogMessage] = useState<any>(undefined);
  const [interactionSubscriptionMessage, setInteractionSubscriptionMessage] =
    useState<any>(undefined);
  const [userActionSubscriptionMessage, setUserActionSubscriptionMessage] =
    useState<any>(undefined);
  const [notificationSubscriptionMessage, setNotificationSubscriptionMessage] =
    useState<any>(undefined);
  const [searchText, setSearchText] = useState<any>(undefined);

  useEffect(() => {
    const handleMessage = (event: any) => {
      try {
        // console.log('event?.data', event?.data);
        if (typeof event?.data !== 'string') return null;
        const message = JSON.parse(event?.data || '{}');
        if (message) {
          switch (message.type) {
            case 'screenPop':
              setScreenPopMessage(event.data);
              break;
            case 'processCallLog':
              setProcessCallLogMessage(event.data);
              break;
            case 'openCallLog':
              setOpenCallLogMessage(event.data);
              break;
            case 'interactionSubscription':
              setInteractionSubscriptionMessage(event.data);
              break;
            case 'userActionSubscription':
              setUserActionSubscriptionMessage(event.data);
              break;
            case 'notificationSubscription':
              setNotificationSubscriptionMessage(event.data);
              break;
            case 'contactSearch':
              setSearchText(message.data.searchString);
              break;
            default:
              break;
          }
        }
      } catch (error) {
        console.error('Error parsing message data:', error);
      }
    };

    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [searchText]);

  const doInteraction = useCallback(
    (action: string) => {
      console.log('process interaction state change');

      const lastInteractionPayload = JSON.parse(
        interactionSubscriptionMessage || '{}'
      );
      const interactionId =
        lastInteractionPayload?.data?.interaction?.old?.id ||
        lastInteractionPayload?.data?.interaction?.id;
      const payload = {
        action: action,
        id: interactionId,
      };
      const softphoneElem = document.getElementById('softphone') as any;
      // console.log('msg', {
      //   type: 'updateInteractionState',
      //   data: payload,
      // });
      // console.log('msg lastInteractionPayload', lastInteractionPayload);
      // console.log(
      //   'msg lastInteractionPayload?.data',
      //   lastInteractionPayload?.data
      // );
      softphoneElem.contentWindow.postMessage(
        JSON.stringify({
          type: 'updateInteractionState',
          data: payload,
        }),
        '*'
      );
    },
    [interactionSubscriptionMessage]
  );

  return (
    <div
      className={cn(
        'h-0 flex-1 flex flex-col gap-2 p-4 bg-white rounded-lg',
        className
      )}
    >
      <div className="flex gap-4 items-center">
        <h2 className="font-bold">[DEV PANEL] WebRTC:</h2>
        <Button
          size="s"
          onClick={() => doInteraction('pickup')}
        >
          WebRTC Pickup
        </Button>
        <Button
          size="s"
          onClick={() => doInteraction('disconnect')}
        >
          WebRTC Disconnect
        </Button>
        <Button
          size="s"
          onClick={() => doInteraction('hold')}
        >
          WebRTC Hold
        </Button>
        <Button
          size="s"
          onClick={() => doInteraction('mute')}
        >
          WebRTC Mute
        </Button>
        {/* <Button size="s" onClick={() => doInteraction('securePause')}>
          securePause
        </Button> */}
        <div className="w-1/2 h-20 overflow-auto border border-solid p-2 text-mini break-all">
          <div>WebRTC Subscription Message:</div>
          <div>{JSON.stringify(interactionSubscriptionMessage ?? {})}</div>
        </div>
      </div>
      <div className="h-0 flex-1 bg-red-20">
        <div className="h-full overflow-hidden">
          <div className="w-full h-full">
            <div className="softphone w-full h-full bg-purple-600">
              <iframe
                id="softphone"
                width="100%"
                height="100%"
                allow="camera *; microphone *; autoplay *; hid *"
                src={iframeSrc}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const WebRtcBlock = ({
  hidden,
  className,
}: {
  hidden?: boolean;
  className?: string;
}) => {
  if (hidden) return null;
  return <WebRtcBlockBody className={className} />;
};

export default WebRtcBlock;
