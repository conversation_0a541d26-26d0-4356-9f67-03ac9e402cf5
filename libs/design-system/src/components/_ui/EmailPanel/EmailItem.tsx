import IconArrowFullDown from '../../../components/_ui/Icon/IconArrowFullDown';
import { GLOBAL_DATETIME_SECOND_FORMAT } from '../../../lib/constants';
import dayjs from 'dayjs';
import * as Accordion from '@radix-ui/react-accordion';
import { useState } from 'react';
import EmailHeader from './EmailHeader';
import EmailAttachment from './EmailAttachment';
import { useTranslation } from 'react-i18next';
import { Attachment } from '../../../@types/email';
import {
  replaceEmailInlineAttachments,
  sanitizeEmailBody,
  downloadFileFromUrl,
} from '../../../lib/utils';

interface EmailItemProps {
  email: any;
}

const EmailItem = ({ email }: EmailItemProps) => {
  const { t } = useTranslation();
  const [openItem, setOpenItem] = useState<string | null>(null);
  const handleValueChange = (value: string) => {
    setOpenItem(value);
  };
  return (
    <Accordion.Root
      type="single"
      collapsible
      className="border-b border-gray-500"
      value={openItem || ''}
      onValueChange={handleValueChange}
    >
      <Accordion.Item value={`${email.id}`}>
        <Accordion.Header>
          <Accordion.Trigger className="w-full">
            <div className="text-left flex flex-col py-4 relative gap-2">
              {email.detail && email.detail.isDraft === 1 && (
                <div
                  className="absolute top-0 right-0 inline-flex items-center px-3 py-1 rounded text-xs font-medium"
                  style={{
                    backgroundColor: '#E6F7FF',
                    border: '1px solid #91D5FF',
                    color: '#1890FF',
                  }}
                >
                  {t('history.email.draft')}
                </div>
              )}
              {/* Email Subject */}
              <span className="text-t6 font-bold">{email.emailSubject}</span>
              {/* Email Sent Date Time */}
              <span className="text-t7 italic">
                {dayjs(email.detail.sentDateTime).format(
                  GLOBAL_DATETIME_SECOND_FORMAT
                )}
              </span>
              {/* Email Expand Icon */}
              <span
                className={`absolute right-0 bottom-1 cursor-pointer m-1 transition-transform duration-300 ease-in-out ${
                  openItem === `${email.id}` ? 'rotate-180' : 'rotate-0'
                }`}
              >
                <IconArrowFullDown
                  color={openItem === `${email.id}` ? '#FFAC4A' : '#000000'}
                  size={'10'}
                />
              </span>
            </div>
          </Accordion.Trigger>
        </Accordion.Header>
        <Accordion.Content>
          <section className="flex flex-col">
            <div className="flex flex-col gap-1">
              <div className="border-b border-grey-200"></div>
              <div className="m-2 flex flex-row gap-1">
                <div className="flex-1 flex flex-col gap-2">
                  {/* Email Sender */}
                  <div className="text-t7 flex flex-row gap-2 items-center">
                    <EmailHeader
                      label={t('history.email.from')}
                      content={
                        email?.detail?.emailSender?.emailAddress?.address
                      }
                    />
                  </div>
                  {/* Email To Recipient */}
                  {email?.detail?.emailToRecipient &&
                    email?.detail?.emailToRecipient.length > 0 && (
                      <div className="text-t7 flex flex-row gap-2 items-center">
                        <EmailHeader
                          label={t('history.email.to')}
                          content={email?.detail?.emailToRecipient
                            ?.map(
                              (recipient: any) => recipient.emailAddress.address
                            )
                            .join(', ')}
                        />
                      </div>
                    )}
                  {/* Email Cc Recipient */}
                  {email?.detail?.emailCcRecipient &&
                    email?.detail?.emailCcRecipient.length > 0 && (
                      <div className="text-t7 flex flex-row gap-2 items-center">
                        <EmailHeader
                          label={t('history.email.cc')}
                          content={email?.detail?.emailCcRecipient
                            ?.map(
                              (recipient: any) => recipient.emailAddress.address
                            )
                            .join(', ')}
                        />
                      </div>
                    )}
                  {/* Email Bcc Recipient */}
                  {email?.detail?.emailBccRecipient &&
                    email?.detail?.emailBccRecipient.length > 0 && (
                      <div className="text-t7 flex flex-row gap-2 items-center">
                        <EmailHeader
                          label={t('history.email.bcc')}
                          content={
                            Array.isArray(email?.detail?.emailBccRecipient)
                              ? email?.detail?.emailBccRecipient
                                  ?.map(
                                    (recipient: any) =>
                                      recipient.emailAddress.address
                                  )
                                  .join(', ')
                              : JSON.parse(email?.detail?.emailBccRecipient)
                                  ?.map(
                                    (recipient: any) =>
                                      recipient.emailAddress.address
                                  )
                                  .join(', ')
                          }
                        />
                      </div>
                    )}
                  {/* Email Attachments */}
                  {email?.attachments?.items &&
                    email?.attachments?.items?.filter(
                      (attachment: Attachment) => attachment.isInLine === false
                    ).length > 0 && (
                      <EmailAttachment<Attachment>
                        attachments={email?.attachments?.items}
                        onDownload={(attachment) => {
                          downloadFileFromUrl(
                            attachment.url,
                            attachment.filename
                          );
                        }}
                      />
                    )}
                </div>
              </div>
              <div className="border-b border-grey-200"></div>
            </div>
            <div
              className="text-t7"
              dangerouslySetInnerHTML={{
                __html: sanitizeEmailBody(
                  replaceEmailInlineAttachments(
                    email?.detail?.emailBody,
                    email?.attachments?.items
                  )
                ),
              }}
            />
          </section>
        </Accordion.Content>
      </Accordion.Item>
    </Accordion.Root>
  );
};

export default EmailItem;
