import { Attachment } from '../../../@types/email';
import { Paperclip, Download } from 'lucide-react';

interface EmailAttachmentProps<T extends Attachment> {
  attachments: Attachment[];
  onDownload: (attachment: T) => void;
}

const EmailAttachment = <T extends Attachment>({
  attachments = [],
  onDownload,
}: EmailAttachmentProps<T>) => {
  if (!attachments || attachments.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-row gap-1 flex-wrap items-center">
      <Paperclip className="w-4 h-4 shrink-0 mx-2" />
      {attachments
        ?.filter((attachment) => attachment.isInLine === false)
        .map((attachment, index) => (
          <div
            key={`${attachment.attachmentName}-${index}`}
            className="border border-grey-200 rounded-md px-2 py-1 flex flex-row gap-1 items-center cursor-pointer"
            onClick={() => {
              onDownload(attachment as T);
            }}
          >
            <span className="text-t7">{attachment.attachmentName}</span>
            <Download className="w-4 h-4" />
          </div>
        ))}
    </div>
  );
};

export default EmailAttachment;
