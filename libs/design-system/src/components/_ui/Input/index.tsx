'use client';

import {
  useState,
  forwardRef,
  useRef,
  ChangeEvent,
  useEffect,
  useMemo,
} from 'react';
import { TMessageStatus } from '../Message';
import { cn } from '../../../lib/utils';
import Icon from '../Icon';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
} from '../DropdownMenu/index';
import { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu';
import { useObserveElementWidth } from '../../../lib/hooks/useObserveElementWidth';
import { useDebounce } from '../../../lib/hooks/useDebounce';

export const messageClasses = {
  danger: 'text-status-danger !border-[1px] border-status-danger',
  info: 'text-status-info !border-[1px] border-status-info',
  success: 'text-status-success !border-[1px] border-status-success',
  warning: 'text-status-warning !border-[1px] border-status-warning',
};

export type InputProps = {
  testId?: string;
  isSearch?: boolean;
  beforeIcon?: React.ReactNode;
  beforeIconFn?: () => void;
  afterIcon?: React.ReactNode;
  afterIconFn?: () => void;
  children?: React.ReactNode;
  message?: string;
  status?: TMessageStatus;
  allowClear?: boolean;
  onChange?: (value: string | number) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLInputElement>) => void;
  containerClassName?: string;
  autoCompleteOptions?: {
    value: string;
    label: string;
    id: string;
  }[];
  size?: 'xs' | 's' | 'ms' | 'm' | 'l';
  autoFocus?: boolean;
  addonBefore?: React.ReactNode;
  addonAfter?: React.ReactNode;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'size'>;

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      testId,
      isSearch = false,
      beforeIcon,
      afterIcon,
      beforeIconFn,
      afterIconFn,
      value,
      message,
      allowClear = false,
      status,
      onChange,
      containerClassName,
      size = 'm',
      onKeyDown,
      autoCompleteOptions,
      autoFocus = false,
      ...props
    },
    ref
  ) => {
    const [focus, setFocus] = useState(false);
    const [autoCompleteOpen, setAutoCompleteOpen] = useState(true);
    const filteredAutoComplete = useMemo(() => {
      const isExactMatch = autoCompleteOptions?.some(
        (item) => item.value === value
      );
      if (isExactMatch) {
        return [];
      }
      return (
        autoCompleteOptions?.filter((item) => {
          if (value) {
            const adjustedValue = `${value}`.toLowerCase();
            return item.label.toLowerCase().includes(adjustedValue);
          }
          return false;
        }) || []
      );
    }, [autoCompleteOptions, value]);

    const onFocus = () => setFocus(true);
    const onBlur = () => setFocus(false);
    const inputRef = useRef<HTMLInputElement | null>(null);

    useEffect(() => {
      if (autoFocus && inputRef.current) {
        inputRef.current.focus(); // 如果 autoFocus 为 true，自动聚焦
      }
    }, [autoFocus]);

    const clearValue = () => {
      if (inputRef.current) {
        inputRef.current.value = '';
        inputRef?.current.focus();
      }
      if (ref && typeof ref === 'object' && ref.current) {
        ref.current.value = '';
        ref?.current.focus();
      }
      if (onChange) onChange('');
    };

    const debounceAutoComplete = useDebounce(filteredAutoComplete, 500);

    useEffect(() => {
      if (debounceAutoComplete?.length) {
        setAutoCompleteOpen(true);
      } else {
        setAutoCompleteOpen(false);
      }
    }, [debounceAutoComplete, ref]);
    // Container Width detection
    const { width: containerWidth, ref: containerRef } =
      useObserveElementWidth<HTMLDivElement>();

    const classes = cn(
      `relative w-full flex flex-col rounded bg-common-white border-[1px] border-grey-200 outline-none`,
      size === 'xs' && 'h-6 text-footnote',
      size === 's' && 'h-8 text-remark',
      size === 'ms' && 'h-[36px] text-body',
      size === 'm' && 'h-field text-body',
      size === 'l' && 'h-15 text-t6'
    );

    const body = (
      <div
        ref={containerRef}
        className={cn(
          classes,
          isSearch && beforeIcon && !focus && `gap-2 bg-grey-50`,
          focus && `border-primary-900 !border bg-common-white shadow-field`,
          !focus && `hover:border-primary-500`,
          inputRef?.current?.value && `!border-black border bg-common-white`,
          isSearch &&
            beforeIcon &&
            !focus &&
            inputRef?.current?.value &&
            `border-black border !bg-common-white`,
          message && status && messageClasses[status],
          props?.disabled && `!bg-common-disable border-none`,
          props?.readOnly &&
            `!bg-common-disable border-none pointer-events-none`,
          containerClassName
        )}
        onKeyDown={(e) => e.stopPropagation()}
      >
        <div className="flex flex-row items-center gap-2 px-2 !border !border-transparent h-full">
          {beforeIcon && (
            <button
              type="button"
              tabIndex={beforeIconFn ? 0 : -1}
              onClick={beforeIconFn}
              disabled={props?.disabled || props?.readOnly}
              className="w-4 h-4 focus:text-primary outline-none disabled:opacity-50"
            >
              {beforeIcon}
            </button>
          )}
          <input
            autoComplete="off"
            ref={(ele: HTMLInputElement) => {
              inputRef.current = ele;
              if (typeof ref === 'function') {
                ref(ele);
              } else if (ref) {
                ref.current = ele;
              }
            }}
            data-testid={testId}
            {...props}
            readOnly={props?.readOnly}
            value={value}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              onChange?.(e?.target?.value)
            }
            className={cn(
              `w-full h-full bg-transparent outline-none ring-0 py-3 focus:outline-none focus:border-[1px] focus:border-transparent disabled:text-grey-400 placeholder:italic placeholder:text-grey-400`,
              !focus && `border-[1px] border-transparent`,
              props?.className
            )}
            onFocus={(e) => {
              onFocus();
              if (props?.onFocus) props?.onFocus(e);
            }}
            onBlur={(e) => {
              onBlur();
              if (props?.onBlur) props?.onBlur(e);
            }}
            onKeyDown={onKeyDown}
          />
          {allowClear && inputRef?.current?.value && (
            <button
              type="button"
              onClick={clearValue}
            >
              <Icon name="cross" />
            </button>
          )}
          {afterIcon && (
            <button
              type="button"
              tabIndex={afterIcon ? 0 : -1}
              onClick={afterIconFn}
              disabled={props?.disabled || props?.readOnly}
              className="w-4 h-4 focus:text-primary outline-none disabled:opacity-50"
            >
              {afterIcon}
            </button>
          )}
        </div>
        {message && <p className="text-red-500 text-xs italic mt-1">{message}</p>}
      </div>
    );

    if (autoCompleteOptions) {
      return (
        <DropdownMenu
          open={autoCompleteOpen && filteredAutoComplete?.length > 0}
          onOpenChange={(isOpen) => {
            setAutoCompleteOpen(isOpen);
          }}
        >
          <DropdownMenuTrigger
            disabled
            asChild
          >
            {body}
          </DropdownMenuTrigger>
          <DropdownMenuContent
            style={{ width: containerWidth }}
            autoFocus={false}
            onCloseAutoFocus={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
          >
            {filteredAutoComplete?.map((item) => (
              <DropdownMenuItem
                key={item.id}
                onClick={() => onChange?.(item.value)}
                className="w-full text-left text-remark"
                autoFocus={false}
                onPointerLeave={(event) => event.preventDefault()}
                onPointerMove={(event) => event.preventDefault()}
              >
                {item.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    }

    return body;
  }
);

Input.displayName = 'Input';

export default Input;
