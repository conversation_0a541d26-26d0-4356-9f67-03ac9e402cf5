import dayjs from 'dayjs';
import IconProfile from '../../Icon/IconProfile';
import IconQMDelete from '../../Icon/IconQMDelete';
import IconQMPublish from '../../Icon/IconQMPublish';
import IconQMReleased from '../../Icon/IconQMReleased';
import { useTranslation } from 'react-i18next';
import { getTranslatedResult } from '../../../../lib/utils';

interface IEvaluationItemProps {
  title: string;
  evaluator: string;
  releaseTime: string;
  status: string;
  result: string;
  isAuto: boolean;
  showButton?: boolean;
  onItemClick?: () => void;
  label?: {
    evaluationForm?: string;
    evaluator?: string;
    releasedAt?: string;
    failed?: string;
    passed?: string;
  };
}

const EvaluationItem = ({
  title = '',
  evaluator = '',
  releaseTime = '',
  status = '',
  result = '',
  isAuto = false,
  showButton = true,
  onItemClick,
  label,
}: IEvaluationItemProps) => {
  const { t } = useTranslation();

  const getBackgroundColor = (result: string) => {
    // tag color control
    if (result.toLowerCase() === 'passed') {
      return 'bg-green-500';
    } else if (result.toLowerCase() === 'failed') {
      return 'bg-red-500';
    } else if (result.toLowerCase() === 'to be reviewed') {
      return 'bg-primary-500'; // 动态百分比背景色
    } else if (result.toLowerCase() === 'not-evaluated') {
      return 'bg-[#C6C6C6]'; // 未评估背景色
    }
    return 'bg-gray-500'; // 默认背景色
  };

  const labelFailed = label?.failed || 'Failed';
  const labelPassed = label?.passed || 'Passed';

  return (
    // Card
    <section
      className="relative m-4 p-4 bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)] rounded-xl hover:bg-gray-50 cursor-pointer"
      onClick={onItemClick}
    >
      {/* result tag */}
      <section>
        {result && result !== '' && (
          <div
            className={`absolute top-0 left-0 min-w-16 px-3 py-[2px] ${getBackgroundColor(result)} rounded-tl-xl rounded-br-3xl text-white text-center text-[11px]`}
          >
            {t(getTranslatedResult(result.toLowerCase(), 'evaluation'))}
          </div>
        )}
      </section>
      {/* flex view */}
      <section className="flex gap-4 items-center">
        {/* ItemIcon */}
        <div className="flex-none">
          <IconProfile
            alt={evaluator}
            size="48"
          />
        </div>
        {/* ItemInfo */}
        <div className="flex-1">
          <div>
            <label className="text-[#949494]">
              {t('evaluation.evaluationForm')}:{' '}
            </label>{' '}
            {title} {isAuto ? '(AI)' : ''}
          </div>
          <div>
            <label className="text-[#949494]">
              {t('evaluation.evaluator')}:{' '}
            </label>{' '}
            {evaluator}
          </div>
          <div>
            <label className="text-[#949494]">
              {t('evaluation.releaseAt')}:{' '}
            </label>{' '}
            {releaseTime !== ''
              ? dayjs(releaseTime).format('YYYY-MM-DD HH:mm:ss')
              : 'N/A'}
          </div>
        </div>
        {showButton && (
          <>
            {/* ItemStatus */}
            {status === 'published' && result.toLowerCase() === 'passed' ? (
              <div className="flex-none">
                <IconQMReleased
                  size="60"
                  alt="qm released icon"
                />
              </div>
            ) : (
              <>
                {status === 'completed' ? (
                  <div
                    className="p-2 flex-none bg-[#FFAC4A] rounded-full hover:bg-orange-500"
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      // TODO: add publish logic
                    }}
                  >
                    <IconQMPublish
                      size="20"
                      alt="qm publish icon"
                    />
                  </div>
                ) : null}
                <div
                  className="p-2 flex-none bg-[#E0E0E0] rounded-full hover:bg-slate-400"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    // TODO: add delete logic
                  }}
                >
                  <IconQMDelete
                    size="20"
                    alt="qm delete icon"
                  />
                </div>
              </>
            )}
          </>
        )}
      </section>
    </section>
  );
};

export default EvaluationItem;
