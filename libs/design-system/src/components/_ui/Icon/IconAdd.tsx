import React from 'react';
interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    color?: string;
}

const IconAdd: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3528_44797)">
                <path d="M8 16.5C10.1217 16.5 12.1566 15.6571 13.6569 14.1569C15.1571 12.6566 16 10.6217 16 8.5C16 6.37827 15.1571 4.34344 13.6569 2.84315C12.1566 1.34285 10.1217 0.5 8 0.5C5.87827 0.5 3.84344 1.34285 2.34315 2.84315C0.842855 4.34344 0 6.37827 0 8.5C0 10.6217 0.842855 12.6566 2.34315 14.1569C3.84344 15.6571 5.87827 16.5 8 16.5Z" fill="white" />
                <path d="M2.34315 14.1561C3.84344 15.6564 5.87827 16.4993 8 16.4993C10.1217 16.4993 12.1566 15.6564 13.6569 14.1561C15.1571 12.6558 16 10.621 16 8.49925C16 6.37752 15.1571 4.34269 13.6569 2.8424C12.1566 1.34211 10.1217 0.499255 8 0.499255C5.87827 0.499255 3.84344 1.34211 2.34315 2.8424C0.842855 4.34269 5.68856e-07 6.37752 6.11003e-07 8.49925C6.11003e-07 10.621 0.842855 12.6558 2.34315 14.1561ZM8 4.91953C8.41543 4.91953 8.7513 5.2554 8.74909 5.66862V7.74574H10.8262C11.2416 7.74574 11.5775 8.08162 11.5753 8.49484C11.5731 8.90805 11.2394 9.24614 10.8262 9.24393H8.74909V11.3211C8.74909 11.7365 8.41322 12.0724 8 12.0701C7.58678 12.0679 7.2487 11.7343 7.25091 11.3211V9.24393H5.17378C4.75836 9.24393 4.42248 8.90805 4.42469 8.49484C4.4269 8.08162 4.76057 7.74353 5.17378 7.74574H7.25091V5.66862C7.25091 5.25319 7.58678 4.91732 8 4.91953Z" fill="black" />
            </g>
            <defs>
                <clipPath id="clip0_3528_44797">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconAdd;
