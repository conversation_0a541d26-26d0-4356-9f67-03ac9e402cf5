import { cn } from '../../../lib/utils';
import IconBack from './IconBack';
import React, { forwardRef } from 'react';
import IconSearch from './IconSearch';
import IconError from './IconError';
import Icon<PERSON>ross from './IconCross';
import IconDropdownArrow from './IconDropdownArrow';
import IconCheck from './IconCheck';
import IconLogo from './IconLogo';
import IconSaa from './IconSaa';
import IconBell from './IconBell';
import IconFilter from './IconFilter';
import IconRightbar from './IconRightbar';
import IconVerticalDots from './IconVerticalDots';
import IconPlus from './IconPlus';
import IconSave from './IconSave';
import IconHorizontalDots from './IconHorizontalDots';
import IconSetting from './IconSetting';
import IconPlay from './IconPlay';
import IconPause from './IconPause';
import IconPrev10s from './IconPrev10';
import IconNext10s from './IconNext10s';
import IconArrowLeft from './IconArrowLeft';
import IconArrowRight from './IconArrowRight';
import IconFullLogo from './IconFullLogo';
import IconMove from './IconMove';
import IconWMsgCorner from './IconWMsgCorner';
import IconInbound from './IconInbound';
import IconOutbound from './IconOutbound';
import IconWorkgroup from './IconWorkgroup';
import IconCalendar from './IconCalendar';
import IconFile from './IconFile';
import IconDirectionLine from './IconDirectionLine';
import IconDownload from './IconDownload';
import IconUser from './IconUser';
import IconTranscript from './IconTranscript';
import IconPhone from './IconPhone';
import IconSpeed from './IconSpeed';
import IconSoundWave from './IconSoundWave';
import IconSoundMute from './IconSoundMuted';
import IconSound from './IconSound';
import IconEye from './IconEye';
import IconPad from './IconPad';
import IconMute from './IconMute';
import IconHold from './IconHold';
import IconTransfer from './IconTransfer';
import IconConsult from './IconConsult';
import IconGroup from './IconGroup';
import IconPickup from './IconPickup';
import IconPhoneEnd from './IconPhoneEnd';
import IconBackspace from './IconBackspace';
import IconPin from './IconPin';
import IconPencil from './IconPencil';
import IconAddressBook from './IconAddressBook';
import IconClose from './IconClose';
import IconSecondCall from './IconSecondCall';
import IconIVR from './IconIVR';
import IconConference from './IconConference';
import IconLogout from './IconLogout';
import IconHeadPhone from './IconHeadPhone';
import IconPen from './IconPen';
import IconBehalf from './IconBehalf';
import IconRemark from './IconRemark';
import IconUndo from './IconUndo';
import IconMsg from './IconMsg';
import IconConnected from './IconConnected';
import IconWrapup from './IconWrapup';
import IconOnhold from './IconOnhold';
import IconConnecting from './IconConnecting';
import IconAlerting from './IconAlerting';
import IconHistory from './IconHistory';
import IconSop from './IconSop';
import IconCallBg from './IconCallBg';
import IconMsgLine from './IconMsgLine';
import IconTransferLine from './IconTransferLine';
import IconDeleteDailPad from './IconDeleteDailPad';
import IconActiveUser from './IconActiveUser';
import IconHoldUser from './IconHoldUser';
import IconLink from './IconLink';
import IconExpand from './IconExpand';
import IconUserLine from './IconUserLine';
import IconEmail from './IconEmail';
import IconGender from './IconGender';
import IconLanguage from './IconLanguage';
import IconOther from './IconOther';
import IconHome from './IconHome';
import IconMobile from './IconMobile';
import IconEmailLine from './IconEmailLine';
import IconEmail2 from './IconEmail2';
import IconEdit from './IconEdit';
import IconAdd from './IconAdd';
import IconDelete from './IconDelete';
import IconSaaInColour from '@cdss-modules/design-system/components/_ui/Icon/IconSaaInColour';

type Size = number | string;
export type IconName =
  | 'back'
  | 'search'
  | 'error'
  | 'cross'
  | 'dropdown-arrow'
  | 'check'
  | 'logo'
  | 'saa'
  | 'saaInColour'
  | 'bell'
  | 'filter'
  | 'rightbar'
  | 'play'
  | 'pin'
  | 'pause'
  | 'prev-10s'
  | 'next-10s'
  | 'arrow-left'
  | 'arrow-right'
  | 'verticalDots'
  | 'plus'
  | 'save'
  | 'horizontalDots'
  | 'setting'
  | 'fullLogo'
  | 'move'
  | 'whatsapp-cornner'
  | 'inbound'
  | 'outbound'
  | 'workgroup'
  | 'calendar'
  | 'file'
  | 'directionline'
  | 'download'
  | 'user'
  | 'transcript'
  | 'sound'
  | 'sound-mute'
  | 'sound-wave'
  | 'speed'
  | 'phone'
  | 'eye'
  | 'pad'
  | 'mute'
  | 'hold'
  | 'transfer'
  | 'consult'
  | 'group'
  | 'pickup'
  | 'phone-end'
  | 'backspace'
  | 'pencil'
  | 'addressBook'
  | 'close'
  | 'secondCall'
  | 'ivr'
  | 'conference'
  | 'headphone'
  | 'logout'
  | 'msg'
  | 'pen'
  | 'behalf'
  | 'remark'
  | 'undo'
  | 'connected'
  | 'connecting'
  | 'alerting'
  | 'onhold'
  | 'wrapup'
  | 'history'
  | 'sop'
  | 'msgLine'
  | 'transferLine'
  | 'deleteDailPad'
  | 'activeUser'
  | 'holdUser'
  | 'link'
  | 'expand'
  | 'userLine'
  | 'email'
  | 'emailLine'
  | 'email2'
  | 'gender'
  | 'language'
  | 'other'
  | 'home'
  | 'mobile'
  | 'callBg'
  | 'add'
  | 'delete'
  | 'edit';
interface IconProps {
  name?: IconName;
  size?: Size;
  alt?: string;
  className?: string;
  selected?: boolean;
}

const Icon = forwardRef<HTMLDivElement, IconProps>(
  ({ name, size = '1rem', alt, className, selected }, ref) => {
    const sizeStyle = typeof size === 'number' ? `${size}px` : size;

    if (name === 'back')
      return (
        <IconBack
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'search') {
      return (
        <IconSearch
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    }

    if (name === 'error') {
      return (
        <IconError
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    }

    if (name === 'cross') {
      return (
        <IconCross
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    }

    if (name === 'dropdown-arrow')
      return (
        <IconDropdownArrow
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'check')
      return (
        <IconCheck
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'logo')
      return (
        <IconLogo
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'saa')
      return (
        <IconSaa
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'saaInColour')
      return (
        <IconSaaInColour
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'bell')
      return (
        <IconBell
          size={sizeStyle}
          alt={alt}
          className={className}
          selected={selected}
        />
      );

    if (name === 'rightbar')
      return (
        <IconRightbar
          size={sizeStyle}
          alt={alt}
          className={cn(className)}
        />
      );

    if (name === 'filter')
      return (
        <IconFilter
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'play')
      return (
        <IconPlay
          size={sizeStyle}
          alt={alt}
          className={cn(className)}
        />
      );

    if (name === 'next-10s')
      return (
        <IconNext10s
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'pause')
      return (
        <IconPause
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'verticalDots')
      return (
        <IconVerticalDots
          size={sizeStyle}
          alt={alt}
          className={cn(className)}
        />
      );

    if (name === 'plus')
      return (
        <IconPlus
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'prev-10s')
      return (
        <IconPrev10s
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'save')
      return (
        <IconSave
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'horizontalDots')
      return (
        <IconHorizontalDots
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'setting')
      return (
        <IconSetting
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'arrow-left')
      return (
        <IconArrowLeft
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'arrow-right')
      return (
        <IconArrowRight
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'fullLogo')
      return (
        <IconFullLogo
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'move')
      return (
        <IconMove
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'whatsapp-cornner')
      return (
        <IconWMsgCorner
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'inbound')
      return (
        <IconInbound
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'outbound')
      return (
        <IconOutbound
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'workgroup')
      return (
        <IconWorkgroup
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'calendar')
      return (
        <IconCalendar
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'file')
      return (
        <IconFile
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'directionline')
      return (
        <IconDirectionLine
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'download')
      return (
        <IconDownload
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'user')
      return (
        <IconUser
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'transcript')
      return (
        <IconTranscript
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'phone')
      return (
        <IconPhone
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'sound')
      return (
        <IconSound
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'sound-mute')
      return (
        <IconSoundMute
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'sound-wave')
      return (
        <IconSoundWave
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'speed')
      return (
        <IconSpeed
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'eye')
      return (
        <IconEye
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'pad')
      return (
        <IconPad
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'mute')
      return (
        <IconMute
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'hold')
      return (
        <IconHold
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'transfer')
      return (
        <IconTransfer
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'consult')
      return (
        <IconConsult
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'group')
      return (
        <IconGroup
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'pickup')
      return (
        <IconPickup
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'pin')
      return (
        <IconPin
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'phone-end')
      return (
        <IconPhoneEnd
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'backspace')
      return (
        <IconBackspace
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'pencil')
      return (
        <IconPencil
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'addressBook')
      return (
        <IconAddressBook
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'close')
      return (
        <IconClose
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'secondCall')
      return (
        <IconSecondCall
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'ivr')
      return (
        <IconIVR
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'conference')
      return (
        <IconConference
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'logout')
      return (
        <IconLogout
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'headphone')
      return (
        <IconHeadPhone
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'behalf')
      return (
        <IconBehalf
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'msg')
      return (
        <IconMsg
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'pen')
      return (
        <IconPen
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    if (name === 'remark')
      return (
        <IconRemark
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'undo')
      return (
        <IconUndo
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'connected')
      return (
        <IconConnected
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'connecting')
      return (
        <IconConnecting
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'alerting')
      return (
        <IconAlerting
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'onhold')
      return (
        <IconOnhold
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'wrapup')
      return (
        <IconWrapup
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'history')
      return (
        <IconHistory
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'sop')
      return (
        <IconSop
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'callBg')
      return (
        <IconCallBg
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'msgLine')
      return (
        <IconMsgLine
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'transferLine')
      return (
        <IconTransferLine
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'deleteDailPad')
      return (
        <IconDeleteDailPad
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'activeUser')
      return (
        <IconActiveUser
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'holdUser')
      return (
        <IconHoldUser
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'link')
      return (
        <IconLink
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'expand')
      return (
        <IconExpand
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'userLine')
      return (
        <IconUserLine
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'email')
      return (
        <IconEmail
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'email2')
      return (
        <IconEmail2
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'emailLine')
      return (
        <IconEmailLine
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'gender')
      return (
        <IconGender
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'language')
      return (
        <IconLanguage
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'other')
      return (
        <IconOther
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'home')
      return (
        <IconHome
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'mobile')
      return (
        <IconMobile
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'edit')
      return (
        <IconEdit
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'add')
      return (
        <IconAdd
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );
    if (name === 'delete')
      return (
        <IconDelete
          size={sizeStyle}
          alt={alt}
          className={className}
        />
      );

    return <div>Wrong icon name</div>;
  }
);

export default Icon;
