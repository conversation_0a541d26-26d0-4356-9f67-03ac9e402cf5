import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconGender: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_620_43700)">
                <path d="M2.42555 4.21979C2.12513 4.73097 2.04007 5.34055 2.1891 5.91443C2.33812 6.48831 2.70902 6.97949 3.22019 7.27991C3.73137 7.58033 4.34095 7.66538 4.91483 7.51636C5.48871 7.36733 5.97989 6.99644 6.28031 6.48526C6.58073 5.97409 6.66579 5.36451 6.51676 4.79063C6.36773 4.21675 5.99684 3.72557 5.48567 3.42515C5.23256 3.27639 4.95263 3.17895 4.66186 3.13838C4.3711 3.09781 4.07519 3.11491 3.79103 3.1887C3.21715 3.33772 2.72597 3.70862 2.42555 4.21979ZM2.65355 8.24332C1.97409 7.84384 1.45709 7.21776 1.19332 6.47501C0.929555 5.73227 0.93588 4.92034 1.21119 4.18179C1.4865 3.44325 2.01319 2.8253 2.69878 2.43645C3.38438 2.0476 4.18505 1.9127 4.9602 2.05546C5.73536 2.19821 6.43545 2.60948 6.93754 3.21705C7.43964 3.82462 7.71164 4.58966 7.70579 5.37783C7.69994 6.166 7.41661 6.92691 6.90556 7.52697C6.3945 8.12702 5.68838 8.52786 4.91119 8.65909V10.4741H6.58767V11.5918H4.91119V14.7916H3.79355V11.5918H2.11708V10.4741H3.79355V8.65909C3.39138 8.59116 3.00502 8.45025 2.65355 8.24332ZM9.50472 10.5658C9.35597 10.8189 9.25853 11.0988 9.21796 11.3896C9.17739 11.6804 9.19449 11.9763 9.26828 12.2604C9.34207 12.5446 9.4711 12.8114 9.64802 13.0457C9.82493 13.28 10.0463 13.4772 10.2994 13.6259C10.5525 13.7747 10.8324 13.8721 11.1232 13.9127C11.4139 13.9532 11.7098 13.9361 11.994 13.8624C12.2782 13.7886 12.545 13.6595 12.7793 13.4826C13.0136 13.3057 13.2107 13.0844 13.3595 12.8313C13.651 12.3207 13.7296 11.7158 13.5783 11.1477C13.427 10.5796 13.058 10.0939 12.5511 9.79596C12.0443 9.498 11.4405 9.41172 10.8705 9.55582C10.3005 9.69991 9.8102 10.0628 9.50584 10.5658H9.50472ZM9.73272 14.5893C9.13984 14.2407 8.66851 13.7182 8.38264 13.0926C8.09677 12.4671 8.01019 11.7687 8.13464 11.0923C8.25909 10.4159 8.58855 9.79409 9.07835 9.31125C9.56815 8.8284 10.1946 8.50786 10.8727 8.39309V4.40197L9.8512 5.4235L9.06102 4.63332L11.4327 2.26279L12.2228 3.05297L13.8032 4.63332L13.013 5.4235L11.9915 4.40197V8.39197C12.4761 8.47411 12.9368 8.66181 13.3409 8.94178C13.7449 9.22175 14.0825 9.58714 14.3296 10.0121C14.5768 10.437 14.7274 10.9111 14.771 11.4007C14.8145 11.8903 14.7499 12.3836 14.5816 12.8454C14.4134 13.3073 14.1456 13.7266 13.7973 14.0734C13.449 14.4203 13.0287 14.6864 12.5662 14.8528C12.1036 15.0192 11.6101 15.0818 11.1207 15.0363C10.6312 14.9908 10.1578 14.8382 9.73384 14.5893H9.73272Z" fill="black" fillOpacity="0.9" />
            </g>
            <defs>
                <clipPath id="clip0_620_43700">
                    <rect width="16" height="16" fill="white" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconGender;
