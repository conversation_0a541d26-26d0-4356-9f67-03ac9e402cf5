import React from 'react';
interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
    color?: string;
}

const IconDelete: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 17" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3528_44826)">
                <path d="M8 16.5C10.1217 16.5 12.1566 15.6571 13.6569 14.1569C15.1571 12.6566 16 10.6217 16 8.5C16 6.37827 15.1571 4.34344 13.6569 2.84315C12.1566 1.34285 10.1217 0.5 8 0.5C5.87827 0.5 3.84344 1.34285 2.34315 2.84315C0.842855 4.34344 0 6.37827 0 8.5C0 10.6217 0.842855 12.6566 2.34315 14.1569C3.84344 15.6571 5.87827 16.5 8 16.5Z" fill="white" />
                <path d="M2.34315 14.1561C3.84344 15.6564 5.87827 16.4993 8 16.4993C10.1217 16.4993 12.1566 15.6564 13.6569 14.1561C15.1571 12.6558 16 10.621 16 8.49926C16 6.37752 15.1571 4.34269 13.6569 2.8424C12.1566 1.34211 10.1217 0.499256 8 0.499256C5.87827 0.499256 3.84344 1.34211 2.34315 2.8424C0.842854 4.34269 -6.33464e-08 6.37752 1.05241e-07 8.49926C1.05241e-07 10.621 0.842855 12.6558 2.34315 14.1561Z" fill="black" />
                <rect x="4" y="7.69922" width="8" height="1.6" rx="0.8" fill="white" />
            </g>
            <defs>
                <clipPath id="clip0_3528_44826">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconDelete;
