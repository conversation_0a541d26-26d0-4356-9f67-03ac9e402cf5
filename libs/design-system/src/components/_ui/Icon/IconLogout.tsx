import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconLogout: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 20 20"
    >
      <g clipPath="url(#clip0_781_16)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.85657 16.8146V17.8114C9.85657 18.6107 9.45821 19.3007 8.76599 19.7003C8.43294 19.8926 8.06122 19.9998 7.67583 20C7.29009 20.0002 6.91849 19.8928 6.58521 19.7003L2.15333 17.1416C1.46103 16.7419 1.06274 16.052 1.06274 15.2526V2.18117C1.06274 0.978398 2.04118 0 3.24392 0H13.0045C14.2073 0 15.1858 0.97832 15.1858 2.18117V4.9366C15.1858 5.33187 14.8649 5.65281 14.4696 5.65281C14.0742 5.65281 13.7536 5.33191 13.7536 4.9366V2.18117C13.7536 1.76816 13.4175 1.43211 13.0045 1.43211H4.45169L8.76599 3.9234C9.45798 4.32301 9.85657 5.01285 9.85657 5.81195V15.3824H13.0045C13.4175 15.3824 13.7536 15.0465 13.7536 14.6335V12.218C13.7536 11.8225 14.074 11.5018 14.4696 11.5018C14.865 11.5018 15.1858 11.8225 15.1858 12.218V14.6335C15.1858 15.8363 14.2073 16.8146 13.0045 16.8146H9.85657ZM16.4926 9.12336L15.7039 9.91207C15.4243 10.1917 15.4243 10.6451 15.7039 10.9248C15.7704 10.9913 15.8493 11.0441 15.9362 11.08C16.0231 11.116 16.1162 11.1344 16.2103 11.1343C16.3044 11.1345 16.3975 11.1161 16.4845 11.0801C16.5714 11.0441 16.6504 10.9914 16.7168 10.9248L18.7277 8.91352C19.0073 8.63391 19.0074 8.18078 18.7277 7.90117L16.7168 5.89023C16.4372 5.61055 15.9838 5.61066 15.704 5.8902C15.4244 6.16957 15.4245 6.62312 15.704 6.90258L16.4926 7.69109H11.209C10.8134 7.69109 10.493 8.01172 10.493 8.40734C10.493 8.80297 10.8134 9.1234 11.209 9.1234H16.4926V9.12336Z"
        //   fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_781_16">
          <rect
            width="20"
            height="20"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconLogout;
