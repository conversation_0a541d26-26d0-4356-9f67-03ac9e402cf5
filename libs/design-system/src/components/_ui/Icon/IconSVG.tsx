import React, { useEffect } from 'react';
import IconOutbound from './IconOutbound';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
  onClick?: () => void;
  iconType: string;
  icon?: string;
}

const IconSVG: React.FC<IconProps> = ({
  iconType,
  size,
  alt,
  className,
  color,
  onClick,
  icon
}) => {
  useEffect(() => {
    // console.log(iconType)
  }, []);
  return (
    <>
      {iconType === 'message' && (
        <svg
          className={`fill-none ${className}`}
          style={{ width: size, height: size }}
          viewBox="-4 -5 24 24"
          fill="none"
          aria-label={alt}
          xmlns="http://www.w3.org/2000/svg"
          onClick={onClick}
        >
          <g clipPath="url(#clip0_240_5990)">
            <path
              d="M0.848728 15L1.92633 11.1484C1.26057 10.0186 0.910893 8.73687 0.912557 7.4325C0.914158 3.33406 4.32126 0 8.5072 0C10.5389 0.0009375 12.4454 0.775005 13.8793 2.17969C14.5865 2.86881 15.1471 3.68843 15.5286 4.5911C15.9101 5.49376 16.105 6.46155 16.1018 7.43843C16.0999 11.5366 12.6928 14.8713 8.5072 14.8713H8.50401C7.23296 14.8706 5.98395 14.5587 4.87475 13.9666L0.848728 15Z"
              fill="#3AC34C"
            />
            <path
              d="M6.60883 4.32499C6.45497 3.96312 6.29857 4.01219 6.18206 4.00624C6.0713 4.00094 5.9449 4 5.81817 4C5.69144 4 5.48622 4.04625 5.31225 4.23218C5.13828 4.41813 4.64833 4.8675 4.64833 5.78156C4.64833 6.69593 5.32822 7.57907 5.42302 7.7028C5.51782 7.82687 6.76108 9.70281 8.66477 10.5072C9.11738 10.6984 9.47075 10.8128 9.74653 10.8984C10.2011 11.0397 10.6147 11.02 10.9416 10.9722C11.3061 10.9187 12.0642 10.5228 12.2225 10.0891C12.3805 9.655 12.3805 9.28312 12.3333 9.20562C12.2857 9.12843 12.1593 9.08187 11.9694 8.98875C11.7798 8.89593 10.8468 8.44656 10.6728 8.38469C10.4989 8.32249 10.3725 8.29155 10.2458 8.4775C10.1193 8.66343 9.75578 9.08187 9.64501 9.20562C9.53426 9.32969 9.4235 9.3453 9.23389 9.25249C9.04398 9.15937 8.43272 8.96312 7.70814 8.33062C7.1438 7.83812 6.763 7.22969 6.65225 7.04376C6.54148 6.85781 6.64043 6.7575 6.73523 6.66468C6.82077 6.58155 6.92515 6.44781 7.01996 6.33968C7.11508 6.23093 7.14668 6.15344 7.20988 6.02969C7.27308 5.90562 7.24148 5.79719 7.19391 5.70405C7.14668 5.61124 6.76714 4.69718 6.60883 4.32531V4.32499Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_240_5990">
              <rect
                width="17.3214"
                height="15"
                fill="white"
                transform="translate(0.814453)"
              />
            </clipPath>
          </defs>
        </svg>
      )}

      {iconType === 'email' && (
        <svg
          className={`fill-none ${className}`}
          style={{ width: size, height: size }}
          viewBox="-3 -4 24 24"
          fill="none"
          aria-label={alt}
          xmlns="http://www.w3.org/2000/svg"
          onClick={onClick}
        >
          <path
            d="M16.5035 5.9043V12.9524C16.5035 13.2745 16.3728 13.5833 16.1402 13.8111C15.9076 14.0388 15.5922 14.1667 15.2632 14.1667H3.68702C3.35807 14.1667 3.04259 14.0388 2.80999 13.8111C2.57739 13.5833 2.44671 13.2745 2.44671 12.9524V5.9043L7.60349 10.1847C8.12635 10.6187 8.7896 10.8569 9.47511 10.8569C10.1606 10.8569 10.8239 10.6187 11.3467 10.1847L16.5035 5.9043ZM15.2632 2.83337C15.4143 2.83354 15.5642 2.86041 15.7056 2.91271C15.9088 2.98849 16.088 3.11557 16.2249 3.28104C16.299 3.37003 16.36 3.46882 16.4059 3.57449C16.4597 3.69916 16.4928 3.83435 16.501 3.97642L16.5035 4.04766V4.45242L10.2801 9.64956C10.0659 9.82843 9.79592 9.93064 9.51454 9.93939C9.23316 9.94815 8.95711 9.86293 8.73176 9.69773L8.67015 9.64916L2.44671 4.45242V4.04766C2.44621 3.8229 2.50964 3.60244 2.62991 3.41099C2.75018 3.21954 2.92252 3.06466 3.12764 2.96371C3.30106 2.87786 3.49269 2.83321 3.68702 2.83337H15.2632Z"
            fill="#EFB336"
          />
        </svg>
      )}

      {iconType=="call"&&icon=="call"&& (
        <svg
          className={`fill-none ${className}`}
          style={{ width: size, height: size }}
          viewBox="-5 -4 24 24"
          fill="none"
          aria-label={alt}
          xmlns="http://www.w3.org/2000/svg"
          onClick={onClick}
        >
          <g clipPath="url(#clip0_292_2837)">
            <path
              d="M3.60703 3.33864C3.4386 2.93179 2.99456 2.71523 2.5702 2.83117L0.645284 3.35614C0.264676 3.46114 0 3.80675 0 4.20048C0 9.61212 4.38793 14 9.79957 14C10.1933 14 10.5389 13.7354 10.6439 13.3548L11.1689 11.4298C11.2848 11.0055 11.0683 10.5615 10.6614 10.393L8.5615 9.51806C8.20495 9.36932 7.79153 9.47212 7.54873 9.7718L6.66502 10.8502C5.12509 10.1218 3.87827 8.87496 3.14986 7.33503L4.22825 6.45351C4.52793 6.20852 4.63073 5.79728 4.48199 5.44074L3.60703 3.34083V3.33864Z"
              fill="#FFAC4A"
            />
            <path
              d="M7.85968 0.970441C7.86321 0.594427 8.17007 0.29327 8.54407 0.298783C8.91806 0.304295 9.22124 0.60918 9.21573 0.983175L9.18906 3.82328L12.8674 0.217367C13.1359 -0.0461441 13.5639 -0.0421247 13.8274 0.226383C14.0909 0.494891 14.0869 0.922906 13.8184 1.18642L10.1421 4.79435L12.9822 4.82102C13.3582 4.82455 13.6593 5.13142 13.6538 5.50541C13.6483 5.87941 13.3434 6.18258 12.9694 6.17707L8.49124 6.13702C8.11523 6.13349 7.81407 5.82662 7.81959 5.45263L7.85968 0.970441Z"
              fill="#FFAC4A"
            />
          </g>
          <defs>
            <clipPath id="clip0_292_2837">
              <rect
                width="14"
                height="14"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
      )}

      {iconType === 'voicemail' && (
        <svg
          className={`fill-none ${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 24 24"
          fill="none"
          aria-label={alt}
          xmlns="http://www.w3.org/2000/svg"
          onClick={onClick}
        >
          <path
            d="M6.29102 16C8.50015 16 10.291 14.2091 10.291 12C10.291 9.79086 8.50015 8 6.29102 8C4.08188 8 2.29102 9.79086 2.29102 12C2.29102 14.2091 4.08188 16 6.29102 16Z"
            stroke="#00A3FF"
            strokeWidth="1.25"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M18.291 16C20.5002 16 22.291 14.2091 22.291 12C22.291 9.79086 20.5002 8 18.291 8C16.0819 8 14.291 9.79086 14.291 12C14.291 14.2091 16.0819 16 18.291 16Z"
            stroke="#00A3FF"
            strokeWidth="1.25"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M6.29102 16H18.291"
            stroke="#00A3FF"
            strokeWidth="1.25"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}

      {iconType === 'callback' && (
        <svg
          className={`fill-none ${className}`}
          style={{ width: size, height: size }}
          viewBox="0 0 24 24"
          fill="none"
          aria-label={alt}
          xmlns="http://www.w3.org/2000/svg"
          onClick={onClick}
        >
          <path
            d="M4.89756 5.89209C5.07149 5.6684 5.29386 5.48703 5.54796 5.36163C5.80206 5.23623 6.08127 5.17005 6.36462 5.16807C6.64797 5.16608 6.92809 5.22834 7.18391 5.35017C7.43974 5.472 7.66463 5.65023 7.84168 5.87146L10.1378 8.74209C10.3881 9.05501 10.5315 9.43988 10.5471 9.84028C10.5627 10.2407 10.4496 10.6355 10.2244 10.967L10.1701 11.0495C9.50031 12.0972 9.64581 13.1232 10.6801 14.3131C11.7751 15.572 12.8749 15.8157 14.1927 15.1227C14.5009 14.9606 14.8481 14.8875 15.1955 14.9116C15.5429 14.9357 15.8767 15.0561 16.1596 15.2592L19.2481 17.4781C19.4926 17.6537 19.6912 17.8857 19.8272 18.1544C19.9631 18.423 20.0324 18.7205 20.0291 19.0215C20.0258 19.3226 19.95 19.6185 19.8082 19.8841C19.6664 20.1497 19.4627 20.3772 19.2143 20.5475L16.7528 22.235C16.4246 22.4599 16.0497 22.6073 15.6562 22.666C15.2627 22.7248 14.861 22.6933 14.4814 22.574C11.3378 21.5855 8.79456 20.0847 6.86331 18.068C5.11168 16.2387 3.76956 13.9951 2.83731 11.3446C2.6291 10.7524 2.57287 10.1175 2.67376 9.49793C2.77466 8.87839 3.02946 8.29409 3.41481 7.79859L4.89756 5.89209ZM6.56893 6.72009C6.49034 6.67349 6.39744 6.65718 6.30767 6.67424C6.21791 6.69129 6.13746 6.74053 6.08143 6.81271L4.59868 8.71959C4.36758 9.0169 4.21479 9.36746 4.15431 9.73913C4.09384 10.1108 4.12762 10.4917 4.25256 10.847C5.11543 13.3013 6.34693 15.3601 7.94706 17.0307C9.69456 18.8555 12.0184 20.2272 14.9314 21.143C15.0941 21.194 15.2662 21.2074 15.4347 21.1822C15.6033 21.157 15.764 21.0939 15.9046 20.9975L18.3664 19.31C18.4161 19.2759 18.4568 19.2304 18.4852 19.1773C18.5136 19.1242 18.5287 19.065 18.5294 19.0048C18.53 18.9446 18.5162 18.8851 18.489 18.8313C18.4618 18.7776 18.4221 18.7312 18.3732 18.6961L15.2843 16.4776C15.2277 16.4369 15.161 16.4129 15.0915 16.408C15.022 16.4032 14.9526 16.4178 14.8909 16.4502C12.9387 17.4773 11.0869 17.0667 9.54831 15.2971C8.06218 13.5875 7.84768 11.7965 8.98393 10.124C9.02896 10.0576 9.05155 9.97861 9.04838 9.8985C9.04521 9.81839 9.01644 9.7414 8.96631 9.67884L6.67056 6.80859C6.65218 6.78558 6.63117 6.76481 6.60793 6.74671L6.56893 6.72009ZM15.6496 1.73821C15.7777 1.87543 15.8497 2.05572 15.8514 2.24346C15.8531 2.4312 15.7842 2.61274 15.6586 2.75221L15.6132 2.79834L13.8162 4.47609H17.3824C20.0119 4.47609 21.4774 5.87184 21.5232 8.42709L21.5243 8.55159C21.5243 11.1428 20.1192 12.7366 17.4964 13.1405C17.2998 13.1707 17.0992 13.1217 16.9388 13.0041C16.7783 12.8864 16.6712 12.7099 16.6409 12.5133C16.6106 12.3167 16.6596 12.116 16.7773 11.9556C16.8949 11.7952 17.0714 11.688 17.2681 11.6577C19.1678 11.3652 20.0243 10.394 20.0243 8.55159C20.0243 6.78759 19.2631 6.00721 17.4897 5.97721L17.3821 5.97609H11.9146C11.2534 5.97609 10.9246 5.18971 11.3622 4.71871L11.4027 4.67821L14.5894 1.70184C14.6614 1.63461 14.746 1.58222 14.8382 1.54767C14.9304 1.51311 15.0286 1.49706 15.127 1.50044C15.2255 1.50382 15.3223 1.52656 15.4119 1.56735C15.5016 1.60815 15.5823 1.66621 15.6496 1.73821Z"
            fill="#1CC500"
          />
        </svg>
      )}
      {iconType=="call"&&icon === 'outbound' && (
        <svg
        className={`fill-none`}
        style={{ width: "14px" }}
        viewBox="0 0 50 50"
        fill="none"
        aria-label={alt}
        // onClick={onClick}
      >
        <g clipPath="url(#clip0_3279_6453)">
          <path
            d="M12.8822 11.9236C12.2807 10.4705 10.6948 9.69709 9.17928 10.1111L2.30459 11.986C0.945271 12.361 0 13.5954 0 15.0015C0 34.3288 15.6712 50 34.9985 50C36.4046 50 37.639 49.0547 38.014 47.6954L39.8889 40.8207C40.3029 39.3052 39.5295 37.7193 38.0764 37.1178L30.5768 33.9929C29.3034 33.4617 27.8269 33.8288 26.9597 34.8991L23.8036 38.7505C18.3039 36.149 13.851 31.6961 11.2495 26.1964L15.1009 23.0481C16.1712 22.1731 16.5383 20.7044 16.0071 19.431L12.8822 11.9314V11.9236Z"
            fill="#FFAC4A"
          />
          <path
            d="M50 18.4301C50 19.773 48.9142 20.8588 47.5784 20.8517C46.2426 20.8446 45.1496 19.7659 45.1568 18.4301V8.28639L32.1414 21.2874C31.1913 22.2375 29.6626 22.2375 28.7126 21.2874C27.7625 20.3374 27.7625 18.8087 28.7126 17.8586L41.7208 4.85039H31.5771C30.2341 4.85039 29.1483 3.76459 29.1554 2.42877C29.1626 1.09294 30.2413 -1.23789e-06 31.5771 0.00714211L47.5712 0C48.9142 0 50 1.0858 49.9929 2.42163L50 18.4301Z"
            fill="#FFAC4A"
          />
        </g>
        <defs>
          <clipPath id="clip0_3279_6453">
            <rect
              width="50"
              height="50"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
      )}
    </>
  );
};

export default IconSVG;
