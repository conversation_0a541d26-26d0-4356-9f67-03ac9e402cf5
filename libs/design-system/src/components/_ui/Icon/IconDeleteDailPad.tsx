import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconDeleteDailPad: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }} aria-label={alt} viewBox="0 0 25 25" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_46_14916)">
                <path d="M22.0813 4.16993H7.75751C7.36925 4.16873 6.98545 4.25212 6.63318 4.41421C6.28092 4.5763 5.96877 4.81314 5.71876 5.10804L0.880018 10.7888C0.473168 11.2675 0.25 11.8736 0.25 12.4999C0.25 13.1262 0.473168 13.7324 0.880018 14.2111L5.71876 19.8918C5.96877 20.1867 6.28092 20.4235 6.63318 20.5856C6.98545 20.7477 7.36925 20.8311 7.75751 20.8299H22.0813C22.7886 20.8285 23.4666 20.549 23.9668 20.0524C24.467 19.5559 24.7486 18.8829 24.75 18.1806V6.8192C24.7486 6.11699 24.467 5.44394 23.9668 4.94741C23.4666 4.45087 22.7886 4.17131 22.0813 4.16993ZM18.0038 14.7236C18.1677 14.8876 18.2597 15.1092 18.2597 15.3403C18.2597 15.5713 18.1677 15.793 18.0038 15.957C17.8374 16.1177 17.6146 16.208 17.3825 16.2089C17.1507 16.2064 16.9285 16.1163 16.7613 15.957L14.5213 13.7334L12.2813 15.957C12.1997 16.0374 12.1029 16.1011 11.9966 16.1444C11.8902 16.1877 11.7763 16.2098 11.6613 16.2094C11.5463 16.209 11.4326 16.1861 11.3265 16.142C11.2204 16.098 11.1241 16.0336 11.0431 15.9527C10.9621 15.8717 10.898 15.7756 10.8544 15.67C10.8107 15.5644 10.7885 15.4513 10.7889 15.3372C10.7893 15.2231 10.8124 15.1101 10.8567 15.0049C10.9011 14.8996 10.9659 14.804 11.0475 14.7236L13.2875 12.4999L11.0475 10.2763C10.8836 10.1123 10.7916 9.8906 10.7916 9.65956C10.7916 9.42852 10.8836 9.20685 11.0475 9.04285C11.1285 8.96232 11.2246 8.89844 11.3305 8.85485C11.4363 8.81126 11.5498 8.78883 11.6644 8.78883C11.779 8.78883 11.8925 8.81126 11.9983 8.85485C12.1042 8.89844 12.2003 8.96232 12.2813 9.04285L14.5213 11.2665L16.7613 9.04285C16.8428 8.96186 16.9397 8.89762 17.0463 8.85379C17.1529 8.80996 17.2671 8.7874 17.3825 8.7874C17.4979 8.7874 17.6121 8.80996 17.7187 8.85379C17.8253 8.89762 17.9222 8.96186 18.0038 9.04285C18.0853 9.12384 18.1501 9.21998 18.1942 9.3258C18.2384 9.43161 18.2611 9.54503 18.2611 9.65956C18.2611 9.7741 18.2384 9.88751 18.1942 9.99332C18.1501 10.0991 18.0853 10.1953 18.0038 10.2763L15.7638 12.4999L18.0038 14.7236Z" fill="black" />
            </g>
            <defs>
                <clipPath id="clip0_46_14916">
                    <rect width="24.5" height="24.5" fill="white" transform="translate(0.25 0.25)" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconDeleteDailPad;
