import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconMobile: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`}
            style={{ width: size, height: size }}
            aria-label={alt} viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
            <path d="M11.3334 1.33325H4.66671C3.93033 1.33325 3.33337 1.93021 3.33337 2.66659V13.3333C3.33337 14.0696 3.93033 14.6666 4.66671 14.6666H11.3334C12.0698 14.6666 12.6667 14.0696 12.6667 13.3333V2.66659C12.6667 1.93021 12.0698 1.33325 11.3334 1.33325Z" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8 12H8.00667" stroke="black" strokeWidth="1.3" strokeLinecap="round" strokeLinejoin="round" />
        </svg>


    );
};

export default IconMobile;
