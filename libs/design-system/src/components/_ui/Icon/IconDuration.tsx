interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconDuration: React.FC<IconProps> = ({ size, alt, className, color }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.3335 14.667H12.6668"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.3335 1.33301H12.6668"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.3332 14.6667V11.8853C11.3331 11.5317 11.1926 11.1927 10.9425 10.9427L7.99984 8L5.05717 10.9427C4.8071 11.1927 4.66658 11.5317 4.6665 11.8853V14.6667"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.6665 1.33301V4.11434C4.66658 4.46793 4.8071 4.80702 5.05717 5.05701L7.99984 7.99967L10.9425 5.05701C11.1926 4.80702 11.3331 4.46793 11.3332 4.11434V1.33301"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconDuration;
