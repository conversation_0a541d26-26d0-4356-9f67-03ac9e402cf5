import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconExpand: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`} style={{ height: size }} aria-label={alt} viewBox="0 0 24 17" xmlns="http://www.w3.org/2000/svg">
            <path d="M3 2.65625C3.225 2.65625 3.375 2.70937 3.525 2.81562L12 8.81875L20.475 2.81562C20.775 2.60312 21.225 2.60312 21.525 2.81562C21.825 3.02812 21.825 3.34688 21.525 3.55938L12.525 9.93437C12.225 10.1469 11.775 10.1469 11.475 9.93437L2.475 3.55938C2.175 3.34688 2.175 3.02812 2.475 2.81562C2.625 2.70937 2.775 2.65625 3 2.65625Z" fill="#333333" />
            <path d="M3 6.90625C3.225 6.90625 3.375 6.95937 3.525 7.06562L12 13.0688L20.475 7.06562C20.775 6.85312 21.225 6.85312 21.525 7.06562C21.825 7.27812 21.825 7.59688 21.525 7.80938L12.525 14.1844C12.225 14.3969 11.775 14.3969 11.475 14.1844L2.475 7.80938C2.175 7.59688 2.175 7.27812 2.475 7.06562C2.625 6.95937 2.775 6.90625 3 6.90625Z" fill="#333333" />
        </svg>


    );
};

export default IconExpand;
