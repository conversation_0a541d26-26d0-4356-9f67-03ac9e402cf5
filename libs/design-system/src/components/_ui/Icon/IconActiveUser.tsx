import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconActiveUser: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`} style={{ height: size+"px" }} aria-label={alt} viewBox="0 0 109 95" xmlns="http://www.w3.org/2000/svg">
            <path d="M88.5 51C88.5 75.3005 68.8005 95 44.5 95C20.1995 95 0.5 75.3005 0.5 51C0.5 26.6995 20.1995 7 44.5 7C68.8005 7 88.5 26.6995 88.5 51Z" fill="#FFAC4A" />
            <rect x="7.5" y="6" width="47" height="57" rx="4" fill="#F6F6F6" />
            <g clipPath="url(#clip0_46_14387)">
                <ellipse cx="64" cy="14" rx="16.5" ry="12" fill="white" />
                <path d="M61.3479 1.65217C63.6532 1.6335 65.8192 1.9415 67.8459 2.57617C69.8726 3.21083 71.6522 4.07417 73.1849 5.16617C74.7176 6.25817 75.9272 7.54617 76.8139 9.03017C77.7006 10.5142 78.1566 12.1055 78.1819 13.8042C78.2072 15.4095 77.8336 16.9215 77.0609 18.3402C76.2882 19.7588 75.2116 21.0142 73.8309 22.1062C72.4502 23.1982 70.8289 24.0988 68.9669 24.8082C67.1049 25.5175 65.0972 25.9562 62.9439 26.1242C62.3866 26.1802 61.7912 26.2268 61.1579 26.2642C60.5246 26.3015 59.8216 26.3295 59.0489 26.3482C58.2762 26.3668 57.4149 26.3668 56.4649 26.3482C55.5149 26.3295 54.4446 26.2735 53.2539 26.1802C50.6192 25.9935 48.6622 25.7508 47.3829 25.4522C46.1036 25.1535 45.5906 24.9855 45.8439 24.9482C47.1866 24.7802 48.3392 24.5095 49.3019 24.1362C49.8086 23.9308 50.0176 23.6368 49.9289 23.2542C49.8402 22.8715 49.5932 22.5122 49.1879 22.1762C47.8959 21.1308 46.8509 19.9128 46.0529 18.5222C45.2549 17.1315 44.8306 15.6335 44.7799 14.0282C44.7546 12.3295 45.1726 10.7335 46.0339 9.24017C46.8952 7.74683 48.0732 6.44017 49.5679 5.32017C51.0626 4.20017 52.8232 3.3135 54.8499 2.66017C56.8766 2.00683 59.0426 1.67083 61.3479 1.65217ZM68.7579 13.9722C68.7579 14.4575 68.9859 14.8728 69.4419 15.2182C69.8979 15.5635 70.4552 15.7362 71.1139 15.7362C71.7726 15.7362 72.3362 15.5635 72.8049 15.2182C73.2736 14.8728 73.5079 14.4575 73.5079 13.9722C73.5079 13.4868 73.2736 13.0762 72.8049 12.7402C72.3362 12.4042 71.7726 12.2362 71.1139 12.2362C70.4552 12.2362 69.8979 12.4042 69.4419 12.7402C68.9859 13.0762 68.7579 13.4868 68.7579 13.9722ZM59.2199 13.9722C59.2199 14.4575 59.4606 14.8775 59.9419 15.2322C60.4232 15.5868 60.9932 15.7642 61.6519 15.7642C62.3359 15.7642 62.9122 15.5868 63.3809 15.2322C63.8496 14.8775 64.0839 14.4575 64.0839 13.9722C64.0839 13.4682 63.8496 13.0435 63.3809 12.6982C62.9122 12.3528 62.3359 12.1802 61.6519 12.1802C60.9932 12.1802 60.4232 12.3528 59.9419 12.6982C59.4606 13.0435 59.2199 13.4682 59.2199 13.9722ZM49.7959 13.9442C49.7959 14.4295 50.0302 14.8495 50.4989 15.2042C50.9676 15.5588 51.5312 15.7362 52.1899 15.7362C52.8739 15.7362 53.4502 15.5588 53.9189 15.2042C54.3876 14.8495 54.6219 14.4295 54.6219 13.9442C54.6219 13.4588 54.3876 13.0435 53.9189 12.6982C53.4502 12.3528 52.8739 12.1802 52.1899 12.1802C51.5312 12.1802 50.9676 12.3528 50.4989 12.6982C50.0302 13.0435 49.7959 13.4588 49.7959 13.9442Z" fill="#1CC500" />
            </g>
            <rect x="16.5" y="24" width="29" height="5" rx="2.5" fill="#FFAC4A" />
            <rect x="16.5" y="42" width="16" height="5" rx="2.5" fill="#FFAC4A" />
            <rect x="16.5" y="33" width="29" height="5" rx="2.5" fill="#FFAC4A" />
            <path d="M38.2649 44.068C38.696 42.2688 40.3047 41 42.1549 41H103.48C106.049 41 107.953 43.3886 107.379 45.8935L98.2119 85.8935C97.7953 87.7113 96.1779 89 94.313 89H32.5715C29.9855 89 28.0791 86.583 28.6816 84.068L38.2649 44.068Z" fill="url(#paint0_linear_46_14387)" />
            <circle cx="68" cy="65.5" r="6.5" fill="#F3F3F3" />
            <defs>
                <linearGradient id="paint0_linear_46_14387" x1="68" y1="41" x2="68" y2="89" gradientUnits="userSpaceOnUse">
                    <stop stop-color="#FFF5DA" />
                    <stop offset="1" stop-color="#FFCA8C" />
                </linearGradient>
                <clipPath id="clip0_46_14387">
                    <rect width="38" height="28" fill="white" transform="translate(42.5)" />
                </clipPath>
            </defs>
        </svg>


    );
};

export default IconActiveUser;
