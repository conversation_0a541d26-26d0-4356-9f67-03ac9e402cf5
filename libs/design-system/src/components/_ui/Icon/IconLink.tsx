import React from 'react';

interface IconProps {
    size?: string;
    alt?: string;
    className?: string;
}

const IconLink: React.FC<IconProps> = ({ size, alt, className }) => {
    return (
        <svg className={`fill-none ${className}`} style={{ width: size, height: size }} aria-label={alt} viewBox="0 0 11 11" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_46_14575)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.61831 0.751662C7.62053 -0.250554 9.24544 -0.250554 10.2477 0.751662C11.2499 1.75388 11.2499 3.37879 10.2476 4.38101L7.91419 6.71442C7.58597 7.04274 7.17483 7.27587 6.72457 7.38897H6.7044L6.5981 7.41097L6.53943 7.42196L6.41296 7.44029H6.35064C6.32301 7.44029 6.29722 7.44337 6.27257 7.44632C6.25471 7.44845 6.23743 7.45052 6.22049 7.45128L6.11051 7.46411H5.97121C5.87977 7.46046 5.7886 7.45189 5.69808 7.43844C5.62295 7.42561 5.54961 7.41094 5.4763 7.39262L5.36814 7.3633C5.32852 7.35213 5.28938 7.33929 5.25084 7.32481C5.23674 7.31943 5.2224 7.31431 5.20799 7.30916C5.18306 7.30025 5.15793 7.29127 5.13353 7.28082C5.1207 7.27532 5.10787 7.27002 5.09504 7.26472C5.06938 7.25413 5.04372 7.24354 5.01805 7.23132C4.74525 7.10491 4.49701 6.93115 4.28486 6.71809C4.2114 6.64445 4.15445 6.55602 4.11779 6.45868C4.08113 6.36134 4.0656 6.25732 4.07222 6.15352C4.08402 5.97653 4.15956 5.80983 4.28486 5.68428C4.57422 5.41192 5.02562 5.41192 5.315 5.68428C5.7444 6.11313 6.44 6.11313 6.8694 5.68428L7.47061 5.08671L7.48528 5.07021L9.20465 3.35269C9.63439 2.92296 9.63439 2.22621 9.20465 1.79647C8.77491 1.36674 8.07817 1.36674 7.64843 1.79647L6.26818 3.1749C6.24261 3.20064 6.20997 3.21821 6.1744 3.22539C6.13884 3.23257 6.10194 3.22903 6.06839 3.21522C5.69664 3.06319 5.29873 2.98535 4.8971 2.98609H4.82928C4.79268 2.98679 4.75671 2.97652 4.726 2.95659C4.69529 2.93666 4.67126 2.90799 4.65699 2.87428C4.64255 2.84067 4.6385 2.8035 4.64536 2.76757C4.65222 2.73163 4.66968 2.69857 4.69548 2.67264L6.61831 0.751662ZM6.28104 3.94111C6.43701 4.04028 6.58148 4.15647 6.71179 4.28755C6.78525 4.36119 6.8422 4.44961 6.87886 4.54694C6.91553 4.64428 6.93107 4.7483 6.92445 4.8521C6.9118 5.0278 6.83632 5.19308 6.71181 5.31769C6.42245 5.59004 5.97106 5.59004 5.68167 5.31769C5.25228 4.88883 4.55667 4.88883 4.12728 5.31769L1.78652 7.6566C1.35678 8.08634 1.35678 8.78308 1.78652 9.21282C2.21626 9.64256 2.913 9.64256 3.34274 9.21282L4.72667 7.82707C4.75224 7.80132 4.78488 7.78375 4.82044 7.77658C4.85601 7.7694 4.89291 7.77294 4.92646 7.78675C5.29895 7.93824 5.69746 8.01545 6.09957 8.01403H6.17289C6.20929 8.01383 6.24494 8.02446 6.27527 8.04459C6.30561 8.06472 6.32926 8.09342 6.34323 8.12705C6.35719 8.16067 6.36082 8.19769 6.35366 8.23338C6.34651 8.26908 6.32888 8.30183 6.30304 8.32748L4.37838 10.2521C4.14053 10.4903 3.85788 10.679 3.54673 10.8073C3.23559 10.9357 2.90211 11.0012 2.56554 11C1.14825 10.9991 7.49123e-05 9.84943 0.000977094 8.43215C0.00141135 7.75297 0.271071 7.10167 0.750862 6.62096L3.08244 4.28755C3.32039 4.04877 3.60324 3.85943 3.91468 3.73045C4.22613 3.60146 4.56002 3.53538 4.89712 3.53601C5.38766 3.536 5.86793 3.67659 6.28104 3.94111Z" fill="white" />
            </g>
            <defs>
                <clipPath id="clip0_46_14575">
                    <rect width="11" height="11" fill="white" />
                </clipPath>
            </defs>
        </svg>

    );
};

export default IconLink;
