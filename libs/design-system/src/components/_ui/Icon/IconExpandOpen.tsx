interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
  onClick?: () => void;
}

const IconExpandClose: React.FC<IconProps> = ({
  size,
  alt,
  className,
  color,
  onClick,
}) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 24 24"
      fill="none"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
      onClick={onClick}
    >
      <circle
        cx="12"
        cy="12"
        r="12"
        transform="matrix(-1 0 0 1 24 0)"
        fill={color}
      />
      <path
        d="M15.7803 12.5043C16.0732 12.2254 16.0732 11.7724 15.7803 11.4935L11.2812 7.20919C10.9883 6.93027 10.5126 6.93027 10.2197 7.20919C9.92677 7.48812 9.92677 7.94109 10.2197 8.22002L14.1892 12L10.222 15.78C9.92912 16.0589 9.92912 16.5119 10.222 16.7908C10.5149 17.0697 10.9906 17.0697 11.2835 16.7908L15.7827 12.5065L15.7803 12.5043Z"
        fill="white"
      />
    </svg>
  );
};

export default IconExpandClose;
