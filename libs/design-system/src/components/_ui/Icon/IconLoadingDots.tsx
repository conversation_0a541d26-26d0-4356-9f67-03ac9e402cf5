import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconLoadingDots: React.FC<IconProps> = ({
  size = '16px',
  alt = 'Loading',
  className = '',
  color = '#6B7280', // 默认灰色 gray-500
}) => {
  return (
    <div
      className={`flex items-center space-x-1 ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
    >
      <div
        className="w-2 h-2 rounded-full animate-bounce"
        style={{ backgroundColor: color, animationDelay: '0ms' }}
      />
      <div
        className="w-2 h-2 rounded-full animate-bounce"
        style={{ backgroundColor: color, animationDelay: '150ms' }}
      />
      <div
        className="w-2 h-2 rounded-full animate-bounce"
        style={{ backgroundColor: color, animationDelay: '300ms' }}
      />
    </div>
  );
};

export default IconLoadingDots;
