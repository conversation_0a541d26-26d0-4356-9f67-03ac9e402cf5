import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconStatusRead: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M5.52072 13.3244C5.47215 13.2666 5.43545 13.1998 5.4127 13.1279C5.38995 13.0559 5.38161 12.9802 5.38815 12.905C5.39468 12.8298 5.41597 12.7566 5.4508 12.6897C5.48563 12.6227 5.53331 12.5633 5.59112 12.5148L12.6359 6.60436C12.7526 6.50634 12.9035 6.45869 13.0553 6.47189C13.2071 6.48509 13.3475 6.55807 13.4455 6.67476C13.5435 6.79146 13.5912 6.94231 13.578 7.09414C13.5648 7.24597 13.4918 7.38634 13.3751 7.48436L6.33032 13.3948C6.27255 13.4433 6.20577 13.48 6.13381 13.5028C6.06185 13.5255 5.98611 13.5339 5.91093 13.5273C5.83574 13.5208 5.76258 13.4995 5.69562 13.4647C5.62867 13.4299 5.56923 13.3822 5.52072 13.3244Z"
        fill="#E7CC83"
      />
      <path
        d="M6.31911 13.3951C6.26134 13.4436 6.19457 13.4804 6.12261 13.5031C6.05065 13.5258 5.97491 13.5342 5.89972 13.5277C5.82454 13.5211 5.75138 13.4998 5.68442 13.465C5.61747 13.4302 5.55803 13.3825 5.50951 13.3247L2.55431 9.80068C2.45629 9.68399 2.40864 9.53313 2.42184 9.3813C2.43504 9.22947 2.50802 9.08911 2.62471 8.99108C2.74141 8.89306 2.89226 8.8454 3.04409 8.85861C3.19592 8.87181 3.33629 8.94479 3.43431 9.06148L6.39111 12.5855C6.43959 12.6433 6.47618 12.7102 6.49879 12.7822C6.52141 12.8542 6.52961 12.93 6.52292 13.0052C6.51624 13.0804 6.4948 13.1535 6.45982 13.2204C6.42485 13.2873 6.37704 13.3467 6.31911 13.3951Z"
        fill="#E7CC83"
      />
      <path
        d="M5.52072 9.32436C5.47215 9.26659 5.43545 9.19982 5.4127 9.12786C5.38995 9.0559 5.38161 8.98016 5.38815 8.90497C5.39468 8.82979 5.41597 8.75663 5.4508 8.68967C5.48563 8.62272 5.53331 8.56328 5.59112 8.51476L12.6359 2.60436C12.7526 2.50634 12.9035 2.45869 13.0553 2.47189C13.2071 2.48509 13.3475 2.55807 13.4455 2.67476C13.5435 2.79146 13.5912 2.94231 13.578 3.09414C13.5648 3.24597 13.4918 3.38634 13.3751 3.48436L6.33032 9.39476C6.27255 9.44333 6.20577 9.48003 6.13381 9.50278C6.06185 9.52553 5.98611 9.53387 5.91093 9.52733C5.83574 9.52079 5.76258 9.4995 5.69562 9.46468C5.62867 9.42985 5.56923 9.38217 5.52072 9.32436Z"
        fill="#E7CC83"
      />
      <path
        d="M6.31911 9.39508C6.26134 9.44365 6.19457 9.48035 6.12261 9.5031C6.05065 9.52585 5.97491 9.53419 5.89972 9.52765C5.82454 9.52111 5.75138 9.49982 5.68442 9.465C5.61747 9.43017 5.55803 9.38249 5.50951 9.32468L2.55431 5.80068C2.45629 5.68399 2.40864 5.53313 2.42184 5.3813C2.43504 5.22947 2.50802 5.08911 2.62471 4.99108C2.74141 4.89306 2.89226 4.8454 3.04409 4.85861C3.19592 4.87181 3.33629 4.94479 3.43431 5.06148L6.39111 8.58548C6.43959 8.64335 6.47618 8.7102 6.49879 8.78222C6.52141 8.85424 6.52961 8.93001 6.52292 9.0052C6.51624 9.0804 6.4948 9.15353 6.45982 9.22043C6.42485 9.28733 6.37704 9.34668 6.31911 9.39508Z"
        fill="#E7CC83"
      />
    </svg>
  );
};

export default IconStatusRead;
