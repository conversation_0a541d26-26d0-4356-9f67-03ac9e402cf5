import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSetting: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 21 21"
    >
      <g clipPath="url(#clip0_2231_6646)">
        <path
          d="M19.8476 8.21975L18.2026 8.01062C18.0685 7.59742 17.902 7.19542 17.7047 6.80838L18.7206 5.50025C18.9191 5.24909 19.018 4.93354 18.9983 4.61403C18.9786 4.29452 18.8417 3.9935 18.6139 3.76863L17.2357 2.3905C17.0108 2.16129 16.7091 2.02319 16.3886 2.00269C16.068 1.98218 15.7512 2.08071 15.4989 2.27938L14.1925 3.29525C13.8052 3.09781 13.4029 2.93133 12.9894 2.79737L12.7802 1.155C12.7422 0.836818 12.5889 0.543608 12.3493 0.330792C12.1097 0.117975 11.8005 0.000296717 11.48 0L9.52 0C8.85762 0 8.2985 0.496125 8.21975 1.15237L8.01062 2.79737C7.59694 2.93095 7.19461 3.09745 6.8075 3.29525L5.50025 2.27938C5.24894 2.08126 4.93347 1.98263 4.61407 2.00232C4.29466 2.02201 3.9937 2.15864 3.76862 2.38613L2.3905 3.76338C2.16105 3.98841 2.02281 4.29025 2.0023 4.61098C1.98179 4.9317 2.08046 5.2487 2.27937 5.50113L3.29525 6.80838C3.0976 7.19525 2.93111 7.59728 2.79737 8.01062L1.155 8.21975C0.496125 8.2985 0 8.85763 0 9.52L0 11.48C0 12.1424 0.496125 12.7015 1.15237 12.7802L2.79737 12.9894C2.933 13.4068 3.10012 13.8092 3.29525 14.1916L2.27937 15.4998C2.08089 15.7509 1.98202 16.0665 2.00172 16.386C2.02142 16.7055 2.15829 17.0065 2.38612 17.2314L3.76425 18.6095C3.98955 18.8382 4.29123 18.9759 4.61164 18.9963C4.93205 19.0166 5.24872 18.9182 5.50112 18.7197L6.80837 17.7039C7.19075 17.8999 7.59325 18.067 8.01062 18.2017L8.21975 19.8433C8.2985 20.5039 8.85762 21 9.52 21H11.48C12.1424 21 12.7015 20.5039 12.7802 19.8476L12.9894 18.2026C13.4026 18.0685 13.8046 17.902 14.1916 17.7048L15.4997 18.7206C16.0291 19.1319 16.7764 19.0837 17.2314 18.6139L18.6095 17.2358C18.8387 17.0108 18.9768 16.7091 18.9973 16.3886C19.0178 16.068 18.9193 15.7512 18.7206 15.4989L17.7047 14.1916C17.9007 13.8092 18.0679 13.4068 18.2026 12.9894L19.8441 12.7802C20.1623 12.7422 20.4555 12.5889 20.6683 12.3493C20.8811 12.1097 20.9988 11.8005 20.9991 11.48V9.52C20.9993 9.19998 20.8823 8.89097 20.6701 8.65139C20.4579 8.41181 20.1653 8.25826 19.8476 8.21975ZM10.5 14.875C8.08762 14.875 6.125 12.9124 6.125 10.5C6.125 8.08763 8.08762 6.125 10.5 6.125C12.9124 6.125 14.875 8.08763 14.875 10.5C14.875 12.9124 12.9124 14.875 10.5 14.875Z"
        //   fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_2231_6646">
          <rect width="21" height="21" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSetting;
