import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconPen: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 21 21"
      fill="none"
      aria-label={alt}
    >
      <mask
        id="mask0_287_4329"
        // style="mask-type:luminance"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="21"
        height="21"
      >
        <path
          d="M0 0H21V21H0V0Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_287_4329)">
        <path
          d="M11.2498 13.4999L6.74976 14.3099L7.49976 9.74991L16.0948 1.18491C16.2343 1.04432 16.4002 0.932723 16.5829 0.856571C16.7657 0.780418 16.9618 0.741211 17.1598 0.741211C17.3578 0.741211 17.5538 0.780418 17.7367 0.856571C17.9194 0.932723 18.0853 1.04432 18.2248 1.18491L19.8148 2.77491C19.9553 2.91435 20.0669 3.08025 20.1431 3.26304C20.2193 3.44583 20.2585 3.64189 20.2585 3.83991C20.2585 4.03792 20.2193 4.23399 20.1431 4.41678C20.0669 4.59957 19.9553 4.76547 19.8148 4.90491L11.2498 13.4999Z"
          stroke="#000001"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M18 14.2502V18.7502C18 19.148 17.8419 19.5296 17.5606 19.8109C17.2794 20.0921 16.8978 20.2502 16.5 20.2502H2.25C1.85217 20.2502 1.47064 20.0921 1.18934 19.8109C0.908035 19.5296 0.75 19.148 0.75 18.7502V4.50024C0.75 4.10241 0.908035 3.72089 1.18934 3.43958C1.47064 3.15828 1.85217 3.00024 2.25 3.00024H6.75"
          stroke="#000001"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </svg>
  );
};

export default IconPen;
