import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconStatusDelivered: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M5.50875 11.6701C5.46019 11.6123 5.42348 11.5455 5.40074 11.4736C5.37799 11.4016 5.36964 11.3259 5.37618 11.2507C5.38272 11.1755 5.40401 11.1023 5.43884 11.0354C5.47367 10.9684 5.52134 10.909 5.57915 10.8605L12.624 4.95006C12.7406 4.85204 12.8915 4.80439 13.0433 4.81759C13.1952 4.83079 13.3355 4.90377 13.4336 5.02047C13.5316 5.13716 13.5792 5.28802 13.566 5.43985C13.5528 5.59167 13.4798 5.73204 13.3632 5.83007L6.31835 11.7405C6.26058 11.789 6.19381 11.8257 6.12185 11.8485C6.04989 11.8712 5.97415 11.8796 5.89896 11.873C5.82378 11.8665 5.75062 11.8452 5.68366 11.8104C5.61671 11.7756 5.55727 11.7279 5.50875 11.6701Z"
        fill="#F7971D"
      />
      <path
        d="M6.30715 11.7408C6.24938 11.7894 6.18261 11.8261 6.11065 11.8488C6.03869 11.8715 5.96295 11.8799 5.88776 11.8734C5.81257 11.8668 5.73941 11.8455 5.67246 11.8107C5.6055 11.7759 5.54607 11.7282 5.49755 11.6704L2.54235 8.14638C2.44433 8.02969 2.39667 7.87883 2.40988 7.727C2.42308 7.57517 2.49605 7.43481 2.61275 7.33678C2.72945 7.23876 2.8803 7.19111 3.03213 7.20431C3.18396 7.21751 3.32433 7.29049 3.42235 7.40718L6.37915 10.9312C6.42762 10.9891 6.46421 11.0559 6.48683 11.1279C6.50945 11.1999 6.51765 11.2757 6.51096 11.3509C6.50427 11.4261 6.48283 11.4992 6.44786 11.5661C6.41289 11.633 6.36507 11.6924 6.30715 11.7408Z"
        fill="#F7971D"
      />
    </svg>
  );
};

export default IconStatusDelivered;
