interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconSaaInColour: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`fill-current ${className}`}
      style={{ width: size, height: size }}
      width="18"
      height="17"
      viewBox="0 0 18 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="9"
        cy="8.5"
        r="8.5"
        fill="#FFAC4A"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.6999 5.80784C11.0312 5.999 11.3521 6.20763 11.6612 6.43286C11.5594 5.92382 11.406 5.42647 11.2035 4.94851C10.6868 5.01052 10.1777 5.1249 9.68416 5.28988C10.0313 5.4453 10.3703 5.61818 10.6999 5.80784ZM11.0275 4.57763C10.5385 3.63884 9.87536 3.06006 9.14562 3.06006C8.41588 3.06006 7.75298 3.63884 7.26397 4.57763C7.90815 4.66881 8.53979 4.83334 9.1466 5.068C9.75286 4.83351 10.3839 4.66899 11.0275 4.57763ZM13.4782 8.16122C13.8062 7.68497 14.0013 7.12987 14.0434 6.55311C14.0365 6.55311 14.0298 6.55413 14.0231 6.55514C14.0164 6.55617 14.0096 6.55719 14.0026 6.55719C13.5734 6.55719 13.2254 6.20926 13.2254 5.78005C13.2262 5.60631 13.2857 5.43793 13.3943 5.3023C12.9715 5.02233 12.3535 4.89023 11.6131 4.91821C11.854 5.51997 12.0247 6.14752 12.1219 6.7884C12.6247 7.19301 13.0797 7.65359 13.4782 8.16122ZM4.28847 6.55721C4.28083 6.55721 4.27347 6.55617 4.26612 6.55513H4.26612H4.26612H4.26611H4.26611H4.26611H4.26611H4.26611C4.26001 6.55426 4.25392 6.5534 4.24768 6.55313C4.28974 7.12986 4.48491 7.68491 4.81306 8.16104C5.21142 7.65354 5.66625 7.19303 6.16878 6.78842C6.2662 6.14752 6.43713 5.51999 6.6782 4.91822C5.93817 4.89064 5.32054 5.02314 4.89756 5.30232C5.00589 5.43804 5.06511 5.60642 5.06562 5.78007C5.06562 6.20926 4.71768 6.55721 4.28847 6.55721ZM9.14661 5.47458C8.67759 5.66145 8.22274 5.88209 7.78564 6.13476C7.34752 6.38692 6.92866 6.67116 6.5325 6.98515C6.39028 7.98354 6.39028 8.99704 6.5325 9.99542C6.92866 10.3094 7.34751 10.5937 7.78564 10.8458C8.2224 11.0986 8.67693 11.3194 9.14565 11.5064C9.61434 11.3193 10.0689 11.0986 10.5057 10.8458C10.9438 10.5937 11.3626 10.3094 11.7588 9.99542C11.8302 9.4969 11.8659 8.99391 11.8656 8.4903C11.866 7.98669 11.8304 7.48369 11.759 6.98517C11.3628 6.67115 10.9438 6.38691 10.5056 6.13478C10.0692 5.88216 9.61498 5.66152 9.14661 5.47458ZM14.3913 5.78005C14.3913 5.99465 14.2174 6.16862 14.0028 6.16862C13.7882 6.16862 13.6142 5.99465 13.6142 5.78005C13.6142 5.56545 13.7882 5.39148 14.0028 5.39148C14.2174 5.39148 14.3913 5.56545 14.3913 5.78005ZM13.2464 8.50005C12.9358 8.08648 12.5843 7.70525 12.1973 7.36212C12.2354 7.74023 12.2544 8.12003 12.2542 8.50005C12.254 8.8801 12.2345 9.2599 12.1959 9.63798C12.5834 9.29493 12.9353 8.91369 13.2464 8.50005ZM7.59133 5.80782C7.92138 5.61795 8.26091 5.44506 8.60861 5.28985C8.11453 5.12481 7.60496 5.01043 7.08775 4.94848C6.88518 5.42644 6.73181 5.92379 6.63001 6.43283C6.93917 6.20764 7.26006 5.99901 7.59133 5.80782ZM10.6999 11.1923C10.3698 11.3821 10.0303 11.555 9.68261 11.7102C10.1767 11.8753 10.6863 11.9897 11.2035 12.0516C11.406 11.5736 11.5594 11.0763 11.6612 10.5673C11.3521 10.7925 11.0312 11.0011 10.6999 11.1923ZM14.0259 10.445L14.026 10.445C14.0318 10.4458 14.0376 10.4466 14.0436 10.447C14.0014 9.87023 13.8062 9.31513 13.4782 8.83888C13.0799 9.34649 12.625 9.80707 12.1224 10.2117C12.025 10.8526 11.8541 11.4801 11.613 12.0819C12.3529 12.1097 12.9705 11.977 13.3937 11.6978C13.2853 11.5621 13.2261 11.3937 13.2256 11.22C13.2256 10.7908 13.5735 10.4429 14.0028 10.4429C14.0107 10.4429 14.0183 10.444 14.0259 10.445ZM7.26376 12.4225C7.75277 13.3613 8.41587 13.9401 9.14561 13.9401C9.87535 13.9401 10.5383 13.3613 11.0273 12.4225C10.3834 12.3312 9.7521 12.1667 9.14561 11.9321C8.53904 12.1667 7.90767 12.3312 7.26376 12.4225ZM14.3913 11.2201C14.3913 11.4347 14.2174 11.6086 14.0028 11.6086C13.7882 11.6086 13.6142 11.4347 13.6142 11.2201C13.6142 11.0055 13.7882 10.8315 14.0028 10.8315C14.2174 10.8315 14.3913 11.0055 14.3913 11.2201ZM4.28847 11.6086C4.50307 11.6086 4.67704 11.4347 4.67704 11.2201C4.67704 11.0055 4.50307 10.8315 4.28847 10.8315C4.07387 10.8315 3.8999 11.0055 3.8999 11.2201C3.8999 11.4347 4.07387 11.6086 4.28847 11.6086ZM6.09396 9.638C5.70682 9.29499 5.35532 8.91374 5.04482 8.50007C5.35544 8.0865 5.70693 7.70527 6.09396 7.36214C6.01754 8.11884 6.01754 8.88131 6.09396 9.638ZM4.24766 10.447C4.28979 9.8703 4.48495 9.31525 4.81304 8.83909C5.2114 9.34653 5.66616 9.80703 6.16857 10.2117C6.26575 10.8527 6.43649 11.4803 6.6774 12.0821C5.93794 12.1101 5.31991 11.977 4.89676 11.6978C5.00536 11.5622 5.06487 11.3938 5.0656 11.2201C5.0656 10.7909 4.71766 10.4429 4.28846 10.4429C4.28144 10.4429 4.27468 10.444 4.26793 10.445C4.26122 10.446 4.25454 10.447 4.24766 10.447ZM4.28847 6.16862C4.50307 6.16862 4.67704 5.99465 4.67704 5.78005C4.67704 5.56545 4.50307 5.39148 4.28847 5.39148C4.07387 5.39148 3.8999 5.56545 3.8999 5.78005C3.8999 5.99465 4.07387 6.16862 4.28847 6.16862ZM6.63001 10.5673C6.93915 10.7925 7.26004 11.0011 7.59133 11.1923C7.92095 11.382 8.25997 11.5548 8.60707 11.7102C8.1135 11.8752 7.60445 11.9896 7.08775 12.0516C6.88518 11.5736 6.73181 11.0763 6.63001 10.5673Z"
        fill="white"
      />
    </svg>
  );
};

export default IconSaaInColour;
