import React from 'react';

interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
}

const IconStatusFailed: React.FC<IconProps> = ({ size, alt, className }) => {
  return (
    <svg
      className={`${className}`}
      style={{ width: size, height: size }}
      aria-label={alt}
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M8 14.25C11.4518 14.25 14.25 11.4518 14.25 8C14.25 4.54822 11.4518 1.75 8 1.75C4.54822 1.75 1.75 4.54822 1.75 8C1.75 11.4518 4.54822 14.25 8 14.25Z"
        stroke="#FF271C"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 5.5V8"
        stroke="#FF271C"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 10.5H8.00625"
        stroke="#FF271C"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconStatusFailed;
