interface IconProps {
  size?: string;
  alt?: string;
  className?: string;
  color?: string;
}

const IconEndTime: React.FC<IconProps> = ({ size, alt, className, color }) => {
  return (
    <svg
      className={`fill-none ${className}`}
      style={{ width: size, height: size }}
      viewBox="0 0 16 16"
      aria-label={alt}
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.3335 1.33301V3.99967"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6665 1.33301V3.99967"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14 9.33366V4.00033C14 3.6467 13.8595 3.30756 13.6095 3.05752C13.3594 2.80747 13.0203 2.66699 12.6667 2.66699H3.33333C2.97971 2.66699 2.64057 2.80747 2.39052 3.05752C2.14048 3.30756 2 3.6467 2 4.00033V13.3337C2 13.6873 2.14048 14.0264 2.39052 14.2765C2.64057 14.5265 2.97971 14.667 3.33333 14.667H8.66667"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 6.66699H14"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.6665 13.3333L11.9998 14.6667L14.6665 12"
        stroke={color}
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default IconEndTime;
