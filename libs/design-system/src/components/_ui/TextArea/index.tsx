import { ChangeEvent, useState } from 'react';
import { cn } from '../../../lib/utils';

const TextArea = ({
  label,
  value,
  onChange,
  placeholder,
  error,
  rows = 3,
  required,
  disabled,
  className,
}: {
  label: string;
  value: string;
  onChange: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  error?: string;
  rows?: number;
  required?: boolean;
  disabled?: boolean;
  className?: string;
}) => {
  const [focus, setFocus] = useState(false);

  return (
    <div>
      <label className="block text-gray-700 text-sm font-bold mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      <textarea
        className={cn(
          'shadow appearance-none border text-sm placeholder:italic rounded w-full py-2 px-3 text-body leading-tight bg-white focus:outline-none resize-y',
          {
            'border-red-500': error,
            'border-grey-200': !error,
            '!border-black border': value,
            'hover:border hover:border-primary-500': !focus && !value,
            'border-primary-900 !border bg-common-white shadow-field': focus,
            'disabled:bg-grey-200 disabled:border-none disabled:cursor-not-allowed disabled:opacity-50':
              disabled,
          },
          className
        )}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        rows={rows}
        onFocus={() => setFocus(true)}
        onBlur={() => setFocus(false)}
        disabled={disabled}
      />
      {error && <p className="text-red-500 text-xs italic mt-1">{error}</p>}
    </div>
  );
};

export default TextArea;
