import { getWhatsAppDetails } from '../../../../lib/api/index';
import { useRouteHandler } from '../../../../context/RouteContext';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { Loader } from 'lucide-react';
import IconEmptyRecords from '../../Icon/IconEmptyRecords';
import { useTranslation } from 'react-i18next';
import { CDSSMessage } from '../../../../@types/Message';
import MessageContent from '../../MessagePage/MessageContent';
import { useState } from 'react';

interface WhatsAppBodyProps {
  data: any;
}

const WhatsAppBody = ({ data }: WhatsAppBodyProps) => {
  console.log('WhatsAppBody ===> data?.data?.data', data?.data?.data);
  const { t } = useTranslation();
  const showedMessages: CDSSMessage[] = data?.data?.data;

  const [highlightedMessageId, setHighlightedMessageId] = useState<
    string | null
  >(null); // 高亮状态

  return (
    <div className="flex flex-col w-full h-full overflow-y-auto p-4 space-y-4 hide-scrollbar">
      <div className="flex-1">
        {showedMessages && showedMessages.length > 0 ? (
          showedMessages?.map((message) => {
            const isCustomer = message.direction === 'inbound';
            return (
              <div
                key={message.id}
                id={message.id}
                className={`flex ${isCustomer ? 'justify-start' : 'justify-end'} ${highlightedMessageId === message.id ? 'bg-primary-200 flashing' : ''}`} // 应用闪烁类
              >
                <div
                  className={`flex flex-col max-w-3xl rounded-lg ${
                    isCustomer ? 'bg-white-100' : 'bg-white-100'
                  }`}
                >
                  <span
                    className={`flex text-gray-400 ${isCustomer ? 'justify-start' : 'justify-end'}`}
                  >
                    {message.userName}
                  </span>
                  <MessageContent
                    message={message}
                    isCustomer={isCustomer}
                  />
                </div>
              </div>
            );
          })
        ) : (
          <div className="w-full h-full flex flex-col items-center justify-center">
            <IconEmptyRecords size="78" />
            <div className="text-grey-500">
              {t('ctint-mf-interaction.whatsapp.noSelectedInteraction')}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

interface WhatsAppProps {
  conversationId: string;
}

const WhatsApp = ({ conversationId }: WhatsAppProps) => {
  const queryClient = new QueryClient();
  const { basePath } = useRouteHandler();
  const { data, isLoading } = useQuery({
    queryKey: ['whatsapp'],
    queryFn: () => getWhatsAppDetails(conversationId, basePath),
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <WhatsAppBody data={data} />
    </QueryClientProvider>
  );
};

export default WhatsApp;
