import {
    DatePicker,
    Panel,
    useRole,
} from '@cdss-modules/design-system';
import { useCallback, useRef, useState } from 'react';
import HistoryDetail from './HistoryDetail';
import dayjs from 'dayjs';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { useCustomerHistory } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
export function History({ data: historyData, loading, phoneNumber }: { data: any, loading: boolean | undefined, phoneNumber: string }) {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const [startDate, setStartDate] = useState<any | undefined>(undefined);
    const [conversationId, setConversationId] = useState<any | undefined>(undefined);
    const [isOpen, setIsOpen] = useState<boolean>(false);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [perPage, setPerPage] = useState<number>(10);
    const { userConfig } = useRole();
    const {
        getCustomerHistory,
        interactionsHistory,
        loading: historyLoading,
    } = useCustomerHistory()
    const count = interactionsHistory ? interactionsHistory?.totalCount || 0 : historyData?.totalCount || 0
    const totalPages = Math.ceil(count / perPage);

    const handleNext = () => {
        let tar = currentPage + 1;
        if (tar > totalPages) tar = totalPages;
        setCurrentPage(tar);
        getData(tar, perPage, startDate)
    };

    const handlePrevious = () => {
        let tar = currentPage - 1;
        if (tar < 1) tar = 1;
        setCurrentPage(tar);
        getData(tar, perPage, startDate)
    };
    const getData = useCallback((page: number, pageSize: number, date?: any) => {
        if (!phoneNumber) return
        const body: any = {
            page: page,
            pageSize: pageSize,
            conversationFilter: [{
                "type": "and",
                "name": "phoneNum",
                "value": phoneNumber
            }]
        }
        if (date) {
            body.conversationStart = date
        }
        if (inputRef?.current?.value) {
            body?.conversationFilter?.push({
                "type": "and",
                "name": "queues",
                "value": inputRef?.current?.value
            })
        }
        getCustomerHistory(body)
    }, [currentPage, perPage, inputRef, phoneNumber])
    const iconRender = (history: any) => {
        if (history?.mediaType === "voice"|| history?.mediaType === "callback"|| history?.mediaType === "call") {
            return <Icon name="phone" />
        }
        if (history?.mediaType === "email") {
            return <Icon name="email2" />
        }
        if (history?.mediaType === "message") {
            return <Icon name="msg" />
        }
        if (history?.mediaType === "whatsapp") {
            return <Icon name="msg" />
        }
        return (
            <>-
            </>
        )
    }
    const historyItem = () => {
        return (interactionsHistory?.interactions || historyData?.interactions)?.map((item: any, index: number) => {
            return <div className="flex flex-col" key={"interactions-" + (item?.conversationId || index)}>
                <div className="bg-white">
                    <div className="flex items-center gap-4 py-2 px-4 border-b-2 border-grey-200">
                        <div className="w-full flex flex-col items-center justify-between">

                            <p className='w-full flex justify-between'>
                                <span className='flex items-center gap-4'>
                                    {iconRender(item)}{item?.queues ||userConfig?.userName+` (from ${item?.direction==="outbound"?item?.dnis?.replace(/^(tel:\+|\+)/, ''):item?.ani?.replace(/^(tel:\+|\+)/, '')})`|| "-"}
                                </span>
                                <span>{item?.conversationStart && dayjs(
                                    item?.conversationStart
                                ).format('YYYY-MM-DD HH:mm') || "-"}</span>
                            </p>
                            <div className='w-full flex items-center justify-between pt-2'>
                                <p className='w-full flex gap-4'>
                                    {item?.wrapups ? item?.wrapups?.split(",")?.map((item: any, i: number) => {
                                        return <span className="bg-[#FFF5DA] w-fit p-2 rounded-md" key={"wrapuup-" + (i)}>{item}</span>
                                    }) : "-"}
                                </p>
                                <button onClick={() => {
                                    setIsOpen(true)
                                    setConversationId(item?.conversationId)
                                }}>
                                    <Icon
                                        name="back"
                                        className='-rotate-180'
                                    />
                                </button>
                                {/* <div onClick={() => {
                                    setIsOpen(true)
                                }}>

                                </div> */}

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        })
    }

    return (
        <>{!isOpen && <div className="px-4 grid grid-cols-2 gap-6">
            <Input
                ref={inputRef}
                // value={inputRef?.current?.value}
                isSearch={true}
                afterIcon={<Icon name="search" />}
                afterIconFn={() => { setCurrentPage(1); getData(1, perPage, startDate) }}
                onChange={(value) => { }}
                allowClear
                size='s'
                placeholder='Search...'
            />
            <DatePicker
                date={startDate}
                onChange={(date) => { setCurrentPage(1); setStartDate(date); getData(1, perPage, date); }}
            />
        </div>}

            {!isOpen &&
                <Panel containerClassName="rounded-none h-full overflow-auto" loading={historyLoading || loading}>
                    <>
                        <div className='flex-1 overflow-y-auto'>
                            {(historyData?.interactions?.length == 0 || interactionsHistory?.interactions?.length == 0) ?
                                <div className="w-full h-full flex flex-col items-center justify-center">
                                    <IconEmptyRecords size="78" />
                                    <div className="text-grey-500">No selected interaction.</div>
                                </div> : historyItem()
                            }
                        </div>
                        {/* {totalPages > 0 && ( */}
                        <section className="flex-row">
                            <div>
                                <Pagination
                                    current={currentPage}
                                    perPage={perPage}
                                    total={totalPages}
                                    totalCount={count}
                                    onChange={(v) => { setCurrentPage(v); getData(v, perPage, startDate); }}
                                    handleOnPrevious={() => handlePrevious()}
                                    handleOnNext={() => handleNext()}
                                    handlePerPageSetter={(p: number) => {
                                        const pageSize = Number(p);
                                        if (!isNaN(pageSize)) {
                                            setPerPage(pageSize);
                                        }
                                        setCurrentPage(1);
                                        getData(currentPage, pageSize, startDate)
                                    }}
                                />
                            </div>
                        </section>
                        {/* )} */}
                    </>
                </Panel>
            }
            <HistoryDetail isOpen={isOpen} onClose={() => setIsOpen(false)} convId={conversationId} />

        </>

    )
}