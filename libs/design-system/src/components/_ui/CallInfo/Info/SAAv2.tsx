/* eslint-disable @next/next/no-img-element */
import React, { memo, useCallback, useEffect, useState, useRef } from 'react';
import { cn } from '../../../../lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import {
  SAAContentItem,
  SAAContentSection,
  SAAResponse,
  SAASearchTerm,
} from '../../../../@types/SAA';
import Button from '../../Button';
import IconEmptyRecords from '../../Icon/IconEmptyRecords';
import { useCustomerHistory } from '../../../../lib/hooks/useCustomerInfo';
interface SAAv2Props {
  data: any;
  onSaaContentCopy?: (contentToSet: string) => void;
  onSaaContentSend?: (contentToSet: string) => void;
  hasAutoMod?: boolean;
  isShowCopy?: boolean; // 控制是否显示Copy按钮
  setIsFullResizablePanel?: (v: boolean) => void;
  autoSAAResult?: Record<string, SAAContentSection[]>;
  SAAManualSearchTerm?: SAASearchTerm;
}

// 分页大小 - 修改为99，不需要滚动分页
const PAGE_SIZE = 99;

// eslint-disable-next-line react/display-name
const SAAv2: React.FC<SAAv2Props> = memo((props) => {
  const {
    data,
    onSaaContentCopy,
    onSaaContentSend,
    hasAutoMod,
    isShowCopy = true,
    setIsFullResizablePanel,
    autoSAAResult,
    SAAManualSearchTerm,
  } = props;

  const { getSAAMatchDirectly } = useCustomerHistory();

  // 从 store 获取方法
  // const { getSAAResult } = useConversationStore();

  // 本地搜索词状态
  const [searchTerm, setSearchTerm] = useState<SAASearchTerm>();
  const [isExpanded, onToggleExpand] = useState(true);
  const [selectedItem, setSelectedItem] = useState<number | null>(null);
  const [selectedSection, setSelectedSection] = useState<number | null>(null);
  const [searchMode, setSearchMode] = useState<'manual' | 'auto'>('manual');
  const [isLocalLoading, setIsLocalLoading] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  // 手动搜索结果
  const [manualSearchResult, setManualSearchResult] =
    useState<SAAContentSection>({
      rawTextBody: '',
      keywords: [],
      messageId: '',
      SAAContentList: [],
    });

  // 存储自动搜索结果
  const [autoSearchResult, setAutoSearchResult] = useState<SAAContentSection[]>(
    []
  );

  const convId = data.convId || '';

  // 从API响应中提取所需数据并更新manualSearchResult
  const updateManualSearchResult = useCallback(
    (saaResponse: SAAResponse) => {
      // 创建搜索结果部分
      const newManualResult: SAAContentSection = {
        rawTextBody: searchTerm?.relatedMessageContent?.rawTextBody,
        keywords: searchTerm?.relatedMessageContent?.keywords,
        messageId: searchTerm?.relatedMessageContent?.messageId,
        timestamp: searchTerm?.relatedMessageContent?.timestamp,
        SAAContentList: (saaResponse?.data?.contents || []).map((item) => ({
          ...item,
          // 确保关键字段存在
          title: item?.title || '',
          content: item?.content || '',
          editable: true,
        })),
      };

      // 更新状态
      setManualSearchResult(newManualResult);
    },
    [searchTerm]
  );

  // 重置手动搜索的结果
  const resetManualSearchResult = useCallback(() => {
    setSearchTerm(undefined);
    setManualSearchResult({
      rawTextBody: '',
      keywords: [],
      messageId: '',
      SAAContentList: [],
    });
  }, []);
  useEffect(() => {
    if (SAAManualSearchTerm) {
      // 执行搜索逻辑
      console.log('收到新的手动搜索条件:', SAAManualSearchTerm);
      setSearchMode('manual');
      setSearchTerm(SAAManualSearchTerm);
    }
  }, [SAAManualSearchTerm]);
  //searchTerm变化时执行搜索逻辑
  useEffect(() => {
    if (searchTerm && searchTerm.searchTerm && searchTerm.triggerSearch) {
      handleSearch();
    }
  }, [searchTerm]);

  // 当会话ID变化时更新搜索模式和重置搜索结果
  useEffect(() => {
    resetManualSearchResult();

    // 默认显示自动模式
    if (hasAutoMod) {
      setSearchMode('auto');
    } else {
      setSearchMode('manual');
    }
  }, [convId, hasAutoMod, resetManualSearchResult]);

  // 监听store中SAAResult的变化，更新自动搜索结果
  useEffect(() => {
    if (convId) {
      // const storeAutoResults = getSAAResult(convId);
      const storeAutoResults = autoSAAResult && autoSAAResult[convId];
      console.log('从Store获取自动搜索结果 总:', autoSAAResult);
      if (storeAutoResults && storeAutoResults.length > 0) {
        console.log('从Store获取自动搜索结果:', storeAutoResults);
        setAutoSearchResult(storeAutoResults);
      } else {
        // 如果没有数据，则清空
        setAutoSearchResult([]);
      }
    }
  }, [convId, autoSAAResult]);

  // 监听searchMode变化，当切换到auto模式时重置选中状态
  useEffect(() => {
    setSelectedItem(null);
    setSelectedSection(null);
  }, [searchMode]);

  // 高亮搜索词 - 使用section自己的keywords
  const renderHighlightedText = useCallback(
    (value: string, keywords: string[]) => {
      if (!value) return ''; // 添加空值检查
      if (!keywords || !keywords.length) return value;

      let result = value;

      // 对每个关键词进行高亮处理
      keywords.forEach((keyword) => {
        if (!keyword) return;

        try {
          const regex = new RegExp(keyword, 'gi');
          result = result.replace(
            regex,
            (match) =>
              `<span class="bg-[#ffe4c4] text-orange-800 px-0.5 rounded">${match}</span>`
          );
        } catch (error) {
          // 忽略无效的正则表达式错误
          console.warn('Invalid regex for keyword:', keyword);
        }
      });

      return result;
    },
    []
  );

  // 处理点击选择item
  const handleItemSelect = (sectionIndex: number, index: number) => {
    // 如果点击已选中的项，则取消选中
    if (selectedItem === index && selectedSection === sectionIndex) {
      setSelectedItem(null);
      setSelectedSection(null);
    } else {
      // 否则选中新项
      setSelectedItem(index);
      setSelectedSection(sectionIndex);
    }
  };

  // 处理点击复制
  const handleSaaCopy = () => {
    if (selectedItem !== null && selectedSection !== null && onSaaContentCopy) {
      let selectedContent;

      // 根据selectedSection和selectedItem获取选中的内容
      if (
        sectionsToRender.length > selectedSection &&
        sectionsToRender[selectedSection].SAAContentList?.length > selectedItem
      ) {
        selectedContent =
          sectionsToRender[selectedSection].SAAContentList[selectedItem];
      }

      if (selectedContent) {
        // 获取内容（标题或内容，根据实际需求）
        const contentToSet = selectedContent.content || selectedContent.title;
        // 调用回调函数并传递内容
        onSaaContentCopy(contentToSet);
      }
    }
  };

  // 处理复制到剪贴板
  const [copyFeedback, setCopyFeedback] = useState(false);

  // 改进的复制到剪贴板函数
  const handleCopyToClipboard = async () => {
    if (selectedItem !== null && selectedSection !== null) {
      let selectedContent;

      // 根据selectedSection和selectedItem获取选中的内容
      if (
        sectionsToRender.length > selectedSection &&
        sectionsToRender[selectedSection].SAAContentList?.length > selectedItem
      ) {
        selectedContent =
          sectionsToRender[selectedSection].SAAContentList[selectedItem];
      }

      if (selectedContent) {
        // 获取内容（标题或内容，根据实际需求）
        const contentToSet = selectedContent.content || selectedContent.title;

        // 检查内容是否为空
        if (!contentToSet || contentToSet.trim() === '') {
          console.warn('复制内容为空');
          return;
        }

        try {
          // 检查是否支持 navigator.clipboard
          if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(contentToSet);
            // 显示视觉反馈
            setCopyFeedback(true);
            setTimeout(() => {
              setCopyFeedback(false);
            }, 2000);
            console.log('内容已复制到剪贴板');
          } else {
            // 降级方案：使用传统的复制方法
            fallbackCopyTextToClipboard(contentToSet);
          }
        } catch (err) {
          console.error('复制到剪贴板失败: ', err);
          // 尝试降级方案
          fallbackCopyTextToClipboard(contentToSet);
        }
      } else {
        console.warn('未找到选中的内容');
      }
    } else {
      console.warn('未选中任何项目');
    }
  };

  // 降级复制方案（适用于不支持 navigator.clipboard 的环境）
  const fallbackCopyTextToClipboard = (text: string) => {
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;

      // 避免在页面中显示
      textArea.style.top = '0';
      textArea.style.left = '0';
      textArea.style.position = 'fixed';
      textArea.style.opacity = '0';

      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);

      if (successful) {
        // 显示视觉反馈
        setCopyFeedback(true);
        setTimeout(() => {
          setCopyFeedback(false);
        }, 2000);
        console.log('内容已复制到剪贴板（降级方案）');
      } else {
        console.error('降级复制方案也失败了');
      }
    } catch (err) {
      console.error('降级复制方案失败: ', err);
    }
  };

  // 处理发送选中内容
  const handleSendContent = () => {
    if (selectedItem !== null && selectedSection !== null && onSaaContentSend) {
      let selectedContent;

      // 根据selectedSection和selectedItem获取选中的内容
      if (
        sectionsToRender.length > selectedSection &&
        sectionsToRender[selectedSection].SAAContentList?.length > selectedItem
      ) {
        selectedContent =
          sectionsToRender[selectedSection].SAAContentList[selectedItem];
      }

      if (selectedContent) {
        const contentToSet = selectedContent.content || selectedContent.title;
        // 调用发送函数并传递内容
        onSaaContentSend(contentToSet);
      }
    }
  };
  // 处理搜索按钮点击
  const handleSearch = () => {
    if (!searchTerm || !searchTerm.searchTerm) return;

    // 更新加载状态
    setIsLocalLoading(true);

    // 根据后端接口参数格式
    const params = {
      content: searchTerm.searchTerm,
      page: 1,
      pageSize: PAGE_SIZE,
    };

    getSAAMatchDirectly(params)
      .then((response) => {
        if (response) {
          updateManualSearchResult(response);
        } else {
          console.warn('No SAA Match data received');
          // 如果没有数据，创建一个空结果集
          setManualSearchResult({
            rawTextBody: '',
            keywords: searchTerm.relatedMessageContent?.keywords || [],
            messageId: '',
            SAAContentList: [],
          });
        }
      })
      .finally(() => {
        setIsLocalLoading(false);
      });
  };

  // 处理输入框变化
  const handleInputChange = (value: string | number) => {
    // 将任何输入转换为字符串
    const strValue = String(value);
    setSearchTerm({ searchTerm: strValue });

    // 如果用户清空了输入框，清空搜索结果
    if (strValue === '') {
      setManualSearchResult({
        rawTextBody: '',
        keywords: [],
        messageId: '',
        SAAContentList: [],
      });
    }
  };
  const handleInputKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    // 检查是否按下回车键
    if (event.key === 'Enter') {
      // 触发搜索
      handleSearch();
    }
  };

  // 检查是否应该渲染按钮
  const shouldRenderCopyButton = !!onSaaContentCopy;
  const shouldRenderSendButton = !!onSaaContentSend;
  const shouldRenderButtonContainer =
    shouldRenderCopyButton || shouldRenderSendButton || isShowCopy;

  const isLoading = isLocalLoading;

  // 计算自动搜索结果的数量
  const autoSearchSectionCount = autoSearchResult.length || 0;
  const autoSearchItemCount = autoSearchResult.reduce(
    (acc, section) => acc + (section.SAAContentList?.length || 0),
    0
  );

  // 计算总数量
  const totalCount =
    searchMode === 'manual'
      ? manualSearchResult.SAAContentList?.length || 0
      : autoSearchItemCount;

  // 显示内容标志
  const hasManualContent = manualSearchResult.SAAContentList?.length > 0;
  const hasAutoContent =
    autoSearchResult.length > 0 &&
    autoSearchResult.some((section) => section.SAAContentList?.length > 0);
  const hasContent =
    searchMode === 'manual' ? hasManualContent : hasAutoContent;

  // 根据当前模式获取要渲染的sections
  const sectionsToRender =
    searchMode === 'manual'
      ? [manualSearchResult] // 将manualSearchResult包装成数组
      : autoSearchResult;

  // 格式化section标题
  const formatSectionTitle = (
    section: SAAContentSection,
    sectionIndex: number
  ) => {
    if (
      !section.rawTextBody &&
      (!section.keywords || section.keywords.length === 0)
    ) {
      return null;
    }

    // 格式化时间戳
    const formattedTime = section.timestamp
      ? new Date(section.timestamp).toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false,
        })
      : '';

    return (
      <div className="w-full p-3 bg-gray-50 border border-gray-200 text-sm text-gray-700">
        {/* 第一行: rawTextBody 和 timestamp */}
        <div className="flex items-center justify-between w-full">
          <div className="max-w-[70%] flex pr-2">
            <span className="flex-shrink-0">&quot;</span>
            <span className="truncate mx-0.5">{section.rawTextBody || ''}</span>
            <span className="flex-shrink-0">&quot;</span>
          </div>
          {formattedTime && (
            <div className="text-gray-400 text-xs shrink-0">
              {formattedTime}
            </div>
          )}
        </div>

        {/* 第二行: keywords 带序号 */}
        {section.keywords && section.keywords.length > 0 && (
          <div className="flex flex-wrap items-center mt-2">
            <div className="text-gray-700 mr-2">keyword:</div>
            {section.keywords.map((keyword, index) => (
              <span
                key={`keyword-${sectionIndex}-${index}`}
                className="bg-[#ffe4c4] text-orange-800 px-2 py-0.5 rounded mr-2 mb-1 flex items-center"
              >
                <span className="text-xs font-medium bg-orange-500 text-white rounded-full w-5 h-5 flex items-center justify-center mr-1">
                  {index + 1}
                </span>
                {keyword}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  };
  // 渲染空状态组件
  const renderEmptyState = () => (
    <div className="w-full h-full flex flex-col items-center justify-center">
      <IconEmptyRecords size="78" />
      <div className="text-gray-500 mt-4">No selected interaction.</div>
    </div>
  );

  return (
    <div
      className={cn(
        'h-full border-t-2 overflow-hidden mt-3 shadow-md',
        isExpanded ? 'flex-1' : ''
      )}
    >
      <div className="bg-gray-50 h-full flex flex-col items-center justify-center relative">
        {/* 第一行: Tags/Tabs for search modes and expand/collapse button */}
        <div className="flex items-center w-full p-3 bg-gray-100 border-b">
          {/* Tags/Tabs for search modes */}
          <div className="flex flex-1">
            <button
              className={cn(
                'px-4 py-1.5 rounded-t-md transition-colors flex items-center',
                searchMode === 'manual'
                  ? 'bg-white text-orange-500 font-medium border-b-2 border-orange-500'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              )}
              onClick={() => setSearchMode('manual')}
            >
              <span>SAA Search</span>
            </button>

            {/* 只有当hasAutoMod为true时才显示Auto Search标签 */}
            {hasAutoMod && (
              <button
                className={cn(
                  'px-4 py-1.5 rounded-t-md transition-colors ml-2 flex items-center',
                  searchMode === 'auto'
                    ? 'bg-white text-orange-500 font-medium border-b-2 border-orange-500'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                )}
                onClick={() => setSearchMode('auto')}
              >
                <span>Auto Results</span>
                {hasAutoContent && (
                  <span className="bg-orange-100 text-primary px-2 py-1 rounded-md ml-2 font-medium text-xs">
                    {autoSearchSectionCount}
                  </span>
                )}
              </button>
            )}
          </div>

          {/* Expand/collapse button at the right of first row */}
          <button
            onClick={() => {
              setIsFullResizablePanel && setIsFullResizablePanel(isExpanded);
              onToggleExpand(!isExpanded);
            }}
            className="ml-2 bg-white rounded-full p-1.5 shadow-md hover:bg-gray-100 transition-colors"
          >
            <Icon
              name={'expand'}
              className={isExpanded ? '' : '-rotate-180'}
            />
          </button>
        </div>

        {isExpanded && (
          <>
            {/* 第二行: SAA Icon and Search Input */}
            <div className="w-full p-3 bg-white border-b flex items-center">
              <div className="flex items-center gap-2 mr-3">
                <Icon
                  name={'saa'}
                  size={20}
                />
                <span className="font-bold text-black">SAA</span>
              </div>

              {/* Separator */}
              <div className="w-px h-5 bg-gray-300 mr-3"></div>

              <div className="flex-1 relative">
                {searchMode === 'manual' && (
                  <Input
                    type="text"
                    isSearch={true}
                    afterIcon={<Icon name="search" />}
                    afterIconFn={handleSearch}
                    onChange={handleInputChange}
                    onKeyDown={handleInputKeyDown}
                    allowClear={true}
                    size="s"
                    placeholder="Input Text Here"
                    value={searchTerm?.searchTerm}
                    className="rounded-md w-full"
                  />
                )}
                {searchMode === 'auto' && (
                  <div className="text-gray-500">
                    Auto search based on conversation
                  </div>
                )}
              </div>
            </div>

            {/* Content Area */}
            <div
              className="flex-1 overflow-y-auto w-full scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent"
              ref={contentRef}
            >
              {/* Content Container */}
              <div className="h-full">
                {hasContent ? (
                  <>
                    <div>
                      {/* 统一渲染逻辑，使用sectionsToRender */}
                      {sectionsToRender.map(
                        (section: SAAContentSection, sectionIndex: number) => (
                          <div
                            key={`section-${sectionIndex}`}
                            className="mb-4"
                          >
                            {/* Section标题 - 显示带格式的rawTextBody和keywords */}
                            {formatSectionTitle(section, sectionIndex)}

                            {/* Section内容列表 */}
                            {section.SAAContentList?.map(
                              (item: SAAContentItem, i: number) => {
                                if (!item) {
                                  console.warn('Undefined item at index:', i);
                                  return null;
                                }

                                // 判断是否被选中
                                const isSelected =
                                  selectedItem === i &&
                                  selectedSection === sectionIndex;

                                // 所有item都可以被选中
                                const canSelect = true;

                                return (
                                  <div
                                    className={cn(
                                      'cursor-pointer p-4 transition-all duration-200 hover:bg-gray-50',
                                      isSelected
                                        ? 'border-l-4 border-l-orange-500 bg-orange-50'
                                        : 'border-l-4 border-l-transparent bg-transparent'
                                    )}
                                    style={{
                                      paddingLeft: isSelected
                                        ? '0.75rem'
                                        : '1rem',
                                    }}
                                    key={`SAA-${sectionIndex}-${i}`}
                                    onClick={() =>
                                      canSelect &&
                                      handleItemSelect(sectionIndex, i)
                                    }
                                  >
                                    <h3 className="font-medium text-base mb-2 text-gray-800 flex items-center gap-2">
                                      <span
                                        className={cn(
                                          'w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold',
                                          isSelected
                                            ? 'bg-primary text-white'
                                            : 'bg-gray-200 text-gray-700'
                                        )}
                                      >
                                        {i + 1}
                                      </span>
                                      <div
                                        dangerouslySetInnerHTML={{
                                          __html: renderHighlightedText(
                                            item?.title || 'Result' + (i + 1),
                                            section.keywords || []
                                          ),
                                        }}
                                      />
                                    </h3>
                                    <div
                                      className={cn(
                                        'text-sm text-gray-600 leading-relaxed user-select-text ml-8 relative',
                                        isSelected
                                          ? 'max-h-none'
                                          : 'max-h-[5.5em] overflow-hidden' // 约5行文本高度
                                      )}
                                    >
                                      <div
                                        dangerouslySetInnerHTML={{
                                          __html: renderHighlightedText(
                                            item?.content || '',
                                            section.keywords || []
                                          ),
                                        }}
                                      />
                                      {!isSelected && (
                                        <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-white to-transparent pointer-events-none"></div>
                                      )}
                                    </div>
                                  </div>
                                );
                              }
                            )}
                          </div>
                        )
                      )}
                    </div>

                    {/* 加载中状态指示器 */}
                    {isLoading && (
                      <div className="flex justify-center items-center p-4">
                        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-500"></div>
                      </div>
                    )}
                  </>
                ) : // 空状态组件
                isLoading ? (
                  <div className="flex justify-center items-center h-full py-12 px-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                  </div>
                ) : (
                  renderEmptyState()
                )}
              </div>
            </div>
          </>
        )}

        {/* 底部按钮 - 只在展开状态显示且有按钮需要渲染 */}
        {isExpanded && (
          <div className="border-t border-gray-200 p-3 flex justify-between items-center w-full bg-white">
            <div className="text-gray-500 text-sm flex items-center">
              {totalCount > 0 && (
                <div className="flex items-center">
                  <span className="bg-orange-100 text-primary px-2 py-1 rounded-md mr-2 font-medium">
                    {totalCount}
                  </span>
                  <span>total results</span>
                </div>
              )}
            </div>

            {shouldRenderButtonContainer && (
              <div className="flex space-x-3">
                {/* 复制到剪贴板按钮，根据isShowCopy属性控制显示 */}
                {isShowCopy && (
                  <div className="relative">
                    <Button
                      onClick={handleCopyToClipboard}
                      disabled={
                        selectedItem === null || selectedSection === null
                      }
                      // variant="orange"
                    >
                      Copy
                    </Button>
                    {copyFeedback && (
                      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                        Copied!
                      </div>
                    )}
                  </div>
                )}

                {shouldRenderCopyButton && (
                  <Button
                    onClick={handleSaaCopy}
                    disabled={selectedItem === null || selectedSection === null}
                  >
                    Copy To Chat
                  </Button>
                )}

                {shouldRenderSendButton && (
                  <Button
                    disabled={selectedItem === null || selectedSection === null}
                    onClick={handleSendContent}
                  >
                    Send
                  </Button>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
});

export default SAAv2;
