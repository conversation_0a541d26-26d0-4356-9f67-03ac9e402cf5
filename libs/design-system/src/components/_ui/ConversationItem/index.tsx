import { useEffect } from 'react';
import { ConversationItem } from '../../../@types/Conversation';
// import { useCDSSStore } from 'cdss/store/conversation';
import IconBadge from './IconBadge';
import { QueueName } from './queueName';
import { useSearchParams } from 'react-router-dom';
import { Tooltip } from '../Tooltip';
import { CallBack, useRole } from '@cdss-modules/design-system';

//差咗props
export const ConversationItemComponent: React.FC<{
  conversation: ConversationItem;
  isExpanded: boolean;
  onClick?: () => void;
  currentFilter?:string
  hasNewMessage?: boolean
}> = ({ conversation, isExpanded, onClick ,currentFilter, hasNewMessage}) => {
    const [searchParams] = useSearchParams();
    const { userConfig } = useRole();
  useEffect(() => {
    console.log("side bar conversation:", conversation);
    // const unsubscribe = useAgentActivityStore.subscribe((state, prevState) => {

    // });

    // // 组件卸载时取消订阅
    // return () => {
    //   unsubscribe();
    // };
  }, []);

  // console.log('conversation看看有没有时间字段：', conversation);

  const nameFormat = (username: string): string => {
    const trimmedText = String(username || '').trim();

    if (!trimmedText) {
      return '?';
    }

    if (/^[A-Za-z\s]+$/.test(trimmedText)) {
      return trimmedText.charAt(0).toUpperCase();
    }

    if (trimmedText.includes('@')) {
      const [username] = trimmedText.split('@');
      return username.substring(0, 2).toLowerCase();
    }

    // 修改电话号码显示，只显示后4位
    if (/^\+?\d+$/.test(trimmedText.replace(/[-()\s]/g, ''))) {
      const cleanNumber = trimmedText.replace(/[^\d]/g, '');
      return cleanNumber.slice(-3);
    }

    return trimmedText.substring(0, 2);
  };

  const getIconType = (conversation: ConversationItem): string => {
    switch (conversation.type) {
      case 'callback':
        return conversation.icon;
      default:
        return conversation.type;
    }
  };

  const formatTime = (dateTimeStr: string | null): string => {
    if (!dateTimeStr) return '';

    const date = new Date(dateTimeStr);
    const now = new Date();

    // Check if the date is today
    const isToday = date.toDateString() === now.toDateString();

    if (isToday) {
      // Format as HH:mm for today's dates
      return date.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
      });
    } else {
      // Format as yyyy/MM/dd for other dates
      return date
        .toLocaleDateString('en-US', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
        })
        .replace(/\//g, '/');
    }
  };

  return (
    <div
      className="p-1"
      onClick={onClick}
    >
      <div
        key={conversation.id}
        className={`relative justify-center cursor-pointer flex items-center w-full transition-all duration-300 rounded-3xl ${
          conversation?.isActive||searchParams?.get("conversationId")==conversation?.id ? 'bg-orange-100 p-2' : 'p-2'
        } hover:bg-orange-100`}
        style={isExpanded ? {} : { aspectRatio: '1 / 1' }}
      >
        {/* Avatar Container */}
        <div
          className="relative w-14 xl-w-14 2xl-w-18 h-14 xl-h-14 2xl-h-18 rounded-full flex items-center justify-center"
          style={{
            background: `conic-gradient(#d4d4d4 ${100 - conversation.borderProgress}%, #F81322 ${
              100 - conversation.borderProgress
            }% 100%)`,
          }}
        >
          {/* Avatar Content */}
          <div
            className="rounded-full flex items-center justify-center relative  w-12 xl-w-12 2xl-w-14 h-12 xl-h-12 2xl-h-14"
            style={{}}
          >
            <div
              className="myClass w-full h-full rounded-full flex items-center justify-center relative w-12 xl-w-12 2xl-w-14 h-12 xl-h-12 2xl-h-14"
              style={{
                background: `linear-gradient(to top, #BFE6FE ${conversation.bgProgress}%, white ${conversation.bgProgress}%)`,
              }}
            >
              <span className="relative text-black font-bold text-xl z-10">
                {nameFormat(conversation.userName)}
              </span>
            </div>

            {/* Source Icon */}
            <IconBadge
              type={getIconType(conversation)}
              hasUnread={false}
              icon={conversation?.icon}
            />
            {/* Red new message Dot */}
            {hasNewMessage && (
            <div className="absolute -top-3 -right-3 w-2 h-2 bg-red-500 rounded-full z-50" />
          )}
          </div>
        </div>
        {isExpanded && (
          <div className="ml-4 flex flex-col flex-1 min-w-0">
            {conversation.queueId && (
              <div className="inline-block">
                <span className="text-gray-500 text-xs bg-gray-200 rounded px-1 py-0.5">
                  <QueueName queueId={conversation.queueId} />
                </span>
              </div>
            )}
            <div className="flex justify-between text-sm text-gray-600">
               <Tooltip
                    key={'Tooltip' + conversation.userName}
                    content={conversation.userName}
                    trigger={ <span className="text-black font-bold text-sm truncate">
                        {`${conversation.userName}`}
                        {/* {conversation.userAddress != conversation.userName && ( //cater outbound
                            <label className="text-black font-light text-sm truncate">
                               ({`${conversation.userAddress}`})
                            </label>
                          )} */}
                      </span>
                    }
                />
              {conversation.connectedTime&&conversation?.type!=="call" && (
                <span className="ml-2 text-gray-500 whitespace-nowrap text-sm">
                  {formatTime(conversation.connectedTime || conversation.startTime)}
                </span>
              )}
              {
                conversation?.type=="call"&&!conversation?.isActive&&currentFilter=="history"&&<CallBack triggerClassName='!hover:bg-orange-100' iconClassName='text-[#1CC500] bg-inherit hover:text-primary' buttonText="" number={conversation?.phoneNumber} userId={conversation?.agentId||userConfig?.id}/>
              }
            </div>

            {conversation.connectedTime && conversation?.type === "message" && (
                <div className="flex flex-col flex-1 min-w-0">
                  {conversation.userAddress !== conversation.userName && (
                   <span className="font-bold text-sm truncate text-slate-500">
                     (Tel: {`${conversation.userAddress}`})
                    </span>
                  )}
                </div>
              )}
     
            {conversation?.type==="call"&&<div className='flex items-center gap-2'>
                {conversation?.connectedTime && (
                <span className="whitespace-nowrap text-sm">
                  {formatTime(conversation?.connectedTime)}
                </span>
              )}
                {conversation?.state&&<p style={{fontSize:"initial",fontWeight:"initial"}}>{conversation?.state=="Missed"?<span className="text-[#FF271C]"> Missed</span>:<span className="text-[#1CC500]"> {conversation?.state}</span>}</p>}
                </div>}
            {conversation?.type === "message" && (
            <div className="flex justify-between text-sm text-gray-600">
               {conversation.latestMessage && (
              <span className="truncate block w-full">
                {conversation.latestMessage}
              </span>
               )}
            </div>
             )}
          </div>
        )}
      </div>
    </div>
  );
};
