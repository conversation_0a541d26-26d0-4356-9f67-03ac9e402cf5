
import { useEffect } from 'react';
import { useQueuesStore } from '../../../../../../apps/ctint-mf-cdss/store/queues';
interface QueueNameProps {
  queueId: string;
}

// 方法 1：使用接口定义 props
export const QueueName: React.FC<QueueNameProps> = ({ queueId }) => {
  const { queues, setQueues } = useQueuesStore();
  const queueName = ():string=>{
    // console.log(queueId)
    // console.log( queues.find(queue=>queue.id===queueId))
    // const queue: {id?: string; name?: string; [key: string]: any} = queues.find((queue: {id?: string; name?: string; [key: string]: any}) => queue.id === queueId)
    // // console.log(queue)
    // return queue?.name;
    const queue = queues.find((queue: {id?: string; name?: string; [key: string]: any}) => queue.id === queueId)|| { id: undefined, name: undefined };
    return queue?.name || '';
  }
  // useEffect(()=>{console.log(queues)},[])
  return <>{queueName()}</>;
};
