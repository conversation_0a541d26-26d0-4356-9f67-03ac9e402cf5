import { useEffect } from 'react';
import IconWhatsapp from '../Icon/IconSVG';
import IconSVG from '../Icon/IconSVG';

interface IconBadgeProps {
  type: string;
  hasUnread?: boolean;
  containerClassName?: string;
  iconClassName?: string;
  icon?: string;
}

const IconBadge = ({
  type,
  hasUnread = false,
  containerClassName = 'absolute -top-2 -right-2 w-6 h-6', // 默认值
  iconClassName = 'w-6 h-6', // 默认值
  icon
}: IconBadgeProps) => {
  useEffect(() => {
    // console.log(type)
  }, []);

  return (
    <div
      className={`${containerClassName} bg-white rounded-full shadow-md z-30 flex items-center justify-center`}
    >
      <IconSVG
        className={`object-contain ${iconClassName}`}
        iconType={type}
        icon={icon}
      />
      {hasUnread && (
        <span
          className="absolute w-1.5 h-1.5 bg-red-500 rounded-full z-40"
          style={{ top: '-1px', right: '-1px' }}
        />
      )}
    </div>
  );
};

export default IconBadge;
