/* eslint-disable @next/next/no-img-element */
import React, { memo, MutableRefObject, useCallback, useEffect, useState } from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Panel } from '@cdss-modules/design-system';

interface SAAProps {
    data: any;
    setIsFullResizablePanel?: (v: boolean) => void;
}

const SAA: React.FC<SAAProps> = memo((props) => {
    const { data, setIsFullResizablePanel } = props

    const [searchTerm, setSearchTerm] = useState('');
    const [isExpanded, onToggleExpand] = useState(true);
    // useEffect(() => {
    //     if (searchTerm && searchTerm?.length > 0) {
    //         data?.getSAADetail(searchTerm)
    //     }
    // }, [searchTerm])
    const render = useCallback((value: string) => {
        if (!searchTerm) return <p>{value}</p>;

        const regex = new RegExp(searchTerm, 'gi');
        const highlightedQuestion = value.replace(regex, match => `<span class="bg-[#ffe4c4]">${match}</span>`);
        return <p dangerouslySetInnerHTML={{ __html: highlightedQuestion }} />;
    }, [searchTerm]);

    return (
        <Panel
            containerClassName="h-full overflow-hidden rounded-none"
            loading={data?.SAADetailLoading}
            className="justify-end"
        >
            <div className={cn('border-t-2 overflow-hidden', isExpanded ? "flex-1" : "")}>
                <div className="bg-gray-50 h-full flex flex-col items-center justify-center">
                    {/* Expand/Collapse Button - Absolute positioned at top center */}
                    {/* Main Container */}
                    {isExpanded && <div className="flex flex-col h-full w-full">
                        {/* Header */}
                        <div className="flex items-center p-3 border-b">
                            <div className="flex items-center gap-2 flex-1">
                                {/* SAA Icon and Text */}
                                <div className="flex items-center gap-2">
                                    <Icon
                                        name={"saa"}
                                        size={20}
                                    />
                                    <span className="font-medium font-bold">SAA</span>
                                </div>

                                {/* Separator */}
                                <div className="w-px h-5 bg-gray-200 mx-3"></div>

                                {/* Search Input Container */}
                                <div className="relative flex-1">
                                    <Input
                                        // ref={inputRef}
                                        type="text"
                                        isSearch={true}
                                        afterIcon={<Icon name="search" />}
                                        afterIconFn={() => {
                                            data?.getSAADetail(searchTerm)
                                        }}
                                        onChange={(value: any) => setSearchTerm(value)}
                                        allowClear
                                        size="s"
                                        placeholder="Search..."
                                        value={searchTerm}

                                    />
                                </div>
                            </div>
                        </div>

                        {/* Content Area */}
                        <div
                            className={`flex-1 overflow-y-auto h-full transition-all duration-300  ${isExpanded ? 'opacity-100' : 'h-0 opacity-0 overflow-hidden'
                                }`}
                        >
                            {/* Custom Scrollbar Container */}
                            <div
                                className={`p-4 h-full`}
                            >
                                <div className="space-y-6 h-full">
                                    {
                                        data?.SAADetail?.faqs?.map((item: any, i: number) => {
                                            return <div className="relative pr-3" key={"SAA-" + i}>
                                                {/* <div className="absolute right-0 top-0 w-1 h-full bg-orange-500 rounded"></div> */}
                                                <h3 className="font-medium text-base mb-2">
                                                    {render(item?.title)}
                                                </h3>
                                                <div>
                                                    {render(item?.question)}
                                                </div>
                                                <pre className="text-sm text-gray-600 leading-relaxed">
                                                    {render(item?.answer)}
                                                </pre>
                                            </div>
                                        })
                                    }
                                </div>
                            </div>
                        </div>
                    </div>}
                    <button
                        onClick={() => {
                            if (isExpanded) {
                                setIsFullResizablePanel && (setIsFullResizablePanel(true))
                                onToggleExpand(false)
                            } else {
                                setIsFullResizablePanel && (setIsFullResizablePanel(false))
                                onToggleExpand(true)
                            }
                        }}
                        className="-translate-x-1/2 z-1"
                    >
                        <Icon
                            name={"expand"}
                            className={isExpanded && '-rotate-180' || ""}
                        // size={64}
                        />
                    </button>
                </div>
            </div>
        </Panel>

    );
}, (prevProps: any, nextProps: any) => {
    // 自定义比较函数，只有当数据真正变化时才重渲染
    return (
        JSON.stringify(prevProps.data) ===
        JSON.stringify(nextProps.data)
    );
});

export default SAA;
