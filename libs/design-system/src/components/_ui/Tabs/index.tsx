import { ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { cn } from '../../../lib/utils';
import { useTabsContext } from '../../../context/TabsContext';
import { log } from 'console';

type Trigger = {
  value: string;
  label: string;
};

type TabsProps = {
  children?: ReactNode;
  customItems?: ReactNode;
  className?: string;
  triggers: Trigger[];
  rightPanel?: ReactNode;
  defaultTab?: string;
  triggerClassName?: string;
  tabsClassName?: string;
  defaultMiniWallboardView?: string | null | undefined;
  defaultNode?: (v: string) => ReactNode;
  headerClass?: string;
  onChangeTabFunc?: (tab: string) => void;
};

type TabsContentProps = {
  value: string;
  className?: string;
  children?: ReactNode;
  selected?: string;
};

const Tabs = ({
  children,
  className,
  triggers,
  rightPanel,
  customItems,
  defaultTab = '',
  triggerClassName,
  tabsClassName,
  defaultNode,
  defaultMiniWallboardView,
  headerClass,
  onChangeTabFunc,
}: TabsProps) => {
  const {
    selected,
    onChangeTab,
    onChangeDefaultMiniWallboardView,
    defaultMiniWallboardView: defaultView,
  } = useTabsContext();
  let newSeleted: any[] = selected || [];
  if (!Array.isArray(newSeleted)) {
    newSeleted = [];
  }
  useEffect(() => {
    triggers.forEach((item, index) => {
      newSeleted = newSeleted?.filter((ite) => {
        return item?.value !== ite;
      });
    });
    newSeleted?.push(defaultTab);
    onChangeTab(newSeleted);
    // if (selected.includes(defaultTab)) {
    //     onChangeTab(selected)
    // } else {
    //     onChangeTab([...selected, defaultTab]);
    // }
    onChangeDefaultMiniWallboardView &&
      onChangeDefaultMiniWallboardView(
        !defaultView ? defaultMiniWallboardView : defaultView
      );
  }, []);
  const onChange = useCallback(
    (tabs: any[], value: string) => {
      newSeleted = selected;
      if (selected.includes(value)) {
        onChangeTab(selected);
      } else {
        tabs.forEach((item, index) => {
          newSeleted = newSeleted?.filter((ite) => {
            return item.value !== ite;
          });
        });
        newSeleted?.push(value);
        onChangeTab(newSeleted);
      }
    },
    [selected, newSeleted]
  );

  return (
    <div
      className={cn(
        'flex flex-col w-full h-full rounded-2xl bg-white',
        className
      )}
    >
      <div
        className={cn(
          'flex w-full justify-between items-center border-b border-grey-200',
          tabsClassName
        )}
      >
        <div
          className={cn(
            'w-full flex items-center justify-between',
            headerClass
          )}
        >
          <div className="flex gap-6 shrink-0 px-4">
            {triggers?.map((trigger) => (
              <div
                key={trigger?.value}
                className="flex gap-2"
              >
                <button
                  key={`trigger-${trigger?.value}`}
                  onClick={() => {
                    onChange(triggers, trigger?.value);
                    onChangeTabFunc && onChangeTabFunc(trigger?.value);
                  }}
                  className={cn(
                    'p-2 text-remark font-bold',
                    selected.includes(trigger?.value)
                      ? 'text-black shadow-b shadow-tab-selected'
                      : 'text-grey-500',
                    triggerClassName
                  )}
                >
                  {trigger?.label}
                </button>
                {defaultNode && defaultNode(trigger?.value)}
              </div>
            ))}
          </div>
          {rightPanel && <div className="inline-flex mr-4">{rightPanel}</div>}
        </div>
        {customItems}
      </div>
      {children}
    </div>
  );
};

const TabsContent = ({
  value,
  className,

  children,
}: TabsContentProps) => {
  const { selected } = useTabsContext();
  if (!selected.includes(value)) return null;

  return <div className={cn('p-2', className)}>{children}</div>;
};

export { Tabs, TabsContent };
