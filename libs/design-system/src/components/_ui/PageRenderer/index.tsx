import { use<PERSON>oute<PERSON><PERSON><PERSON> } from '../../../context/RouteContext';
import { IRoute } from '../../../context/RouteContext';
import React, { ReactNode } from 'react';
import dynamic from 'next/dynamic';
import { LoadingBlock } from '../Loader';
import { cn, removeTrailingSlash } from '../../../lib/utils';
import { matchPath } from 'react-router-dom';

const PageContext = dynamic(
  () => {
    return import('./PageContext');
  },
  {
    ssr: false,
    loading: () => <LoadingBlock fullScreen />,
  }
);

export const PageBody = ({ basePath = '' }: { basePath?: string }) => {
  const { activePath, allRoutes, browsingHisotry } = useRouteHandler();
  return (
    <>
      <div className="h-full overflow-auto relative">
        {allRoutes
          .filter((route) => {
            const targetPath = removeTrailingSlash(basePath + route?.path);
            return browsingHisotry?.some((historyPath) =>
              matchPath({ path: targetPath, end: true }, historyPath)
            );
          })
          .map((route: IRoute) => {
            const group = route?.group;
            const targetPath = removeTrailingSlash(basePath + route?.path);
            const isActive = matchPath(
              { path: targetPath, end: true },
              removeTrailingSlash(activePath)
            );
            return (
              <React.Fragment key={`${group}-${targetPath}`}>
                <section
                  data-group={`group-${group}`}
                  data-path={targetPath}
                  key={targetPath}
                  className={cn('h-full', !isActive && 'hidden')}
                >
                  {route.component}
                </section>
              </React.Fragment>
            );
          })}
      </div>
    </>
  );
};

export const PageRenderer = ({
  children,
  routes,
  basePath = '',
}: {
  children?: ReactNode;
  routes: IRoute[];
  basePath?: string;
}) => {
  return (
    <PageContext
      routes={routes}
      basePath={basePath}
    >
      {children ?? <PageBody basePath={basePath} />}
    </PageContext>
  );
};

export default PageRenderer;
