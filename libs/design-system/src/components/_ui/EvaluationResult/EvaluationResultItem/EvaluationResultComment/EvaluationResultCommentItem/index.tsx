import dayjs from 'dayjs';
import {
  EvaluationResultSubStep,
  ManualDetailList,
} from '../../../EvaluationFinalResult/types/evaluation';
import IconArrowFullDown from '../../../../Icon/IconArrowFullDown';
import { useState } from 'react';
import { ManualDetailItem } from '../../types/evaluation';
import { useTranslation } from 'react-i18next';
import { getTranslatedResult } from '../../../../../../lib/utils';

interface EvaluationResultCommentItemProps {
  data: ManualDetailItem | ManualDetailList;
  showMore?: boolean;
  opened?: boolean;
  setOpened?: (opened: boolean) => void;
}

const EvaluationResultCommentItem: React.FC<
  EvaluationResultCommentItemProps
> = ({ data, showMore = false, opened, setOpened }) => {
  const { t } = useTranslation();
  return (
    <>
      <section className="my-2 p-2 border border-[#F9D17B] bg-[#FFF5DA] gap-1 rounded-md">
        <div className="relative flex flex-col gap-1">
          {/* comment subStep.manualComment*/}
          <div className="flex-none shrink-0 font-light">
            {data.manualResult ? (
              <span className="font-bold">
                {t('evaluation.overrideTo')}{' '}
                <label
                  className={`${data.manualResult === 'Passed' ? 'text-[#57B62D]' : 'text-[#FF271C]'}`}
                >
                  {getTranslatedResult(
                    data.manualResult.toLowerCase(),
                    'evaluation'
                  )}
                </label>
              </span>
            ) : (
              <span className="font-bold">
                {t('evaluation.leaveComment')}:{' '}
              </span>
            )}
            <span> : </span>
            <span className="text-balance">
              {data.comment && data.comment.length > 0
                ? data.comment
                : t('evaluation.noComment')}
            </span>
            {/* updator & datetime */}
            <span className="ml-1 shrink-0 text-[#949494] font-light">
              {' - ' +
                data.createBy +
                ' ' +
                dayjs(data.createTime).format('YYYY/MM/DD')}
            </span>
          </div>

          {showMore && (
            <span
              className="flex items-center shrink-0 gap-1 cursor-pointer absolute right-0 bottom-0"
              onClick={() => setOpened && setOpened(!opened)}
            >
              <span>{t('evaluation.more')}</span>
              <span>
                <IconArrowFullDown
                  className={`transition-transform duration-300 ease-in-out ${
                    opened ? 'rotate-180' : 'rotate-0'
                  }`}
                  color={'#000000'}
                  size={'10px'}
                />
              </span>
            </span>
          )}
        </div>
      </section>
    </>
  );
};

export default EvaluationResultCommentItem;
