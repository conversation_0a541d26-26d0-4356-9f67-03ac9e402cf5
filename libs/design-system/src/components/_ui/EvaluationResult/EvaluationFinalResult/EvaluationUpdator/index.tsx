import { useState } from 'react';
import * as ToggleGroup from '@radix-ui/react-toggle-group';
import { useTranslation } from 'react-i18next';
import { getTranslatedResult } from '../../../../../lib/utils';

interface EvaluationUpdatorProps {
  evaluationId: string;
  result?: string;
  resultComment?: string;
  onSubmit: (comment: string, manualResult: string) => void;
}

const EvaluationUpdator: React.FC<EvaluationUpdatorProps> = ({
  evaluationId,
  result,
  resultComment = '',
  onSubmit,
}) => {
  const { t } = useTranslation();
  const [comment, setComment] = useState<string>('');
  const [manualResult, setManualResult] = useState<string | undefined>();

  return (
    <section className="w-full overflow-x-auto">
      <div className="w-full flex flex-col px-6 py-4 border border-[#EEEFF1] bg-[#FCFCFD] rounded-md gap-2">
        {/* Override to */}
        <div className="flex items-center">
          <span className="flex-wrap text-[#949494] mr-2">
            {t('evaluation.overrideTo')}:{' '}
          </span>
          <span className="flex flex-1 gap-2 items-center">
            {/* manual result */}
            <div>
              <ToggleGroup.Root
                type="single"
                onValueChange={(value) => {
                  console.log('value', value);

                  if (value && value?.length > 0) setManualResult(value);
                  else setManualResult(undefined);
                }}
                className="flex-wrap"
              >
                <ToggleGroup.Item
                  value={result === 'Passed' ? 'Failed' : 'Passed'}
                  className="data-[state=on]:bg-black data-[state=on]:text-white font-light px-2 py-[2px] rounded-[0.25rem] border-[1px] border-[#E0E0E0]"
                >
                  {t('evaluation.overrideTo')}:{' '}
                  {result === 'Passed'
                    ? t('evaluation.failed')
                    : t('evaluation.passed')}
                </ToggleGroup.Item>
              </ToggleGroup.Root>
            </div>
            {/* manual comment and submit button */}
            <div className="flex flex-1 border-[1px] border-[#E0E0E0] roundesd-[0.25rem]">
              <input
                type="text"
                alt="comment"
                placeholder={
                  manualResult
                    ? `${t('evaluation.overrideTo')} ${t(getTranslatedResult(manualResult, 'evaluation'))}, ${t('evaluation.because')}`
                    : t('evaluation.leaveComment')
                }
                value={comment}
                className="flex-1 px-2 py-[2px] rounded-l-[0.25rem] focus:outline-none font-light"
                onChange={(v) => setComment(v.target.value)}
              />
              <button
                className="px-2 py-[2px] bg-black text-white rounded-r-[0.25rem] font-light"
                onClick={() => onSubmit(comment, manualResult || '')}
              >
                {t('evaluation.submit')}
              </button>
            </div>
          </span>
        </div>
      </div>
    </section>
  );
};

export default EvaluationUpdator;
