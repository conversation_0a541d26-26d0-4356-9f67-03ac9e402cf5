import { useEffect } from 'react';
import { ConversationItem } from '../../../@types/Conversation';
// import { useCDSSStore } from 'cdss/store/conversation';
import IconBadge from './IconBadge';
import IconSVG from '../Icon/IconSVG';
//差咗props
export const ManualItemComponent: React.FC<{
  queueName: string;
  isExpanded: boolean;
  type: string;
  onClick?: () => void;
}> = ({ queueName, isExpanded, onClick, type }) => {
  useEffect(() => {
    // console.log(queueId);
  }, []);

  return (
    <div
      className="flex m-0 mb-3 p-0 w-full cursor-pointer"
      onClick={onClick}
    >
      <div
        className={`p-1 aspect-square rounded-md flex items-center justify-center border-2  ${
          isExpanded ? 'w-1/4' : 'w-full'
        }`}
      >
        <IconSVG
          className="object-contain w-8 h-8"
          iconType={type}
        />
      </div>
      {isExpanded && (
        <div className="items-center justify-center text-center flex flex-1 ml-5">
          {queueName}
        </div>
      )}
    </div>
  );
};
