import { useEffect } from 'react';
import IconWhatsapp from '../Icon/IconSVG';
import IconSVG from '../Icon/IconSVG';

/* eslint-disable @next/next/no-img-element */
interface IconBadgeProps {
  type: string;
  hasUnread?: boolean;
}

const IconBadge = ({ type, hasUnread = false }: IconBadgeProps) => {
  useEffect(() => {
    console.log(type);
  }, []);
  return (
    <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full shadow-md z-30 flex items-center justify-center">
      <IconSVG
        className="object-contain w-6 h-6"
        iconType={type}
      />
      {hasUnread && (
        <span
          className="absolute w-1.5 h-1.5 bg-red-500 rounded-full z-40"
          style={{ top: '-1px', right: '-1px' }}
        />
      )}
    </div>
  );
};

export default IconBadge;
