import React, { useState, useCallback, useEffect, useRef } from 'react';

interface FixedRightMenuProps {
  children: React.ReactNode;
  panelContent?: React.ReactNode;
}

/**
 * A floating button component fixed to the right center of the screen
 * Supports click to show/hide panel with circular animation
 */
const FixedRightMenu: React.FC<FixedRightMenuProps> = ({
  children,
  panelContent,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  // 初始化时设置为 null，等容器挂载后再设置实际宽度
  const [width, setWidth] = useState<number | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);
  const lastWidthRef = useRef(0);

  // 在组件挂载时设置初始宽度为容器宽度的 30%
  useEffect(() => {
    if (containerRef.current && width === null) {
      const initialWidth =
        containerRef.current.getBoundingClientRect().width * 0.3;
      setWidth(initialWidth);
      lastWidthRef.current = initialWidth;
    }
  }, [width]);

  // 处理拖拽开始
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsDragging(true);
      setStartX(e.clientX);
      setStartWidth(width || 0);
      lastWidthRef.current = width || 0;
      document.body.style.userSelect = 'none';
    },
    [width]
  );

  // 处理拖拽过程
  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (isDragging && containerRef.current) {
        const containerRect = containerRef.current.getBoundingClientRect();
        const deltaX = startX - e.clientX;
        const newWidth = startWidth + deltaX;

        // 计算当前宽度占容器的百分比
        const currentWidthPercentage = (newWidth / containerRect.width) * 100;

        let finalWidth;

        // 应用限制：最小30%，最大为屏幕宽度
        if (currentWidthPercentage < 30) {
          finalWidth = containerRect.width * 0.3;
        } else if (newWidth > window.innerWidth) {
          finalWidth = window.innerWidth;
        } else {
          finalWidth = newWidth;
        }

        // 只有当宽度变化超过1px时才更新
        if (Math.abs(finalWidth - lastWidthRef.current) >= 1) {
          requestAnimationFrame(() => {
            setWidth(Math.round(finalWidth));
            lastWidthRef.current = finalWidth;
          });
        }
      }
    },
    [isDragging, startX, startWidth]
  );

  // 处理拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    document.body.style.userSelect = '';
  }, []);

  // 添加和移除事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove, {
        passive: true,
      });
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging, handleMouseMove, handleMouseUp]);

  return (
    <div
      ref={containerRef}
      className="h-full w-full"
    >
      {/* Fixed button */}
      <div
        style={{
          position: 'fixed',
          right: '0',
          top: '50%',
          transform: 'translate3d(0, -50%, 0)',
          zIndex: 1001,
          cursor: 'pointer',
          willChange: 'transform, opacity',
          backgroundColor: 'white',
          // boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          borderRadius: '10px 0 0 10px',
          opacity: isPanelOpen ? 0 : 1,
          transition: 'opacity 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
          visibility: isPanelOpen ? 'hidden' : 'visible',
          pointerEvents: isPanelOpen ? 'none' : 'auto',
        }}
        onClick={() => setIsPanelOpen(true)}
      >
        {children}
      </div>

      {/* Panel with circular animation */}
      <div
        className="fixed bg-white z-[999] overflow-hidden"
        style={{
          top: 0,
          bottom: 0,
          right: 0,
          width: width ? `${width}px` : '30%',
          clipPath: `circle(${isPanelOpen ? '150%' : '0%'} at calc(100% - 20px) 50%)`,
          transition: isDragging
            ? 'none'
            : 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
          visibility: isPanelOpen ? 'visible' : 'visible',
          opacity: 1,
          transform: 'translate3d(0,0,0)',
          willChange: isDragging ? 'width' : 'clip-path, width',
          backfaceVisibility: 'hidden',
          WebkitBackfaceVisibility: 'hidden',
          perspective: 1000,
          WebkitPerspective: 1000,
        }}
      >
        {/* Close button */}
        <div
          className="absolute top-4 right-4 cursor-pointer z-[1002] flex items-center justify-center"
          onClick={() => setIsPanelOpen(false)}
          style={{
            width: '24px',
            height: '24px',
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            borderRadius: '50%',
            transition: 'background-color 0.2s',
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'rgba(0, 0, 0, 0.1)';
          }}
        >
          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1 1L13 13M1 13L13 1"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
            />
          </svg>
        </div>

        {/* Resize handle */}
        <div
          className="absolute left-0 top-0 bottom-0 w-1 cursor-col-resize hover:bg-gray-300 z-[1001]"
          onMouseDown={handleMouseDown}
          style={{
            touchAction: 'none',
            backgroundColor: isDragging ? '#666' : 'transparent',
            transform: 'translate3d(0,0,0)',
          }}
        />

        {panelContent || (
          <div className="w-full h-full flex items-center justify-center">
            默认面板内容
          </div>
        )}
      </div>
    </div>
  );
};

export default FixedRightMenu;
