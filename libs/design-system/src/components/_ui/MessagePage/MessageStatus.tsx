/* eslint-disable @next/next/no-img-element */
import React from 'react';
import IconStatusQueued from '../Icon/IconStatusQueued';
import IconStatusSent from '../Icon/IconStatusSent';
import IconStatusDelivered from '../Icon/IconStatusDelivered';
import IconStatusRead from '../Icon/IconStatusRead';
import IconStatusFailed from '../Icon/IconStatusFailed';
interface MessageStatusProps {
  status: string;
}
const iconMap: { [key: string]: JSX.Element | null } = {
  sending: (
    <IconStatusQueued
      size={'24px'}
      className={'animate-spin '}
    />
  ),
  queued: <IconStatusSent size={'24px'} />,
  sent: <IconStatusSent size={'24px'} />,
  delivered: <IconStatusDelivered size={'24px'} />,
  read: <IconStatusRead size={'24px'} />,
  failed: <IconStatusFailed size={'24px'} />,
};

const MessageStatus: React.FC<MessageStatusProps> = ({ status }) => {
  const icon = iconMap[status];

  if (icon === undefined) {
    console.warn(`Unknown message status: ${status}`);
    return null;
  }

  return icon;
};

export default MessageStatus;
