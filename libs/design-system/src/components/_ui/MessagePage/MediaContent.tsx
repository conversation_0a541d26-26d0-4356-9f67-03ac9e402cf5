/* eslint-disable @next/next/no-img-element */
import { CheckCircle, Download, FileIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { CDSSMessage, MediaItem } from '../../../@types/Message';
import FileTool from './FileTool';
import MessageStatus from './MessageStatus';
import ChatTimer from '../chatTimer/ChatTimer';
import MessageContextMenu from '@cdss-modules/design-system/context/MessageContextMenu';

interface MediaContentProps {
  message: CDSSMessage;
  isCustomer: boolean;
  setReferenceMessage?: (referenceMessage: CDSSMessage) => void;
  showContextMenu?: boolean;
  isReplyContent?: boolean;
}

const MediaContent: React.FC<MediaContentProps> = ({
  message,
  isCustomer,
  setReferenceMessage,
  showContextMenu = true,
  isReplyContent = false,
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const media = message.medias?.[0];

  useEffect(() => {
    console.log('MediaContent message', message);
  }, [message]);

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const ImagePreviewModal = () => {
    if (!showPreview) return null;

    return (
      <div
        className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
        onClick={() => setShowPreview(false)}
      >
        <div className="relative max-w-4xl max-h-screen bg-white rounded-lg p-2">
          <img
            src={media?.url}
            alt="Preview"
            className="max-w-full max-h-[90vh] object-contain"
          />
          <button
            className="absolute top-2 right-2 p-2 rounded-full bg-gray-800 text-white hover:bg-gray-700"
            onClick={() => setShowPreview(false)}
          >
            ✕
          </button>
        </div>
      </div>
    );
  };

  return (
    <>
      <MessageContextMenu
        message={message}
        setReferenceMessage={setReferenceMessage}
        disabled={!showContextMenu}
      >
        <div>
          <FileTool
            message={message}
            isCustomer={isCustomer}
            isShowStt={true}
            isReplyContent={isReplyContent}
          />
        </div>
      </MessageContextMenu>
      {!isReplyContent && (
        <div
          className={`flex items-center space-x-1 px-1 ${
            isCustomer ? 'self-start' : 'self-end'
          }`}
        >
          <ChatTimer
            timestamp={message.timestamp}
            className="text-xs text-gray-500"
          />
          {!isCustomer && message.status && (
            <MessageStatus status={message.status} />
          )}
          {/* {message.status && <MessageStatus status={message.status} />} */}
        </div>
      )}

      <ImagePreviewModal />
    </>
  );
};

export default MediaContent;
