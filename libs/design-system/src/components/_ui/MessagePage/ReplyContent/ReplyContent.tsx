import React from 'react';
import MessageContextMenu from '@cdss-modules/design-system/context/MessageContextMenu';
import { CDSSMessage } from '../../../../@types/Message';
import ChatTimer from '@cdss-modules/design-system/components/_ui/chatTimer/ChatTimer';
import MessageStatus from '@cdss-modules/design-system/components/_ui/MessagePage/MessageStatus';
import MediaContent from '@cdss-modules/design-system/components/_ui/MessagePage/MediaContent';
import MessageReply from '@cdss-modules/design-system/components/_ui/MessagePage/MessageReply';

interface ReplyContentProps {
  currentMessages?: CDSSMessage[];
  message: CDSSMessage;
  isCustomer: boolean;
  setReferenceMessage?: (referenceMessage: CDSSMessage) => void;
  showContextMenu?: boolean;
  setHighlightedMessageId?: (messageId: string) => void;
}

const ReplyContent: React.FC<ReplyContentProps> = ({
  currentMessages,
  message,
  isCustomer,
  setReferenceMessage,
  showContextMenu = true,
  setHighlightedMessageId,
}) => {
  // 查找引用的消息 先从store中查找保证最新，如果无就直接用message的reference
  const quoteMessage =
    currentMessages?.find((m) => m.id === message.referenceId) ||
    message.reference;

  const isPureImageMessage = (message: CDSSMessage) => {
    // 检查是否有媒体文件
    if (!message.medias || message.medias.length === 0) return false;

    // 检查文本内容是否为空
    const hasTextContent = message.textBody && message.textBody.trim() !== '';
    if (hasTextContent) return false;

    // 检查所有媒体文件是否都是图片
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    const allAreImages = message.medias.every((media) => {
      if (!media.filename) return false;
      const extension = media.filename.split('.').pop()?.toLowerCase();
      return extension && imageExtensions.includes(extension);
    });

    return allAreImages;
  };

  const isQuotePureImage = quoteMessage
    ? isPureImageMessage(quoteMessage)
    : false;
  // 处理文本内容中的换行符
  const re_textBody = message?.textBody?.replace(/\\n/g, '\n') || '';

  // 渲染当前消息的文本内容
  const renderTextContent = () => {
    if (!re_textBody) return null;

    return (
      <div className="w-full">
        <p className="text-gray-800 whitespace-pre-wrap break-words">
          {re_textBody}
        </p>
      </div>
    );
  };

  // 渲染当前消息的媒体内容
  const renderMediaContent = () => {
    if (!message.medias || message.medias.length === 0) return null;

    return (
      <div className="w-full w-[26rem]">
        <MediaContent
          message={message}
          isCustomer={isCustomer}
          setReferenceMessage={setReferenceMessage}
          showContextMenu={false} // 禁用 MediaContent 的菜单
          isReplyContent={true}
        />
      </div>
    );
  };

  return (
    <div
      className={`flex flex-col ${isCustomer ? 'items-start' : 'items-end'}`}
    >
      {/* 主要内容容器 - 整个区域都包装在 MessageContextMenu 中 */}
      <MessageContextMenu
        message={message}
        setReferenceMessage={setReferenceMessage}
        disabled={!showContextMenu}
      >
        <div
          className={`rounded-lg p-3 w-[32rem] min-h-0 ${
            isCustomer ? 'bg-gray-100' : 'bg-orange-100'
          } group`}
        >
          {/* 被引用的消息 */}
          {quoteMessage && (
            <div
              className={`mb-2 ${isQuotePureImage ? 'w-[16rem]' : 'w-[26rem]'}`}
            >
              <MessageReply
                quotedMessage={quoteMessage}
                hasCloseButton={false}
                //不展示关闭按钮
                onClose={() => {}}
                setHighlightedMessageId={setHighlightedMessageId}
              />
            </div>
          )}

          {/* 当前消息内容 */}
          <div className="w-[26rem]">
            {/* 渲染媒体内容（如果存在） */}
            {message.medias &&
              message.medias.length > 0 &&
              renderMediaContent()}

            {/* 渲染文本内容（如果存在） */}
            {re_textBody && renderTextContent()}
          </div>
        </div>
      </MessageContextMenu>

      {/* 消息状态和时间 */}
      <div
        className={`flex items-center space-x-1 px-1 mt-1 ${
          isCustomer ? 'self-start' : 'self-end'
        }`}
      >
        <ChatTimer
          timestamp={message.timestamp}
          className="text-xs text-gray-500"
        />
        {!isCustomer && message.status && (
          <MessageStatus status={message.status} />
        )}
      </div>
    </div>
  );
};

export default ReplyContent;
