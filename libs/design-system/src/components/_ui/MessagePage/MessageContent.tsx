/* eslint-disable jsx-a11y/anchor-is-valid */
import React, { useEffect, useMemo } from 'react';
import MediaContent from './MediaContent';
import MessageStatus from './MessageStatus';
import { CDSSMessage } from '../../../@types/Message';
import ChatTimer from '../chatTimer/ChatTimer';
import TemplateContent from './TemplateContent';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { SAASearchTerm } from '../../../@types/SAA';
import MessageContextMenu from '@cdss-modules/design-system/context/MessageContextMenu';
import ReplyContent from '@cdss-modules/design-system/components/_ui/MessagePage/ReplyContent/ReplyContent';

interface MessageContentProps {
  currentMessages?: CDSSMessage[];
  message: CDSSMessage;
  isCustomer: boolean;
  setSAAManualSearchTerm?: (manualSearchTerm: SAASearchTerm) => void;
  setReferenceMessage?: (referenceMessage: CDSSMessage) => void;
  showContextMenu?: boolean;
  setHighlightedMessageId?: (messageId: string) => void;
}

const MessageContent: React.FC<MessageContentProps> = ({
  currentMessages,
  message,
  isCustomer,
  setSAAManualSearchTerm,
  setReferenceMessage,
  showContextMenu = true,
  setHighlightedMessageId,
}) => {
  const re_textBody = message?.textBody?.replace(/\\n/g, '\n') || '';

  if (message.reference && message.reference.id) {
    return (
      <ReplyContent
        currentMessages={currentMessages}
        message={message}
        isCustomer={isCustomer}
        setReferenceMessage={setReferenceMessage}
        showContextMenu={showContextMenu}
        setHighlightedMessageId={setHighlightedMessageId}
      />
    );
  }

  // 表示用户在wa quote了一个条message 但是在cdss里面不存在 需要call api获取
  if (message.direction === 'inbound' && message.externalReferenceId) {
    //todo 先按照普通message来展示 背地里call api 获取quote message的详情 如果找到就更新store
  }

  if (message.type === 'whatsappTemplate') {
    // Handle message type templates
    return (
      <TemplateContent
        message={message}
        isCustomer={isCustomer}
        setReferenceMessage={setReferenceMessage}
        showContextMenu={showContextMenu}
      />
    );
  }

  // Handle media messages
  if (message.medias && message.medias.length > 0) {
    return (
      <MediaContent
        message={message}
        isCustomer={isCustomer}
        setReferenceMessage={setReferenceMessage}
        showContextMenu={showContextMenu}
      />
    );
  }

  // Check if the message has SAA results
  const showSaaIcon =
    message.direction === 'inbound' && message.saaResult !== undefined;

  // 解析 matchedTags 字符串为数组
  const parseMatchedTags = (matchedTagsStr: string): string[] => {
    try {
      // 尝试解析为 JSON
      return JSON.parse(matchedTagsStr);
    } catch (e) {
      // 不是有效的 JSON，尝试作为逗号分隔字符串处理
      return matchedTagsStr
        .split(',')
        .map((tag) => tag.trim())
        .filter(Boolean);
    }
  };

  const handleSaaIconClick = () => {
    console.log('manual search onclick');
    if (message.saaResult) {
      // 解析 matchedTags
      let keywords: string[] = [];
      if (message.saaResult.matchedTags) {
        keywords = parseMatchedTags(message.saaResult.matchedTags);
      }

      const searchTerm: SAASearchTerm = {
        searchTerm: message.textBody,
        triggerSearch: true,
        relatedMessageContent: {
          timestamp: message.timestamp,
          rawTextBody: message.textBody,
          keywords: keywords, // 使用解析出的 keywords
          messageId: message.platformMessageId,
        },
      };

      // 放入 store
      setSAAManualSearchTerm && setSAAManualSearchTerm(searchTerm);

      console.log('已保存SAA搜索词到store:', searchTerm);
    }
  };

  // Process and highlight text if matchedTags exist
  const highlightedContent = useMemo(() => {
    if (!message.saaResult?.matchedTags) {
      return <span>{re_textBody}</span>;
    }

    try {
      // 使用同一个函数解析 matchedTags
      const tags = parseMatchedTags(message.saaResult.matchedTags);

      if (tags.length === 0) {
        return <span>{re_textBody}</span>;
      }

      // Create regex pattern to match all tags (case insensitive)
      const pattern = new RegExp(
        `(${tags.map((tag) => escapeRegExp(tag)).join('|')})`,
        'gi'
      );

      // Split text by matched patterns
      const parts = re_textBody.split(pattern);

      // Map parts to either regular text or highlighted spans
      return parts.map((part, index) => {
        const matchesAnyTag = tags.some((tag) =>
          new RegExp(`^${escapeRegExp(tag)}$`, 'i').test(part)
        );

        return matchesAnyTag ? (
          <span
            key={index}
            className="text-orange-500 font-medium underline"
          >
            {part}
          </span>
        ) : (
          <span key={index}>{part}</span>
        );
      });
    } catch (error) {
      console.error('Error processing highlights:', error);
      return <span>{re_textBody}</span>;
    }
  }, [message.saaResult, re_textBody]);

  return (
    <div
      className={`flex flex-col ${isCustomer ? 'items-start' : 'items-end'}`}
    >
      <div className="flex items-center">
        <MessageContextMenu
          message={message}
          setReferenceMessage={setReferenceMessage}
          disabled={!showContextMenu}
        >
          <div
            className={`relative rounded-lg p-3 mb-1 max-w-3xl ${
              isCustomer ? 'bg-gray-100' : 'bg-orange-100'
            } group`}
          >
            <p className="text-gray-800 whitespace-pre-wrap break-words">
              {highlightedContent}
            </p>
          </div>
        </MessageContextMenu>

        {/* 添加SAA图标，仅在入站消息且有saaResult时显示 */}
        {showSaaIcon && (
          <div
            className="ml-8 flex-shrink-0 cursor-pointer"
            onClick={handleSaaIconClick}
          >
            <Icon
              name="saaInColour"
              size={32}
            />
          </div>
        )}
      </div>

      <div
        className={`flex items-center space-x-1 px-1 ${
          isCustomer ? 'self-start' : 'self-end'
        }`}
      >
        <ChatTimer
          timestamp={message.timestamp}
          className="text-xs text-gray-500"
        />
        {!isCustomer && message.status && (
          <MessageStatus status={message.status} />
        )}
      </div>
    </div>
  );
};

// Helper function to escape special regex characters
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export default MessageContent;
