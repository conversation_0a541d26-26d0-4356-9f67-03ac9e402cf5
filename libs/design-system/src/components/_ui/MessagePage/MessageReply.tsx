import React, { useCallback, useMemo } from 'react';
import { CDSSMessage } from '../../../@types/Message';
import FileIcon from '@cdss-modules/design-system/components/_ui/FileIcon';
import { X } from 'lucide-react';

interface MessageReplyProps {
  quotedMessage?: CDSSMessage;
  hasCloseButton?: boolean;
  onClose?: (messageId: string) => void;
  setHighlightedMessageId?: (messageId: string) => void;
}

const MessageReply: React.FC<MessageReplyProps> = ({
  quotedMessage,
  hasCloseButton = true,
  onClose,
  setHighlightedMessageId,
}) => {
  const handleClose = (e: React.MouseEvent) => {
    // 阻止事件冒泡，防止触发外层的点击事件
    e.stopPropagation();
    if (onClose && quotedMessage?.id) {
      onClose(quotedMessage.id);
    }
    console.log(`Closing message ${quotedMessage?.id}`);
  };

  const handleLocateToChat = useCallback(
    (messageId: string) => {
      if (!setHighlightedMessageId) return;

      setHighlightedMessageId(messageId);
      // scroll到message的位置
      const messageElement = document.getElementById(messageId);
      if (messageElement) {
        messageElement.scrollIntoView({ behavior: 'smooth' });
      }
      // Set a timeout to remove highlight after 5 seconds
      const timeout = setTimeout(() => {
        setHighlightedMessageId('');
      }, 5000);

      // Clear timeout on component unmount
      return () => clearTimeout(timeout);
    },
    [setHighlightedMessageId]
  );

  const handleComponentClick = useCallback(
    (e: React.MouseEvent) => {
      // 只有当 setHighlightedMessageId 存在且 quotedMessage.id 存在时才处理点击
      if (setHighlightedMessageId && quotedMessage?.id) {
        handleLocateToChat(quotedMessage.id);
      }
    },
    [setHighlightedMessageId, handleLocateToChat, quotedMessage?.id]
  );

  // 格式化文件大小
  const formatFileSize = useCallback((bytes: number): string => {
    if (!bytes) return 'file is not ready';
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // 解析模板数据 - 使用 useMemo 缓存结果
  const templateData = useMemo(() => {
    if (!quotedMessage || quotedMessage.type !== 'whatsappTemplate') {
      return null;
    }

    if (!quotedMessage.textBody) {
      return null;
    }

    try {
      const parsed = JSON.parse(quotedMessage.textBody);
      // 只在开发环境下打印，并且添加节流
      return parsed;
    } catch (error) {
      console.error('Failed to parse template data:', error);
      return null;
    }
  }, [quotedMessage?.textBody, quotedMessage?.type]);

  // 渲染文本消息 - 限制两行
  const renderTextMessage = useCallback(
    () => (
      <div className="px-4 py-3">
        <div
          className="text-black text-base line-clamp-2 min-w-0 break-words"
          style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            wordWrap: 'break-word',
            wordBreak: 'break-word',
          }}
        >
          {quotedMessage?.textBody || ''}
        </div>
      </div>
    ),
    [quotedMessage?.textBody]
  );

  // 渲染模板消息
  const renderTemplateMessage = useCallback(() => {
    if (!templateData || !templateData.components) {
      return null;
    }

    // 查找header和body组件
    const headerComponent = templateData.components.find(
      (comp: any) => comp.type === 'HEADER'
    );
    const bodyComponent = templateData.components.find(
      (comp: any) => comp.type === 'BODY'
    );

    return (
      <div className="px-4 py-3">
        {/* Template Header */}
        {headerComponent && (
          <div className="mb-3">
            {headerComponent.format === 'TEXT' ? (
              <div
                className="text-black text-base font-medium line-clamp-1 min-w-0 break-words"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 1,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                }}
              >
                {headerComponent.text || ''}
              </div>
            ) : headerComponent.format === 'IMAGE' &&
              headerComponent.image?.link ? (
              // 图片header - 左右布局
              <div className="flex items-start space-x-3">
                <div className="flex-1 min-w-0">
                  {bodyComponent && (
                    <div
                      className="text-black text-base line-clamp-2 min-w-0 break-words"
                      title={bodyComponent.text || ''}
                      style={{
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        wordWrap: 'break-word',
                        wordBreak: 'break-word',
                      }}
                    >
                      {bodyComponent.text || ''}
                    </div>
                  )}
                </div>
                <div className="flex-shrink-0">
                  <img
                    src={headerComponent.image.link}
                    alt="image"
                    className="w-16 h-16 object-cover rounded-lg"
                  />
                </div>
              </div>
            ) : null}
          </div>
        )}

        {/* Template Body - 限制两行 */}
        {bodyComponent && headerComponent?.format !== 'IMAGE' && (
          <div
            className="text-black text-base line-clamp-2 min-w-0 break-words"
            title={bodyComponent.text || ''}
            style={{
              display: '-webkit-box',
              WebkitLineClamp: 2,
              WebkitBoxOrient: 'vertical',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              wordWrap: 'break-word',
              wordBreak: 'break-word',
            }}
          >
            {bodyComponent.text || ''}
          </div>
        )}
      </div>
    );
  }, [templateData]);

  // 渲染文件消息
  const renderFileMessage = useCallback(() => {
    const media = quotedMessage?.medias?.[0];
    if (!media) return null;

    const isImage =
      media.mediaType === 'image' || media.mime?.startsWith('image/');

    return (
      <div className="px-4 py-3">
        {/* 图片消息带caption - 左右布局 */}
        {isImage && media.url && quotedMessage?.textBody ? (
          <div className="flex items-start space-x-3">
            <div className="flex-1 min-w-0">
              <div
                className="text-black text-base line-clamp-2 break-words"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                }}
              >
                {quotedMessage.textBody}
              </div>
            </div>
            <div className="flex-shrink-0">
              <img
                src={media.url}
                alt="image"
                className="w-16 h-16 object-cover rounded-lg"
              />
            </div>
          </div>
        ) : isImage && media.url ? (
          // 图片消息无caption - 正方形显示，宽度为组件的30%
          <div className="flex justify-center">
            <div className="aspect-square w-[16rem]">
              <img
                src={media.url}
                alt="image"
                className="w-full h-full object-cover rounded-lg"
              />
            </div>
          </div>
        ) : (
          // 非图片文件
          <>
            {/* Caption - 限制两行 */}
            {quotedMessage?.textBody && (
              <div
                className="text-black text-base mb-3 line-clamp-2 min-w-0 break-words"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  wordWrap: 'break-word',
                  wordBreak: 'break-word',
                }}
              >
                {quotedMessage.textBody}
              </div>
            )}

            {/* 文件信息卡片 */}
            <div className="flex items-center space-x-3 bg-gray-50 rounded-lg p-3">
              {/* 文件图标 - 使用 FileIcon 组件 */}
              <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-md flex items-center justify-center">
                <FileIcon
                  fileName={media.filename || 'file'}
                  size="48"
                  className="flex-shrink-0"
                  iconClassName="text-gray-600"
                />
              </div>

              {/* 文件详情 */}
              <div className="flex-1 min-w-0">
                <div
                  className="text-black text-base font-medium line-clamp-1 break-words"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 1,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    wordWrap: 'break-word',
                    wordBreak: 'break-word',
                  }}
                >
                  {media.filename || 'Unknown file'}
                </div>
                <div className="text-gray-400 text-sm mt-1">
                  {formatFileSize(media.contentSizeBytes || 0)}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    );
  }, [quotedMessage?.medias, quotedMessage?.textBody, formatFileSize]);

  // 根据消息类型渲染内容
  const renderMessageContent = useCallback(() => {
    if (!quotedMessage) return null;

    switch (quotedMessage.type) {
      case 'whatsappTemplate':
        return renderTemplateMessage();
      case 'file':
      case 'image':
      case 'audio':
      case 'video':
        return renderFileMessage();
      default:
        return renderTextMessage();
    }
  }, [
    quotedMessage?.type,
    renderTemplateMessage,
    renderFileMessage,
    renderTextMessage,
  ]);

  // 如果没有quotedMessage，直接返回null
  if (!quotedMessage) {
    return null;
  }

  // 确定是否可点击和相关样式
  const isClickable = setHighlightedMessageId !== undefined;
  const clickableClasses = isClickable
    ? 'cursor-pointer hover:bg-gray-50 transition-colors'
    : '';

  return (
    <div
      className={`bg-white border border-gray-200 rounded-lg shadow-sm max-w-full flex ${clickableClasses}`}
      onClick={isClickable ? handleComponentClick : undefined}
    >
      {/* 左侧深灰色条 */}
      <div className="w-1 bg-gray-200 rounded-sm flex-shrink-0 my-3 ml-3 mr-2"></div>

      {/* 主要内容 */}
      <div className="flex-1 min-w-0">
        {/* 头部：发送者名称和关闭按钮 */}
        <div className="flex items-center justify-between px-4 py-3 border-b border-gray-100">
          <div
            className="text-gray-500 text-sm font-medium flex-1 min-w-0 mr-2 truncate"
            style={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {quotedMessage?.userName || 'Unknown user'}
          </div>
          {hasCloseButton && (
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
            >
              <X size={20} />
            </button>
          )}
        </div>

        {/* 消息内容 */}
        {renderMessageContent()}
      </div>
    </div>
  );
};

export default MessageReply;
