/* eslint-disable @next/next/no-img-element */
import React, { useEffect, useState } from 'react';
import { CDSSMessage, InnerTranscript } from '../../../@types/Message';
import { Download, CheckCircle, AlertCircle } from 'lucide-react';
import FileSizeDisplay from './FileSizeDisplay';
import FileIcon from '../FileIcon';
import IconLoadingDots from '../Icon/IconLoadingDots';
import IconVirusScan from '../Icon/IconVirusScan';

interface FileToolProps {
  message: CDSSMessage;
  isCustomer: boolean;
  isShowStt?: boolean;
  isReplyContent?: boolean;
}
type VirusScanStatusType = 'success' | 'failed' | 'detected' | 'scanning' | '';
type SttStatusType = 'loading' | 'failed' | 'success' | 'processing' | '';
type PiiStatusType =
  | 'piiFailed'
  | 'piiSuccess'
  | 'processing'
  | 'piiNoMask'
  | '';

const FileTool: React.FC<FileToolProps> = ({
  message,
  isCustomer,
  isShowStt = false,
  isReplyContent = false,
}) => {
  const media = message.medias?.[0];
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);
  const [transcriptText, setTranscriptText] = useState('');
  const [showFullTranscript, setShowFullTranscript] = useState(false);
  const [showFullFilename, setShowFullFilename] = useState(false);
  const [virusScanStatus, setVirusScanStatus] =
    useState<VirusScanStatusType>('');
  const [sttStatus, setSttStatus] = useState<SttStatusType>('');
  const [piiStatus, setPiiStatus] = useState<PiiStatusType>('');
  // 处理文本内容中的换行符
  const re_textBody = message?.textBody?.replace(/\\n/g, '\n') || '';
  let metaObject: any = {};
  if (message.metadata) {
    const metadataStr = message.metadata;
    try {
      metaObject = JSON.parse(metadataStr);
    } catch (error) {
      console.error('Error parsing metadata:', error);
      // 可以在这里添加其他错误处理逻辑,例如设置默认值
    }
  }

  const sortTranscriptsByStart = (
    transcripts: InnerTranscript[]
  ): InnerTranscript[] => {
    if (!Array.isArray(transcripts)) {
      console.error('Expected transcripts to be an array, got:', transcripts);
      return [];
    }

    return transcripts.sort((a, b) => {
      const [ah, am, as] = a.start.split(':').map(Number);
      const [bh, bm, bs] = b.start.split(':').map(Number);
      const aSeconds = ah * 3600 + am * 60 + as;
      const bSeconds = bh * 3600 + bm * 60 + bs;
      return aSeconds - bSeconds;
    });
  };

  const concatTranscriptText = (transcripts: InnerTranscript[]): string => {
    if (!Array.isArray(transcripts)) {
      console.error(
        'Expected transcripts to be an array in concatTranscriptText, got:',
        transcripts
      );
      return '';
    }

    return transcripts.reduce((acc, curr) => {
      return acc + curr.text + '\n';
    }, '');
  };

  // 检查文件是否可以下载
  const isFileDownloadable = (): boolean => {
    if (!isCustomer || !media?.url) {
      return !!media?.url; // 如果不是客户，只要有URL就可以下载
    }

    const needsVirusScan = metaObject?.virusScan === '1';
    const needsPii = metaObject?.pii === '1';

    // 如果两者都需要，则两者都必须成功
    if (needsVirusScan && needsPii) {
      return (
        virusScanStatus === 'success' &&
        (piiStatus === 'piiSuccess' || piiStatus === 'piiNoMask')
      );
    }

    // 如果只需要病毒扫描，则病毒扫描必须成功
    if (needsVirusScan) {
      return virusScanStatus === 'success';
    }

    // 如果只需要PII检测，则PII检测必须成功
    if (needsPii) {
      return piiStatus === 'piiSuccess' || piiStatus === 'piiNoMask';
    }

    // 如果都不需要，则可以下载
    return true;
  };

  useEffect(() => {
    // 首先判断病毒扫描状态
    let virusFailed = false;

    // 设置virusScanStatus
    if (metaObject?.virusScan === '1') {
      setVirusScanStatus('scanning');
      if (
        message.virusScanJobResult &&
        message.virusScanJobResult.status === '2' &&
        message.virusScanJobResult.result === true
      ) {
        setVirusScanStatus('success');
      } else if (
        message.virusScanJobResult &&
        message.virusScanJobResult.status === '2' &&
        message.virusScanJobResult.result !== true
      ) {
        console.log('virusScanJobResult print: ', message.virusScanJobResult);
        setVirusScanStatus('detected');
        virusFailed = true; // 标记病毒扫描失败
      } else if (
        message.virusScanJobResult &&
        message.virusScanJobResult.status === '-2'
      ) {
        setVirusScanStatus('failed');
        virusFailed = true; // 标记病毒扫描失败
      } else if (
        message.virusScanJobResult &&
        message.virusScanJobResult.status === '-1'
      ) {
        setVirusScanStatus('failed');
        virusFailed = true; // 标记病毒扫描失败
      }
    }

    // 设置piiStatus
    if (metaObject?.pii === '1') {
      if (virusFailed) {
        setPiiStatus('piiFailed');
        return;
      }
      // 初始为processing状态
      setPiiStatus('processing');

      if (message.piiJobResult) {
        if (message.piiJobResult.status === '2') {
          setPiiStatus('piiSuccess');
        } else if (message.piiJobResult.status === '3') {
          setPiiStatus('piiNoMask');
        } else {
          setPiiStatus('piiFailed');
        }
      }
    }

    // 设置 sttStatus
    if (metaObject?.stt === '1') {
      // 如果病毒扫描失败，直接设置 STT 为失败，不执行后续逻辑
      if (virusFailed) {
        setSttStatus('failed');
        return; // 提前退出，不执行后续 STT 状态设置逻辑
      }

      // 如果病毒扫描没有失败，正常处理 STT 状态
      if (!message.sttJobResult) {
        setSttStatus('loading');
      } else if (message.sttJobResult.status === '-1') {
        setSttStatus('failed');
      } else if (message.sttJobResult.status === '-2') {
        setSttStatus('failed');
      } else if (message.sttJobResult.status === '2') {
        setSttStatus('success');
        try {
          const parsedTranscript = JSON.parse(message.sttJobResult.transcript);
          if (
            parsedTranscript &&
            parsedTranscript.transcripts &&
            Array.isArray(parsedTranscript.transcripts)
          ) {
            const sortedTranscripts = sortTranscriptsByStart(
              parsedTranscript.transcripts
            );
            const transcripts = concatTranscriptText(sortedTranscripts);
            setTranscriptText(transcripts);
          } else {
            console.error(
              'Parsed transcript structure is not as expected:',
              parsedTranscript
            );
            setTranscriptText('');
          }
        } catch (error) {
          console.error('Error parsing transcript JSON:', error);
          setTranscriptText('');
        }
      } else {
        setSttStatus('processing');
      }
    }
  }, [message, metaObject]);

  const handleDownload = async (url: string, filename: string) => {
    try {
      // 创建一个临时的 a 标签元素
      const tempLink = document.createElement('a');

      // 设置 a 标签的 href 和 download 属性
      tempLink.href = url;
      tempLink.download = filename;

      // 将 a 标签添加到 DOM 树中
      document.body.appendChild(tempLink);

      // 触发点击事件
      tempLink.click();

      // 移除 a 标签
      document.body.removeChild(tempLink);
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  if (!media) return null;

  const getScanStatusMessage = (status: VirusScanStatusType): string => {
    switch (status) {
      case 'success':
        return 'No virus detected.';
      case 'detected':
        return 'Virus detected.';
      case 'failed':
        return 'Antivirus failed';
      case 'scanning':
        return 'Scanning for security checks...';
      default:
        return 'Virus scan turned off';
    }
  };

  const getPiiStatusMessage = (status: PiiStatusType): string => {
    switch (status) {
      case 'piiSuccess':
        return 'PII masked.';
      case 'piiNoMask':
        return 'No PII detected.';
      case 'piiFailed':
        return 'PII mask failed.';
      case 'processing':
        return 'PII masking...';
      default:
        return 'pII turned off';
    }
  };
  const getNoneDisplayReason = (): string => {
    if (virusScanStatus === 'detected') {
      return 'This file has detected a virus risk, please be aware.';
    } else if (virusScanStatus === 'failed') {
      return 'virus scan run failed';
    } else if (piiStatus === 'piiFailed') {
      return 'Hello, the personal information identity occlusion was unsuccessful, so the image cannot be displayed.';
    }
    return '';
  };

  return (
    <div
      className={`w-[30rem] rounded-lg overflow-hidden ${
        isReplyContent ? 'bg-white' : 'bg-gray-100'
      }`}
    >
      <div className="w-full border border-black-200 rounded-md p-3 flex items-start gap-3">
        {/* File Icon Container */}
        <div className="flex-shrink-0 w-12 h-12 bg-orange-100 rounded-md flex items-center justify-center">
          <FileIcon
            fileName={media.filename || 'file'}
            size="48"
            className="flex-shrink-0"
            iconClassName="text-gray-600"
          />
        </div>

        {/* File Information - 优化后的布局 */}
        <div className="flex-grow min-w-0">
          <div className="flex flex-col w-full">
            {/* Filename with tooltip for long names - fixed 80% width */}
            <div
              className="flex items-center text-base font-medium text-black-100 mb-1"
              onMouseEnter={() => setShowFullFilename(true)}
              onMouseLeave={() => setShowFullFilename(false)}
            >
              <div className="relative w-[80%] group">
                <div className="truncate">{media.filename}</div>
                {showFullFilename &&
                  media.filename &&
                  media.filename.length > 20 && (
                    <div className="absolute z-10 left-0 top-6 bg-white p-2 rounded shadow-md text-sm break-all">
                      {media.filename}
                    </div>
                  )}
              </div>
              {isFileDownloadable() && (
                <button
                  className="ml-2 flex-shrink-0 w-4 h-4 text-black-500 hover:text-black-700 transition-colors"
                  onClick={() => handleDownload(media.url, media.filename)}
                  aria-label="Download file"
                >
                  <Download />
                </button>
              )}
            </div>
            {/* File size (20%) and scan results (remaining width) */}
            <div className="flex items-center text-sm text-gray-500 w-full">
              <div className="w-[20%] flex-shrink-0">
                <FileSizeDisplay
                  bytes={media.contentSizeBytes ? media.contentSizeBytes : 0}
                />
              </div>
              {isCustomer && (
                <div className="flex items-center gap-1 flex-grow">
                  {/* 病毒扫描状态 - 只在 metaObject.virusScan === '1' 时显示 */}
                  {metaObject && metaObject.virusScan === '1' && (
                    <>
                      <IconVirusScan status={virusScanStatus} />
                      <span className="truncate">
                        {getScanStatusMessage(virusScanStatus)}
                      </span>
                    </>
                  )}

                  {/* PII 状态 - 只在 metaObject.pii === '1' 时显示 */}
                  {metaObject && metaObject.pii === '1' && (
                    <>
                      <IconVirusScan
                        status={piiStatus}
                        className={'ml-2'}
                      />
                      <span className="truncate">
                        {getPiiStatusMessage(piiStatus)}
                      </span>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* STT Transcript section with different states */}
      {isShowStt && !isReplyContent && metaObject.stt === '1' && (
        <div className="relative border-t p-2">
          {sttStatus === 'loading' ? (
            // Loading state
            <div className="flex items-center justify-center py-2">
              <IconLoadingDots size="24px" />
            </div>
          ) : sttStatus === 'failed' ? (
            // Failed state
            <div className="flex items-center justify-center py-2 text-red-500">
              <AlertCircle className="w-4 h-4 mr-1" />
              <span>STT failed</span>
            </div>
          ) : sttStatus === 'success' ? (
            // Success state
            <div
              className="relative"
              onMouseEnter={() => setShowFullTranscript(true)}
              onMouseLeave={() => setShowFullTranscript(false)}
            >
              <span
                className="block whitespace-pre-wrap break-words"
                style={{
                  display: '-webkit-box',
                  WebkitBoxOrient: 'vertical',
                  WebkitLineClamp: showFullTranscript ? 'unset' : 3,
                  overflow: showFullTranscript ? 'visible' : 'hidden',
                }}
              >
                {transcriptText}
              </span>
              {!showFullTranscript && transcriptText.length > 100 && (
                <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-[#F0F0F0] to-transparent"></div>
              )}
            </div>
          ) : (
            // Default case (processing or other status)
            <div className="flex items-center justify-center py-2 text-gray-500">
              <span>Processing...</span>
            </div>
          )}
        </div>
      )}

      {/* 图片预览部分 */}
      {media.mediaType === 'Image' && (
        <div className="relative border-t p-4">
          <div className="w-full max-w-[30rem] flex justify-center">
            <img
              src={media.url}
              alt="Preview"
              className={`max-w-[30rem] max-h-[500px] h-auto object-contain rounded-md${
                imageLoading ? ' opacity-0' : ' opacity-100'
              }`}
              onLoad={() => setImageLoading(false)}
              onError={() => {
                setImageLoading(false);
                setImageError(true);
              }}
            />
          </div>
        </div>
      )}
      {(virusScanStatus === 'failed' || piiStatus === 'piiFailed') && (
        <div className="border-t p-3 flex items-start">
          <div className="mr-2">
            <IconVirusScan status={'reason'} />
          </div>
          <p className="text-gray-500 whitespace-pre-wrap break-words">
            {getNoneDisplayReason()}
          </p>
        </div>
      )}
      {/* 添加文本显示部分 */}
      {message.textBody && !isReplyContent && (
        <div className="border-t p-3">
          <p className="text-gray-800 whitespace-pre-wrap break-words">
            {re_textBody}
          </p>
        </div>
      )}
    </div>
  );
};

export default FileTool;
