import React from 'react';

interface FileSizeDisplayProps {
  bytes: number | null;
}

const FileSizeDisplay: React.FC<FileSizeDisplayProps> = ({ bytes }) => {
  const formatFileSize = (size: number): string => {
    if (size < 1024) {
      return `${size.toFixed(2)} B`;
    } else if (size < 1024 * 1024) {
      return `${(size / 1024).toFixed(2)} KB`;
    } else {
      return `${(size / (1024 * 1024)).toFixed(2)} MB`;
    }
  };

  if (bytes === null) {
    return <span>-</span>;
  }

  return <span>{formatFileSize(bytes)}</span>;
};

export default FileSizeDisplay;
