import { timeStringToSeconds } from '../../../lib/utils';
import { useGlobalAudioPlayer } from 'react-use-audio-player';

interface ITranscriptItemProps {
  id: string;
  content: string;
  timerStart: string;
  timerEnd: string;
  participant: string;
  isFocus: boolean;
}

const TranscriptItem = ({
  id,
  content,
  timerStart,
  timerEnd,
  participant,
  isFocus,
}: ITranscriptItemProps) => {
  const focusStyle = () => {
    return isFocus
      ? 'bg-[#EEEFF1] border-[#FFAC4A]'
      : 'border-[#EEEFF1] bg-[#FCFCFD]';
  };
  const { seek } = useGlobalAudioPlayer();

  return (
    <div
      id={id}
      className={`my-2 p-2 flex flex-col gap-1 border rounded-[8px] ${focusStyle()}`}
      onClick={() => {
        console.log('click', timerStart);
      }}
    >
      <div className="w-full text-[#FFAC4A]">{participant}</div>
      <div className="w-full">
        {content}{' '}
        <span
          className="text-[#ACACAC] cursor-pointer"
          onClick={() => {
            seek(timeStringToSeconds(timerStart));
          }}
        >
          {`${timerStart} - ${timerEnd}`}
        </span>
      </div>
    </div>
  );
};

export default TranscriptItem;
