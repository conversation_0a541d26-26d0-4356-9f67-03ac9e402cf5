import { formatAudioTime } from '../../../lib/utils';
import React, {
  useCallback,
  useEffect,
  useRef,
  useState,
  MouseEvent,
  useMemo,
} from 'react';
import { AudioLoadOptions, useGlobalAudioPlayer,AudioPlayer as TAudioPlayer, useAudioPlayer } from 'react-use-audio-player';
import Icon from '../Icon';
import { cn } from '../../../lib/utils';
import { Select } from '../Select';
import Loader from '../Loader';
import SeekBar from '../SeekBar';
import {
  DropdownArrow,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '../DropdownMenu';
import Input from '../Input';
import Button from '../Button';
import { useQM } from '../../../context/QMContext';

const PLAYBACK_SPEED = [0.5, 1, 1.5, 2];

export const AudioPlayButton = ({audioPlayer}:{audioPlayer?:TAudioPlayer|undefined}) => {
  const {
    play,
    pause,
    playing,
    // stop, mute, muted, loop, looping
  } = audioPlayer?audioPlayer:useGlobalAudioPlayer();

  return (
    <>
      <button
        className="p-1 border-[#E0E0E0] border-2 rounded"
        onClick={() => (playing ? pause() : play())}
      >
        <Icon
          size={18}
          name={playing ? 'pause' : 'play'}
        />
      </button>
      {/* <button onClick={() => stop()}>stop</button>
      <button onClick={() => mute(!muted)}>toggle mute</button>
      <button onClick={() => loop(!looping)}>toggle loop</button> */}
    </>
  );
};

export const AudioControls = ({ label,audioPlayer }: { label?: any,audioPlayer?:TAudioPlayer|undefined }) => {
  const { getPosition, seek, rate, setRate } = audioPlayer?audioPlayer:useGlobalAudioPlayer();
  const { toggleStrandScript, openedQA, formId } = useQM();

  const goToPrev10s = () => {
    const currentPosition = getPosition();
    const newPosition = Math.max(currentPosition - 10, 0);
    seek(newPosition);
  };

  const goToNext10s = () => {
    const currentPosition = getPosition();
    const newPosition = currentPosition + 10;
    seek(newPosition);
  };

  return (
    <div className="flex items-center justify-between mb-2">
      <div className="flex items-center gap-x-2">
        <AudioPlayButton audioPlayer={audioPlayer}/>
        <button
          className="p-1 border-[#E0E0E0] border-2 rounded"
          onClick={() => goToPrev10s()}
        >
          <Icon
            size={18}
            name="prev-10s"
          />
        </button>
        <button
          className="p-1 border-[#E0E0E0] border-2 rounded"
          onClick={() => goToNext10s()}
        >
          <Icon
            size={18}
            name="next-10s"
          />
        </button>
        <AudioFixSpeedControl label={label} audioPlayer={audioPlayer}/>
      </div>
      <AudioTimeLabel audioPlayer={audioPlayer}/>
      <div className="flex items-center gap-x-2 text-grey-500">
        {openedQA && formId && (
          <button
            className="p-1 border-[#E0E0E0] border-2 rounded"
            onClick={() => {
              toggleStrandScript && toggleStrandScript();
            }}
          >
            <Icon
              size={18}
              name="sop"
            />
          </button>
        )}
        <AudioVolumnControl audioPlayer={audioPlayer}/>
        <AudioSpeedControl label={label} audioPlayer={audioPlayer}/>
        <AudioGoTo label={label} audioPlayer={audioPlayer}/>
      </div>
    </div>
  );
};

type TAudioSeekBarProps = {
  updatePos?: (pos: number) => void;
  audioPlayer?:TAudioPlayer|undefined;
};

export const AudioSeekBar = ({ updatePos,audioPlayer }: TAudioSeekBarProps) => {
  const { getPosition, duration, seek } = audioPlayer?audioPlayer:useGlobalAudioPlayer();
  const [_seeking, setSeeking] = useState(false);
  const [pos, setPos] = useState(0);
  const frameRef = useRef<number>();

  const seekBarElem = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const animate = () => {
      setPos(getPosition());
      updatePos && updatePos(getPosition());
      frameRef.current = requestAnimationFrame(animate);
    };

    frameRef.current = window.requestAnimationFrame(animate);

    return () => {
      if (frameRef.current) {
        cancelAnimationFrame(frameRef.current);
      }
    };
  }, [getPosition, updatePos]);

  const goTo = useCallback(
    (event: MouseEvent) => {
      const { pageX: eventOffsetX } = event;

      if (seekBarElem.current) {
        const elementOffsetX = seekBarElem.current.offsetLeft;
        const elementWidth = seekBarElem.current.clientWidth;
        const percent = (eventOffsetX - elementOffsetX) / elementWidth;
        seek(percent * duration);
      }
    },
    [duration, seek]
  );

  const seekTo = (value: number) => {
    seek(value * duration);
  };
  const handleSeekChange = (e: any) => {
    seekTo(parseFloat(e.target.value));
  };

  const handleSeekMouseDown = () => {
    setSeeking(true);
  };
  const handleSeekMouseUp = (e: any) => {
    setSeeking(false);
    seekTo(parseFloat(e.target.value));
  };

  if (duration === Infinity) return null;

  const progress = Math.min(pos / duration, 1);
  return (
    // fix: progress drag issue
    <SeekBar
      ref={seekBarElem}
      progress={progress}
      handleSeekMouseDown={handleSeekMouseDown}
      handleSeekChange={handleSeekChange}
      handleSeekMouseUp={handleSeekMouseUp}
    />
  );
};

export const AudioTimeLabel = ({audioPlayer}:{audioPlayer?:TAudioPlayer|undefined}) => {
  const [pos, setPos] = useState(0);
  const { duration, getPosition } = audioPlayer?audioPlayer:useGlobalAudioPlayer();

  useEffect(() => {
    const i = setInterval(() => {
      setPos(getPosition());
    }, 500);

    return () => clearInterval(i);
  }, [getPosition]);

  return (
    <div className="whitespace-nowrap font-bold">{`${formatAudioTime(
      pos
    )} / ${formatAudioTime(duration)}`}</div>
  );
};

export const AudioGoTo = ({ label,audioPlayer }: { label?: any,audioPlayer?:TAudioPlayer|undefined }) => {
  const { seek, duration } = audioPlayer?audioPlayer:useGlobalAudioPlayer();
  const [inputValue, setInputValue] = useState<string>('00:00');
  const [open, setOpen] = useState(false);

  const isValidTimeFormat = useMemo(() => {
    const timeString = inputValue;
    // Regular expression to match the mm:ss format
    const timeFormatRegex = /^\d{2}:\d{2}$/;

    // Check if the string matches the mm:ss format
    if (!timeFormatRegex.test(timeString)) {
      return false;
    }

    // Split the string into minutes and seconds
    const [minutes, seconds] = timeString.split(':').map(Number);

    // Check if minutes and seconds are within the 0-59 range
    if (minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
      return false;
    }

    return true;
  }, [inputValue]);

  const seekToTarget = () => {
    if (!isValidTimeFormat) return;
    const [minutes, seconds] = inputValue.split(':').map(Number);
    let totalSecs = minutes * 60 + seconds;
    if (totalSecs > duration) totalSecs = duration;
    if (totalSecs < 0) totalSecs = 0;
    seek(totalSecs);
  };

  return (
    <div className="inline-flex relative">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <DropdownMenuTrigger>
          <div
            className={cn(
              'p-1 border-[#E0E0E0] border-2 rounded',
              open && 'bg-primary-200'
            )}
          >
            <Icon
              size={18}
              name="sound-wave"
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
            <p className=" whitespace-nowrap">
              {label?.['jumpToTime'] || 'Jump to time'}
            </p>
            <Input
              value={inputValue}
              placeholder="mm:ss"
              className="w-16"
              maxLength={5}
              onChange={(v) => setInputValue(v as string)}
            />
            <Button
              bodyClassName="min-w-[50px]"
              size="s"
              onClick={() => seekToTarget()}
              disabled={!isValidTimeFormat}
            >
              <span className="whitespace-nowrap">
                {isValidTimeFormat
                  ? label?.['go'] || 'Go'
                  : label?.['invalidTime'] || 'Invalid Time'}
              </span>
            </Button>
            <DropdownArrow
              width={12}
              height={10}
              className="fill-white"
            />
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export const AudioVolumnControl = ({audioPlayer}:{audioPlayer?:TAudioPlayer|undefined}) => {
  const { muted, mute, volume, setVolume } = audioPlayer?audioPlayer:useGlobalAudioPlayer();
  const [open, setOpen] = useState(false);
  const [_seeking, setSeeking] = useState(false);

  const seekBarElem = useRef<HTMLDivElement>(null);

  const seekTo = useCallback((value: number) => setVolume(value), [setVolume]);

  const goTo = useCallback(
    (event: MouseEvent) => {
      const { pageX: eventOffsetX } = event;

      if (seekBarElem.current) {
        const elementOffsetX = seekBarElem.current.offsetLeft;
        const elementWidth = seekBarElem.current.clientWidth;
        const percent = (eventOffsetX - elementOffsetX) / elementWidth;
        seekTo(percent);
      }
    },
    [seekTo]
  );

  const handleSeekChange = (e: any) => {
    seekTo(parseFloat(e.target.value));
  };

  const handleSeekMouseDown = () => {
    setSeeking(true);
  };
  const handleSeekMouseUp = (e: any) => {
    setSeeking(false);
    seekTo(parseFloat(e.target.value));
  };

  return (
    <div className="inline-flex relative">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <DropdownMenuTrigger>
          <div
            className={cn(
              'p-1 border-[#E0E0E0] border-2 rounded',
              open && 'bg-primary-200'
            )}
          >
            <Icon
              size={18}
              name={muted ? 'sound-mute' : 'sound'}
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
            <button
              onClick={() => mute(!muted)}
              className={cn(
                'p-2 rounded-md hover:bg-primary-200',
                muted && 'bg-primary-200'
              )}
            >
              <Icon
                size={18}
                name="sound-mute"
              />
            </button>
            <div className={cn('w-[100px]', muted && 'opacity-40')}>
              <SeekBar
                ref={seekBarElem}
                goTo={goTo}
                progress={volume}
                handleSeekMouseDown={handleSeekMouseDown}
                handleSeekChange={handleSeekChange}
                handleSeekMouseUp={handleSeekMouseUp}
              />
            </div>
            <div>{`${Math.round(volume * 100)}%`}</div>
            <DropdownArrow
              width={12}
              height={10}
              className="fill-white"
            />
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export const AudioFixSpeedControl = ({ label,audioPlayer }: { label?: any,audioPlayer?:TAudioPlayer|undefined }) => {
  const { rate, setRate } = useGlobalAudioPlayer();
  const [open, setOpen] = useState(false);
  const [_seeking, setSeeking] = useState(false);

  const seekBarElem = useRef<HTMLDivElement>(null);

  const lowerLimit = PLAYBACK_SPEED[0];
  const upperLimit = PLAYBACK_SPEED[PLAYBACK_SPEED.length - 1];
  const isCustomSpeed = PLAYBACK_SPEED.indexOf(rate) === -1;

  const progress = (rate - lowerLimit) / (upperLimit - lowerLimit);

  const seekTo = useCallback(
    (value: number) => {
      let convertedValue = lowerLimit + value * (upperLimit - lowerLimit);
      if (convertedValue > upperLimit) convertedValue = upperLimit;
      if (convertedValue < lowerLimit) convertedValue = lowerLimit;
      setRate(convertedValue);
    },
    [setRate, lowerLimit, upperLimit]
  );

  const handleSeekChange = (e: any) => {
    seekTo(parseFloat(e.target.value));
  };

  const handleSeekMouseDown = () => {
    setSeeking(true);
  };
  const handleSeekMouseUp = (e: any) => {
    setSeeking(false);
    seekTo(parseFloat(e.target.value));
  };

  const speedLabel = label?.['speed'] || 'Speed';

  return (
    <div className="inline-flex relative">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <DropdownMenuTrigger>
          <button
            key={`rate-${rate}`}
            className={cn(
              'p-1 border-[#E0E0E0] border-2 rounded',
              open && 'bg-primary-200'
            )}
          >
            <div className="h-[18px] leading-[18px]">{rate}x</div>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
            {PLAYBACK_SPEED.map((r) => (
              <button
                key={`rate-${r}`}
                className={cn(
                  'p-2 hover:text-primary rounded-md',
                  rate === r && 'bg-primary-200'
                )}
                onClick={() => setRate(r)}
              >
                <div className="h-[18px] leading-[18px]">{r}x</div>
              </button>
            ))}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export const AudioSpeedControl = ({ label,audioPlayer }: { label?: any,audioPlayer?:TAudioPlayer|undefined }) => {
  const { rate, setRate } = audioPlayer?audioPlayer:useGlobalAudioPlayer();
  const [open, setOpen] = useState(false);
  const [_seeking, setSeeking] = useState(false);

  const seekBarElem = useRef<HTMLDivElement>(null);

  const lowerLimit = PLAYBACK_SPEED[0];
  const upperLimit = PLAYBACK_SPEED[PLAYBACK_SPEED.length - 1];
  const isCustomSpeed = PLAYBACK_SPEED.indexOf(rate) === -1;

  const progress = (rate - lowerLimit) / (upperLimit - lowerLimit);

  const seekTo = useCallback(
    (value: number) => {
      let convertedValue = lowerLimit + value * (upperLimit - lowerLimit);
      if (convertedValue > upperLimit) convertedValue = upperLimit;
      if (convertedValue < lowerLimit) convertedValue = lowerLimit;
      setRate(convertedValue);
    },
    [setRate, lowerLimit, upperLimit]
  );

  const handleSeekChange = (e: any) => {
    seekTo(parseFloat(e.target.value));
  };

  const handleSeekMouseDown = () => {
    setSeeking(true);
  };
  const handleSeekMouseUp = (e: any) => {
    setSeeking(false);
    seekTo(parseFloat(e.target.value));
  };

  const speedLabel = label?.['speed'] || 'Speed';

  return (
    <div className="inline-flex relative">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <DropdownMenuTrigger>
          <div
            className={cn(
              'p-1 border-[#E0E0E0] border-2 rounded',
              open || (isCustomSpeed && 'bg-primary-200')
            )}
          >
            <Icon
              size={18}
              name="speed"
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
            <span>{`${speedLabel} (${lowerLimit}x - ${upperLimit}x)`}:</span>
            <div className={cn('w-[100px]')}>
              <SeekBar
                ref={seekBarElem}
                progress={progress}
                handleSeekMouseDown={handleSeekMouseDown}
                handleSeekChange={handleSeekChange}
                handleSeekMouseUp={handleSeekMouseUp}
              />
            </div>
            <div>{`${rate.toFixed(1)}x`}</div>
            <DropdownArrow
              width={12}
              height={10}
              className="fill-white"
            />
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export const Audio = () => {
  const { seek, duration } = useGlobalAudioPlayer();
  const [inputValue, setInputValue] = useState<string>('00:00');
  const [open, setOpen] = useState(false);

  const isValidTimeFormat = useMemo(() => {
    const timeString = inputValue;
    // Regular expression to match the mm:ss format
    const timeFormatRegex = /^\d{2}:\d{2}$/;

    // Check if the string matches the mm:ss format
    if (!timeFormatRegex.test(timeString)) {
      return false;
    }

    // Split the string into minutes and seconds
    const [minutes, seconds] = timeString.split(':').map(Number);

    // Check if minutes and seconds are within the 0-59 range
    if (minutes < 0 || minutes > 59 || seconds < 0 || seconds > 59) {
      return false;
    }

    return true;
  }, [inputValue]);

  const seekToTarget = () => {
    if (!isValidTimeFormat) return;
    const [minutes, seconds] = inputValue.split(':').map(Number);
    let totalSecs = minutes * 60 + seconds;
    if (totalSecs > duration) totalSecs = duration;
    if (totalSecs < 0) totalSecs = 0;
    console.log('totalSecs', totalSecs);
    seek(totalSecs);
  };

  return (
    <div className="relative">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <DropdownMenuTrigger>
          <div className={cn('p-2 rounded-md', open && 'bg-primary-200')}>
            <Icon
              size={18}
              name="sound-wave"
            />
          </div>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative px-4 py-2 bg-white flex flex-row gap-2.5 items-center">
            <p className=" whitespace-nowrap">Jump to time</p>
            <Input
              value={inputValue}
              placeholder="mm:ss"
              className="w-16"
              maxLength={5}
              onChange={(v) => setInputValue(v as string)}
            />
            <Button
              bodyClassName="min-w-[50px]"
              size="s"
              onClick={() => seekToTarget()}
              disabled={!isValidTimeFormat}
            >
              <span className="whitespace-nowrap">
                {isValidTimeFormat ? 'Go' : 'Invalid Time'}
              </span>
            </Button>
            <DropdownArrow
              width={12}
              height={10}
              className="fill-white"
            />
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

type TAudioPlayerProps = {
  error?: string | null | false;
  label?: any;
  seekBarCustomComponent?: React.ReactNode;
} & TAudioSeekBarProps;

export const AudioPlayer = ({
  error,
  updatePos,
  label,
  seekBarCustomComponent,
  audioPlayer
}: TAudioPlayerProps) => {
  const state = useGlobalAudioPlayer();
  if (error) return <p>{error}</p>;
  if (state.isReady) {
    return (
      <div>
        <div className="mt-2">
          <AudioControls label={label} />
        </div>
        <div className="relative flex items-center gap-x-2">
          <div className="mt-6 w-full h-full relative">
            {seekBarCustomComponent}
            <AudioSeekBar updatePos={updatePos} audioPlayer={audioPlayer}/>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export const AudioState = () => {
  const state = useGlobalAudioPlayer();
  if (state.isReady) {
    return (
      <div>
        <h3>Audio state:</h3>
        <table>
          <tbody>
            {Object.entries(state).map(([k, v]) => {
              if (typeof v === 'function') return null;

              return (
                <tr key={k}>
                  <td>{k}</td>
                  <td>{v?.toString() ?? '--'}</td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  }

  return null;
};

export const AudioFileLoader = ({ audioSrc,audioPlayer }: any) => {
  const [audioFile, setAudioFile] = useState(audioSrc);
  const { load, isReady, error } = audioPlayer?audioPlayer:useGlobalAudioPlayer();

  useEffect(() => {
    const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
    if (audioFile.includes('stream')||audioFile.startsWith('blob')) {
      loadOptions.html5 = true;
      loadOptions.format = 'mp3';
    }

    load(audioFile, loadOptions);
  }, [audioFile, load]);

  const selectAudioFile = (e: any) => {
    if (e) {
      setAudioFile(e);
    }
  };

  const isLoading = !isReady && !error;

  return (
    <div className="hidden">
      <h5 className="mb-4">Select audio file as source for testing:</h5>

      {isLoading ? (
        <>
          <Loader size={64} />
          {`${isReady} | ${JSON.stringify(error)}`}
        </>
      ) : (
        <div className="max-w-[300px]">
          <Select
            placeholder="Select audio..."
            mode="single"
            options={[
              {
                id: '/audio/test-mp3.mp3',
                label: 'test-mp3.mp3',
                value: '/audio/test-mp3.mp3',
              },
              {
                id: '/audio/test-wav.wav',
                label: 'test-wav.wav',
                value: '/audio/test-wav.wav',
              },
              {
                id: '/doesntExist.mp3',
                label: 'does not exist',
                value: '/doesntExist.mp3',
              },
              {
                id: 'test',
                label: 'test',
                value:
                  'https://kelvintorage.blob.core.windows.net/recordings/a5d659f4-c5c7-472e-9a7a-f9dcdafc44cf.mp3?se=2024-03-28T17%3A01%3A32Z&sig=ZhpAKZ1rC0aRT3v4m1xs%2FsmhLxxIXLN20R5P75iqYD8%3D&sp=r&spr=https&sr=b&sv=2020-10-02',
              },
              {
                id: 'https://stream.toohotradio.net/128',
                label: 'streaming internet radio',
                value: 'https://stream.toohotradio.net/128',
              },
            ]}
            showSearch={true}
            value={audioFile}
            onChange={selectAudioFile}
          />
        </div>
      )}
      {error && <p className="errorMessage">Failed to load</p>}
    </div>
  );
};
type TProps = {
    audioSrc:string;
};
export const MultipleAudioRender: React.FC<TProps> = React.memo(({
    audioSrc
}) => {
    const song = useAudioPlayer();
    useEffect(() => {
        const loadOptions: AudioLoadOptions = { initialVolume: 0.5 };
        if (audioSrc?.includes('stream') || audioSrc?.startsWith('blob')) {
            loadOptions.html5 = true;
            loadOptions.format = 'mp3';
        }
        song?.load(audioSrc, loadOptions);
        // 组件卸载时的清理函数
        return () => {
            song?.stop(); // 停止音频播放
        };
    }, [audioSrc]);

    return (
        <>
            <div>
                <AudioFileLoader audioSrc={audioSrc} audioPlayer={song} />
                <div className="mt-2">
                    <AudioControls audioPlayer={song} />
                    <AudioSeekBar audioPlayer={song} />
                </div>
            </div>
        </>
    );
});
export default AudioPlayer;
