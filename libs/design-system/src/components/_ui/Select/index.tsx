'use client';

import * as React from 'react';
import * as SelectPrimitive from '@radix-ui/react-select';
import { ChevronDown, ChevronUp, X } from 'lucide-react';

import { cn } from '../../../lib/utils';
import { TMessageStatus } from '../Message';
import { useMemo, useState } from 'react';
import Icon from '../Icon';
import Checkbox from '../Checkbox';
import Input from '../Input';
import { useObserveElementWidth } from '../../../lib/hooks/useObserveElementWidth';
import { getOptionLabelFromValue } from '../../../lib/utils';
import Fuse from 'fuse.js';
import { useTranslation } from 'react-i18next';

export type TItemProps = {
  id: string;
  label: string | React.ReactNode;
  value: string;
  isNotQueue?: boolean;
};

type TDropdownProps = {
  options: TItemProps[];
  placeholder?: string;
  showSearch?: boolean;
  value: string | string[];
  onChange: (value: any) => void;
  mode?: 'single' | 'multiple';
  disabled?: boolean;
  status?: TMessageStatus;
  noSearchResultMessage?: string;
  removeAllSelection?: () => void;
  removeSelection?: (value: string) => void;
  displaySelectedItems?: boolean;
  defaultValue?: string;
  readonly?: boolean;
  labelContainerClassName?: string;
  labelClassName?: string;
  optionClassName?: string;
  isPagination?: boolean;
  triggerClassName?: string;
  name?: string;
  message?: string;
};

const Select: React.FC<TDropdownProps> = ({
  name,
  options,
  placeholder,
  showSearch = false,
  value,
  onChange,
  mode = 'single',
  disabled = false,
  status,
  noSearchResultMessage = 'No data',
  removeAllSelection,
  removeSelection,
  displaySelectedItems = false,
  defaultValue,
  readonly,
  labelContainerClassName,
  labelClassName,
  optionClassName,
  isPagination = true,
  triggerClassName = '',
  message,
}) => {
  const { t } = useTranslation();
  const messageClasses = {
    danger: 'text-status-danger',
    info: 'text-status-info',
    success: 'text-status-success',
    warning: 'text-status-warning',
  };
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const { width, ref } = useObserveElementWidth<HTMLDivElement>();
  const calculatedWidth = Math.max(width, 200);

  const filteredOptions = useMemo(() => {
    if (!inputValue) return options;

    const fuse = new Fuse(options, {
      includeScore: true,
      keys: ['label'],
    });

    const results = fuse.search(inputValue);
    return results?.map((result) => result.item) ?? [];
  }, [options, inputValue]);

  const multipleIndicator = (num: number) => {
    return (
      <div className="flex flex-row items-center px-2 py-[2px] bg-primary-200 rounded-full gap-1 cursor-pointer z-20 hover:bg-primary-300">
        <div className="text-remark">+{num}</div>
        <div
          onClick={(e) => {
            // Test #4: Minimal with better positioning
            if (removeAllSelection) {
              removeAllSelection();
            }
            return false;
          }}
          className="w-4 h-4 rounded-full bg-common-black "
        >
          <Icon name="cross" />
        </div>
      </div>
    );
  };

  const renderSelectedItems = (
    disabled: boolean,
    options: TItemProps[],
    value: string | string[]
  ) => {
    if (Array.isArray(value)) {
      return (
        <div className="flex flex-row items-center gap-1 text-sm overflow-x-auto">
          {value.map((item) => {
            const option = options.find((option) => option.value === item);
            return (
              <span
                key={item}
                className={cn(
                  'bg-gray-100 rounded-md px-2 py-[3px] flex flex-row items-center gap-1',
                  disabled && 'bg-white'
                )}
              >
                {option?.label}{' '}
                {!disabled && (
                  <X
                    className="cursor-pointer w-3 h-3 text-grey-400 item-remove-btn hover:text-grey-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (removeSelection) {
                        removeSelection(item);
                      }
                      return false;
                    }}
                  />
                )}
              </span>
            );
          })}
        </div>
      );
    }
    return value;
  };

  return (
    <BaseSelect
      value={mode === 'single' ? (value as string) : undefined}
      onValueChange={onChange}
      open={open}
      onOpenChange={() => {
        // Check for active click on cross button
        const isActiveOnCross = document.querySelector(
          '.w-4.h-4.rounded-full.bg-common-black:active, .item-remove-btn:active'
        );
        if (!isActiveOnCross) {
          setOpen(!open);
        } else {
          console.log('Prevented toggle due to cross button click');
        }
      }}
      disabled={disabled}
      defaultValue={defaultValue}
      name={name}
    >
      <div
        ref={ref}
        className="relative disabled:cursor-not-allowed"
      >
        <BaseSelectTrigger
          className={cn(
            `m-0 disabled:bg-grey-200 disabled:border-none disabled:pointer-events-none ${triggerClassName}`,
            value && 'border-black',
            status && messageClasses[status],
            readonly && 'pointer-events-none',
            labelContainerClassName
          )}
        >
          <div className="w-full flex flex-row gap-1 items-center justify-between">
            <p
              className={cn(
                'h-[25px] text-black overflow-hidden truncate',
                (value?.length === 0 || !value) &&
                  placeholder &&
                  'italic text-grey-400',
                status === 'danger' && 'text-status-danger',
                labelClassName
              )}
            >
              {displaySelectedItems && value?.length > 0
                ? renderSelectedItems(disabled, options, value)
                : (Array.isArray(value)
                    ? getOptionLabelFromValue(options, value?.[0])
                    : getOptionLabelFromValue(options, value)) ?? placeholder}
              {isPagination ? t('pagination.perPage') : ''}
            </p>
            <div className="inline-flex items-center gap-x-1">
              {mode === 'multiple' &&
                value?.length > 1 &&
                !disabled &&
                multipleIndicator(value?.length - 1)}
              <Icon
                name="dropdown-arrow"
                size={8}
                className={cn(
                  'transition-all mt-[2px]',
                  open ? `rotate-180` : `rotate-0`,
                  disabled && `text-grey-600`
                )}
              />
            </div>
          </div>
        </BaseSelectTrigger>
        {message && <p className="text-red-500 text-xs italic mt-1">{message}</p>}
      </div>

      <BaseSelectContent
        style={{ width: calculatedWidth }}
        className={cn('flex flex-col gap-4 rounded shadow-card')}
      >
        {showSearch && (
          <div
            className="m-2"
            style={{ width: `calc(${calculatedWidth} - 8px)` }}
          >
            <Input
              isSearch
              size="s"
              beforeIcon={<Icon name="search" />}
              placeholder={`${t('global.search')} ...`}
              onChange={(v) => setInputValue(`${v}`)}
              allowClear
            />
          </div>
        )}
        {filteredOptions?.length === 0 ? (
          <div className="py-6 w-full text-center">{noSearchResultMessage}</div>
        ) : (
          <div className="flex flex-col gap-2 mt-2">
            {filteredOptions.map((item: TItemProps) => {
              const itemClassName = cn(
                'flex w-full hover:bg-primary-100 px-4 py-2 text-remark overflow-hidden break-words',
                value === item.value && 'bg-primary-100 hover:bg-primary-200',
                optionClassName
              );
              if (mode === 'single') {
                return (
                  <BaseSelectItem
                    key={item.id}
                    value={item.value}
                    id={item.id}
                    className={itemClassName}
                  >
                    {item.label}
                  </BaseSelectItem>
                );
              } else {
                return (
                  <div
                    key={item.id}
                    className={cn(itemClassName)}
                  >
                    <Checkbox
                      label={item.label}
                      id={item.id}
                      value={item.value}
                      onChange={onChange}
                      checked={value?.includes(item.value)}
                    />
                  </div>
                );
              }
            })}
          </div>
        )}
      </BaseSelectContent>
    </BaseSelect>
  );
};

const BaseSelect = SelectPrimitive.Root;

const BaseSelectGroup = SelectPrimitive.Group;

const BaseSelectValue = SelectPrimitive.Value;

const BaseSelectTrigger = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      'flex w-full items-center justify-between rounded border border-grey-200 bg-white px-2 py-1 text-body data-[state=open]:border data-[state=open]:border-primary-900 hover:border hover:border-primary-500 focus:outline-none focus:border focus:border-primary-900 focus:shadow-field disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1 ',
      className
    )}
    {...props}
    onClick={(e) => e.stopPropagation()}
  >
    {children}
  </SelectPrimitive.Trigger>
));
BaseSelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

const BaseSelectScrollUpButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      'flex cursor-default items-center justify-center py-1',
      className
    )}
    {...props}
  >
    <ChevronUp className="h-4 w-4" />
  </SelectPrimitive.ScrollUpButton>
));
BaseSelectScrollUpButton.displayName =
  SelectPrimitive.ScrollUpButton.displayName;

const BaseSelectScrollDownButton = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      'flex cursor-default items-center justify-center py-1',
      className
    )}
    {...props}
  >
    <ChevronDown className="h-4 w-4" />
  </SelectPrimitive.ScrollDownButton>
));
BaseSelectScrollDownButton.displayName =
  SelectPrimitive.ScrollDownButton.displayName;

const BaseSelectContent = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({ className, children, position = 'popper', ...props }, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        'relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-sm bg-white data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
        position === 'popper' &&
          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
        className
      )}
      position={position}
      {...props}
    >
      <BaseSelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          'w-full flex flex-col',
          position === 'popper' && 'h-[var(--radix-select-trigger-height)]'
        )}
      >
        {children}
      </SelectPrimitive.Viewport>
      <BaseSelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
BaseSelectContent.displayName = SelectPrimitive.Content.displayName;

const BaseSelectLabel = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Label>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn('py-1.5 pl-8 pr-2 text-sm font-semibold', className)}
    {...props}
  />
));
BaseSelectLabel.displayName = SelectPrimitive.Label.displayName;

const BaseSelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      'relative flex w-full cursor-pointer select-none items-center py-2 px-3 text-body outline-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
      className
    )}
    {...props}
  >
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
BaseSelectItem.displayName = SelectPrimitive.Item.displayName;

const BaseSelectSeparator = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn('-mx-1 my-1 h-px bg-slate-100 dark:bg-slate-800', className)}
    {...props}
  />
));
BaseSelectSeparator.displayName = SelectPrimitive.Separator.displayName;

export {
  Select,
  BaseSelect,
  BaseSelectGroup,
  BaseSelectValue,
  BaseSelectTrigger,
  BaseSelectContent,
  BaseSelectLabel,
  BaseSelectItem,
  BaseSelectSeparator,
  BaseSelectScrollUpButton,
  BaseSelectScrollDownButton,
};
