import { X } from 'lucide-react';
import React, { PropsWithChildren, useState } from 'react';
import { CDSSMessage } from '../../../@types/Message';
import { useTranslation } from 'react-i18next';
import SessionRecordPage from './SessiongRecordPage';
interface PopupInlineProps {
  title: string;
  messages: CDSSMessage[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  setHighlightedMessageId: (messageId: string) => void;
}

const PopupInline: React.FC<PropsWithChildren & PopupInlineProps> = ({
  children,
  title,
  messages,
  open,
  onOpenChange,
  setHighlightedMessageId,
}) => {
  const { t } = useTranslation();

  const handleLocateToChat = (messageId: string) => {
    setHighlightedMessageId(messageId);
    // scroll到message的位置
    const messageElement = document.getElementById(messageId);
    if (messageElement) {
      messageElement.scrollIntoView({ behavior: 'smooth' });
    }
    onOpenChange(false);

    // Set a timeout to remove highlight after 5 seconds
    const timeout = setTimeout(() => {
      setHighlightedMessageId('');
    }, 5000);

    // Clear timeout on component unmount
    return () => clearTimeout(timeout);
  };

  if (!open) return null;
  return (
    <React.Fragment>
      {open && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center"
          style={{
            marginTop: '32px', // 为标题留出空间
            background: 'rgba(255, 255, 255, 0.5)', // 半透明背景
          }}
        >
          <div
            className="bg-white rounded-lg flex flex-col overflow-hidden shadow-lg"
            style={{
              width: '70%',
              maxHeight: 'calc(100% - 40px)', // 减去标题高度和一些间距
            }}
          >
            {/* 弹窗标题栏 */}
            <div className="px-4 py-2 bg-primary-500 text-black font-bold flex justify-between items-center">
              <p>{title}</p>
              <button
                className="outline-none"
                onClick={() => onOpenChange(false)}
              >
                <X size={20} />
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="flex-1 overflow-auto">
              <SessionRecordPage
                currentMessages={messages}
                onLocateToChat={handleLocateToChat}
              />
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
};

export default PopupInline;
