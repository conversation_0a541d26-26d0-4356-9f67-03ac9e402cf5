/* eslint-disable jsx-a11y/img-redundant-alt */
/* eslint-disable @next/next/no-img-element */
import React, { useState, useMemo } from 'react';
import { X, Search, Link2, Download, CalendarSearch } from 'lucide-react';
import { CDSSMessage, MediaItem } from '../../../@types/Message';
import { useToast } from '../../../index';
import FileIcon from '../../../components/_ui/FileIcon';
import * as Popover from '@radix-ui/react-popover';
import ReactDatePicker from 'react-datepicker';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';

interface SessionRecordPageProps {
  currentMessages: CDSSMessage[];
  onBack?: () => void;
  onLocateToChat: (messageId: string) => void;
}

type TabType = 'all' | 'files' | 'images-videos' | 'links';

// 判断文件类型的辅助函数
const isImageOrVideo = (filename: string) => {
  if (!filename) return false;
  const ext = filename.split('.').pop()?.toLowerCase();
  const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
  const videoExts = ['mp4', 'webm', 'mov', 'avi', 'wmv', 'flv', 'mkv'];
  return imageExts.includes(ext as string) || videoExts.includes(ext as string);
};

const SessionRecordPage: React.FC<SessionRecordPageProps> = ({
  currentMessages,
  onBack,
  onLocateToChat,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState<TabType>('all');
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    null,
    null,
  ]);
  const [startDate, endDate] = dateRange;
  const { toast } = useToast();
  const { t } = useTranslation();

  // 处理文本复制功能
  const handleCopy = (text: string, event: React.MouseEvent) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({
        title: 'Success',
        description: 'copied to clipboard',
        variant: 'success',
      });
    });
  };
  //文字高亮辅助函数
  const highlightText = (text: string, query: string): React.ReactNode => {
    if (!query || !text) return text;

    const parts = text.split(new RegExp(`(${query})`, 'gi'));

    return parts.map((part, index) => {
      if (part.toLowerCase() === query.toLowerCase()) {
        return (
          <span
            key={index}
            className="text-orange-500 font-medium"
          >
            {part}
          </span>
        );
      }
      return part;
    });
  };

  // 判断下载按钮是否应该禁用的辅助函数
  // 修正后的 isDownloadDisabled 方法 - 带有安全的类型检查
  const isDownloadDisabled = (message: CDSSMessage): boolean => {
    try {
      // 检查是否有 metadata
      if (!message.metadata) return false;

      // 尝试解析 metadata
      const metadata = JSON.parse(message.metadata);

      // 检查 virusScan 状态
      const virusScan = metadata.virusScan;
      const pii = metadata.pii;

      // 如果 virusScan 是 1，则需要检查 virusScanJobResult
      if (virusScan === '1' && pii === '1') {
        // 检查病毒扫描任务是否未完成（状态非2）
        const isVirusScanNotCompleted =
          !message.virusScanJobResult ||
          message.virusScanJobResult.status !== '2';

        // 检查病毒扫描结果是否检测到病毒（安全的类型检查）
        const isVirusDetected =
          message.virusScanJobResult?.status === '2' &&
          message.virusScanJobResult?.result === false; // false 表示检测到病毒

        // 检查PII任务是否未完成（状态非2且非3）
        const isPiiNotCompleted =
          !message.piiJobResult ||
          !['2', '3'].includes(message.piiJobResult.status!);

        // 当任意一个任务未完成或检测到病毒时返回 true
        return isVirusScanNotCompleted || isPiiNotCompleted || isVirusDetected;
      } else if (virusScan === '1' && pii !== '1') {
        // 只需要病毒扫描
        const isVirusScanNotCompleted = !(
          message.virusScanJobResult &&
          message.virusScanJobResult.status === '2'
        );

        // 检查病毒扫描结果是否检测到病毒（安全的类型检查）
        const isVirusDetected =
          message.virusScanJobResult?.status === '2' &&
          message.virusScanJobResult?.result === false; // false 表示检测到病毒

        return isVirusScanNotCompleted || isVirusDetected;
      } else if (virusScan !== '1' && pii === '1') {
        // 只需要PII扫描
        return !(
          (message.piiJobResult && message.piiJobResult.status === '2') ||
          (message.piiJobResult && message.piiJobResult.status === '3')
        );
      } else {
        // 都不需要扫描
        return false;
      }
    } catch (error) {
      console.error('Error checking virus scan status:', error);
      return true; // 出错时默认禁用
    }
  };
  //格式化日期显示
  const formatDate = (timestamp: string | number | Date): string => {
    const date = timestamp instanceof Date ? timestamp : new Date(timestamp);

    // 获取小时和分钟，并确保它们是两位数
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');

    // 获取日期
    const day = date.getDate();

    // 获取月份名称缩写
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    const month = months[date.getMonth()];

    // 获取年份
    const year = date.getFullYear();

    // 返回格式化的日期字符串：16:51, 25 Jul 2025
    return `${hours}:${minutes}, ${day} ${month} ${year}`;
  };

  // 处理文件下载功能
  const handleDownload = async (url: string, filename: string) => {
    try {
      // 创建一个临时的 a 标签元素
      const tempLink = document.createElement('a');

      // 设置 a 标签的 href 和 download 属性
      tempLink.href = url;
      tempLink.download = filename;

      // 将 a 标签添加到 DOM 树中
      document.body.appendChild(tempLink);

      // 触发点击事件
      tempLink.click();

      // 移除 a 标签
      document.body.removeChild(tempLink);

      // 显示下载成功提示
      toast({
        title: 'Success',
        description: 'File download started',
        variant: 'success',
      });
    } catch (error) {
      console.error('下载失败:', error);
      toast({
        title: 'Error',
        description: 'Download failed',
        variant: 'error',
      });
    }
  };

  const filteredMessages = useMemo(() => {
    const messages: CDSSMessage[] = currentMessages;

    // First filter by search query
    const searchFiltered = messages.filter((msg: CDSSMessage) => {
      if (!searchQuery) return true;
      //如果是media+textBody
      if (
        msg.textBody &&
        msg.textBody.length > 0 &&
        msg.medias &&
        msg.medias.length > 0
      ) {
        return (
          msg.textBody.toLowerCase().includes(searchQuery.toLowerCase()) ||
          msg.medias.some((item) =>
            item.filename.toLowerCase().includes(searchQuery.toLowerCase())
          )
        );
      }

      if (msg.textBody && msg.textBody.length > 0) {
        return msg.textBody.toLowerCase().includes(searchQuery.toLowerCase());
      }

      if (msg.medias && msg.medias.length > 0) {
        return msg.medias.some((item) =>
          item.filename.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }
      // 如果以上条件都不满足，则不包含搜索内容
      return false;
    });

    // Then filter by tab selection
    switch (activeTab) {
      case 'files':
        return searchFiltered
          .filter((msg) => {
            // 只有含有媒体且非图片或视频的消息才显示在files标签下
            if (!msg.medias || msg.medias.length === 0) return false;
            // 检查是否所有媒体项都不是图片或视频
            return msg.medias.some(
              (item: MediaItem) => !isImageOrVideo(item.filename || '')
            );
          })
          .filter((msg) => {
            const messageDate = new Date(msg.timestamp);
            return (
              (!startDate || messageDate >= startDate) &&
              (!endDate || messageDate <= endDate)
            );
          });

      case 'images-videos':
        return searchFiltered
          .filter((msg) => {
            // 只有含有图片或视频的消息才显示在images-videos标签下
            if (!msg.medias || msg.medias.length === 0) return false;
            // 检查是否有任何媒体项是图片或视频
            return msg.medias.some((item: MediaItem) =>
              isImageOrVideo(item.filename || '')
            );
          })
          .filter((msg) => {
            const messageDate = new Date(msg.timestamp);
            return (
              (!startDate || messageDate >= startDate) &&
              (!endDate || messageDate <= endDate)
            );
          });

      case 'links':
        return searchFiltered
          .filter((msg) => {
            // 排除只有媒体文件的消息，但允许同时有媒体和链接的消息
            if (!!msg.medias && msg.medias.length > 0 && !msg.textBody)
              return false;
            const urlRegex = /(https?:\/\/[^\s]+)/g;
            return urlRegex.test(msg.textBody);
          })
          .filter((msg) => {
            const messageDate = new Date(msg.timestamp);
            return (
              (!startDate || messageDate >= startDate) &&
              (!endDate || messageDate <= endDate)
            );
          });

      default:
        if (startDate && endDate) {
          return searchFiltered.filter((msg) => {
            const messageDate = new Date(msg.timestamp);
            return (
              (!startDate || messageDate >= startDate) &&
              (!endDate || messageDate <= endDate)
            );
          });
        }
        return searchFiltered;
    }
  }, [currentMessages, searchQuery, activeTab, startDate, endDate]);

  const renderMessageItem = (message: CDSSMessage) => {
    const hasMedia = !!message.medias && message.medias.length > 0;
    const hasText = !!message.textBody;
    const isWhatsAppTemplate = message.type === 'whatsappTemplate';

    // 处理 WhatsApp 模板消息
    if (isWhatsAppTemplate) {
      return (
        <div
          key={message.id}
          className="p-3 hover:bg-gray-50"
        >
          <div className="flex justify-between items-start mb-1">
            <span className="text-sm text-gray-500 truncate max-w-[250px]">
              {message.userName}
            </span>
            <span className="text-sm text-gray-500">
              {formatDate(message.timestamp)}
            </span>
          </div>
          <div className="text-sm italic text-gray-700">
            {highlightText('This is a WhatsApp template message', searchQuery)}
          </div>
          <button
            className="mt-2 text-orange-400 text-sm"
            onClick={() => onLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // 只有媒体文件，没有文本内容
    const isMediaOnly = hasMedia && !hasText;
    // 同时有媒体文件和文本内容
    const isMediaWithText = hasMedia && hasText;
    // 只有链接的文本消息
    const isLink = hasText && /(https?:\/\/[^\s]+)/g.test(message.textBody);

    // 处理只有媒体文件的情况
    if (isMediaOnly) {
      const media = message.medias?.[0] as MediaItem;
      const isImgOrVideo = isImageOrVideo(media.filename || '');
      const downloadDisabled = isDownloadDisabled(message);

      return (
        <div
          key={message.id}
          className="flex items-center p-3 hover:bg-gray-50"
        >
          <div className="flex-shrink-0 w-12 h-12 overflow-hidden">
            <FileIcon
              fileName={media.filename || 'file'}
              size="48"
              className="flex-shrink-0"
              iconClassName="text-gray-600"
            />
          </div>
          <div className="ml-3 flex-grow">
            <div className="text-sm font-medium truncate max-w-[250px]">
              {searchQuery && media.filename
                ? highlightText(media.filename, searchQuery)
                : media.filename || 'Untitled'}
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <span className="truncate max-w-[250px]">{message.userName}</span>
              <button
                onClick={() => handleDownload(media.url, media.filename)}
                className={`flex items-center ml-2 p-1 rounded-full ${
                  downloadDisabled
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
                title={
                  downloadDisabled ? 'Virus scan in progress' : 'Download file'
                }
                disabled={downloadDisabled}
              >
                <Download
                  className={`w-4 h-4 ${
                    downloadDisabled
                      ? 'text-gray-400'
                      : 'text-gray-500 hover:text-orange-400'
                  }`}
                />
              </button>
            </div>
          </div>
          <div className="text-sm text-gray-500 whitespace-nowrap ml-2">
            {formatDate(message.timestamp)}
          </div>
          <button
            className="ml-4 text-orange-400 text-sm whitespace-nowrap"
            onClick={() => onLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // 也修改媒体文件名的高亮显示
    if (isMediaWithText) {
      const media = message.medias?.[0] as MediaItem;
      const downloadDisabled = isDownloadDisabled(message);

      return (
        <div
          key={message.id}
          className="flex items-center p-3 hover:bg-gray-50"
        >
          <div className="flex-shrink-0 w-12 h-12 overflow-hidden">
            <FileIcon
              fileName={media.filename || 'file'}
              size="48"
              className="flex-shrink-0"
              iconClassName="text-gray-600"
            />
          </div>
          <div className="ml-3 flex-grow relative group">
            <div className="text-sm font-medium truncate max-w-[250px]">
              {searchQuery && media.filename
                ? highlightText(media.filename, searchQuery)
                : media.filename}
            </div>
            <div className="text-sm font-light truncate max-w-[250px]">
              {highlightText(message.textBody, searchQuery)}
            </div>

            <div className="flex items-center text-sm text-gray-500">
              <span className="truncate max-w-[250px]">{message.userName}</span>
              <button
                onClick={() => handleDownload(media.url, media.filename)}
                className={`flex items-center ml-2 p-1 rounded-full ${
                  downloadDisabled
                    ? 'cursor-not-allowed opacity-50'
                    : 'hover:bg-gray-100'
                }`}
                title={
                  downloadDisabled ? 'Virus scan in progress' : 'Download file'
                }
                disabled={downloadDisabled}
              >
                <Download
                  className={`w-4 h-4 ${
                    downloadDisabled
                      ? 'text-gray-400'
                      : 'text-gray-500 hover:text-orange-400'
                  }`}
                />
              </button>
            </div>
          </div>
          <div className="text-sm text-gray-500 whitespace-nowrap ml-2">
            {formatDate(message.timestamp)}
          </div>
          <button
            className="ml-4 text-orange-400 text-sm whitespace-nowrap"
            onClick={() => onLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // 处理链接消息
    if (isLink) {
      return (
        <div
          key={message.id}
          className="flex items-center p-3 hover:bg-gray-50 relative"
        >
          <div className="flex-shrink-0 w-8 h-8">
            <Link2 className="w-6 h-6 text-gray-400" />
          </div>
          <div className="ml-3 flex-grow relative group">
            <div
              className="text-sm max-w-xs truncate overflow-hidden whitespace-normal break-words line-clamp-2"
              onClick={(e) => handleCopy(message.textBody, e)}
            >
              {highlightText(message.textBody, searchQuery)}
              <span className="absolute left-0 top-full mt-1 w-max max-w-xs p-2 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 z-10">
                {message.textBody}
              </span>
            </div>
            <div className="text-sm text-gray-500 truncate max-w-[250px]">
              {message.userName}
            </div>
          </div>
          <div className="text-sm text-gray-500 whitespace-nowrap ml-2">
            {formatDate(message.timestamp)}
          </div>
          <button
            className="ml-4 text-orange-400 text-sm whitespace-nowrap"
            onClick={() => onLocateToChat(message.id)}
          >
            Locate to chat
          </button>
        </div>
      );
    }

    // 处理普通文本消息
    return (
      <div
        key={message.id}
        className="p-3 hover:bg-gray-50"
      >
        <div className="flex justify-between items-start mb-1">
          <span className="text-sm text-gray-500 truncate max-w-[250px]">
            {message.userName}
          </span>
          <span className="text-sm text-gray-500">
            {formatDate(message.timestamp)}
          </span>
        </div>
        <div className="text-sm">
          {highlightText(message.textBody, searchQuery)}
        </div>
        <button
          className="mt-2 text-orange-400 text-sm"
          onClick={() => onLocateToChat(message.id)}
        >
          Locate to chat
        </button>
      </div>
    );
  };

  return (
    <div className="flex-1 border border-gray-50">
      <div className="flex items-center justify-center h-[700px] w-full">
        <div className="w-full h-full bg-white rounded-b-2xl shadow-2xl flex flex-col">
          {/* <div className="bg-[#f8a04a] text-white p-3 flex items-center justify-between">
            <span className="text-lg font-bold text-black">Session Record</span>
            <X
              className="w-4 h-4 cursor-pointer text-black"
              onClick={() => {
                if (onBack) onBack();
              }}
            />
          </div> */}

          <div className="sticky top-0 z-10 bg-white">
            <div className="flex items-center justify-between p-3 bg-gray-100">
              <div className="flex-none mr-2">
                <Popover.Root>
                  <Popover.Trigger className="text-gray-400">
                    <CalendarSearch />
                  </Popover.Trigger>
                  <Popover.Portal>
                    <Popover.Content
                      className="mt-2 bg-white rounded-md shadow-lg p-4 min-w-[400px] border border-gray-200 relative z-50 animate-in fade-in-80 data-[side=bottom]:slide-in-from-top-2 data-[side=top]:slide-in-from-bottom-2 data-[side=right]:slide-in-from-left-2 data-[side=left]:slide-in-from-right-2"
                      onOpenAutoFocus={(e) => {
                        e.preventDefault();
                      }}
                    >
                      <div className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white border-l border-t border-gray-200 rotate-45"></div>
                      <div className="mb-2">
                        <h3 className="text-sm font-medium text-gray-700 mb-2">
                          {t('ctint-mf-interaction.details.dateRange')}
                        </h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">
                              {t('ctint-mf-interaction.details.startTime')}
                            </label>
                            <ReactDatePicker
                              selected={startDate}
                              dateFormat={'yyyy-MM-dd HH:mm:ss'}
                              className="block w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
                              onChange={(date) => setDateRange([date, endDate])}
                              onKeyDown={(e) => {
                                e.preventDefault();
                              }}
                              showTimeSelect
                              placeholderText={t(
                                'ctint-mf-interaction.details.selectStartTime'
                              )}
                              autoFocus={false}
                            />
                          </div>
                          <div>
                            <label className="block text-xs text-gray-500 mb-1">
                              {t('ctint-mf-interaction.details.endTime')}
                            </label>
                            <ReactDatePicker
                              selected={endDate}
                              dateFormat={'yyyy-MM-dd HH:mm:ss'}
                              className="block w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
                              onChange={(date) =>
                                setDateRange([startDate, date])
                              }
                              onKeyDown={(e) => {
                                e.preventDefault();
                              }}
                              showTimeSelect
                              placeholderText={t(
                                'ctint-mf-interaction.details.selectEndTime'
                              )}
                              minDate={startDate || undefined}
                              autoFocus={false}
                            />
                          </div>
                        </div>
                      </div>
                      <div className="flex justify-between mt-3">
                        <button
                          className="text-xs text-gray-500 hover:text-gray-700"
                          onClick={() => setDateRange([null, null])}
                        >
                          {t('ctint-mf-interaction.details.clearDate')}
                        </button>
                        <div className="text-xs text-gray-500">
                          {startDate && endDate ? (
                            <span>
                              {dayjs(startDate).format('YYYY-MM-DD HH:mm:ss')} -{' '}
                              {dayjs(endDate).format('YYYY-MM-DD HH:mm:ss')}
                            </span>
                          ) : (
                            <span>
                              {t('ctint-mf-interaction.details.noDateRange')}
                            </span>
                          )}
                        </div>
                      </div>
                    </Popover.Content>
                  </Popover.Portal>
                </Popover.Root>
              </div>
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-2.5 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  className="w-full pl-10 pr-4 py-2 rounded-md border border-gray-200"
                  placeholder="Search..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            <div className="border-b border-gray-200 bg-white">
              <nav className="flex">
                {[
                  { id: 'all', label: 'all' },
                  { id: 'files', label: 'files' },
                  { id: 'images-videos', label: 'Images and videos' },
                  { id: 'links', label: 'links' },
                ].map((tab) => (
                  <button
                    key={tab.id}
                    className={`px-4 py-2 text-sm ${
                      activeTab === tab.id
                        ? 'text-orange-400 border-b-2 border-orange-400'
                        : 'text-gray-500'
                    }`}
                    onClick={() => setActiveTab(tab.id as TabType)}
                  >
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            {filteredMessages.map(renderMessageItem)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SessionRecordPage;
