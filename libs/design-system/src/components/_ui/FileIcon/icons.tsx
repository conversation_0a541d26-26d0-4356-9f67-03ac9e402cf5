// fileIcons.js - 文件图标定义
import React, { ReactNode } from 'react';

// 定义文件图标组件
const createFileIcon = (paths: ReactNode, size?: string, className = '') => {
  if (!size) {
    // 如果没有指定size，创建可以填满父容器的SVG
    return (
      <svg
        width="100%"
        height="100%"
        viewBox="0 0 32 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        style={{ display: 'block' }}
      >
        {paths}
      </svg>
    );
  }

  // 如果指定了size，创建固定尺寸的SVG
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {paths}
    </svg>
  );
};

// 图片文件图标内容
const ImageIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M5.81803 0H21.9504L29.0908 6.85745V29.0909C29.0908 29.8624 28.7843 30.6024 28.2387 31.1479C27.6931 31.6935 26.9532 32 26.1817 32H5.81803C5.04649 32 4.30655 31.6935 3.76099 31.1479C3.21543 30.6024 2.90894 29.8624 2.90894 29.0909V2.90909C2.90894 2.13755 3.21543 1.39761 3.76099 0.852053C4.30655 0.306493 5.04649 0 5.81803 0Z"
        fill="#FFA742"
      />
      <g clipPath="url(#clip0_2658_42043)">
        <path
          opacity="0.01"
          d="M7.90894 10H24.9089V27H7.90894V10Z"
          fill="#202425"
        />
        <path
          d="M23.7756 13.9669C23.7757 13.5948 23.7024 13.2263 23.56 12.8825C23.4177 12.5387 23.209 12.2264 22.9459 11.9632C22.6828 11.7001 22.3704 11.4914 22.0266 11.349C21.6829 11.2066 21.3144 11.1333 20.9423 11.1333C20.5702 11.1333 20.2018 11.2066 19.858 11.349C19.5142 11.4914 19.2019 11.7001 18.9388 11.9632C18.6757 12.2264 18.467 12.5387 18.3246 12.8825C18.1822 13.2263 18.109 13.5948 18.109 13.9669C18.109 14.7183 18.4076 15.4389 18.9389 15.9702C19.4703 16.5015 20.1909 16.8 20.9423 16.8C21.6937 16.8 22.4144 16.5015 22.9457 15.9702C23.4771 15.4389 23.7756 14.7183 23.7756 13.9669ZM15.3584 14.8282C15.3088 14.7415 15.2372 14.6695 15.1508 14.6194C15.0644 14.5693 14.9664 14.5429 14.8665 14.5429C14.7667 14.5429 14.6686 14.5693 14.5822 14.6194C14.4958 14.6695 14.4242 14.7415 14.3746 14.8282L8.55102 25.0186C8.50166 25.1047 8.47581 25.2024 8.47608 25.3017C8.47634 25.401 8.5027 25.4985 8.55252 25.5844C8.60234 25.6703 8.67386 25.7417 8.75992 25.7912C8.84599 25.8408 8.94357 25.8669 9.04288 25.8669H20.6901C20.7894 25.8668 20.8869 25.8407 20.9729 25.7912C21.0588 25.7417 21.1303 25.6704 21.1801 25.5846C21.2299 25.4987 21.2563 25.4013 21.2566 25.3021C21.257 25.2029 21.2312 25.1053 21.182 25.0191L15.3589 14.8276L15.3584 14.8282Z"
          fill="#FFDBB0"
        />
        <path
          d="M19.8838 18.7946C19.9334 18.7079 20.005 18.6359 20.0913 18.5858C20.1777 18.5357 20.2758 18.5093 20.3756 18.5093C20.4755 18.5093 20.5736 18.5357 20.66 18.5858C20.7463 18.6359 20.8179 18.7079 20.8675 18.7946L24.4245 25.0188C24.4737 25.105 24.4994 25.2026 24.4991 25.3018C24.4988 25.401 24.4724 25.4984 24.4226 25.5843C24.3728 25.6701 24.3013 25.7414 24.2153 25.7909C24.1293 25.8404 24.0318 25.8665 23.9326 25.8666H16.8187C16.7194 25.8665 16.622 25.8404 16.536 25.7909C16.45 25.7414 16.3785 25.6701 16.3287 25.5843C16.2789 25.4984 16.2525 25.401 16.2522 25.3018C16.2519 25.2026 16.2776 25.105 16.3268 25.0188L19.8838 18.794V18.7946Z"
          fill="white"
        />
      </g>
      <path
        d="M21.9509 0L29.0913 6.85745H23.4055C23.0197 6.85745 22.6497 6.70421 22.377 6.43143C22.1042 6.15865 21.9509 5.78868 21.9509 5.40291V0Z"
        fill="#FFBF6B"
      />
      <defs>
        <clipPath id="clip0_2658_42043">
          <rect
            width="17"
            height="17"
            fill="white"
            transform="translate(7.90894 10)"
          />
        </clipPath>
      </defs>
    </>,
    size,
    className
  );

// Excel文件图标内容
const ExcelIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M16.6014 2.6665H18.4001V5.14785H27.4854C28.0117 5.10324 28.5397 5.21208 29.0054 5.46119C29.2471 5.94681 29.3552 6.48798 29.3187 7.02919C29.3019 12.4167 29.3019 17.8003 29.3187 23.1798C29.3767 24.0855 29.3419 24.9947 29.2147 25.8932C29.0961 26.5398 28.3374 26.5598 27.8334 26.5772H18.4161V29.3332H16.5334C11.9054 28.4865 7.28409 27.6918 2.66675 26.8638V5.15185C7.31344 4.3225 11.9587 3.50782 16.6014 2.6665Z"
        fill="#207245"
      />
      <path
        d="M18.3306 6.09717H28.3826V25.6038H18.3306V23.7465H20.7679V21.5798H18.3306V20.3278H20.7679V18.1731H18.3306V16.9358H20.7679V14.7652H18.3306V13.5132H20.7679V11.3612H18.3306V10.1092H20.7679V7.95045H18.3306V6.09717Z"
        fill="white"
      />
      <path
        d="M21.9854 7.93848H26.252V10.1198H21.9854L21.9854 7.93848Z"
        fill="#207245"
      />
      <path
        d="M11.6867 10.8238C12.3783 10.7731 13.069 10.7287 13.7587 10.6904C12.9454 12.3953 12.1267 14.0989 11.3027 15.8011C12.1387 17.5451 12.9961 19.2678 13.8254 21.0251L11.6187 20.8838C11.0394 19.6069 10.5319 18.2987 10.0987 16.9651C9.68543 18.2264 9.09343 19.4278 8.61743 20.6664C7.95074 20.6664 7.28409 20.6291 6.61743 20.5998C7.39874 19.0238 8.15609 17.4464 8.96274 15.8931C8.27609 14.2931 7.52543 12.7144 6.81874 11.1171L8.82674 11.0011C9.28143 12.2211 9.77209 13.4224 10.1507 14.6744C10.5734 13.3411 11.1787 12.1011 11.6867 10.8211V10.8238Z"
        fill="white"
      />
      <path d="M21.9854 11.3159H26.252V13.4973H21.9854L21.9854 11.3159ZM21.9854 14.7426H26.252V16.9279H21.9854L21.9854 14.7426ZM21.9854 18.1733H26.252V20.3546H21.985" />
    </>,
    size,
    className
  );

// Word文件图标内容
const WordIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M16.6027 2.6665H18.4147V5.14785C21.6574 5.17585 24.8961 5.11316 28.1321 5.17585C28.2916 5.15674 28.4535 5.17439 28.6051 5.22744C28.7568 5.2805 28.8944 5.36756 29.0072 5.48196C29.1201 5.59636 29.2053 5.73506 29.2563 5.88745C29.3073 6.03984 29.3227 6.20188 29.3014 6.36116C29.3547 12.4732 29.3014 18.5958 29.3334 24.6985C29.3997 25.341 29.2982 25.9897 29.0387 26.5812C28.5678 26.8265 28.0376 26.9351 27.5081 26.8945H18.4147V29.3332H16.5334C11.9107 28.4825 7.28944 27.6918 2.66675 26.8638V5.1545C7.31075 4.3225 11.9574 3.51319 16.6027 2.6665Z"
        fill="#2A5699"
      />
      <path
        d="M18.416 6.08008H28.416V25.9334H18.416V23.4521H26.292V22.2001H18.416V20.6627H26.292V19.4001H18.416V17.8667H26.292V16.6121H18.416V15.0441H26.292V13.8361H18.416V12.2667H26.292V11.0427H18.416V9.48945H26.292V8.2507H18.416V6.08008ZM8.94001 11.5614C9.51736 11.5267 10.0907 11.5014 10.6734 11.4707C11.0734 13.5667 11.488 15.6561 11.928 17.7454C12.2694 15.5907 12.6494 13.4441 13.016 11.2947C13.6213 11.2734 14.2267 11.2387 14.832 11.2001C14.1454 14.2054 13.5467 17.2387 12.7973 20.2254C12.292 20.4921 11.5427 20.2254 10.9307 20.2574C10.5107 18.1987 10.0427 16.1574 9.68267 14.0961C9.32801 16.0961 8.86801 18.0867 8.46267 20.0761C7.87797 20.0451 7.29351 20.0095 6.70936 19.9694C6.20801 17.2401 5.61467 14.5267 5.14136 11.7907C5.66387 11.7654 6.18655 11.7436 6.70936 11.7254C7.02267 13.7014 7.37601 15.6694 7.64936 17.6481C8.06936 15.6187 8.50536 13.5921 8.94136 11.5614H8.94001Z"
        fill="white"
      />
    </>,
    size,
    className
  );

// PDF文件图标内容
const PdfIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M16.6014 2.6665H18.4121V5.14919C21.6454 5.1665 24.8787 5.1145 28.1294 5.1665C28.2884 5.14764 28.4496 5.16509 28.6009 5.21752C28.7521 5.26995 28.8895 5.356 29.0027 5.46921C29.1159 5.58241 29.202 5.71981 29.2544 5.87108C29.3069 6.02234 29.3243 6.18353 29.3054 6.3425C29.3547 12.4545 29.3054 18.5692 29.3347 24.6798C29.4018 25.32 29.3007 25.9667 29.0414 26.5558C28.5755 26.8143 28.0393 26.9175 27.5107 26.8505H18.4174V29.3332H16.5187C11.9067 28.4838 7.28675 27.6918 2.66675 26.8625V5.15454C7.31075 4.32254 11.9574 3.51319 16.6014 2.6665Z"
        fill="#A33639"
      />
      <path
        d="M18.4041 6.07861H28.4041V25.9333H18.4041V23.4506H26.2827V22.2093H18.4041V20.66H26.2827V19.4306H18.4041V17.8666H26.2827V16.6253H18.4041V15.08H26.2827V13.848H18.4041V12.28H26.2827V11.0386H18.4041V9.4853H26.2827V25.044H18.4041V6.07861Z"
        fill="white"
      />
      <path
        d="M19.4653 16.4668H27.344V17.7081H19.4653L19.4653 16.4668ZM19.4653 18.6001H27.344V19.8415H19.4653L19.4653 18.6001ZM19.4653 20.7335H27.344V21.9748H19.4653L19.4653 20.7335ZM18.1853 22.8668H27.344V24.1081H18.1853L18.1853 22.8668Z"
        fill="#A33639"
      />
      <path
        d="M7.36937 10.9346C8.91337 11.0079 10.7814 10.3079 12.0747 11.4906C12.3774 11.8869 12.5948 12.3416 12.7131 12.8261C12.8315 13.3105 12.8482 13.8143 12.7623 14.3055C12.6764 14.7968 12.4896 15.2649 12.2139 15.6804C11.9381 16.096 11.5793 16.4499 11.16 16.7199C10.5003 16.9902 9.78117 17.0821 9.07471 16.9866V20.3986L7.36937 20.2546C7.3427 17.148 7.3427 14.0412 7.36937 10.9346V10.9346Z"
        fill="white"
      />
      <path
        d="M9.04934 12.5024C9.60799 12.4758 10.3027 12.3691 10.6787 12.9024C10.832 13.1961 10.9153 13.5213 10.922 13.8525C10.9287 14.1838 10.8587 14.5121 10.7173 14.8118C10.3933 15.3998 9.66134 15.3531 9.09334 15.4211C9.04455 14.449 9.02988 13.4755 9.04934 12.5024ZM25.7253 12.5584C25.2621 12.5257 24.8212 12.3463 24.4667 12.0464C23.7742 12.2017 23.0969 12.4181 22.4426 12.6931C21.9093 13.6264 21.4227 14.1104 20.996 14.1104C20.9058 14.1137 20.8167 14.0901 20.74 14.0424C20.6518 14.002 20.577 13.9369 20.5248 13.8551C20.4725 13.7733 20.4449 13.6782 20.4453 13.5811C20.4453 13.4278 20.48 12.9931 22.0947 12.3011C22.4617 11.6254 22.7639 10.9164 22.9973 10.1838C22.792 9.77442 22.3507 8.76907 22.656 8.2571C22.7068 8.16451 22.784 8.0891 22.8778 8.04043C22.9716 7.99177 23.0777 7.97204 23.1826 7.98376C23.2684 7.9847 23.3529 8.00496 23.4298 8.04305C23.5066 8.08114 23.5739 8.13607 23.6266 8.20376C23.8467 8.49845 23.8293 9.15976 23.5413 10.1158C23.8167 10.629 24.1774 11.0916 24.608 11.4838C24.9591 11.4116 25.3162 11.3723 25.6747 11.3664C26.4746 11.3838 26.5947 11.7571 26.5773 11.9811C26.5773 12.5691 26.016 12.5691 25.728 12.5691L25.7253 12.5584ZM20.9053 13.6558L20.964 13.6411C21.215 13.5682 21.4345 13.4139 21.588 13.2024C21.321 13.2865 21.0837 13.4452 20.904 13.6598L20.9053 13.6558ZM23.3373 8.43842H23.2813C23.2556 8.43577 23.2298 8.44188 23.208 8.45576C23.145 8.75394 23.1835 9.06462 23.3173 9.33842C23.4228 9.05016 23.4298 8.73509 23.3373 8.44242V8.43842ZM23.2373 10.9358V10.9678L23.22 10.9504C23.0786 11.3238 22.9253 11.6918 22.7493 12.0531L22.7787 12.0358V12.0744C23.1205 11.9435 23.4701 11.8335 23.8253 11.7451L23.808 11.7304H23.8547C23.6235 11.4877 23.4167 11.2229 23.2373 10.9398V10.9358ZM25.7013 11.8411C25.5423 11.8338 25.383 11.8486 25.228 11.8851C25.3953 11.9822 25.5819 12.0414 25.7747 12.0584C25.8966 12.0777 26.0215 12.0634 26.136 12.0171C26.124 11.9491 26.0507 11.8411 25.6893 11.8411L25.7013 11.8411Z"
        fill="#A33639"
      />
    </>,
    size,
    className
  );

// PPT文件图标内容
const PptIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M18.4214 6.36279H28.4214V25.2868H18.4214V6.36279Z"
        fill="white"
      />
      <path
        d="M24 10V13H27C27 11.3481 25.6519 10 24 10Z"
        fill="#E64A19"
      />
      <path
        d="M29.3027 6.67588C29.3215 6.51674 29.3041 6.35543 29.252 6.20392C29.1998 6.05241 29.1142 5.9146 29.0015 5.80072C28.8888 5.68684 28.7518 5.59983 28.6009 5.54613C28.4499 5.49244 28.2888 5.47344 28.1294 5.49057C24.8961 5.43988 21.6534 5.49057 18.4081 5.49057V2.6665H16.6961C12.0174 3.4825 7.34275 4.3265 2.66675 5.1545V26.8638C7.28944 27.6958 11.9147 28.4825 16.5334 29.3332H18.4147V26.2425H27.5081C28.04 26.2956 28.5755 26.1863 29.0441 25.9292C29.3006 25.3369 29.4002 24.6885 29.3334 24.0465C29.2894 18.2678 29.3547 12.4705 29.3014 6.67585L29.3027 6.67588ZM28.4121 25.3106H18.4121V6.39057H28.4121V25.3106Z"
        fill="#E64A19"
      />
      <path
        d="M27 17.9905H20V18.981H27V17.9905Z"
        fill="#E64A19"
      />
      <path
        d="M27 19.9905H20V21.0001H27V19.9905Z"
        fill="#E64A19"
      />
      <path
        d="M20 14.0096C20 12.3525 21.3505 11.0001 23.0054 11.0001V14.0096H26.0108C26.0108 15.6667 24.6603 17.0191 23.0054 17.0191C21.3505 17.0191 20 15.6667 20 14.0096Z"
        fill="#E64A19"
      />
      <path
        d="M7.43338 11.5535C9.08004 11.6348 11.076 10.8868 12.4534 12.1495C12.7735 12.5749 13.0024 13.0617 13.1256 13.5796C13.2488 14.0976 13.2638 14.6353 13.1696 15.1593C13.0753 15.6833 12.8739 16.1821 12.5779 16.6246C12.2819 17.0671 11.8977 17.4437 11.4494 17.7308C10.746 18.0195 9.97891 18.1179 9.22538 18.0162V21.6548L7.43069 21.4948C7.40404 18.1815 7.39469 14.8655 7.43338 11.5535Z"
        fill="white"
      />
      <path
        d="M9.22274 13.2265C9.81874 13.2011 10.5561 13.0931 10.9641 13.6625C11.1262 13.9762 11.2139 14.323 11.2204 14.6761C11.2268 15.0292 11.1519 15.379 11.0014 15.6985C10.6561 16.3265 9.87474 16.2731 9.26808 16.3478C9.2138 15.3083 9.19691 14.2672 9.21743 13.2265H9.22274Z"
        fill="#E64A19"
      />
    </>,
    size,
    className
  );

// 文本文件图标内容
const TextIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M16.6027 2.6665H18.4147V5.14785C21.6574 5.17585 24.8961 5.11316 28.1321 5.17585C28.2916 5.15674 28.4535 5.17439 28.6051 5.22744C28.7568 5.2805 28.8944 5.36756 29.0072 5.48196C29.1201 5.59636 29.2053 5.73506 29.2563 5.88745C29.3073 6.03984 29.3227 6.20188 29.3014 6.36116C29.3547 12.4732 29.3014 18.5958 29.3334 24.6985C29.3997 25.341 29.2982 25.9897 29.0387 26.5812C28.5678 26.8265 28.0376 26.9351 27.5081 26.8945H18.4147V29.3332H16.5334C11.9107 28.4825 7.28944 27.6918 2.66675 26.8638V5.1545C7.31075 4.3225 11.9574 3.51319 16.6027 2.6665Z"
        fill="#F7971D"
      />
      <path
        d="M18.416 6.08008H28.416V25.9334H18.416V23.4521H26.292V22.2001H18.416V20.6627H26.292V19.4001H18.416V17.8667H26.292V16.6121H18.416V15.0441H26.292V13.8361H18.416V12.2667H26.292V11.0427H18.416V9.48945H26.292V8.2507H18.416V6.08008Z"
        fill="white"
      />
      <path
        d="M11.3389 12.0469V22H9.29492V12.0469H11.3389ZM14.4014 12.0469V13.6533H6.28027V12.0469H14.4014Z"
        fill="white"
      />
    </>,
    size,
    className
  );

// 视频文件图标内容
const VideoIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M5.81803 0H21.9504L29.0908 6.85745V29.0909C29.0908 29.8624 28.7843 30.6024 28.2387 31.1479C27.6931 31.6935 26.9532 32 26.1817 32H5.81803C5.04649 32 4.30655 31.6935 3.76099 31.1479C3.21543 30.6024 2.90894 29.8624 2.90894 29.0909V2.90909C2.90894 2.13755 3.21543 1.39761 3.76099 0.852053C4.30655 0.306493 5.04649 0 5.81803 0Z"
        fill="#FFA742"
      />
      <path
        d="M21.9509 0L29.0913 6.85745H23.4055C23.0197 6.85745 22.6497 6.70421 22.377 6.43143C22.1042 6.15865 21.9509 5.78868 21.9509 5.40291V0Z"
        fill="#FFBF6B"
      />
      <path
        d="M11.5987 22.14C10.5409 21.9607 9.69717 21.1588 9.46436 20.1113C9.03369 18.173 9.03369 16.1637 9.46436 14.2254C9.69717 13.1781 10.5409 12.376 11.5987 12.1967C14.4982 11.7053 17.4596 11.7053 20.3591 12.1967C21.4169 12.376 22.2606 13.1779 22.4935 14.2254C22.9241 16.1637 22.9241 18.173 22.4935 20.1113C22.2606 21.1586 21.4169 21.9607 20.3591 22.14C17.4596 22.6314 14.4982 22.6314 11.5987 22.14Z"
        fill="white"
      />
      <path
        d="M17.6977 16.305L17.6703 16.2769C17.0476 15.6364 16.2567 15.1846 15.3887 14.9736C14.7744 14.8241 14.1598 15.2185 14.0396 15.8392L14.0348 15.864C13.868 16.7255 13.868 17.6109 14.0348 18.4724L14.0396 18.4972C14.1598 19.1179 14.7742 19.5122 15.3887 19.3628C16.2567 19.1517 17.0475 18.6999 17.6703 18.0595L17.6977 18.0314C18.1648 17.5507 18.1648 16.7857 17.6977 16.305Z"
        fill="#FFAC4A"
      />
      <path
        d="M15.979 24.6124C14.813 24.6124 13.6467 24.5566 12.4889 24.445C12.1669 24.414 11.9309 24.1276 11.962 23.8056C11.993 23.4835 12.2794 23.2476 12.6014 23.2786C14.8426 23.4946 17.1155 23.4946 19.3567 23.2786C19.6788 23.2476 19.9651 23.4835 19.9962 23.8056C20.0272 24.1276 19.7913 24.414 19.4692 24.445C18.3112 24.5566 17.1452 24.6124 15.9792 24.6124H15.979Z"
        fill="#FFDBB0"
      />
    </>,
    size,
    className
  );

// 音频文件图标内容
const AudioIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M5.81778 0H21.9501L29.0905 6.85745V29.0909C29.0905 29.8624 28.784 30.6024 28.2385 31.1479C27.6929 31.6935 26.953 32 26.1814 32H5.81778C5.04624 32 4.3063 31.6935 3.76074 31.1479C3.21518 30.6024 2.90869 29.8624 2.90869 29.0909V2.90909C2.90869 2.13755 3.21518 1.39761 3.76074 0.852053C4.3063 0.306493 5.04624 0 5.81778 0Z"
        fill="#FFA742"
      />
      <path
        d="M21.9504 0L29.0908 6.85745H23.405C23.0192 6.85745 22.6492 6.70421 22.3765 6.43143C22.1037 6.15865 21.9504 5.78868 21.9504 5.40291V0Z"
        fill="#FFBF6B"
      />
      <path
        d="M12.7269 12.7265C12.6552 12.7265 12.5843 12.7406 12.5181 12.768C12.452 12.7954 12.3918 12.8356 12.3412 12.8862C12.2905 12.9369 12.2504 12.997 12.2229 13.0632C12.1955 13.1294 12.1814 13.2003 12.1814 13.2719V20.9083C12.1814 21.053 12.2389 21.1917 12.3412 21.294C12.4435 21.3963 12.5822 21.4537 12.7269 21.4537C12.8715 21.4537 13.0103 21.3963 13.1126 21.294C13.2149 21.1917 13.2723 21.053 13.2723 20.9083V13.2719C13.2723 13.2003 13.2582 13.1294 13.2308 13.0632C13.2034 12.997 13.1632 12.9369 13.1126 12.8862C13.0619 12.8356 13.0018 12.7954 12.9356 12.768C12.8694 12.7406 12.7985 12.7265 12.7269 12.7265ZM9.45415 14.9083C9.38252 14.9083 9.31159 14.9224 9.24541 14.9498C9.17923 14.9772 9.1191 15.0174 9.06845 15.0681C9.0178 15.1187 8.97762 15.1788 8.95021 15.245C8.9228 15.3112 8.90869 15.3821 8.90869 15.4537V17.9992C8.90869 18.1439 8.96616 18.2826 9.06845 18.3849C9.17074 18.4872 9.30948 18.5447 9.45415 18.5447C9.59881 18.5447 9.73755 18.4872 9.83984 18.3849C9.94213 18.2826 9.9996 18.1439 9.9996 17.9992V15.4537C9.9996 15.3821 9.98549 15.3112 9.95808 15.245C9.93067 15.1788 9.89049 15.1187 9.83984 15.0681C9.78919 15.0174 9.72906 14.9772 9.66288 14.9498C9.5967 14.9224 9.52578 14.9083 9.45415 14.9083ZM19.2723 12.7265C19.2007 12.7265 19.1298 12.7406 19.0636 12.768C18.9974 12.7954 18.9373 12.8356 18.8866 12.8862C18.836 12.9369 18.7958 12.997 18.7684 13.0632C18.741 13.1294 18.7269 13.2003 18.7269 13.2719V20.9083C18.7269 21.053 18.7843 21.1917 18.8866 21.294C18.9889 21.3963 19.1277 21.4537 19.2723 21.4537C19.417 21.4537 19.5557 21.3963 19.658 21.294C19.7603 21.1917 19.8178 21.053 19.8178 20.9083V13.2719C19.8178 13.2003 19.8037 13.1294 19.7763 13.0632C19.7488 12.997 19.7087 12.9369 19.658 12.8862C19.6074 12.8356 19.5472 12.7954 19.4811 12.768C19.4149 12.7406 19.344 12.7265 19.2723 12.7265ZM22.5451 14.9083C22.4734 14.9083 22.4025 14.9224 22.3363 14.9498C22.2701 14.9772 22.21 15.0174 22.1594 15.0681C22.1087 15.1187 22.0685 15.1788 22.0411 15.245C22.0137 15.3112 21.9996 15.3821 21.9996 15.4537V17.9992C21.9996 18.1439 22.0571 18.2826 22.1594 18.3849C22.2617 18.4872 22.4004 18.5447 22.5451 18.5447C22.6897 18.5447 22.8285 18.4872 22.9307 18.3849C23.033 18.2826 23.0905 18.1439 23.0905 17.9992V15.4537C23.0905 15.3821 23.0764 15.3112 23.049 15.245C23.0216 15.1788 22.9814 15.1187 22.9307 15.0681C22.8801 15.0174 22.82 14.9772 22.7538 14.9498C22.6876 14.9224 22.6167 14.9083 22.5451 14.9083ZM15.9996 9.81738C15.928 9.81738 15.857 9.83149 15.7909 9.8589C15.7247 9.88631 15.6646 9.92649 15.6139 9.97714C15.5633 10.0278 15.5231 10.0879 15.4957 10.1541C15.4683 10.2203 15.4541 10.2912 15.4541 10.3628V23.0901C15.4541 23.2348 15.5116 23.3735 15.6139 23.4758C15.7162 23.5781 15.8549 23.6356 15.9996 23.6356C16.1443 23.6356 16.283 23.5781 16.3853 23.4758C16.4876 23.3735 16.5451 23.2348 16.5451 23.0901V10.3628C16.5451 10.2912 16.5309 10.2203 16.5035 10.1541C16.4761 10.0879 16.4359 10.0278 16.3853 9.97714C16.3346 9.92649 16.2745 9.88631 16.2083 9.8589C16.1422 9.83149 16.0712 9.81738 15.9996 9.81738Z"
        fill="white"
      />
    </>,
    size,
    className
  );

// 默认文件图标内容
const DefaultIcon = (className: string, size?: string) =>
  createFileIcon(
    <>
      <path
        d="M5.81803 0H21.9504L29.0908 6.85745V29.0909C29.0908 29.8624 28.7843 30.6024 28.2387 31.1479C27.6931 31.6935 26.9532 32 26.1817 32H5.81803C5.04649 32 4.30655 31.6935 3.76099 31.1479C3.21543 30.6024 2.90894 29.8624 2.90894 29.0909V2.90909C2.90894 2.13755 3.21543 1.39761 3.76099 0.852053C4.30655 0.306493 5.04649 0 5.81803 0Z"
        fill="#FFA742"
      />
      <path
        d="M21.9089 12.7429C21.9089 12.9399 21.8406 13.1288 21.7189 13.2682C21.5973 13.4075 21.4323 13.4857 21.2603 13.4857H10.5576C10.3856 13.4857 10.2206 13.4075 10.0989 13.2682C9.97728 13.1288 9.90894 12.9399 9.90894 12.7429C9.90894 12.5458 9.97728 12.3569 10.0989 12.2176C10.2206 12.0783 10.3856 12 10.5576 12H21.2604C21.4324 12 21.5974 12.0783 21.719 12.2176C21.8406 12.357 21.9089 12.5459 21.9089 12.7429ZM21.9089 18.3144C21.9089 18.5114 21.8406 18.7003 21.7189 18.8397C21.5973 18.979 21.4323 19.0572 21.2603 19.0572H10.5576C10.3856 19.0572 10.2206 18.979 10.0989 18.8397C9.97728 18.7003 9.90894 18.5114 9.90894 18.3144C9.90894 18.1173 9.97728 17.9284 10.0989 17.7891C10.2206 17.6498 10.3856 17.5715 10.5576 17.5715H21.2604C21.4324 17.5715 21.5974 17.6498 21.719 17.7891C21.8406 17.9285 21.9089 18.1174 21.9089 18.3144ZM21.9089 24.2571C21.9089 24.4542 21.8406 24.6431 21.7189 24.7824C21.5973 24.9217 21.4323 25 21.2603 25H10.5576C10.3856 25 10.2206 24.9217 10.0989 24.7824C9.97728 24.6431 9.90894 24.4542 9.90894 24.2571C9.90894 24.0601 9.97728 23.8712 10.0989 23.7318C10.2206 23.5925 10.3856 23.5143 10.5576 23.5143H21.2604C21.4324 23.5143 21.5974 23.5926 21.719 23.7319C21.8406 23.8712 21.9089 24.0601 21.9089 24.2571Z"
        fill="white"
      />
      <path
        d="M21.9509 0L29.0913 6.85745H23.4055C23.0197 6.85745 22.6497 6.70421 22.377 6.43143C22.1042 6.15865 21.9509 5.78868 21.9509 5.40291V0Z"
        fill="#FFBF6B"
      />
    </>,
    size,
    className
  );

// 导出所有图标
export const FileIcons = {
  img: ImageIcon,
  xls: ExcelIcon,
  doc: WordIcon,
  pdf: PdfIcon,
  ppt: PptIcon,
  txt: TextIcon,
  vid: VideoIcon,
  aud: AudioIcon,
  def: DefaultIcon,
};

export default FileIcons;
