import React from 'react';
import FileIcons from './icons';

/**
 * 文件类型的联合类型
 */
type FileType =
  | 'img'
  | 'xls'
  | 'doc'
  | 'pdf'
  | 'ppt'
  | 'txt'
  | 'vid'
  | 'aud'
  | 'def';

/**
 * FileIcon组件属性类型定义
 */
interface FileIconProps {
  /** 文件名 */
  fileName: string;
  /** 图标大小 */
  size?: string;
  /** 容器额外的CSS类名 */
  className?: string;
  /** 图标额外的CSS类名 */
  iconClassName?: string;
  /** 是否显示文件名 */
  showFileName?: boolean;
  /** 点击事件处理函数 */
  onClick?: () => void;
}

/**
 * FileIcon组件 - 根据文件名显示对应类型的图标  不太好用 迟点改掉
 */
const FileIcon: React.FC<FileIconProps> = ({
  fileName,
  size,
  className = '',
  iconClassName = '',
  showFileName,
  onClick,
}) => {
  // 从文件名获取扩展名
  const getFileExtension = (fileName: string): string => {
    if (!fileName) return '';
    return fileName.split('.').pop()?.toLowerCase() || '';
  };

  // 文件类型映射
  const fileTypeMap: Record<FileType, string[]> = {
    // 图片类 (img)
    img: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'tiff', 'ico'],

    // Excel类 (xls)
    xls: ['xls', 'xlsx', 'xlsm', 'csv'],

    // Word文档类 (doc)
    doc: ['doc', 'docx', 'docm'],

    // 文本类 (txt)
    txt: ['txt', 'rtf', 'log', 'md', 'json', 'xml'],

    // PPT类 (ppt)
    ppt: ['ppt', 'pptx', 'pptm'],

    // PDF类 (pdf)
    pdf: ['pdf'],

    // 视频类 (vid)
    vid: ['mp4', 'avi', 'mov', 'wmv', 'mkv', 'flv', 'webm'],

    // 音频类 (aud)
    aud: ['mp3', 'wav', 'ogg', 'flac', 'aac', 'm4a'],

    // 默认类型
    def: [],
  };

  // 根据扩展名获取文件类型
  const getFileType = (extension: string): FileType => {
    for (const [type, extensions] of Object.entries(fileTypeMap)) {
      if (extensions.includes(extension)) {
        return type as FileType;
      }
    }
    return 'def'; // 默认类型
  };

  const extension = getFileExtension(fileName);
  const fileType = getFileType(extension);

  // 使用预定义的图标
  const Icon = FileIcons[fileType](iconClassName, size);

  return (
    <div
      className={`flex items-center ${className}`}
      onClick={onClick}
      style={{ cursor: onClick ? 'pointer' : 'default' }}
    >
      {Icon}
      {showFileName && <span className="ml-2">{fileName}</span>}
    </div>
  );
};

export default FileIcon;
