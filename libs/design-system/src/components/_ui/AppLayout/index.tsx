'use client';
import { useRole, useRouteHandler } from '@cdss-modules/design-system';
import { cn } from '../../../lib/utils';
// import { RoleProvider } from '../../../context/RoleContext';
import Header from '../Header';
import { Roboto } from 'next/font/google';

export type TAppLayoutProps = {
  headerPanel?: React.ReactNode;
  sideNav?: React.ReactNode;
  hasSideNav?: boolean;
  noPadding?: boolean;
} & React.HTMLAttributes<HTMLDivElement>;

const roboto = Roboto({
  weight: ['100', '300', '400', '500', '700', '900'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
});

const AppLayoutBody = ({
  hasSideNav = true,
  sideNav,
  headerPanel,
  noPadding,
  children,
}: TAppLayoutProps) => {
  const { noHeaderMode, noSiderMode, devMode, globalConfig } = useRole();
  const { activePath } = useRouteHandler();
  const isNoPadding =
    noPadding ||
    (activePath ? activePath?.indexOf('ctint-mf-msg') > -1 : false);
  return (
    <div
      className={cn(
        'bg-common-bg flex flex-row items-start overflow-hidden',
        roboto.className
      )}
    >
      <div className="flex flex-col w-full h-screen">
        {(!noHeaderMode || devMode) && (
          <Header
            globalConfig={globalConfig}
            headerPanel={headerPanel}
            hasLogo={true}
          />
        )}
        <section className="flex flex-row">
          {!noSiderMode && hasSideNav && sideNav}
          <main
            className={cn(
              'relative',
              'overflow-hidden',
              !noSiderMode ? 'h-[calc(100vh_-_64px)]' : 'h-screen',
              'flex-1',
              // 'h-[calc(100%_-_64px)] p-4',
              !noSiderMode && 'p-4',
              isNoPadding && 'p-0'
            )}
          >
            {children}
          </main>
        </section>
      </div>
    </div>
  );
};

const AppLayout = (props: TAppLayoutProps) => <AppLayoutBody {...props} />;

export default AppLayout;
