'use client';

import { cn, logout } from '../../../lib/utils';
import Icon from '../Icon';
import Input from '../Input';
import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../DropdownMenu';
import { useRouteHandler } from '../../../context/RouteContext';
import { useObserveElementWidth } from '../../../lib/hooks/useObserveElementWidth';
import { TglobalConfig, useRole } from '../../../context/RoleContext';
import Link from 'next/link';
import Avatar from '../Avatar';
import { RightHeader } from './RightHeader';
import { useInteractionContext } from '../../../context/InteractionContext';

import { useTranslation } from 'react-i18next';

type THeaderProps = {
  hasLogo?: boolean;
  hasSearch?: boolean;
  hasNotification?: boolean;
  hasRightMenu?: boolean;
  headerPanel?: React.ReactNode;
  globalConfig?: TglobalConfig;
} & React.HTMLAttributes<HTMLDivElement>;

const Header = ({
  hasLogo,
  hasSearch,
  hasNotification,
  hasRightMenu,
  headerPanel,
  globalConfig,
}: THeaderProps) => {
  const selected = false;
  const [showSearch, setShowSearch] = useState<boolean>(false);
  const { width, ref } = useObserveElementWidth<HTMLDivElement>();

  const { t } = useTranslation();

  const { basePath } = useRouteHandler();
  const { userConfig } = useRole();
  const user = userConfig;
  return (
    <header className="w-full h-16 bg-common-white flex flex-row justify-between items-center gap-x-4 pl-4 pr-6 py-2">
      {hasLogo && (
        <Link href="/">
          <Icon
            name="logo"
            size={40}
          />
        </Link>
      )}
      {hasSearch ? (
        <div className="flex items-center">
          {showSearch ? (
            <div className="relative flex items-center justify-center transition-all duration-500 ">
              <Input
                placeholder="Placeholder"
                isSearch={true}
                beforeIcon={
                  <Icon
                    className="fill-grey-600 hover:fill-primary-900 group"
                    name="search"
                    size={16}
                  />
                }
                beforeIconFn={() => setShowSearch(!showSearch)}
              />
            </div>
          ) : (
            <button
              onClick={() => setShowSearch(!showSearch)}
              className="transition-all duration-500"
            >
              <Icon
                className="fill-grey-600 hover:fill-primary-900 group "
                name="search"
                size={16}
              />
            </button>
          )}
        </div>
      ) : (
        <div />
      )}
      {headerPanel && <div className="w-full">{headerPanel}</div>}
      <div className="flex flex-row justify-between items-center gap-6">
        {hasNotification && (
          <Icon
            className={cn(
              `fill-white hover:fill-primary-100 group`,
              selected && `fill-primary-500 group`
            )}
            name="bell"
            size={34}
            selected={selected}
          />
        )}

        {globalConfig?.microfrontends?.['header-type'] === 'dropdown' ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div
                ref={ref}
                className="flex flex-row items-center justify-end gap-2 min-w-[180px]"
              >
                <Avatar
                  text={
                    localStorage.getItem('userName')
                      ? localStorage.getItem('userName') ?? 'NULL'
                      : user?.userName?.[0] ?? 'TR'
                  }
                  className="border-2 border-status-success"
                />
                <div>
                  {localStorage.getItem('userName')
                    ? localStorage.getItem('userName')
                    : user?.userName}
                </div>
                <Icon
                  name="dropdown-arrow"
                  size={8}
                />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              // align = "start" | "center" | "end", default center
              style={{ width: width, minWidth: '190px' }}
            >
              <DropdownMenuGroup>
                <DropdownMenuItem asChild>
                  <Link
                    href="/"
                    className="w-full"
                  >
                    {t('global.home')}
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <button
                    className="w-full text-left"
                    onClick={() => {
                      logout(basePath);
                    }}
                  >
                    {t('global.logout')}
                  </button>
                </DropdownMenuItem>
              </DropdownMenuGroup>
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <RightHeader />
        )}

        {hasRightMenu && (
          <Icon
            className="fill-white hover:fill-primary-100 group"
            name="rightbar"
            size={34}
          />
        )}
      </div>
    </header>
  );
};

export default Header;
