import {
  But<PERSON>,
  toast,
  Tooltip,
  useR<PERSON>,
  useRout<PERSON><PERSON><PERSON><PERSON>,
  useToast,
} from '@cdss-modules/design-system';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import { cn, logout } from '@cdss-modules/design-system/lib/utils';
import { useEffect, useMemo, useRef, useState } from 'react';
import _ from 'lodash';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { useOpenStationContext } from '@cdss-modules/design-system/context/StationContext';
import { TStation } from '@cdss-modules/design-system/@types/station';
import {
  TbarProvider,
  useTbarContext,
} from '@cdss-modules/design-system/context/TBarContext';
import { Popup, PopupContent } from '../Popup';

import Directory from '../Directory';
import WebRtcBlock from '../../_other/WebRtcBlock';
import { useTranslation } from 'react-i18next';
import { fireUpdateGreetingMessage, GreetingMessageItem } from '../../../lib/api';

type TAgentStatusProps = {
  id: string;
  type: string;
  languageLabelEnUs: string;
  systemPresence: string;
  divisionId: string;
  deactivated: boolean;
  selfUri: string;
};
type Tobject = {
  [x: string]: any[];
};
type TSelectStatus = {
  currentStateId: string;
  agentStatus: string;
  agentStatusList: TAgentStatusProps[];
  onChangeAgentColor: (color: string) => void;
  onChangeAgentStatus: (statusId: string, status: string) => void;
  selectStatus?: boolean
};
type TSelectStation = {
  stationHandler: any;
  station?: TStation;
  open?: boolean;
  setOpen: (v: boolean) => void;
  updateCurrentStation: (stationId: string) => void;
  deleteCurrentStation: (stationId: string) => void;
};
export const SelectStatus = ({
  agentStatusList,
  currentStateId,
  agentStatus,
  onChangeAgentStatus,
  onChangeAgentColor,
  selectStatus
}: TSelectStatus) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [searchValue, setSearchValue] = useState('');
  const { userConfig, refreshUserConfig } = useRole();
  const { basePath } = useRouteHandler();
  const {t} = useTranslation();
  const [open, setOpen] = useState(false);

  const [openStatus, setOpenStatus] = useState(false);
  const [openStatusItem, setOpenStatusItem] = useState(false);
  const [currentStatus, setCurrentStatus] = useState<string | undefined>('');
  const [newAgentStatusList, setNewAgentStatusList] = useState<
    TAgentStatusProps[]
  >([]);
  const [showGreetingPopup, setShowGreetingPopup] = useState(false);
  const [englishContent, setEnglishContent] = useState('');
  const [chineseContent, setChineseContent] = useState('');

  useEffect(() => {
    if (userConfig) {
      setEnglishContent(userConfig?.authenticationInfo?.greetingEn || '');
      setChineseContent(userConfig?.authenticationInfo?.greeting || '');
    }
  }, [userConfig]);

  const selectGroup = (statusId: string, status: string, color: string) => {
    setOpen(false);
    setOpenStatus(false);
    setOpenStatusItem(false);
    onChangeAgentStatus(statusId, status);
    onChangeAgentColor(color);
  };
  useEffect(() => {
    setCurrentStatus(currentStateId);
  }, [currentStateId]);
  useEffect(() => {
    setNewAgentStatusList(agentStatusList);
  }, [agentStatusList]);
  useEffect(() => {
    if (!searchValue || searchValue == '') {
      setNewAgentStatusList(agentStatusList);
    } else {
      setNewAgentStatusList(
        agentStatusList?.filter((item: TAgentStatusProps) =>
          item?.languageLabelEnUs.toLowerCase().includes(searchValue)
        ) || []
      );
    }
  }, [searchValue]);
  const formattedList = useMemo(() => {
    const groupOrder = [
      'Available',
      'Away',
      'Break',
      'Meal',
      'Training',
      'Busy',
      'Meeting',
    ];
    const orderMap = groupOrder.reduce((acc: any, item, index) => {
      acc[item] = index;
      return acc;
    }, {});

    const orderedList = _.orderBy(
      (newAgentStatusList.length > 0 && newAgentStatusList) || agentStatusList,
      [
        (item) => orderMap[item.systemPresence],
        (item) => (groupOrder.includes(item?.languageLabelEnUs) ? 0 : 1),
        'languageLabelEnUs',
      ],
      'asc'
    );

    const groupedList = _.groupBy(orderedList, 'systemPresence');

    return {
      ...groupedList,
    };
  }, [newAgentStatusList, agentStatusList, searchValue]);

  return (
    <div className="inline-flex relative items-center justify-center">
      <DropdownMenu
        open={open}
        onOpenChange={(open) => setOpen(open)}
      >
        <DropdownMenuTrigger
          className="hover:text-primary text-[#5B5E63] flex items-center justify-center gap-x-1"
          onClick={() => {}}
        >
          <div className="w-10 h-10 flex items-center justify-center rounded-full border-2 border-[#1CC500]">
            <strong>
              {userConfig?.userName
                ? (
                    userConfig?.userName?.[0] + userConfig?.userName?.[1]
                  ).toUpperCase()
                : 'TR'}
            </strong>
          </div>
          <Icon
            name="dropdown-arrow"
            size={8}
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative py-2 bg-white">
            <div className="p-2">Hi {userConfig?.userName || 'TR'}</div>
            {selectStatus&&<DropdownMenuSub
              open={openStatus}
              onOpenChange={(v) => {
                setOpenStatus(v);
              }}
            >
              <DropdownMenuSubTrigger className="text-remark">
                <div className="w-full flex justify-between items-center">
                  {agentStatus || 'Status'}
                  <div className="text-[#636363]">
                    <Icon
                      name="back"
                      className="-rotate-180"
                      size={11}
                    />
                  </div>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <div className="relative p-2">
                    <div className="relative p-2">
                      <Input
                        ref={inputRef}
                        isSearch={true}
                        afterIcon={<Icon name="search" />}
                        afterIconFn={() => console.log('click after')}
                        onChange={(value: any) => setSearchValue(value)}
                        allowClear
                        size="s"
                        placeholder="Search..."
                      />
                    </div>
                    {Object.entries(formattedList).map(([key, list], i) => {
                      return (
                        <DropdownMenuSub key={key || i}>
                          <DropdownMenuSubTrigger className="text-remark">
                            <div
                              className="w-full flex justify-between items-center"
                              key={`agent-category-${key}`}
                            >
                              <div className="flex gap-1 pt-1 pb-1 items-center w-full">
                                <div
                                  style={{
                                    backgroundColor:
                                      AGENT_STATUS_COLOR_MAP[
                                        key?.toLowerCase()
                                      ],
                                  }}
                                  className={`size-[10px] rounded-full`}
                                />
                                {key}
                              </div>
                              <div className="text-[#636363]">
                                <Icon
                                  name="back"
                                  className="-rotate-180"
                                  size={11}
                                />
                              </div>
                            </div>
                          </DropdownMenuSubTrigger>
                          <DropdownMenuPortal>
                            <DropdownMenuSubContent>
                              {(list || [])?.map((item, i) => {
                                return (
                                  <Pill
                                    variant="person"
                                    onClick={() => {
                                      selectGroup(
                                        item.id,
                                        item.languageLabelEnUs,
                                        AGENT_STATUS_COLOR_MAP[
                                          item.systemPresence?.toLowerCase()
                                        ]
                                      );
                                    }}
                                    key={item.id || i}
                                    className={cn(
                                      'border-none  w-full',
                                      (currentStatus || currentStateId) ==
                                        item.id && '!bg-primary-100'
                                    )}
                                    disabled={
                                      (currentStatus || currentStateId) ==
                                      item.id
                                    }
                                  >
                                    <div className="flex gap-1 pt-1 pb-1 items-center w-full">
                                      <div
                                        style={{
                                          backgroundColor:
                                            AGENT_STATUS_COLOR_MAP[
                                              item.systemPresence?.toLowerCase()
                                            ],
                                        }}
                                        className={`size-[10px] rounded-full`}
                                      />
                                      <div className="text-remark">
                                        {item?.languageLabelEnUs}
                                      </div>
                                    </div>
                                  </Pill>
                                );
                              })}
                            </DropdownMenuSubContent>
                          </DropdownMenuPortal>
                        </DropdownMenuSub>
                      );
                    })}
                  </div>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>}
            {/* Agent Greeting */}
            <div 
              className='p-2 hover:bg-primary-200 hover:font-bold' 
              onClick={() => {
                setOpen(false);
                setShowGreetingPopup(true);
              }}
            >
              <button>
                {t('global.greeting')}
              </button>
            </div>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
      {/* Greeting Popup */}
      <Popup open={showGreetingPopup} onOpenChange={setShowGreetingPopup}>
        <PopupContent
          title={t('global.greetingMessage')}
          className="w-[640px] max-w-[90vw] rounded-2xl shadow-[0px_0px_32px_0px_rgba(0,0,0,0.16)]"
          headerClassName="bg-[#FFAC4A]"
        >
          {/* Form Content */}
          <div className="px-12 py-8 space-y-8">
            {/* English Field */}
            <div className="flex flex-col gap-1">
              <label className="text-black font-bold text-[15px] leading-[2.2em] font-roboto">
                {t('global.english')}
              </label>
              <textarea
                className="w-full h-[45px] border border-[#DEDEDE] rounded bg-white px-2 py-1 text-sm leading-[1.171875em] font-roboto resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={t('global.inputContent')}
                value={englishContent}
                onChange={(e) => setEnglishContent(e.target.value)}
              />
            </div>

            {/* Chinese (Cantonese) Field */}
            <div className="flex flex-col gap-1">
              <label className="text-black font-bold text-sm leading-[1.171875em] font-roboto">
                {t('global.chineseCantonese')}
              </label>
              <textarea
                className="w-full h-[45px] border border-[#DEDEDE] rounded bg-white px-2 py-1 text-sm leading-[1.171875em] font-roboto resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder={t('global.inputContent')}
                value={chineseContent}
                onChange={(e) => setChineseContent(e.target.value)}
              />
            </div>
          </div>

          {/* Footer Buttons */}
          <div className="flex gap-[5px] px-12 pb-5 justify-end">
            <button
              className="flex items-center justify-center gap-2 px-2 py-1 border border-black rounded bg-white"
              onClick={() => setShowGreetingPopup(false)}
            >
              <span className="text-black text-[15px] leading-[1.4em] font-roboto w-[46px]">{t('global.cancel')}</span>
            </button>
            <button
              className="flex items-center justify-center gap-2 px-4 py-3 rounded bg-black w-[83px]"
              onClick={async () => {
                try {
                  // 构建API请求数据
                  const greetingMessages: GreetingMessageItem[] = [
                    {
                      language: 'zh-HK',
                      message: chineseContent.trim(),
                    },
                    {
                      language: 'en',
                      message: englishContent.trim(),
                    },
                  ];

                  // 调用API更新greeting message
                  const response = await fireUpdateGreetingMessage(greetingMessages, basePath);

                  if (response.data.isSuccess) {
                    console.log('Greeting message updated successfully');
                    toast({
                      title: t('global.success'),
                      description: t('global.greetingUpdateSuccess'),
                      variant: 'success',
                    });
                  } else {
                    console.error('Failed to update greeting message:', response.data.error);
                    toast({
                      title: t('global.error'),
                      description: t('global.greetingUpdateFailed'),
                      variant: 'error',
                    });
                  }
                } catch (error) {
                  console.error('Error updating greeting message:', error);
                  toast({
                    title: t('global.error'),
                    description: t('global.greetingUpdateError'),
                    variant: 'error',
                  });
                } finally {
                  setShowGreetingPopup(false);
                  refreshUserConfig && refreshUserConfig();
                }
              }}
            >
              <span className="text-white text-sm leading-[1.4em] font-roboto">{t('global.confirm')}</span>
            </button>
          </div>
        </PopupContent>
      </Popup>
    </div>
  );
};
export const SelectStation = ({
  stationHandler,
  station,
  updateCurrentStation,
  deleteCurrentStation,
}: TSelectStation) => {
  const { open, setOpen, openOption, setOpenOption } = useOpenStationContext();

  const inputRef = useRef<HTMLInputElement | null>(null);
  const [currentItem, setCurrentItem] = useState<string | null>(null);
  const [currentStation, setCurrentStation] = useState<string>('');
  const [stations, setStations] = useState<any[]>([]);
  useEffect(() => {
    if (!open) {
      setCurrentItem(null);
    } else {
      setCurrentItem('Stations');
    }
  }, [open]);
  useEffect(() => {
    const newStations = stationHandler?.data?.data?.reduce(
      (a: any[], item: any) => {
        if (station?.id == item?.id) {
          return [item, ...a];
        }
        return [...a, item];
      },
      []
    );

    setStations(newStations);
  }, [stationHandler]);
  const filteredList = useMemo(() => {
    return (
      stations?.reduce((a: any[], item: any) => {
        if (station?.id == item?.id) {
          return [item, ...a];
        }
        return [...a, item];
      }, []) || []
    );
  }, [stations, station]);
  const searchStations = (keyword: string) => {
    if (!keyword) {
      const newStations = stationHandler?.data?.data?.reduce(
        (acc: any, item: any) => {
          return [...acc, item];
        },
        []
      );
      setStations(newStations);
    } else {
      setStations(
        stationHandler?.data?.data?.filter((item: any) => {
          return item?.name?.includes(keyword);
        })
      );
    }
  };
  useEffect(() => {
    const newStations = stationHandler?.data?.data?.reduce(
      (a: any[], item: any) => {
        if (currentStation == item?.id) {
          return [item, ...a];
        }
        return [...a, item];
      },
      []
    );
    setStations(newStations);
  }, [open]);
  useEffect(() => {
    setCurrentStation(station?.id || '');
  }, [station]);

  return (
    <div className="inline-flex relative items-center justify-center">
      <DropdownMenu
        open={openOption}
        onOpenChange={(v) => {
          if (v) {
            stationHandler?.refetch();
          }
          setOpenOption(v);
          // setOpen(v)
        }}
      >
        <DropdownMenuTrigger
          className="hover:text-primary text-[#5B5E63] flex items-center justify-center gap-x-1"
          onClick={() => {}}
        >
          <Tooltip
            content="setting"
            trigger={
              <Icon
                name="setting"
                size={18}
              />
            }
          />
        </DropdownMenuTrigger>
        <DropdownMenuContent
          side="top"
          arrowPadding={2}
          className="overflow-visible"
        >
          <div className="relative py-2 bg-white">
            <DropdownMenuSub
              open={open}
              onOpenChange={(v) => {
                setOpen(v);
              }}
            >
              <DropdownMenuSubTrigger className="text-remark">
                <div className="w-full flex justify-between items-center">
                  {station?.name || 'Stations'}
                  <div className="text-[#636363]">
                    <Icon
                      name="back"
                      className="-rotate-180"
                      size={11}
                    />
                  </div>
                </div>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent>
                  <div className="relative p-2">
                    <Input
                      ref={inputRef}
                      isSearch={true}
                      afterIcon={<Icon name="search" />}
                      afterIconFn={() => console.log('click after')}
                      onChange={(value: any) => searchStations(value)}
                      allowClear
                      size="s"
                      placeholder="Search..."
                    />
                  </div>
                  <Pill
                    variant="person"
                    onClick={() => {
                      setOpen(false);
                      setOpenOption(false);
                      deleteCurrentStation(currentStation || '');
                      setCurrentStation('');
                    }}
                    className={cn(
                      'border-none w-full',
                      currentStation == '' && '!bg-primary-100'
                    )}
                    disabled={currentStation == ''}
                  >
                    <div className="flex gap-1 items-center w-full">
                      <div
                        className={cn(`size-[10px] rounded-full bg-grey-300`)}
                      />
                      <div className="text-remark">Deselect Phone</div>
                    </div>
                  </Pill>
                  <div className="relative bg-white h-[290px] overflow-y-auto">
                    {(stations || filteredList)?.map((item) => (
                      <Pill
                        variant="person"
                        onClick={() => {
                          setOpen(false);
                          setOpenOption(false);
                          updateCurrentStation(item?.id);
                          setCurrentStation(item);
                        }}
                        key={item.id}
                        className={cn(
                          'border-none  w-full',
                          currentStation == item.id && '!bg-primary-100'
                        )}
                        disabled={
                          currentStation == item.id ||
                          item?.stationStatus === 'ASSOCIATED'
                        }
                      >
                        <div className="flex gap-1 pt-1 pb-1 items-center w-full">
                          <div
                            className={cn(
                              `size-[10px] rounded-full bg-status-success`,
                              item?.stationStatus === 'ASSOCIATED' &&
                                'bg-status-danger'
                            )}
                          />
                          <div className="text-remark">{item?.name}</div>
                        </div>
                      </Pill>
                    ))}
                  </div>
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
export const RightHeaderBody = () => {
  const { globalConfig } = useRole();
  const { open, setOpen, setOpenOption } = useOpenStationContext();
  const [openDirectory, setOpenDirectory] = useState(false);
  const { basePath } = useRouteHandler();
  const {
    statusContext: {
      agentStatus,
      isOnQueue,
      agentStatusList,
      currentStateId,
      onChangeAgentStatus,
      onChangeAgentColor,
      onChangeIsOnQueue,
    },
    stationContext: {
      stationHandler,
      station,
      updateCurrentStation,
      deleteCurrentStation,
    },
  } = useTbarContext();

  const microfrontendsConfig = globalConfig?.microfrontends
  const outboundCall = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableOutboundCall"];
  const selectStation = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableSelectStation"];
  const selectStatus = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableSelectStatus"];
  const onQueueSwitch = microfrontendsConfig?.["ctint-mf-cdss"]?.["functionConfig"]?.["enableOnQueueSwitch"];

  const { dismiss } = useToast();
  const handleToastClose = () => {
    dismiss();
  };
  const selectStationTip = () => {
    return toast({
      variant: 'error',
      title: 'Warning',
      description: (
        <div className="flex flex-col w-full gap-2">
          <div>You have no phone selected and will not receive calls.</div>
          <div className="flex w-full gap-2">
            <Button
              variant={'primary'}
              size={'s'}
              onClick={() => {
                setOpen(true);
                setOpenOption(true);
                handleToastClose();
                stationHandler?.refetch();
              }}
            >
              Select Phone
            </Button>
            <Button
              variant={'blank'}
              size={'s'}
              onClick={() => {
                onChangeIsOnQueue();
                handleToastClose();
              }}
            >
              Continue without phone
            </Button>
          </div>
        </div>
      ),
    });
  };
  const handleStationChange = () => {
    if (isOnQueue) {
      onChangeIsOnQueue();
    } else {
      if (station?.name === '') {
        selectStationTip();
      } else {
        onChangeIsOnQueue();
      }
    }
  };
  useEffect(() => {
    setOpen(open);
  }, [open]);
  return (
    <div className="flex gap-x-6 items-center">
      {station?.type == 'inin_webrtc_softphone' && (
        <WebRtcBlock className="hidden" />
      )}

      {outboundCall&&<Directory
        setOpenSeletedStation={setOpen}
        open={openDirectory}
        setOpenDirectory={setOpenDirectory}
      >
        <Tooltip
          content="secondCall"
          trigger={
            <Icon
              name="secondCall"
              size={18}
            />
          }
        />
      </Directory>}
      {selectStation&&<SelectStation
        stationHandler={stationHandler}
        open={open}
        setOpen={setOpen}
        station={station}
        updateCurrentStation={updateCurrentStation}
        deleteCurrentStation={deleteCurrentStation}
        // isWebRTC={isWebRTC}
        // updateIsWebRTC={updateIsWebRTC}
      />}
    
        <SelectStatus
            agentStatusList={agentStatusList}
            currentStateId={currentStateId}
            onChangeAgentStatus={onChangeAgentStatus}
            onChangeAgentColor={onChangeAgentColor}
            agentStatus={agentStatus || ''}
            selectStatus={selectStatus}
        />
      {selectStatus&&<>
        <div className="h-[25px] border-r-2 border-black"></div>
        <div className="w-max">{agentStatus}</div>
      </>}
      

      {onQueueSwitch&&<div className="flex items-center gap-2">
        <Switch
          id="queue"
          size="s"
          activeColor="green"
          checked={isOnQueue}
          onChange={handleStationChange}
        />
        <label
          className={`${isOnQueue ? 'text-status-success' : 'text-grey-500'} w-max`}
        >
          {isOnQueue ? 'On Queue' : 'Off Queue'}
        </label>
      </div>}
      <Tooltip
        content="logout"
        trigger={
          <button
            className="hover:text-primary text-[#5B5E63]"
            onClick={() => {
              logout(basePath);
            }}
          >
            <Icon
              name="logout"
              size={18}
            />
          </button>
        }
      />
    </div>
  );
};
export const RightHeader = () => {
  return (
    <TbarProvider>
      <RightHeaderBody />
    </TbarProvider>
  );
};
