import { DropdownMenuPortal, DropdownMenuSub, DropdownMenuSubContent, DropdownMenuSubTrigger } from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import React, { memo } from 'react';

interface OnBehalfOfProps {
    triggerClassName?: string;
    children: React.ReactNode;
    open?: boolean;
    disabled?: boolean;
    content: React.ReactNode;
    title?: string;
    onOpenChange: (v: boolean) => void;
    side?: "top" | "bottom" | "left" | "right" | undefined;
}

export const OnBehalfOf: React.FC<OnBehalfOfProps> = memo(({
    triggerClassName,
    children,
    open,
    disabled,
    content,
    title,
    onOpenChange,
    side = "top",
}) => {
    return (
        <>
            <DropdownMenuSub open={open} onOpenChange={(v)=>{
                onOpenChange(v)
            }}>
                <DropdownMenuSubTrigger className="text-remark p-0 rounded-full" disabled={disabled}>
                    {children}
                </DropdownMenuSubTrigger>
                <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                        <div className="relative p-2">
                            <div className="pb-3">
                                <strong>{title}</strong>
                            </div>
                            {content}
                        </div>
                    </DropdownMenuSubContent>
                </DropdownMenuPortal>
            </DropdownMenuSub>
        </>
    );
});
