import { cn, removePhoneNumberSpaces, validateGlobalPhoneNumber } from "@cdss-modules/design-system/lib/utils";
import { Button, toast, useToast } from "@cdss-modules/design-system";
import NewConversationContainer from './NewConversation'
import { useState } from "react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@cdss-modules/design-system/components/_ui/DropdownMenu";
import Icon from "@cdss-modules/design-system/components/_ui/Icon";
import { useTbarContext } from "@cdss-modules/design-system/context/TBarContext";


export const CallBack = (
    {
        className,
        triggerClassName,
        iconClassName,
        number,
        onCallBack,
        userId,
        buttonText = "Call Back Now"
    }:
        {
            className?: string,
            triggerClassName?: string,
            iconClassName?: string,
            number?: string,
            userId?: string,
            buttonText?: string,
            onCallBack?: (res: any) => void
        }
) => {
    const {
        stationContext: {
            station,
            stationHandler
        }
    } = useTbarContext();

    const [showModal, setShowModal] = useState(false);
    
    const disabled = !number;
    return (

        <>
            <DropdownMenu
                open={showModal}
                onOpenChange={(v) => {
                    setShowModal(v)
                }}
            >
                <DropdownMenuTrigger
                    className={cn("hover:text-primary text-[#5B5E63] ", triggerClassName, disabled && "cursor-no-drop")}
                    onClick={() => { }}
                    disabled={disabled}
                >
                    <div className={cn(
                        'bg-[#1CC500] text-[#ffffff] py-2 flex justify-center items-center rounded-lg font-bold w-full'
                        , disabled ? buttonText==""?"text-[#ACACAC] bg-inherit":"bg-grey-400": "hover:bg-primary-300 "+iconClassName ,buttonText===""&&"px-2")}>
                        <Icon
                            name="phone"
                            size={16}
                        // className="!fill-white"
                        />
                        {buttonText&&buttonText!==""&&<strong className="pl-2">{buttonText}</strong>}
                    </div>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                    side="top"
                    arrowPadding={2}
                    className="overflow-visible"
                >
                    <div className="relative p-2">
                        <div className="pb-3">
                            <strong>On behalf of</strong>
                        </div>
                        <NewConversationContainer
                            returnEvent={() => {
                                setShowModal(false)
                            }}
                            phoneNumber={number ? { phoneNumber: removePhoneNumberSpaces(number) } : { callUserId: userId }}
                            station={station}
                            onCallBack={(res) => {
                                onCallBack && onCallBack(res)
                            }}
                        />
                    </div>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
};