import Input from '@cdss-modules/design-system/components/_ui/Input';
import {
    TItemProps,
} from '@cdss-modules/design-system/components/_ui/Select';
import React, { useEffect, useState } from 'react';
import { Button, toast, useToast } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';
import { useOpenStationContext } from '@cdss-modules/design-system/context/StationContext';
interface TNewConversationContainer {
    returnEvent: (isExpanded: boolean) => void;
    phoneNumber?: any;
    station?: any;
    stationHandler?: any;
    onCallBack?: (res: any) => void;
}
const NewConversationContainer = ({
    returnEvent,
    phoneNumber, station,
    stationHandler,
    onCallBack
}: TNewConversationContainer) => {
    // const [queue, setQueue] = useState('');
    const [selected, setSelected] = useState<TItemProps | null>(null);
    const { call } = useCallControl();
    const { getWorkgroupByUserHandle } = useGetworkGroupOrUser();
    const { setOpen, setOpenOption } = useOpenStationContext();
    const [inputValue, setInputValue] = useState('');
    const [data, setData] = useState<TItemProps[] | null>(null);
    const { dismiss } = useToast();
    const handleToastClose = () => {
        dismiss();
    };
    const getQueuesItemForSelect = (): TItemProps[] => {
        const queueSelectData: TItemProps[] = [];
        getWorkgroupByUserHandle?.data?.map((q: any) => {
            const itemProps: TItemProps = {
                id: q.id,
                label: q.name,
                value: q.id,
            };
            queueSelectData.push(itemProps);
        });

        if (station) {
            return [{ id: station?.id, label: "My Direct Line(" + station?.name + ")", value: station?.id, isNotQueue: true }, ...queueSelectData]
        }
        return queueSelectData;
    };
    const makeCallByDifferentKey = (queue: TItemProps) => {
        if (queue) {
            if (queue?.isNotQueue) {
                return {
                    ...phoneNumber,
                };
            }
            return {
                ...phoneNumber,
                callFromQueueId: queue?.id,
            };
        }
        return {}
    };
    const selectStationTip = () => {
        return toast({
            variant: 'error',
            title: 'Warning',
            description: (
                <div className="flex flex-col w-full gap-2">
                    <div>You have no phone selected and will not receive calls.</div>
                    <div className="flex w-full gap-2">
                        <Button
                            variant={'primary'}
                            size={'s'}
                            onClick={() => {
                                setOpenOption(true)
                                setOpen(true)
                                returnEvent(false)
                                // setOpenSeletedStation && setOpenSeletedStation(true)
                                handleToastClose();
                                stationHandler?.refetch();
                            }}
                        >
                            Select Phone
                        </Button>
                        <Button
                            variant={'blank'}
                            size={'s'}
                            onClick={() => {
                                handleToastClose();
                            }}
                        >
                            Close
                        </Button>
                    </div>
                </div>
            ),
        });
    };
    const handleDial = (queue: TItemProps) => {
        if (
            queue
        ) {
            call(makeCallByDifferentKey(queue)).then((res: any) => {
                onCallBack && onCallBack(res?.conversationId)
            })
            return
        } else {
            return toast({
                variant: 'error',
                title: 'Error',
                description:
                    'Invalid phone number format. Please use one of the following formats: +852 6xxxxxxx, +8526xxxxxxx, or 6xxxxxxx.',
            });
        }
    };
    const submit = (queue: TItemProps) => {
        if (!station || station?.name === "") {
            selectStationTip()
            return
        }
        handleDial(queue)
        // setSelected(null)
        setData(null)
        returnEvent(false)
    };
    useEffect(() => {
        const d = getQueuesItemForSelect()?.filter((q: any) => {
            return q?.label?.includes(inputValue)
        })
        setData(d)
    }, [inputValue])
    useEffect(() => {

        setData(null)
    }, [])
    return (
        <div className="w-full h-full max-w-md bg-white">
            {/* Channel Selection */}
            <div className="mb-4 w-full">
                {/* <div className="text-base font-medium mb-1 ">On behalf of</div> */}
                <Input
                    // ref={inputRef}
                    type="text"
                    isSearch={true}
                    beforeIcon={<Icon name="search" />}
                    beforeIconFn={() => console.log('click before')}
                    onChange={(value: any) => setInputValue(value)}
                    allowClear
                    size="s"
                    placeholder="Search..."
                    value={inputValue}

                />
                <div className='h-[300px] overflow-y-auto'>
                    {(data || getQueuesItemForSelect())?.map((q: any) => {
                        return (
                            <Pill
                                variant="person"
                                key={q.id}
                                onClick={() => {
                                    setSelected(q)
                                    submit(q)
                                }}
                                active={selected?.id === q.id}
                                className="border-none  w-full"
                            >
                                <div className="flex items-center gap-x-4 pointer-events-auto w-full">
                                    <div className="flex flex-row w-full justify-between">
                                        <div className='flex flex-col items-start'>
                                            <div className="flex gap-1 items-center">
                                                <div className="text-remark">{q?.label}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </Pill>
                        );
                    })}
                </div>

            </div>
        </div>
    );
};

export default NewConversationContainer;
