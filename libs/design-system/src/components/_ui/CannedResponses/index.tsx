import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { ChevronDown, ChevronUp, X } from 'lucide-react';
import { toast, useRouteHandler } from '@cdss-modules/design-system';
import { getTemplate, getTemplateDetail } from '../../../lib/api';

interface CannedResponsesProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectTemplate: (content: string, templateId: string, attachments: any[] | null) => void;
  contentType?: 'html' | 'text';
  showNotes?: boolean;
  onSaveNotes?: (notes: string) => void;
  spaceId?: string;
  spaceType?: string;
  containerClassName?: string;
  popupClassName?: string;
  headerClassName?: string;
  notesClassName?: string;
  searchClassName?: string;
  directoryClassName?: string;
  templateClassName?: string;
  templatePreviewClassName?: string;
}

// 空间（顶层）
interface TemplateSpace {
  id: string;
  name: string;
  spaceType: string;
  description: string;
  items: TemplateDirectory[] | null;
}

// 目录（中间层）
interface TemplateDirectory {
  id: string;
  title: string;
  parentId?: string | null;
  items: TemplateItem[];
  contentType: string;
}

// 模板（底层）
interface TemplateItem {
  id: string;
  title: string;
  parentId?: string | null;
  language: string;
  contentType: string;
}

interface TemplateDetail {
  id: string;
  title: string;
  parentId: string | null;
  language: string;
  attachments: any[] | null;
  content: string;
}

const TemplateSelector: React.FC<CannedResponsesProps> = ({
                                                            isOpen,
                                                            onClose,
                                                            onSelectTemplate,
                                                            contentType = 'html',
                                                            showNotes = true,
                                                            onSaveNotes,
                                                            spaceId,
                                                            spaceType = 'emailTemplate',
                                                            containerClassName = '',
                                                            popupClassName = 'max-h-[90vh] max-w-[500px] w-full overflow-hidden',
                                                            headerClassName = 'text-black',
                                                            notesClassName = 'mb-4',
                                                            searchClassName = 'mb-4',
                                                            directoryClassName = 'divide-y border rounded overflow-hidden',
                                                            templateClassName = 'flex justify-between items-center bg-orange-50 px-3 py-2 cursor-pointer',
                                                            templatePreviewClassName = 'p-3 bg-orange-50',
                                                          }) => {
  // 存储空间数据
  const [spaces, setSpaces] = useState<TemplateSpace[]>([]);
  const [originalSpaces, setOriginalSpaces] = useState<TemplateSpace[]>([]);
  const [templateFilter, setTemplateFilter] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);  // 新增：区分初始加载和搜索过滤
  const [selectedTemplateDetail, setSelectedTemplateDetail] = useState<TemplateDetail | null>(null);
  const [loadingTemplate, setLoadingTemplate] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [notes, setNotes] = useState('');

  // 跟踪每个层级的展开状态
  const [expandedSpaces, setExpandedSpaces] = useState<Record<string, boolean>>({});
  const [expandedDirectories, setExpandedDirectories] = useState<Record<string, boolean>>({});
  const [expandedTemplates, setExpandedTemplates] = useState<Record<string, boolean>>({});

  const { basePath } = useRouteHandler();

  // 当组件打开时获取模板
  useEffect(() => {
    if (isOpen && initialLoading) {
      fetchTemplates();
    }
  }, [isOpen, initialLoading]);

  // 获取模板
  const fetchTemplates = async () => {
    setInitialLoading(false);
    setLoading(true);
    setError(null);
    try {
      const response = await getTemplate(basePath, spaceId, templateFilter, spaceType);
      const data = response?.data;

      if (data?.isSuccess && data?.data) {
        const spacesData: TemplateSpace[] = Array.isArray(data.data) ? data.data : [];
        setSpaces(spacesData);

        // 保存原始数据以便本地过滤使用
        if (!templateFilter) {
          setOriginalSpaces(spacesData);
        }

        // 初始化空间的展开状态
        const initialSpaceState: Record<string, boolean> = {};
        spacesData.forEach((space) => {
          if (space && space.id) {
            // 保留现有的展开状态如果存在
            initialSpaceState[space.id] = expandedSpaces[space.id] || false;
          }
        });
        setExpandedSpaces(initialSpaceState);

        // 初始化目录的展开状态
        const initialDirState: Record<string, boolean> = {};
        spacesData.forEach((space) => {
          if (space && space.items) {
            space.items.forEach((dir) => {
              if (dir && dir.id) {
                // 保留现有的展开状态如果存在
                initialDirState[dir.id] = expandedDirectories[dir.id] || false;
              }
            });
          }
        });
        setExpandedDirectories(initialDirState);
      } else {
        setSpaces([]);
        // 不重置展开状态，以防是暂时性错误
      }
    } catch (error: any) {
      const errorMessage = error?.response?.data?.error || error?.message || 'Operation failed';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'error',
      });
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 本地过滤模板（不进行API调用）
  const filterTemplatesLocally = useCallback((filter: string) => {
    if (!filter) {
      setSpaces(originalSpaces);
      return;
    }

    const filterLower = filter.toLowerCase();

    const filteredSpaces = originalSpaces.map(space => {
      if (!space.items) return space;

      // 筛选目录中匹配的模板
      const filteredItems = space.items.map(dir => {
        if (!dir.items) return dir;

        // 筛选匹配的模板项
        const filteredTemplates = dir.items.filter(template =>
          template.title.toLowerCase().includes(filterLower)
        );

        // 如果有匹配的模板，则保留此目录
        return {
          ...dir,
          items: filteredTemplates
        };
      }).filter(dir => dir.items.length > 0); // 只保留有匹配模板的目录

      // 返回更新后的空间，只包含有匹配模板的目录
      return {
        ...space,
        items: filteredItems
      };
    }).filter(space => space.items && space.items.length > 0); // 只保留有匹配目录的空间

    setSpaces(filteredSpaces);
  }, [originalSpaces]);

  // 当搜索过滤器变更时处理
  useEffect(() => {
    if (!isOpen) return undefined; // 明确返回 undefined

    // 如果原始数据提前加载，使用本地过滤
    if (originalSpaces.length > 0) {
      const timer = setTimeout(() => {
        filterTemplatesLocally(templateFilter);
      }, 300);
      return () => clearTimeout(timer);
    } else if (!initialLoading) {
      // 如果原始数据不可用，则通过API获取
      const timer = setTimeout(() => {
        fetchTemplates();
      }, 300);
      return () => clearTimeout(timer);
    }

    return undefined; // 为所有代码路径添加明确的返回值
  }, [templateFilter, isOpen, originalSpaces, filterTemplatesLocally, initialLoading]);

  const fetchTemplateDetail = async (templateId: string) => {
    if (!templateId) {
      toast({
        title: '错误',
        description: 'Template ID is missing',
        variant: 'error',
      });
      return null;
    }

    setLoadingTemplate(true);
    try {
      const response = await getTemplateDetail(basePath, templateId);
      const data = response?.data;
      if (data?.isSuccess && data?.data) {
        setSelectedTemplateDetail(data.data);
        return data.data;
      } else {
        const errorMsg = data?.error || 'Failed to load template details';
        toast({
          title: '错误',
          description: errorMsg,
          variant: 'error',
        });
        return null;
      }
    } catch (error: any) {
      const errorMsg = error?.response?.data?.error || error?.message || 'Failed to load template details';
      toast({
        title: '错误',
        description: errorMsg,
        variant: 'error',
      });
      return null;
    } finally {
      setLoadingTemplate(false);
    }
  };

  const handleSelectTemplate = async (templateId: string) => {
    if (!templateId) return;

    const templateDetail = await fetchTemplateDetail(templateId);

    if (templateDetail && templateDetail.content) {
      onSelectTemplate(templateDetail.content, templateId, templateDetail.attachments);
      onClose();
    }
  };

  const handleSaveNotes = () => {
    if (onSaveNotes) {
      onSaveNotes(notes);
    } else {
      console.log('Notes saved:', notes);
      setNotes('');
      toast({
        title: 'Success',
        description: 'Notes saved successfully',
        variant: 'success',
      });
    }
  };

  // 切换空间展开状态
  const toggleSpace = (spaceId: string) => {
    if (!spaceId) return;

    setExpandedSpaces((prev) => ({
      ...prev,
      [spaceId]: !prev[spaceId],
    }));
  };

  // 切换目录展开状态
  const toggleDirectory = (directoryId: string) => {
    if (!directoryId) return;

    setExpandedDirectories((prev) => ({
      ...prev,
      [directoryId]: !prev[directoryId],
    }));
  };

  // 切换模板展开状态
  const toggleTemplate = (templateId: string) => {
    if (!templateId) return;

    if (!expandedTemplates[templateId]) {
      fetchTemplateDetail(templateId);
    }

    setExpandedTemplates((prev) => ({
      ...prev,
      [templateId]: !prev[templateId],
    }));
  };

  // 获取匹配的模板数量
  const getMatchingTemplatesCount = useMemo(() => {
    if (!templateFilter || !spaces) return 0;

    return spaces.reduce((total, space) => {
      if (space && space.items) {
        return total + space.items.reduce((dirTotal, dir) => {
          if (dir && dir.items && Array.isArray(dir.items)) {
            return dirTotal + dir.items.length;
          }
          return dirTotal;
        }, 0);
      }
      return total;
    }, 0);
  }, [spaces, templateFilter]);

  // 处理HTML预览
  const processHtmlForPreview = (html: string, attachments: any[] | null) => {
    if (!html) return '';

    try {
      let processedHtml = html.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '');

      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = processedHtml;

      if (attachments && Array.isArray(attachments) && attachments.length > 0) {
        const imgElements = tempDiv.querySelectorAll('img');

        imgElements.forEach((img) => {
          const src = img.getAttribute('src');

          if (src && src.startsWith('cid:')) {
            const cid = src.substring(4);
            const attachment = attachments.find((att) => att && att.contentId === cid);

            if (attachment && attachment.url) {
              img.setAttribute('src', attachment.url);
              img.setAttribute('style', 'max-width:100px; max-height:80px;');
              img.setAttribute('alt', 'Email attachment');
            } else {
              const span = document.createElement('span');
              span.className =
                'inline-block px-2 py-1 bg-gray-200 text-xs rounded';
              span.textContent = '[Image]';
              img.parentNode?.replaceChild(span, img);
            }
          } else {
            const span = document.createElement('span');
            span.className =
              'inline-block px-2 py-1 bg-gray-200 text-xs rounded';
            span.textContent = '[图片]';
            img.parentNode?.replaceChild(span, img);
          }
        });

        processedHtml = tempDiv.innerHTML;
      } else {
        processedHtml = processedHtml.replace(
          /<img[^>]*>/gi,
          '<span class="inline-block px-2 py-1 bg-gray-200 text-xs rounded">[图片]</span>'
        );
      }

      processedHtml = processedHtml.replace(
        /\[Image\](?!\s*<\/span>)/g,
        '<span class="inline-block px-2 py-1 bg-gray-200 text-xs rounded">[图片]</span>'
      );

      return processedHtml;
    } catch (error) {
      console.error('Error processing HTML preview:', error);
      return html.replace(
        /<img[^>]*>/gi,
        '<span class="inline-block px-2 py-1 bg-gray-200 text-xs rounded">[图片]</span>'
      );
    }
  };

  const handleClearSearch = () => {
    setTemplateFilter('');
    // 清空搜索后应用原始数据
    setSpaces(originalSpaces);
  };

  // 渲染模板层级
  const renderTemplateHierarchy = () => {
    if (!spaces || spaces.length === 0) {
      return (
        <div className="p-4 text-center text-gray-500">
          {templateFilter
            ? 'No templates found matching your search'
            : 'No templates available'}
        </div>
      );
    }

    return spaces.map((space) => (
      space && space.id ? (
        <div
          key={space.id}
          className="border-b last:border-b-0"
        >
          {/* 空间标题 */}
          <div
            className="flex items-center justify-between bg-white px-3 py-2 cursor-pointer"
            onClick={() => toggleSpace(space.id)}
          >
            <div className="font-medium flex items-center">
              {space.name}
              {space.items && Array.isArray(space.items) && space.items.length > 0 && (
                <span className="ml-1 text-gray-500">
                  ({space.items.length})
                </span>
              )}
            </div>
            {expandedSpaces[space.id] ? (
              <ChevronUp size={16} />
            ) : (
              <ChevronDown size={16} />
            )}
          </div>

          {/* 中间层：空间中的目录 */}
          {expandedSpaces[space.id] && space.items && Array.isArray(space.items) && (
            <div className="divide-y pl-3">
              {space.items.length === 0 ? (
                <div className="p-2 text-sm text-gray-500">No directories available</div>
              ) : (
                space.items.map((directory) => (
                  directory && directory.id ? (
                    <div
                      key={directory.id}
                      className="border-b last:border-b-0"
                    >
                      {/* 目录标题 */}
                      <div
                        className="flex items-center justify-between bg-white px-3 py-2 cursor-pointer"
                        onClick={() => toggleDirectory(directory.id)}
                      >
                        <div className="font-medium flex items-center">
                          {directory.title || 'Unnamed Directory'}
                          {directory.items && Array.isArray(directory.items) && (
                            <span className="ml-1 text-gray-500">({directory.items.length})</span>
                          )}
                        </div>
                        {expandedDirectories[directory.id] ? (
                          <ChevronUp size={16} />
                        ) : (
                          <ChevronDown size={16} />
                        )}
                      </div>

                      {/* 底层：目录中的模板 */}
                      {expandedDirectories[directory.id] &&
                        directory.items &&
                        Array.isArray(directory.items) && (
                          <div className="divide-y pl-3">
                            {directory.items.length === 0 ? (
                              <div className="p-2 text-sm text-gray-500">No templates available</div>
                            ) : (
                              directory.items.map((template) => (
                                template && template.id ? (
                                  <div key={template.id}>
                                    {/* 模板项 */}
                                    <div
                                      className="bg-amber-50 px-3 py-2 cursor-pointer flex justify-between items-center"
                                      onClick={() => toggleTemplate(template.id)}
                                    >
                                      <div className="font-medium">
                                        {template.title || 'Unnamed Template'}
                                      </div>
                                      <div className="flex items-center">
                                        {/* 使用模板按钮 */}
                                        <button
                                          className="border border-gray-400 rounded p-1 hover:bg-white transition-colors"
                                          onClick={(e) => {
                                            e.stopPropagation(); // 防止触发行点击事件
                                            handleSelectTemplate(template.id);
                                          }}
                                          title="Use this template"
                                          disabled={loadingTemplate}
                                        >
                                          {loadingTemplate && selectedTemplateDetail?.id === template.id ? (
                                            <div
                                              className="animate-spin h-4 w-4 border-b-2 border-gray-500 rounded-full"></div>
                                          ) : (
                                            <svg
                                              width="16"
                                              height="16"
                                              viewBox="0 0 24 24"
                                              fill="none"
                                              xmlns="http://www.w3.org/2000/svg"
                                            >
                                              <path
                                                d="M5 12H19"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                              />
                                              <path
                                                d="M12 5L19 12L12 19"
                                                stroke="currentColor"
                                                strokeWidth="2"
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                              />
                                            </svg>
                                          )}
                                        </button>
                                      </div>
                                    </div>

                                    {/* 模板预览 */}
                                    {expandedTemplates[template.id] &&
                                      selectedTemplateDetail &&
                                      selectedTemplateDetail.id === template.id && (
                                        <div className="p-3 bg-amber-50 border-t border-amber-200">
                                          {contentType === 'html' ? (
                                            <div
                                              className="max-h-[200px] overflow-y-auto text-sm"
                                              dangerouslySetInnerHTML={{
                                                __html: processHtmlForPreview(
                                                  selectedTemplateDetail.content,
                                                  selectedTemplateDetail.attachments
                                                ),
                                              }}
                                            />
                                          ) : (
                                            <div
                                              className="max-h-[200px] overflow-y-auto text-sm whitespace-pre-wrap font-mono">
                                              {selectedTemplateDetail.content}
                                            </div>
                                          )}
                                        </div>
                                      )}
                                  </div>
                                ) : null
                              ))
                            )}
                          </div>
                        )}
                    </div>
                  ) : null
                ))
              )}
            </div>
          )}
        </div>
      ) : null
    ));
  };

  return (
    <Popup
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          onClose();
        }
      }}
    >
      <PopupContent
        title="Canned responses"
        className={popupClassName}
        headerClassName={headerClassName}
      >
        <div
          className={`flex flex-col p-4 overflow-auto ${containerClassName}`}
          style={{ maxHeight: 'calc(90vh - 56px)' }}
        >
          {initialLoading ? (
            <div className="py-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
              <div>Loading templates...</div>
            </div>
          ) : error ? (
            <div className="py-8 text-center">
              <div className="text-red-500 mb-2">Error loading templates</div>
              <div className="text-sm text-gray-600">{error}</div>
              <button
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                onClick={fetchTemplates}
              >
                Retry
              </button>
            </div>
          ) : (
            <>
              {/* 备注部分 */}
              {showNotes && (
                <div className={notesClassName}>
                  <div className="text-gray-700 font-medium mb-2">Notes</div>
                  <div className="flex items-center gap-2">
                    <input
                      type="text"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      placeholder="Type your notes here..."
                      className="flex-1 px-3 py-2 border rounded-md outline-none focus:ring-1 focus:ring-blue-500"
                    />
                    <Button
                      variant="primary"
                      size="m"
                      onClick={handleSaveNotes}
                    >
                      Save
                    </Button>
                  </div>
                </div>
              )}

              {/* 模板搜索字段 */}
              <div className={searchClassName}>
                <div className="text-gray-700 font-medium mb-2">Find response</div>
                <div className="relative">
                  <input
                    type="text"
                    className="w-full px-3 py-2 pl-8 border rounded-md outline-none focus:ring-1 focus:ring-blue-500"
                    placeholder="Search response"
                    value={templateFilter}
                    onChange={(e) => {
                      setTemplateFilter(e.target.value);
                    }}
                  />
                  <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-gray-400"
                    >
                      <circle
                        cx="11"
                        cy="11"
                        r="8"
                      ></circle>
                      <line
                        x1="21"
                        y1="21"
                        x2="16.65"
                        y2="16.65"
                      ></line>
                    </svg>
                  </div>
                  {templateFilter && (
                    <button
                      className="absolute inset-y-0 right-0 flex items-center pr-2"
                      onClick={handleClearSearch}
                    >
                      <X
                        size={16}
                        className="text-gray-400 hover:text-gray-600"
                      />
                    </button>
                  )}
                </div>
                {templateFilter && getMatchingTemplatesCount > 0 && (
                  <div className="text-xs text-gray-500 mt-1">
                    Showing {getMatchingTemplatesCount}{' '}
                    {getMatchingTemplatesCount === 1
                      ? 'template'
                      : 'templates'}{' '}
                    matching &quot;{templateFilter}&quot;
                  </div>
                )}
              </div>

              {/* 空间、目录和模板层级 */}
              <div className={directoryClassName}>
                {loading ? (
                  <div className="p-4 text-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
                    <div className="text-sm">Filtering...</div>
                  </div>
                ) : (
                  renderTemplateHierarchy()
                )}
              </div>
            </>
          )}
        </div>
      </PopupContent>
    </Popup>
  );
};

export default TemplateSelector;
