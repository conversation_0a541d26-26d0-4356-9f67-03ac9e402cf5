'use client';

import {
    ResizablePanelGroup,
    ResizablePanel,
} from '@cdss-modules/design-system/components/_ui/Resizable';
import { Call } from './Call';
import CallInfo from '@cdss-modules/design-system/components/_ui/CallInfo';
import { MiniWallboard } from '@cdss-modules/design-system/components/_ui/MiniWallboard';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { PanelResizeHandle } from 'react-resizable-panels';
import { useCustomerHistory, useCustomerInfo } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import { microfrontends } from '@cdss/types/microfrontendsConfig';
import { Panel, useRole } from '@cdss-modules/design-system';
import Saa from '@cdss-modules/design-system/components/_ui/Saa';
import { useEffect, useRef, useState } from 'react';

export function CallDetailBody() {
    const { selectedInteraction, selectInteraction } = useTbarContext();
    const [isFullResizablePanel, setIsFullResizablePanel] = useState<boolean>(false);
    const { globalConfig } = useRole();
    const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
    const useSAAv2: boolean | undefined =
        microfrontendsConfig['ctint-mf-call']['useSAAv2'];
    const { conversationId, customerData, telPhonneNumber } =
        useHandleInteractionData(selectedInteraction);
    const {
        SAADetail,
        getSAADetail,
        SAADetailLoading
    } = useCustomerHistory()
    useEffect(() => {
        getSAADetail()
    }, [])
    
    return (
        <div className="relative flex flex-col h-full gap-4 justify-between">
            <ResizablePanelGroup
                autoSaveId="call-detail-panel"
                direction="horizontal"
                className="h-full flex-1 flex w-full h-0 gap-x-3 "
            >
                <ResizablePanel minSize={50}>
                    <Call selectInteraction={selectInteraction} />
                </ResizablePanel>
                <ResizablePanel minSize={50}>
                    <ResizablePanelGroup
                        autoSaveId="SAA-detail-panel"
                        direction="vertical"
                        className="h-full flex-1 flex w-full h-0 gap-x-3 "
                    >
                        <ResizablePanel style={isFullResizablePanel && {
                            flexGrow: 2000,
                            flexShrink: 1,
                            flexBasis: "0px",
                        }}>
                            <CallInfo
                                customerData={{
                                    conversationId: conversationId,
                                    phoneNumber: telPhonneNumber,
                                    customerId: customerData?.id,
                                }}
                            />
                        </ResizablePanel>
                        <PanelResizeHandle />
                        <ResizablePanel style={{
                            flexGrow: !isFullResizablePanel ?50:140,
                            flexShrink: 1,
                            flexBasis: "0px",
                        }}>
                            <Saa data={
                                { SAADetail: SAADetail, getSAADetail: getSAADetail, convId: conversationId, SAADetailLoading: SAADetailLoading }
                            }
                                useSAAv2={true}
                                setIsFullResizablePanel={setIsFullResizablePanel}
                                isShowCopy={false}
                            />
                        </ResizablePanel>
                    </ResizablePanelGroup>


                </ResizablePanel>
            </ResizablePanelGroup>
            <MiniWallboard />
        </div>
    );
}
export const CallModule = () => {
    return <CallDetailBody />;
};
