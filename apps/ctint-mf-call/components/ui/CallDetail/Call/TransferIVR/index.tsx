
import { useMemo, useState } from 'react';
import * as yup from 'yup';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { toast } from '@cdss-modules/design-system';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import { TAttributesData, TIVRItem, TWorkgroup } from 'apps/ctint-mf-call/types';
import IVRForm from './IVRForm';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { useAttributes } from '@cdss-modules/design-system/lib/hooks/useAttributes';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
export function TransferIVR({
    className,
    onOpen,
}: {
    className?: string;
    onOpen?: () => void;
}) {
    const [searchValue, setSearchValue] = useState('');
    const [selectedIVR, setSelectedIVR] = useState<TIVRItem | null>(null);
    const { getWorkgroupByUserHandle } = useGetworkGroupOrUser();
    const [isInputFieldOpen, setIsInputFieldOpen] = useState(false);
    const [workgroupData, setWorkgroupData] = useState<TIVRItem[] | undefined>([]);


    const { IVRConference, transfer } = useCallControl();
    const { submitAttributes } = useAttributes();
    const {
        selectedInteraction,
        activeParticipantData,
        updateActiveParticipantData,
        getSingleHistoryConversation,
    } = useTbarContext();
    const { conversationId, agentData, consultData, customerData } =
        useHandleInteractionData(selectedInteraction);

    const allIvrWorkgroup = useMemo(() => {
        let allIvrWorkgroup: TIVRItem[] = [];
        getWorkgroupByUserHandle.data?.forEach((v: TWorkgroup) => {
            if (v.externalPhoneNums) {
                allIvrWorkgroup.push(...v.externalPhoneNums);
            }
        });
        const uniqueAllIvrWorkgroup = Array.from(
            new Set(allIvrWorkgroup.map((item) => item.number))
        )
            .map((number) => allIvrWorkgroup.find((item) => item.number === number))
            .filter((item): item is TIVRItem => item !== undefined);
        setWorkgroupData(uniqueAllIvrWorkgroup);
        return uniqueAllIvrWorkgroup;
    }, [getWorkgroupByUserHandle?.data]);
    const handleInputChange = (e: any) => {
        const value = e;
        setSearchValue(value);
        if (value === '') {
            setWorkgroupData(allIvrWorkgroup);
            return;
        }
        const searchIvrWorkGroup = allIvrWorkgroup.filter(
            (record) =>
                record.name.toLowerCase().includes(value) ||
                record.number.includes(value)
        );
        setWorkgroupData(searchIvrWorkGroup);
    };

    const ivrFormData = getWorkgroupByUserHandle?.data?.find(
        (item: TWorkgroup) => item.externalPhoneNums !== null
    )?.externalPhoneNums?.[0].formItems;
    const ivrYupObj = ivrFormData?.reduce(
        (acc: Record<string, any>, cur: TAttributesData) => {
            if (cur?.isRequired) {
                acc[cur.key] = yup.string().required(`${[cur.name]} is required`);
            }
            return acc;
        },
        {}
    );
    const ivrFormSchema = yup.object(ivrYupObj).required();

    const handleIVRFormOnChange = (key: string, value: string | number) => {
        const isPII = ivrFormData?.find((item) => item.key === key)?.isPII;

        const isEncrypted = ivrFormData?.find(
            (item) => item.key === key
        )?.isEncrypted;

        updateActiveParticipantData({
            ...activeParticipantData,
            [key]: {
                value: value,
                isPII,
                isEncrypted,
            },
        });
    };
    const validateIVRFormData = () => {
        
        return ivrFormData?.every((item: TAttributesData) => {
            if (item?.isRequired) {
                return activeParticipantData?.[item?.key]||activeParticipantData?.[item?.key]?.value === '';
            }

            return true;
        });
    };

    const transferRender = () => {
        return (
            <div className="flex items-center justify-center gap-2">
                <CallControlButton
                    tooltip={'conference'}
                    icon={
                        <Icon
                            name="conference"
                            size={16}
                            className="text-white"
                        />
                    }
                    className={cn(
                        'flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
                    )}
                    onClick={(e) => {
                        e?.stopPropagation()
                        if (searchValue !== '' && !selectedIVR) {
                            IVRConference(conversationId, searchValue || '');
                            setSelectedIVR(null);
                            setIsInputFieldOpen(false);
                        } else if (activeParticipantData !== null && validateIVRFormData()) {
                            IVRConference(conversationId, selectedIVR?.number|| '');
                            submitAttributes(
                                conversationId,
                                customerData?.id || consultData?.id,
                                activeParticipantData
                            );
                            onOpen && onOpen();
                            setSelectedIVR(null);
                            setIsInputFieldOpen(false);
                        } else {
                            toast({
                                variant: 'error',
                                title: 'Error in conferenceIVR',
                                description: 'Please fill in all required fields.',
                            });
                        }
                    }}
                // disabled={validateIVRFormData()}
                />
                <CallControlButton
                    tooltip={'Blind'}
                    icon={
                        <Icon
                            name="transfer"
                            size={16}
                            className="text-white"
                        />
                    }
                    className={cn(
                        'flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
                    )}
                    onClick={(e) => {
                        e?.stopPropagation()
                        if (searchValue !== '' && !selectedIVR) {
                            transfer(conversationId, agentData?.id, {
                                destinationAddress: searchValue,
                            });
                            setSelectedIVR(null);
                            setIsInputFieldOpen(false);
                        } else if (activeParticipantData !== null && validateIVRFormData()) {
                            transfer(conversationId, agentData?.id, {
                                destinationAddress: selectedIVR?.number,
                            });

                            submitAttributes(
                                conversationId,
                                customerData?.id || consultData?.id,
                                activeParticipantData
                            ).then(() => getSingleHistoryConversation(conversationId));
                            onOpen && onOpen();
                            setSelectedIVR(null);
                            setIsInputFieldOpen(false);
                        } else {
                            toast({
                                variant: 'error',
                                title: 'Error in transferIVR',
                                description: 'Please fill in all required fields.',
                            });
                        }
                    }}
                // disabled={validateIVRFormData()}
                />
            </div>
        );
    };


    // useEffect(() => {
    //     setWorkgroupData(getWorkgroupByUserHandle?.data)
    // }, [])

    return (
        <div className={cn('w-full p-2 pb-0', className && className)}>
            <div className="flex items-center justify-center gap-2">
                <button
                    onClick={() => {
                        onOpen && onOpen();
                    }}
                >
                    <Icon
                        name="back"
                        size={12}
                    />
                </button>

                <Input
                    type="text"
                    size="s"
                    placeholder="Input IVR ID or Name to search IVR"
                    value={searchValue}
                    onChange={handleInputChange}
                    allowClear
                />
                {transferRender()}
            </div>

            {workgroupData?.map(
                (ivr: TIVRItem) => {
                    return (
                        <>
                            <div
                                className={cn(
                                    'w-full p-2 border-b-2 border-grey-200 box-border hover:bg-primary-100',
                                    selectedIVR?.name === ivr?.name && 'bg-primary-100'
                                )}
                                key={ivr?.name + ivr?.number}
                            >
                                <div className="flex flex-wrap gap-x-2 gap-y-0 w-full items-start overflow-y-auto">
                                    <Pill
                                        variant="person"
                                        key={ivr?.name}
                                        onClick={() => {
                                            setSelectedIVR(ivr);
                                            if (isInputFieldOpen) {
                                                setIsInputFieldOpen(false);
                                            } else {
                                                setIsInputFieldOpen(true);
                                            }
                                            // setIsInputFieldOpen(true);
                                        }}
                                        className="border-none w-full !justify-between"
                                    // active={selectedIVR?.name === ivr?.name}
                                    >
                                        <div className="flex flex-row items-start justify-center  items-center gap-4">
                                            <div className="flex gap-1 items-center">
                                                <div className="size-[10px] rounded-full bg-status-success" />
                                                <div className="text-remark">
                                                    <strong>{ivr?.name}</strong>
                                                </div>
                                            </div>
                                            <div className="text-grey-600">{ivr?.number}</div>
                                        </div>
                                        {selectedIVR?.name === ivr?.name && transferRender()}
                                    </Pill>
                                </div>
                            </div>
                            {selectedIVR?.name === ivr?.name && isInputFieldOpen && (
                                <div className="flex flex-col gap-2 w-full overflow-auto p-2 bg-[#DEDEDE]">
                                    <IVRForm
                                        schema={ivrFormSchema}
                                        formItems={ivrFormData || []}
                                        // onSubmit={onSubmitIVRForm}
                                        handleOnChange={handleIVRFormOnChange}
                                        fieldClassName="w-full"
                                        formClassName="grid grid-cols-2 gap-4"
                                        fieldItemStyle={{ 2: 'col-span-full' }}
                                        direction="vertical"
                                    // haveSubmitButton
                                    />
                                </div>
                            )}
                        </>
                    );
                }
            )}
            {getWorkgroupByUserHandle?.data?.length === 0 && (
                <div className="text-body">
                    No search result, continue to make the call.
                </div>
            )}
            {getWorkgroupByUserHandle?.data?.[0]?.externalPhoneNums === null && (
                <div className="text-body">
                    No search result, continue to make the call.
                </div>
            )}
        </div>
    );
}
