import { yupResolver } from '@hookform/resolvers/yup';
import { useForm, Controller } from 'react-hook-form';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { useEffect } from 'react';
import _ from 'lodash';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { TAttributesData } from 'apps/ctint-mf-call/types';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
type TIVRFormProps = {
  schema: any;
  formItems: TAttributesData[];
  values?: any;
  onSubmit?: (data: any) => void;
  handleOnChange: (key: string, value: string | number) => void;
  haveSubmitButton?: boolean;
  fieldClassName?: string;
  fieldItemStyle?: { [key: number]: string };
  formClassName?: string;
  direction?: 'horizontal' | 'vertical';
};

const IVRForm = ({
  schema,
  values,
  formItems,
  onSubmit,
  handleOnChange,
  haveSubmitButton = false,
  fieldClassName = '',
  formClassName = '',
  fieldItemStyle,
  direction = 'horizontal',
}: TIVRFormProps) => {
  const {
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(schema),
  });

  const { activeParticipantData } = useTbarContext();

  const handleFormSubmit = async (data: any) => {
    onSubmit && onSubmit(data);
    reset();
  };

  useEffect(() => {
    if (activeParticipantData === null) {
      reset();
    }
  }, [activeParticipantData, reset]);

  useEffect(() => {
    if (typeof values === 'object' && !_.isEmpty(values)) {
      for (const [key, value] of Object.entries(values)) {
        setValue(key, value);
      }
    } else if (_.isEmpty(values)) {
      reset();
    }
  }, [setValue, values, reset]);

  return (
    <form
      onSubmit={handleSubmit(handleFormSubmit)}
      className={formClassName ? formClassName : 'flex gap-2 flex-col h-full'}
    >
      {formItems?.map((item: any, index: number) => {
        return (
          <div
            key={item.key}
            className={cn(
              'flex gap-2 w-full items-center',
              fieldItemStyle && fieldItemStyle[index]
            )}
          >
            <Field
              direction={direction}
              title={item.name}
              icon={<Icon name="error" />}
              status={errors?.[item?.key]?.message ? 'danger' : undefined}
              message={errors?.[item?.key]?.message as string}
              className={fieldClassName}
            >
              <Controller
                name={item?.key}
                control={control}
                rules={{ required: item?.isRequired }}
                render={({ field: { onChange, value } }) => (
                  <Input
                    className="w-full"
                    value={value || ''}
                    type="text"
                    size="s"
                    placeholder={'Please type something...'}
                    onChange={(e: any) => {
                      onChange(e);
                      handleOnChange(item.key, e);
                    }}
                  />
                )}
              />
            </Field>
          </div>
        );
      })}

      {haveSubmitButton && (
        <Button
          fullWidth
          type="submit"
        >
          Submit
        </Button>
      )}
    </form>
  );
};

export default IVRForm;
