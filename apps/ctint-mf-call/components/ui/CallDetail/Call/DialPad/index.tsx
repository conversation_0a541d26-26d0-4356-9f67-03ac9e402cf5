'use client';

import React, { useState } from 'react';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { cn } from '@cdss-modules/design-system/lib/utils';
import IconDeleteDailPad from '@cdss-modules/design-system/components/_ui/Icon/IconDeleteDailPad';
import { useRouteHandler } from '@cdss-modules/design-system';
import { fireDigits } from 'apps/ctint-mf-call/lib/api';

type TDialPadProps = {
    value?: string[];
    buttonClassName?: string;
    conversationId?: string;
    participantId?: string;
    callEndButton: () => React.ReactNode
};

const keys = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '*', '0', '#'];

const DialPad: React.FC<TDialPadProps> = ({
    buttonClassName,
    conversationId,
    participantId,
    callEndButton
}) => {
    const { basePath } = useRouteHandler();
    const [inputValue, setInputValue] = useState<string>("");
    const inputRef = React.useRef<HTMLInputElement>(null);

    const handleInputValue = (e: any) => {
        fireDigits(conversationId, participantId, e, basePath)
        setInputValue(inputValue + e);
    };
    React.useEffect(() => {
        if (inputRef.current) {
            inputRef.current.scrollLeft = inputRef.current.scrollWidth;
        }
    }, [inputValue]);
    const onDelete = () => {
        setInputValue(inputValue.slice(0, -1))
    }

    return (
        <div className="flex flex-col justify-center items-center gap-4 text-t6">
            <Input
                ref={inputRef}
                value={inputValue || ''}
                onFocus={() => {
                    return null;
                }}
                containerClassName="bg-grey-100 border-none rounded-lg"
                className="focus:shadow-none cursor-default text-end font-bold text-[24px]"
                size="s"
            />
            <div className="flex flex-col gap-y-4 max-w-[465px]">
                <div className="grid grid-cols-3  gap-y-3 justify-items-center items-center">
                    {keys.map((key) => (
                        <button
                            key={`key-${key}`}
                            className={cn(
                                'w-10 h-10 flex justify-center items-center rounded-full bg-grey-200 hover:bg-grey-300 active:bg-grey-400 font-bold',
                                key === '*' && 'pt-3',
                                buttonClassName
                            )}
                            onClick={() => handleInputValue(key)}
                        >
                            {key}
                        </button>
                    ))}
                    <div className='col-end-3'>
                        <div className='flex items-center justify-center gap-6 col-end-3'>
                            <div className='h-16 w-16 flex items-center justify-center rounded-full bg-[#FF271C]'>
                                {callEndButton()}
                            </div>
                        </div>
                    </div>

                    <div className='col-end-4'>
                        <button
                            className='w-10 h-10 flex justify-center items-center rounded-full bg-grey-200 hover:bg-grey-300 active:bg-grey-400 font-bold '

                            onClick={() => onDelete()}
                        >
                            <IconDeleteDailPad
                                size={"24"}
                            />
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DialPad;
