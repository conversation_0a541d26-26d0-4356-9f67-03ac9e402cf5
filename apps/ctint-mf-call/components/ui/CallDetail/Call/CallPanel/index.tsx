
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import {
    cn,
    formatInteractionDuration,
} from '@cdss-modules/design-system/lib/utils';
import { useEffect, useState } from 'react';
import DialPad from '../DialPad';
import { useSearchParams } from 'react-router-dom';
import React from 'react';
import IconPickup from '@cdss-modules/design-system/components/_ui/Icon/IconPickup';
import IconPhoneEnd from '@cdss-modules/design-system/components/_ui/Icon/IconPhoneEnd';
import IconHoldUser from '@cdss-modules/design-system/components/_ui/Icon/IconHoldUser';
import IconActiveUser from '@cdss-modules/design-system/components/_ui/Icon/IconActiveUser';
import IconLink from '@cdss-modules/design-system/components/_ui/Icon/IconLink';
import { Tooltip } from '@cdss-modules/design-system/components/_ui/Tooltip';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import IconCallBg from '@cdss-modules/design-system/components/_ui/Icon/IconCallBg';
import { TransferIVR } from '../TransferIVR';
import { TCallControlType } from 'apps/ctint-mf-call/types';
import { CallBack } from '@cdss-modules/design-system/components/_ui/CallBack';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { useRole } from '@cdss-modules/design-system';
interface CallPanelProps {
    customerData: any;
    consultData: any;
    voicemailData: any
}
const CallPanel: React.FC<CallPanelProps> = React.memo(({ customerData: customerDatas, consultData: consultDatas, voicemailData }) => {
    const { userConfig } = useRole();
    const { callConverSationControl, conference, call, consultCompleted, transfer } = useCallControl();
    const [time, setTime] = useState<string>('00:00');
    const [participants, setParticipants] = useState<any[]>([]);
    const [openDialPad, setOpenDialPad] = useState(false);
    const [openTransferIVR, setOpenTransferIVR] = useState(false);
    const [ivrZindex, setIvrZindex] = useState<number>(1);
    const [dialpadZindex, setDialpadZindex] = useState<number>(2);
    const [searchParams] = useSearchParams();
    const [openDirectory, setOpenDirectory] = useState(false);
    const convId = searchParams?.get('conversationId');
    const customerData = customerDatas?.sort((a: any, b: any) => {
        const timeA = new Date(a.connectedTime).getTime();
        const timeB = new Date(b.connectedTime).getTime();
        return timeA - timeB;
    })?.[0];
    const {
        selectedInteraction,
        refreshInteractions,
        handleAfterCallFunc,
        stationContext: {
            station,
        }
    } = useTbarContext();
    const {
        connectedTime,
        conversationId,
        phoneNumber,
        isConference,
        fullConference,
        agentData: agent,
        isHistory,
        consultInitiator,
        tBarStatus,
        talkTime,
        consultData
    } = useHandleInteractionData(selectedInteraction);
    useEffect(() => {
        const t = formatInteractionDuration(agent?.connectedTime || selectedInteraction?.conversationStart, agent?.endTime || selectedInteraction?.conversationEnd)
        if (isHistory || tBarStatus == "disconnected") {
            if (t) {
                setTime(t)
            } else {
                if (talkTime) {
                    setTime(talkTime)
                } else {
                    setTime("00:00")
                }
            }
            return
        }
    }, [agent, isHistory, tBarStatus, selectedInteraction])
    useEffect(() => {
        if (isHistory || tBarStatus == "disconnected") {
            return;
        }
        // if (!connectedTime) return;
        // Convert connectedTime to a Date object
        const initialDate = new Date(connectedTime || new Date());

        // Function to update the time
        const updateTimer = () => {
            const now = new Date();
            const diffInMs = Math.max(now.getTime() - initialDate.getTime(), 0);
            const diffInSeconds = Math.floor(diffInMs / 1000);
            // setCount(diffInMs);

            const minutes = Math.floor(diffInSeconds / 60);
            const seconds = diffInSeconds % 60;

            // Format time mm:ss
            const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            setTime(formattedTime);
        };
        updateTimer();
        // Set interval to update time every second
        const intervalId = setInterval(updateTimer, 1000);

        // Clear interval on component unmount
        return () => clearInterval(intervalId);
    }, [connectedTime, isHistory, tBarStatus]);

    const handleCallControl = (type: string, rowData?: any) => {
        let contorlType: TCallControlType = 'mute';
        if (type == 'pickup') {
            // const { role, id } = rowData;
            // const parmarType = role == 'consult' ? 'DESTINATION' : 'OBJECT';
            conference("BOTH", conversationId, customerData?.id);
            return
        }
        if (type == 'hold') {
            const { role, id } = rowData;
            const parmarType = role == 'consult' ? 'OBJECT' : 'DESTINATION';
            conference(parmarType, conversationId, customerData?.id);
            return
        }
        if (type == 'muteAndUnMute') {
            contorlType = agent?.muted ? 'unmute' : 'mute';
        }
        if (type == 'holdAndUnHold') {
            contorlType = agent?.held ? 'unhold' : 'hold';
        }
        if (type == 'disconnected') {
            const { role, id } = rowData;
            if (isConference && consultInitiator === 'agent' && role === 'consult') {
                consultCompleted(conversationId, customerData?.id);
            } else {
                if (role === "customer") {
                    callConverSationControl({
                        type: 'disconnect',
                        conversationId,
                        agentParticipantId: customerData?.id,
                    });
                } else if (role === "voicemail") {
                    callConverSationControl({
                        type: 'disconnect',
                        conversationId,
                        agentParticipantId: id,
                    });
                } else {
                    callConverSationControl({
                        type: 'disconnect',
                        conversationId,
                        agentParticipantId: agent?.id,
                    });
                }

            }
            return;
        }
        callConverSationControl({
            type: contorlType,
            conversationId,
            agentParticipantId: agent.id,
        });
    };
    const callUserItemRenderFor2 = (data: any, className?: string) => {

        return (
            <div
                key={data?.id || data?.phoneNumber}
                className={cn(
                    'w-auto h-auto gap-4 shadow px-3 flex flex-col items-start',
                    fullConference
                        ? ''
                        : (data?.confined || (consultInitiator == 'consult' && !fullConference && data?.role == 'customer'))
                            ? 'bg-[#F8F8F8] text-[#949494] w-min'
                            : '',
                    className,
                    (isHistory || tBarStatus == "disconnected") ? ' justify-end pb-2' : 'justify-center'
                )}
            >
                <div className="w-max pr-8">
                    <p>{data?.name || data?.participantName}</p>
                    <p className="text-[20px]">
                        <strong>{data?.phoneNumber}</strong>
                    </p>
                    {((!isHistory && tBarStatus !== "disconnected") && consultInitiator !== 'consult') && (
                        <div className="flex gap-1">
                            {data?.confined ? (
                                <div className="h-6 w-6 flex items-center justify-center rounded-full bg-[#1CC500]">
                                    <button
                                        className=" hover:text-primary"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleCallControl('pickup', data);
                                        }}
                                    >
                                        <IconPickup
                                            size={'12'}
                                            className="!fill-white"
                                        />
                                    </button>
                                </div>
                            ) : (
                                data?.role !== 'voicemail'&&<div className="h-6 w-6 flex items-center justify-center rounded-full bg-[#1CC500]">
                                    <button
                                        className=" hover:text-primary"
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            handleCallControl('hold', data);
                                        }}
                                    >
                                        <Icon
                                            name="hold"
                                            size={12}
                                            className="text-white"
                                        />
                                    </button>
                                </div>
                            )}
                            {data?.role == 'customer' && !fullConference && (
                                <CallControlButton
                                    tooltip={'Blind'}
                                    icon={
                                        <Icon
                                            name="transfer"
                                            size={12}
                                            className="text-white"
                                        />
                                    }
                                    className={cn(
                                        '!h-6 !w-6 flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
                                    )}
                                    onClick={() => {
                                        callConverSationControl({
                                            type: 'disconnect',
                                            conversationId,
                                            agentParticipantId: agent.id,
                                        });
                                    }}
                                />
                            )}
                            <div className="h-6 w-6 flex items-center justify-center rounded-full bg-[#FF271C]">
                                <button
                                    className=" hover:text-primary"
                                    onClick={(e) => {
                                        handleCallControl("disconnected", data)
                                    }}
                                >
                                    <IconPhoneEnd
                                        size={'12'}
                                        className="!fill-white"
                                    />
                                </button>
                            </div>
                        </div>
                    )}
                </div>
                <div className="w-full flex justify-center">
                    {data?.confined || (consultInitiator == 'consult' && !fullConference && data?.role == 'customer') || isHistory || tBarStatus == "disconnected" ? (
                        <IconHoldUser
                            size={'100%'}
                        // className='!fill-white'
                        />
                    ) : (
                        <IconActiveUser
                            size={'100%'}
                        // className='!fill-white'
                        />
                    )}
                </div>
                {(isHistory || tBarStatus == "disconnected") && <CallBack className="w-full" triggerClassName="w-full" number={data?.phoneNumber} userId={data?.user?.id || userConfig?.id} />}
            </div>
        );
    };
    const callUserItemRenderFor3 = (data: any, className?: string) => {
        return (
            <div
                key={data?.id || data?.phoneNumber}
                className={cn(
                    'w-full shadow px-3 pt-1 pb-4 flex flex-col items-center justify-center',
                    (isHistory || tBarStatus == "disconnected") ? '' : 'gap-4'
                )}
            >
                <div
                    className={cn(
                        'w-min w-full gap-4 flex flex-row items-center justify-between',
                        fullConference
                            ? ''
                            : data?.confined
                                ? 'bg-[#F8F8F8] text-[#949494]'
                                : '',
                        className,
                        (isHistory || tBarStatus == "disconnected") ? 'h-full' : ''
                    )}
                >
                    <div className="w-max flex-1">
                        <p className='w-max'>{data?.name || data?.participantName}</p>
                        <p className="w-max text-[20px]">
                            <strong>{data?.phoneNumber}</strong>
                        </p>
                        {((!isHistory && tBarStatus !== "disconnected") && consultInitiator !== 'consult') && (
                            <div className="flex gap-1">
                                {data?.confined ? (
                                    <div className="h-6 w-6 flex items-center justify-center rounded-full bg-[#1CC500]">
                                        <button
                                            className=" hover:text-primary"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleCallControl('pickup', data);
                                            }}
                                        >
                                            <IconPickup
                                                size={'12'}
                                                className="!fill-white"
                                            />
                                        </button>
                                    </div>
                                ) : (
                                    <div className="h-6 w-6 flex items-center justify-center rounded-full bg-[#1CC500]">
                                        <button
                                            className=" hover:text-primary"
                                            onClick={(e) => {
                                                e.stopPropagation();
                                                handleCallControl('hold');
                                            }}
                                        >
                                            <Icon
                                                name="hold"
                                                size={12}
                                                className="text-white"
                                            />
                                        </button>
                                    </div>
                                )}
                                {data?.role == 'customer' && consultInitiator !== 'consult' && !fullConference && (
                                    <CallControlButton
                                        tooltip={'Blind'}
                                        icon={
                                            <Icon
                                                name="transfer"
                                                size={12}
                                                className="text-white"
                                            />
                                        }
                                        className={cn(
                                            '!h-6 !w-6 flex-none size-8 bg-status-info disabled:bg-grey-400 hover:border-none'
                                        )}
                                        onClick={() => {
                                            callConverSationControl({
                                                type: 'disconnect',
                                                conversationId,
                                                agentParticipantId: agent.id,
                                            });
                                        }}
                                    />
                                )}
                                <div className="h-6 w-6 flex items-center justify-center rounded-full bg-[#FF271C]">
                                    <button
                                        className=" hover:text-primary"
                                        onClick={(e) => {
                                            handleCallControl("disconnected", data)
                                        }}
                                    >
                                        <IconPhoneEnd
                                            size={'12'}
                                            className="!fill-white"
                                        />
                                    </button>
                                </div>
                            </div>
                        )}
                    </div>
                    <div className="flex">
                        {isHistory || tBarStatus == "disconnected" || data?.confined ? (
                            <IconHoldUser
                                size={'115px'}
                            // className='!fill-white'
                            />
                        ) : (
                            <IconActiveUser
                                size={'115'}
                            // className='!fill-white'
                            />
                        )}
                    </div>
                </div>
                {(isHistory || tBarStatus == "disconnected") && <CallBack className="w-full" triggerClassName="w-full" number={data?.phoneNumber} userId={data?.user?.id || userConfig?.id} />}
            </div>
        );
    };
    const callUserRender = (data: any[]) => {

        let className = '';
        if (data?.length == 2) {
            className = 'grid grid-cols-2 gap-4';
            return (
                <>
                    <div className="absolute w-[50%] h-6 -bottom-6 left-[25%] border-x border-b border-zinc-300 flex justify-center">
                        <div
                            className={cn(
                                'absolute -bottom-3.5 h-6 w-6 flex items-center justify-center rounded-full',
                                fullConference && !isHistory && tBarStatus !== "disconnected" ? 'bg-[#FFAC4A]' : 'bg-[#D9D9D9]'
                            )}
                        >
                            <IconLink size={'12'} />
                        </div>
                    </div>
                    <div className={className}>
                        {data?.map((item) => {
                            return (
                                (isHistory || tBarStatus == "disconnected") ? callUserItemRenderFor2(
                                    item,
                                ) : <Tooltip
                                    key={'Tooltip' + item?.id || item?.phoneNumber}
                                    content={item?.role}
                                    trigger={callUserItemRenderFor2(
                                        item,

                                    )}
                                />
                            );
                        })}
                    </div>
                </>
            );
        }
        if (data?.length == 3) {
            className = 'grid grid-rows-2 grid-cols-2 gap-4';
            return (
                <div className={className}>
                    {data?.map((item) => {
                        if (item?.role == 'customer') {
                            return (
                                (isHistory || tBarStatus == "disconnected") ? callUserItemRenderFor2(item, 'row-span-full') : <Tooltip
                                    key={'Tooltip' + item?.id || item?.phoneNumber}
                                    content={item?.role}
                                    trigger={callUserItemRenderFor2(item, 'row-span-full')}
                                />
                            );
                        }
                        return (
                            (isHistory || tBarStatus == "disconnected") ? callUserItemRenderFor3(item) : <Tooltip
                                key={'Tooltip' + item?.id || item?.phoneNumber}
                                content={item?.role}
                                trigger={callUserItemRenderFor3(item)}
                            />
                        );
                    })}
                </div>
            );
        }
        if (data?.length >= 4) {
            className = 'grid grid-rows-2 grid-cols-2 gap-4 w-max';
            return (
                <div className={className}>
                    {data?.map((item) => {
                        return (
                            (isHistory || tBarStatus == "disconnected") ? callUserItemRenderFor3(item) : <Tooltip
                                key={'Tooltip' + item?.id || item?.phoneNumber}
                                content={item?.role}
                                trigger={callUserItemRenderFor3(item)}
                            />
                        );
                    })}
                </div>
            );
        }
        return <></>;
    };
    const callEndButton = () => {
        return (
            <CallControlButton
                tooltip={'Disconnect'}
                tooltipPosition="right"
                onClick={() => {
                    callConverSationControl({
                        type: 'disconnect',
                        conversationId,
                        agentParticipantId: agent?.id,
                    });
                    // getSingleActiveConversation();
                }}
                icon={
                    <Icon
                        name="phone-end"
                        size={32}
                    />
                }
                className={cn(
                    'flex-none size-16 bg-status-danger hover:border-status-danger hover:opacity-80 text-white disabled:bg-grey-100 disabled:text-grey-300'
                    // tBarStatus === 'wrapup' && 'hidden'
                )}
                handleOnChange={() => null}
            // disabled={!conversationId}
            />
        );
    };

    useEffect(() => {
        let list: any[] = [];
        // if (acdData) {
        //     list.push(acdData);
        // }
        // if (customerData) {
        //     list.push(customerData);
        // }

        if (customerDatas && customerDatas?.length > 0) {
            const sortedData = customerDatas?.sort((a: any, b: any) => {
                const timeA = new Date(a?.connectedTime).getTime();
                const timeB = new Date(b?.connectedTime).getTime();
                return timeA - timeB;
            });
            sortedData?.forEach((item: any, index: number) => {
                item.role = index === 0 ? 'customer' : 'consult';
                list.push(item)
            });

        }

        if (consultDatas && consultDatas?.length > 0) {
            consultDatas?.forEach((item: any) => {
                list.push(item);
            })

        }

        if (voicemailData&&!isHistory) {
            list.push(voicemailData);
        }
        setParticipants(list);
    }, [customerDatas, consultDatas, voicemailData]);
    const formatTime = (dateTimeStr: string | null): string => {
        if (!dateTimeStr) return '';

        const date = new Date(dateTimeStr);
        const now = new Date();

        // Check if the date is today
        const isToday = date.toDateString() === now.toDateString();

        if (isToday) {
            // Format as HH:mm for today's dates
            return date.toLocaleTimeString('en-US', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
            });
        } else {
            // Format as yyyy/MM/dd for other dates
            return date
                .toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                })
                .replace(/\//g, '/');
        }
    };
    return convId !== conversationId ? (
        <>
            <div className="w-full h-full flex flex-col items-center justify-center">
                <IconEmptyRecords size="78" />
                <div className="text-grey-500">No selected interaction.</div>
            </div>
        </>
    ) : (
        <>
            {dialpadZindex > ivrZindex ? (
                <div className='flex-1 w-full flex justify-center overflow-auto pt-2'>
                    <div
                        className={cn(
                            'flex flex-col gap-6 items-center justify-around',
                            openDialPad && 'z-' + dialpadZindex,
                            (isHistory || tBarStatus == "disconnected") && (isConference || fullConference) ? "w-[70%]" : "w-full"
                        )}
                    >
                        {(!isConference && !fullConference) && (
                            <div className="flex flex-col items-center justify-center relative">
                                <p>
                                    <strong className="text-[24px]">{phoneNumber}</strong>
                                </p>
                                <div>
                                    <span className="text-[#DEDEDE]">
                                        {(isHistory || tBarStatus == "disconnected") ? <>{selectedInteraction?.missedCall ? formatTime(agent?.startTime || agent?.connectedTime) : "通话时长: " + time}</> : <>通话中: {time}</>}
                                    </span>
                                    <span>
                                        {(isHistory || tBarStatus == "disconnected") ? (
                                            !selectedInteraction?.missedCall ? (
                                                agent?.errorInfo?.message?.length > 0 ? <span className="text-[#FF271C]">  Dial out failed</span> :
                                                    <span className="text-[#1CC500]"> {tBarStatus}</span>
                                            ) : (
                                                <span className="text-[#FF271C]"> Missed</span>
                                            )
                                        ) : (
                                            ''
                                        )}
                                    </span>
                                </div>
                            </div>
                        )}
                        {/* <div className="h-max flex flex-col items-center"> */}
                        {(isConference || fullConference) && !openDialPad && (
                            <>
                                <div className="relative">
                                    {callUserRender(participants)}

                                </div>
                                <div className="w-full flex flex-col items-center justify-center">
                                    <div>
                                        <span className="text-[#DEDEDE]">
                                            {(isHistory || tBarStatus == "disconnected") ? (
                                                <>{selectedInteraction?.missedCall ? formatTime(agent?.startTime || agent?.connectedTime) : "通话时长: " + time}</>
                                            ) : (
                                                <>通话中: {time}</>
                                            )}
                                        </span>
                                        <span>
                                            {(isHistory || tBarStatus == "disconnected") ? (
                                                selectedInteraction?.missedCall ? (
                                                    <span className="text-[#FF271C]"> Missed</span>
                                                ) : (
                                                    agent?.errorInfo?.message?.lenght > 0 ? <span className="text-[#FF271C]">  Dial out failed</span> :
                                                        <span className="text-[#1CC500]"> {tBarStatus}</span>
                                                )
                                            ) : (
                                                ''
                                            )}
                                        </span>
                                    </div>
                                </div>
                            </>
                        )}
                        {openDialPad && (
                            <div>
                                <DialPad callEndButton={() => callEndButton()} conversationId={conversationId} participantId={agent?.id} />
                            </div>
                        )}
                        {!openDialPad && (!isConference && !fullConference) && (
                            <div>
                                <IconCallBg size="100%" />
                            </div>
                        )}
                        {/* </div> */}
                        {
                            (isHistory || tBarStatus == "disconnected") && (
                                isConference || fullConference ? (
                                    <></>
                                ) : <CallBack className="w-[50%]" triggerClassName="w-[30%]" number={phoneNumber} />
                            )
                        }
                        {!isHistory && (
                            tBarStatus == "disconnected" ? <></> : <div className="w-full flex flex-col items-center justify-center gap-4">
                                {
                                    !openDialPad && callEndButton()
                                    // <button
                                    //     className='hover:text-primary h-16 w-16 flex items-center justify-center rounded-full bg-[#FF271C]'
                                    //     onClick={() => { }}
                                    // >
                                    //     <IconPhoneEnd
                                    //         size={"32"}
                                    //         className='!fill-white'
                                    //     />
                                    // </button>
                                }
                                <div className="flex items-center gap-x-4 cdssctr-controlbar__call-btns">
                                    <CallControlButton
                                        tooltip={agent?.muted ? 'Unmute' : 'Mute'}
                                        icon={
                                            <Icon
                                                name="mute"
                                                size={25}
                                            />
                                        }
                                        className={cn('size-10')}
                                        active={agent?.muted}
                                        handleOnChange={() => handleCallControl('muteAndUnMute')}
                                        disabled={!selectedInteraction || isHistory}
                                    />
                                    <CallControlButton
                                        id="toolbar-hold-btn"
                                        tooltip={agent?.held ? 'Unhold' : 'Hold'}
                                        icon={
                                            <Icon
                                                name="hold"
                                                size={22}
                                            />
                                        }
                                        className={cn('size-10')}
                                        active={agent?.held}
                                        handleOnChange={() => handleCallControl('holdAndUnHold')}
                                        disabled={!selectedInteraction || isHistory}
                                    />
                                    <CallControlButton
                                        tooltip={'Transfer to IVR'}
                                        icon={
                                            <Icon
                                                name="ivr"
                                                size={25}
                                            />
                                        }
                                        onClick={() => {
                                            if (openTransferIVR) {
                                                setIvrZindex(1);
                                                setOpenTransferIVR(false);
                                            } else {
                                                setIvrZindex(dialpadZindex + 1);
                                                setOpenTransferIVR(true);
                                            }
                                        }}
                                        className={cn('size-10', openTransferIVR && 'bg-primary-300')}
                                        // active={activeModal === 'toolbar-ivr-panel'}
                                        handleOnChange={() => { }}
                                        disabled={
                                            !selectedInteraction ||
                                            isConference ||
                                            isHistory
                                        }
                                    />
                                    <CallControlButton
                                        tooltip={'Conference'}
                                        icon={
                                            <Icon
                                                name="conference"
                                                size={25}
                                            />
                                        }
                                        className={cn('size-10')}
                                        active={fullConference}
                                        disabled={
                                            participants?.some(participant => participant?.state === 'dialing' || participant?.state === 'alerting') ||
                                            fullConference ||
                                            !isConference ||
                                            isHistory ||
                                            consultInitiator === 'consult'
                                        }
                                        onClick={() =>
                                            conference('BOTH', conversationId, customerData?.id || '')
                                        }
                                    />
                                    <CallControlButton
                                        tooltip={'Dialpad'}
                                        onClick={() => {
                                            if (openDialPad) {
                                                setOpenDialPad(false);
                                                setDialpadZindex(2);
                                            } else {
                                                setDialpadZindex(ivrZindex + 1);
                                                setOpenDialPad(true);
                                            }
                                        }}
                                        icon={
                                            <Icon
                                                name="pad"
                                                size={25}
                                            />
                                        }
                                        className={cn('size-10', openDialPad && 'bg-primary-300')}
                                        // active={activeModal === 'dialpad-panel'}
                                        handleOnChange={() => null}
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            ) : (
                openTransferIVR && (
                    <TransferIVR
                        className={cn(openTransferIVR && 'z-' + ivrZindex)}
                        onOpen={() => {
                            setIvrZindex(1);
                            setOpenTransferIVR(false);
                        }}
                    />
                )
            )}

        </>
    );
});

export default CallPanel;
