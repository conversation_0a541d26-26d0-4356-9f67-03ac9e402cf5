import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { CallDetailBody } from "../../ui/CallDetail";
import { Toaster } from "@cdss-modules/design-system/components/_ui/Toast/toaster";
const queryClient = new QueryClient();
export default function Detail() {
    return (
        <>
        <QueryClientProvider client={queryClient}>
        <CallDetailBody/> 
        <Toaster />
        </QueryClientProvider>
        </>
       
    )
}