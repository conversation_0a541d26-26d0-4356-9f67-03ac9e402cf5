const isUAT = process.env['NEXT_PUBLIC_ENVIRONMENT'] !== 'dev';
export const apiConfig = {
  isUAT,
  paths: {
    session: isUAT
      ? '/api/process-api/ctint-session/ctint-bff-cdss/session'
      : '/api/session/ctint-session/ctint-bff-cdss/session',
    logout: `/api/process-api/ctint-auth/logout`,
    callControl: {
      getAllActiveConversations:
        '/api/process-api/ctint-conv/conversation/activeList',
      getAllConversations: '/api/process-api/ctint-conv/conversation',
      action: '/api/process-api/ctint-call-control/conversations/calls',
      userRoutingStatus:
        '/api/process-api/ctint-call-control/routingstatus/users',
      updateRoutingStatus:
        '/api/process-api/ctint-call-control/presences/users',
      getAllAgentStatus: '/api/process-api/ctint-call-control/presences',
      getAllStations: '/api/process-api/ctint-call-control/stations',
      call: '/api/process-api/ctint-call-control/conversations/call',
      blindTransfer:
        '/api/process-api/ctint-call-control/conversations/calls/blind/transfer',
      consult:
        '/api/process-api/ctint-call-control/conversations/calls/consult',
      consultDisconnect:
        '/api/process-api/ctint-call-control/conversations/calls/consult/disconnect',
      consultCancel:
        '/api/process-api/ctint-call-control/conversations/calls/consult/cancel',
      conference:
        '/api/process-api/ctint-call-control/conversations/calls/conference',
      getWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapupcodes',
      submitWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapup/add',
      updateWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapup/update',
      attributes:
        '/api/process-api/ctint-call-control/conversations/calls/attributes',
      getWrapupCategory: '/api/process-api/ctint-call-control/wrapUp',
      getCustomerInfo:'/api/process-api/ctint-conv/conversation/customer/info/query',///api/process-api/ctint-conv/conversation/customer/info/query
      getCustomerHistory:'/api/process-api/ctint-conv/interactions',///api/process-api/ctint-conv/interactions
      getInteractionHistoryDetail:'/api/process-api/ctint-conv/interaction/detail',///api/process-api/ctint-conv/interaction/detail/{conversationId}
      getContactDetail:'/api/process-api/ctint-conv/contact/detail',///api/process-api/ctint-conv/contact/detail/{conversationId}
      getInteractionHistoryRecordingId:'/api/process-api/ctint-conv/interaction/recordings',///api/process-api/ctint-conv/interaction/recordings/{conversationId}
      getInteractionHistoryRecording:'/api/process-api/ctint-conv/recordings',///api/process-api/ctint-conv/recordings/{recordingId}
      getSingleHistoryConversation:'/api/process-api/ctint-conv/conversation/call/history',///api/process-api/ctint-conv/ctint/conv/conversation/call/history/{conversationId}
      getSingleActiveConversation:'/api/process-api/ctint-conv/conversation/call/active',///api/process-api/ctint-conv/ctint/conv/conversation/call/active/{conversationId}
      digits:'/api/process-api/ctint-call-control/conversations/calls'///api/process-api/ctint-call-control/conversations/calls/{conversationId}/participants/{participantId}/digits
    },
    users: {
      getAllUsers: '/api/process-api/ctint-user/users', // /api/process-api/ctint-user/users?filterType=all
      getCurrentUser: '/api/process-api/ctint-user/user/me',
      getAllWorkgroups: '/api/process-api/ctint-user/queues', // /api/process-api/ctint-user/queues?filterType=all
      searchUsers: '/api/process-api/ctint-user/users/search',
      searchWorkgroups: '/api/process-api/ctint-user/queues/search',
      getWorkGroupsByUser: '/api/process-api/ctint-user/queues',
      getSAA: '/api/process-api/ctint-user/user/content/faq',
    },
    miniWallboard: {
      getUserStat:
        '/api/process-api/ctint-user/analytics/users/status/aggregates?type=miniwallboard',
      getUserConversationStat:
        '/api/process-api/ctint-user/analytics/users/converstation/aggregates?type=miniwallboard',
      getQueueStat:
        '/api/process-api/ctint-user/analytics/queues/status/aggregates?type=miniwallboard',
      getQueueConversationStat:
        '/api/process-api/ctint-user/analytics/queues/conversations/aggregates?type=miniwallboard ',
      getUserAggregates:
        '/api/process-api/ctint-user/users/conversations/aggregates?site=miniwallboard',
      getQueueAggregates:
        '/api/process-api/ctint-user/queues/aggregates?site=miniwallboard',
    },
    config: {
      getTenatConfig: '/api/process-api/ctint-config/tenantconfig',
      updateTenantConfig: '/api/process-api/ctint-config/tenantconfig',
    },
  },
};
