export type TSubmitWrapup = {
  id?: string;
  categoryName?: string;
  categoryCode?: string;
  groupName?: string;
  groupCode?: string;
  codeName?: string;
  wrapUpCode: string;
  wrapUpName: string;
  remark?: string;
};

export type TWrapupItem = {
  id: number;
  parentId: number;
  type: 'CATEGORY' | 'GROUP' | 'CODE';
  code: string;
  name: string;
  description: string;
  tenant: string;
  channel: string;
  isActive: boolean;
  items: TWrapupItem[] | null;
};

export type TWrapupContext = {
  selectLastActiveAgentId: string;
  selectLastActiveConvId: string;
  showWrapup: boolean;
  setShowWrapup: (data: boolean) => void;
  selectedWrapupList: any[];
  updateSelectedWrapupList: (data: any) => void;
  selectedWrapupId: string | null;
  updateSelectedWrapupId: (id: string | null) => void;
  wrapupCategoryListHandle:any
};
