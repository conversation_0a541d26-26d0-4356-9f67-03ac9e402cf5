{"name": "ctint-mf-campaign", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-campaign", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-campaign", "outputPath": "dist/apps/ctint-mf-campaign"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-campaign:build", "dev": true, "port": 4308, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-campaign:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-campaign:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-campaign:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-campaign"], "options": {"jestConfig": "apps/ctint-mf-campaign/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-campaign/**/*.{ts,tsx,js,jsx}"]}}}}