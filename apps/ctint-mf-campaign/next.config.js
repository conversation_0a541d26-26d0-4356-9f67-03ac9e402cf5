// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');
const { NextFederationPlugin } = require('@module-federation/nextjs-mf');
const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// Config
const mfName = 'ctint-mf-campaign';

// Get MF paths (Host, Base Path or Full URL) from config
const getMFPath = (targetMFName) => {
  const yamlEnv = process?.env?.CDSS_PUBLIC_ENVIRONMENT || 'dev';
  // TODO: move to a common location
  let yamlPath = path.join(
    process.cwd(),
    `apps/${mfName}/public/config/ctint-global-config-${yamlEnv}.yaml`
  );
  if (!fs.existsSync(yamlPath)) {
    yamlPath = path.join(
      process.cwd(),
      `public/config/ctint-global-config-${yamlEnv}.yaml`
    );
    if (!fs.existsSync(yamlPath)) {
      throw new Error(`Configuration file not found: ${yamlPath}`);
    }
  }
  const fileContents = fs.readFileSync(yamlPath, 'utf8');
  const configData = yaml.load(fileContents);
  const mfHost = configData?.microfrontends?.[targetMFName]?.host || '';
  const mfBasePath = configData?.microfrontends?.[targetMFName]?.basepath || '';
  return {
    host: mfHost,
    basePath: mfBasePath,
    url: mfHost + mfBasePath,
  };
};

const remotes = (isServer) => {
  const location = isServer ? 'ssr' : 'chunks';
  return {
    cdss: `cdss@${getMFPath('ctint-mf-cdss').url}/_next/static/${location}/remoteEntry.js`,
  };
};

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  output: 'standalone',
  basePath: getMFPath(mfName).basePath,
  swcMinify: false,
  env: {
    mfName: mfName,
    basePath: getMFPath(mfName).basePath,
    languages: '',
  },
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  experimental: { esmExternals: 'loose' },
  /**
   *
   * @param {import('webpack').Configuration} config
   * @returns {import('webpack').Configuration}
   */
  webpack(config, { isServer }) {
    if (!isServer) {
      config.resolve.fallback.fs = false;
    }
    config.output.publicPath = 'auto';
    config.plugins.push(
      new NextFederationPlugin({
        name: 'campaign',
        filename: 'static/chunks/remoteEntry.js',
        remotes: remotes(isServer),
        extraOptions: {
          automaticAsyncBoundary: true,
        },
        exposes: {
          './main': './components/_screen/Main/index.tsx',
          './detail': './components/_screen/Detail/index.tsx',
          './locales': './i18n/locales/index.ts',
        },
        shared: {
          'react-singleton-context': { singleton: true, eager: true },
          'react-router-dom': { singleton: true, eager: true },
          'react-i18next': { singleton: true, eager: true },
          i18next: { singleton: true, eager: true },
          zustand: { singleton: true, eager: true },
        },
      })
    );

    return config;
  },
  // Add rewrites
  async rewrites() {
    return [
      {
        source: '/api/session/:path*',
        destination: `/api/mock/:path*`,
      },
      // Redirect all paths to root for SPA handling
      {
        source: `/:path((?!api|process-api).*)`,
        destination: '/',
      },
    ];
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
