import _ from 'lodash';
// Global translations
import globalEn from '@cdss-modules/design-system/i18n/locales/en/translation.json';
import globalHK from '@cdss-modules/design-system/i18n/locales/zh-HK/translation.json';
import globalCN from '@cdss-modules/design-system/i18n/locales/zh-HK/translation.json';
import globalJa from '@cdss-modules/design-system/i18n/locales/ja/translation.json';

const globalLocales = {
  en: globalEn,
  'zh-HK': globalHK,
  'zh-CN': globalCN,
  ja: globalJa,
};

// Local translations
import en from './en/translation.json';
import hk from './zh-HK/translation.json';
import cn from './zh-HK/translation.json';
import ja from './ja/translation.json';

export const mfLocales = {
  en: en,
  'zh-HK': hk,
  'zh-CN': cn,
  ja: ja,
};

export const appLocales = _.merge({}, globalLocales, mfLocales);

export const prepareLocales = async (callback: (localeData: any) => void) =>
  callback(appLocales);

export const allLocales = {
  mfLocales,
  appLocales,
  prepareLocales,
};

export default allLocales;
