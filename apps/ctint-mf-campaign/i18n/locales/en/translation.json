{"ctint-mf-broadcast": {"filter": {"title": "Filter", "add": "Add Filter", "search": "Search", "clear": "Clear All", "available": "Available Filters", "save": "Save filters", "clearSave": "Clear saved filters", "result": "Result", "columns": "columns", "prepare": "Preparing", "download": "Download", "downloadList": "Download list", "selectedList": "Selected interactions", "reload": "Reload", "addColumns": "Add / Remove Columns", "saveColumns": "Save columns", "clearColumns": "Clear saved columns", "apply": "Apply", "view": "View", "recording": "Recording", "recordingError": "There is a error loading the audio file. ", "onlyId": "If checked, only search by Interaction ID.", "greaterOrLess": "Greater, Less than or Equal to", "greater": "Greater than", "less": "Less than", "equal": "Equal", "allMediaSource": "All Media Source", "mediaSource": "Media Source", "evaluation": "Evaluation", "all": "All", "filter": "filter", "filters": "filters", "applied": " applied", "noResult": "No result(s).", "voice": "Voice", "chat": "Cha<PERSON>", "message": "Message", "email": "Email", "callback": "CallBack", "allMediaType": "All Media Type", "reset": "Reset"}, "audio": {"jumpToTime": "Jump to time", "go": "Go", "speed": "Speed", "invalidTime": "Invalid time"}, "transcript": {"generate": "Generate Transcript", "processing": "Transcript is processing..."}, "columns": {"id": "Interaction ID", "conversationId": "Conversation ID", "conversationStart": "Start Time", "conversationEnd": "End Time", "conversationDuration": "Duration", "direction": "Direction", "users": "Users", "mediaType": "Type", "dnis": "DNIS", "mediaUri": "Media URI", "ani": "ANI", "recordingMediaSource": "Media Source", "inbound": "Inbound", "outbound": "Outbound", "queues": "Queues", "divisions": "Divisions", "customerRemote": "Customer Remote", "wrapups": "W<PERSON><PERSON>", "recording": "Recording", "provider": "Provider", "evaluation": "QM", "metadata": "<PERSON><PERSON><PERSON>"}, "details": {"metrics": "Interaction Metrics", "wrapups": "Wrap-up Information", "participant": "Participant Data", "recordings": "Recordings", "internalParticipantTitle": "Internal Participant(s)", "externalParticipantTitle": "External Participant(s)", "disconnectReasonTitle": "Disconnect Reason", "notAvailable": "Not Available", "reGenerate": "Re-generate", "loading": "Loading..."}}}