import React from 'react';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import { Controller, UseFormReturn } from 'react-hook-form';

// 定义 DatePickerField 属性接口
interface DatePickerFieldProps {
  methods: UseFormReturn<any, any, undefined>;
}

// 格式化日期为 yyyy-MM-dd
const formatDate = (date: Date | null): string => {
  if (!date) return '';
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

const DatePickerField: React.FC<DatePickerFieldProps> = ({ methods }) => {
  return (
    <Field
      title="Date"
      isRequired
    >
      <Controller
        name="date"
        control={methods.control}
        render={({ field }) => {
          // 拆分时间值
          const getHoursValue = (): string => {
            try {
              return field.value && field.value instanceof Date
                ? String(field.value.getHours()).padStart(2, '0')
                : '';
            } catch (e) {
              return '';
            }
          };

          const getMinutesValue = (): string => {
            try {
              return field.value && field.value instanceof Date
                ? String(field.value.getMinutes()).padStart(2, '0')
                : '';
            } catch (e) {
              return '';
            }
          };

          const getSecondsValue = (): string => {
            try {
              return field.value && field.value instanceof Date
                ? String(field.value.getSeconds()).padStart(2, '0')
                : '';
            } catch (e) {
              return '';
            }
          };

          // 处理日期变化
          const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            const dateStr = e.target.value;
            if (!dateStr) return;

            const newDate =
              field.value instanceof Date ? new Date(field.value) : new Date();

            const [year, month, day] = dateStr.split('-').map(Number);
            newDate.setFullYear(year);
            newDate.setMonth(month - 1);
            newDate.setDate(day);

            field.onChange(newDate);
          };

          // 处理时间变化
          const handleTimeChange = (
            type: 'hours' | 'minutes' | 'seconds',
            valueStr: string
          ) => {
            const value = parseInt(valueStr) || 0;
            const newDate =
              field.value instanceof Date ? new Date(field.value) : new Date();

            if (type === 'hours') {
              newDate.setHours(value);
            } else if (type === 'minutes') {
              newDate.setMinutes(value);
            } else if (type === 'seconds') {
              newDate.setSeconds(value);
            }

            field.onChange(newDate);
          };

          // 设置为当前日期时间
          const setToday = () => {
            field.onChange(new Date());
          };

          // 清除日期
          const clearDate = () => {
            field.onChange(null);
          };

          return (
            <div className="date-time-picker-container w-full">
              <div className="w-full bg-white border border-gray-300 rounded-md shadow-sm">
                {/* 日期选择 */}
                <div className="p-2">
                  <input
                    type="date"
                    className="block w-full h-10 outline-none ring-0 p-2 border border-grey-300 rounded-md focus:border-primary"
                    value={formatDate(field.value)}
                    onChange={handleDateChange}
                    required
                  />
                </div>

                {/* 时间选择 - 直接放在日期选择器下方 */}
                <div className="flex items-center space-x-2 p-2 border-t border-gray-200">
                  <input
                    type="number"
                    min="0"
                    max="23"
                    placeholder="HH"
                    className="w-12 p-1 border border-gray-300 rounded text-center"
                    value={getHoursValue()}
                    onChange={(e) => handleTimeChange('hours', e.target.value)}
                  />
                  <span>:</span>
                  <input
                    type="number"
                    min="0"
                    max="59"
                    placeholder="MM"
                    className="w-12 p-1 border border-gray-300 rounded text-center"
                    value={getMinutesValue()}
                    onChange={(e) =>
                      handleTimeChange('minutes', e.target.value)
                    }
                  />
                  <span>:</span>
                  <input
                    type="number"
                    min="0"
                    max="59"
                    placeholder="SS"
                    className="w-12 p-1 border border-gray-300 rounded text-center"
                    value={getSecondsValue()}
                    onChange={(e) =>
                      handleTimeChange('seconds', e.target.value)
                    }
                  />
                </div>

                {/* 底部按钮 */}
                <div className="flex justify-between p-2 border-t border-gray-200">
                  <button
                    type="button"
                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                    onClick={clearDate}
                  >
                    清除
                  </button>
                  <button
                    type="button"
                    className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800"
                    onClick={setToday}
                  >
                    今天
                  </button>
                </div>
              </div>
            </div>
          );
        }}
      />
    </Field>
  );
};

export default DatePickerField;
