import React, { useEffect, useState } from 'react';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { BroadcastEdit } from './broadcastEdit';
import { BroadcastView } from './broadcastView';
import { Toaster, useRouteHandler } from '@cdss-modules/design-system';

const queryClient = new QueryClient();

const BroadcastDetail = () => {
  const { searchParams } = useRouteHandler();

  // 直接从 URL 获取参数
  const modeParam = searchParams.get('mode');

  // 使用原始参数值进行渲染决策
  const isEditMode = modeParam === 'edit' || modeParam === 'copy';

  return (
    <QueryClientProvider client={queryClient}>
      {isEditMode ? <BroadcastEdit /> : <BroadcastView />}
      <Toaster />
    </QueryClientProvider>
  );
};

export default BroadcastDetail;
