import React, { useState, useEffect, useRef } from 'react';
import { useF<PERSON>, Form<PERSON><PERSON>ider, Controller } from 'react-hook-form';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import BroadcastHeader from './broadcastHeader';
import ReactDatePicker from 'react-datepicker';
import dayjs from 'dayjs';
import 'react-datepicker/dist/react-datepicker.css';
import {
  saveOrUpdate,
  getDetail,
  getContactGroupList,
  getOptOutList,
  getMessageTemplateList,
  downloadContactGroupTemplate,
  uploadContactGroup,
  uploadOptOutGroup,
  getMessagePlatformAccount,
  getMessageTemplateDetail,
} from '../../../lib/api/index';
import { toast, useRole, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import TemplateDetailPreview, { TemplateData } from './messagePreview';
import { string } from 'yup';

export const BroadcastEdit = () => {
  const [loading, setLoading] = useState(false);
  const [contactGroupOptions, setContactGroupOptions] = useState<
    Array<{ id: string; value: string; label: string }>
  >([]);
  const [optOutGroupOptions, setOptOutGroupOptions] = useState<
    Array<{ id: string; value: string; label: string }>
  >([]);
  const [messageTemplateOptions, setMessageTemplateOptions] = useState<
    Array<{ id: string; value: string; label: string }>
  >([]);
  const [fromOptions, setFromOptions] = useState<
    Array<{ id: string; value: string; label: string }>
  >([]);

  // Template state management
  const [templateLoading, setTemplateLoading] = useState(false);
  const [templateData, setTemplateData] = useState<TemplateData | null>(null);
  const [headerImages, setHeaderImages] = useState({});
  const { basePath, toPath, searchParams } = useRouteHandler();
  const contactFileInputRef = useRef<HTMLInputElement>(null);
  const optOutFileInputRef = useRef<HTMLInputElement>(null);

  // 从URL获取模式和ID
  const mode = searchParams.get('mode') || 'edit';
  const broadcastId = searchParams.get('id') || '';
  const isCopyMode = mode === 'copy';
  const isNewBroadcast = broadcastId === 'new' || broadcastId === '';

  const methods = useForm({
    defaultValues: {
      broadcastName: '',
      from: '',
      messageTemplate: '',
      date: null,
      notificationEmail: '',
      contactGroup: '',
      optOutGroup: '',
    },
  });

  const { globalConfig } = useRole();
  const microfrontendsConfig = globalConfig?.microfrontends || {};
  const campaignConfig = microfrontendsConfig['ctint-mf-campaign'] || {};
  const filterTemplatePrefix: string[] =
    campaignConfig.filterTemplatePrefix || [];

  // Load message templates based on selected account
  // const loadMessageTemplates = async (account: string) => {
  //   if (!account) {
  //     setMessageTemplateOptions([]);
  //     setTemplateData(null);
  //     return;
  //   }
  //
  //   try {
  //     setTemplateLoading(true);
  //
  //     // 保存当前选中的模板ID
  //     const currentTemplateId = methods.getValues('messageTemplate');
  //     console.log('Loading templates for account:', account);
  //     console.log('Saving current template ID:', currentTemplateId);
  //
  //     // 获取模板列表
  //     const templateResponse = await getMessageTemplateList(basePath, account);
  //     if (templateResponse.data?.data) {
  //       const options = templateResponse.data.data.map((item: any) => ({
  //         id: item.id,
  //         value: item.id,
  //         label: item.name,
  //       }));
  //       console.log('Loaded template options:', options);
  //       setMessageTemplateOptions(options);
  //     } else {
  //       setMessageTemplateOptions([]);
  //       console.warn('Message template data is null or undefined');
  //     }
  //   } catch (error) {
  //     console.error('Error fetching message templates:', error);
  //     toast({
  //       title: 'Error',
  //       description: 'Failed to load message templates',
  //       variant: 'error',
  //     });
  //     setMessageTemplateOptions([]);
  //   } finally {
  //     setTemplateLoading(false);
  //   }
  // };
  const loadMessageTemplates = async (account: string) => {
    if (!account) {
      setMessageTemplateOptions([]);
      setTemplateData(null);
      return;
    }

    try {
      setTemplateLoading(true);
      // 保存当前选中的模板ID
      const currentTemplateId = methods.getValues('messageTemplate');
      console.log('Loading templates for account:', account);
      console.log('Saving current template ID:', currentTemplateId);
      console.log('filterTemplatePrefix:', filterTemplatePrefix);
      // 获取模板列表
      const templateResponse = await getMessageTemplateList(basePath, account);
      if (templateResponse.data?.data) {
        let options = templateResponse.data.data.map((item: any) => ({
          id: item.id,
          value: item.id,
          label: item.name,
        }));
        // 根据配置过滤模板 - 支持字符串数组
        if (
          Array.isArray(filterTemplatePrefix) &&
          filterTemplatePrefix.length > 0
        ) {
          console.log(
            'Filtering templates with prefixes:',
            filterTemplatePrefix
          );
          // 过滤掉所有匹配前缀的模板
          options = options.filter((option: any) => {
            const shouldExclude = filterTemplatePrefix.some(
              (prefix: string) => {
                const trimmedPrefix = prefix?.trim();
                return trimmedPrefix && option.label.startsWith(trimmedPrefix);
              }
            );

            if (shouldExclude) {
              console.log('Excluding template:', option.label);
            }
            return !shouldExclude;
          });

          console.log('Templates after filtering:', options.length);
        } else {
          console.log(
            'No template filtering applied - filterTemplatePrefix is empty or not an array'
          );
        }

        console.log('Final template options:', options);
        setMessageTemplateOptions(options);
      } else {
        setMessageTemplateOptions([]);
        console.warn('Message template data is null or undefined');
      }
    } catch (error) {
      console.error('Error fetching message templates:', error);
      toast({
        title: 'Error',
        description: 'Failed to load message templates',
        variant: 'error',
      });
      setMessageTemplateOptions([]);
    } finally {
      setTemplateLoading(false);
    }
  };

  // Get template details when template is selected
  const loadMessageTemplateDetail = async (messageTemplateId: string) => {
    try {
      setTemplateLoading(true);
      const templateResponse = await getMessageTemplateDetail(
        messageTemplateId,
        basePath
      );

      if (templateResponse.data?.data) {
        console.log('Message template details:', templateResponse.data.data);
        setTemplateData(templateResponse.data.data);
      } else {
        console.warn('Message template details is null or undefined');
        setTemplateData(null);
      }
    } catch (error) {
      console.error('Error fetching template details:', error);
      toast({
        title: 'Error',
        description: 'Failed to load message template details',
        variant: 'error',
      });
      setTemplateData(null);
    } finally {
      setTemplateLoading(false);
    }
  };

  const handleImageUpload = (componentId: string, imageData: any) => {
    console.log(
      'Handling image upload in broadcastEdit:',
      componentId,
      imageData
    );

    setHeaderImages((prev) => ({
      ...prev,
      [componentId]: imageData,
    }));

    // Also update the templateData to reflect the change
    if (templateData && templateData.components) {
      const componentIndex = parseInt(componentId.split('-')[1], 10);

      if (
        !isNaN(componentIndex) &&
        componentIndex >= 0 &&
        componentIndex < templateData.components.length
      ) {
        // Create a deep copy of the template data
        const newTemplateData = JSON.parse(JSON.stringify(templateData));

        // Update the header image URL
        if (
          newTemplateData.components[componentIndex].type === 'HEADER' &&
          newTemplateData.components[componentIndex].format === 'IMAGE'
        ) {
          // 同时更新两个字段用于预览
          newTemplateData.components[componentIndex].headerImage =
            imageData.url;
          newTemplateData.components[componentIndex].headerImageProxy =
            imageData.proxyUrl;
          setTemplateData(newTemplateData);
          console.log('Updated template data with new image:', newTemplateData);
        }
      }
    }
  };

  // Load broadcast data if in copy mode or editing an existing broadcast
  // broadcastEdit.tsx - 完整正确的 useEffect 代码

  useEffect(() => {
    const loadBroadcastData = async () => {
      if (broadcastId !== 'new') {
        try {
          setLoading(true);
          const response = await getDetail(broadcastId, basePath);

          if (response.data?.data) {
            const broadcastData = response.data.data;

            // 获取模板信息和ID
            let templateId = '';
            let parsedTemplate = null;

            console.log('Template data in broadcast:', broadcastData.template);

            // 判断模板数据类型并提取ID和完整数据
            if (broadcastData.template) {
              if (typeof broadcastData.template === 'string') {
                try {
                  // 尝试解析JSON字符串
                  parsedTemplate = JSON.parse(broadcastData.template);
                  templateId = parsedTemplate.id || '';
                  console.log('Parsed template:', parsedTemplate);
                  console.log('Parsed template ID:', templateId);
                } catch (e) {
                  // 如果解析失败，可能是普通的ID字符串
                  templateId = broadcastData.template;
                  console.log('Template as string ID:', templateId);
                }
              } else if (typeof broadcastData.template === 'object') {
                // 如果是对象，直接使用
                parsedTemplate = broadcastData.template;
                templateId = parsedTemplate.id || '';
                console.log('Template object:', parsedTemplate);
                console.log('Template object ID:', templateId);
              }
            }

            // 先设置其他表单字段
            const fromValue = broadcastData.from || '';
            let sendDate: any = null;
            if (broadcastData.sendDate) {
              sendDate = new Date(broadcastData.sendDate);
            }

            // 设置基本表单值
            methods.setValue('from', fromValue);
            methods.setValue('date', sendDate || null);
            methods.setValue(
              'contactGroup',
              broadcastData.contactGroupId || ''
            );
            methods.setValue('optOutGroup', broadcastData.optOutGroupId || '');
            methods.setValue(
              'notificationEmail',
              broadcastData.notificationRecipient ||
                broadcastData.notificationEmail ||
                ''
            );

            // 设置广播名称
            if (isCopyMode) {
              methods.setValue(
                'broadcastName',
                `${broadcastData.name || ''} - Copy`
              );
            } else {
              methods.setValue('broadcastName', broadcastData.name || '');
            }

            // 重要：先加载模板选项，再设置模板ID和数据
            if (fromValue) {
              // 1. 先等待模板列表加载
              await loadMessageTemplates(fromValue);

              // 2. 检查templateId是否有效
              if (templateId) {
                console.log('Ready to set message template ID:', templateId);

                // 3. 设置模板ID并加载详情
                setTimeout(() => {
                  console.log(
                    'Setting message template with delay:',
                    templateId
                  );
                  methods.setValue('messageTemplate', templateId, {
                    shouldValidate: true,
                    shouldDirty: true,
                    shouldTouch: true,
                  });

                  // 4. 如果有完整的模板数据，直接使用；否则重新加载
                  if (parsedTemplate && parsedTemplate.components) {
                    console.log('Using parsed template data:', parsedTemplate);
                    setTemplateData(parsedTemplate);

                    // 重要：初始化已有的图片状态
                    const initialImages: {
                      [key: string]: {
                        url: string;
                        proxyUrl?: string;
                        name: string;
                        isNewUpload: boolean;
                      };
                    } = {};
                    parsedTemplate.components.forEach(
                      (component: any, index: number) => {
                        if (
                          component.type === 'HEADER' &&
                          component.format === 'IMAGE'
                        ) {
                          const componentId = `header-${index}`;
                          if (
                            component.headerImageProxy ||
                            component.headerImage
                          ) {
                            initialImages[componentId] = {
                              url: component.headerImage || '',
                              proxyUrl:
                                component.headerImageProxy ||
                                component.headerImage ||
                                '',
                              name: 'Existing Image',
                              isNewUpload: false, // 标记为已有图片
                            };
                          }
                        }
                      }
                    );

                    console.log(
                      'Setting initial header images:',
                      initialImages
                    );
                    setHeaderImages(initialImages);
                  } else {
                    // 如果没有完整数据，重新加载模板详情
                    loadMessageTemplateDetail(templateId);
                  }
                }, 300);
              }
            }
          }
        } catch (error) {
          console.error('Error loading broadcast data:', error);
          toast({
            title: 'Error',
            description: 'Failed to load broadcast data',
            variant: 'error',
          });
        } finally {
          setLoading(false);
        }
      }
    };

    // Get dropdown data first, then load broadcast data
    fetchDropdownData().then(() => {
      if (isCopyMode || (!isNewBroadcast && mode === 'edit')) {
        loadBroadcastData();
      }
    });
  }, [broadcastId, mode, basePath, isCopyMode, isNewBroadcast, methods]);

  useEffect(() => {
    // 监听模板选项变化，确保在选项加载后设置正确的选中值
    if (messageTemplateOptions.length > 0) {
      // 获取当前保存的模板ID
      const currentTemplateId = methods.getValues('messageTemplate');

      console.log(
        'Template options updated, current options:',
        messageTemplateOptions
      );
      console.log('Current template ID:', currentTemplateId);

      // 检查当前ID是否存在于选项中
      if (currentTemplateId) {
        const templateExists = messageTemplateOptions.some(
          (opt) => opt.value === currentTemplateId
        );

        if (templateExists) {
          console.log(
            'Template found in options, ensuring selection is set to:',
            currentTemplateId
          );
          // 确保值被设置，使用setTimeout避免React批处理更新的问题
          setTimeout(() => {
            methods.setValue('messageTemplate', currentTemplateId, {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });
          }, 100);
        } else {
          console.warn('Template ID not found in options:', currentTemplateId);
        }
      }
    }
  }, [messageTemplateOptions]);
  // Fetch dropdown data
  const fetchDropdownData = async () => {
    try {
      setLoading(true);
      // Get contact group list
      const contactGroupResponse = await getContactGroupList(basePath);
      if (contactGroupResponse.data?.data) {
        const options = contactGroupResponse.data.data.map((item: any) => ({
          id: item.id,
          value: item.id,
          label: item.name,
        }));
        setContactGroupOptions(options);
      } else {
        setContactGroupOptions([]);
        console.warn('Contact group data is null or undefined');
      }

      // Get opt-out group list
      const optOutResponse = await getOptOutList(basePath);
      if (optOutResponse.data?.data) {
        const options = optOutResponse.data.data.map((item: any) => ({
          id: item.id,
          value: item.id,
          label: item.name,
        }));
        setOptOutGroupOptions(options);
      } else {
        setOptOutGroupOptions([]);
        console.warn('Opt-out group data is null or undefined');
      }

      // Get message platform account list
      const accountResponse = await getMessagePlatformAccount(basePath);
      if (accountResponse.data?.data) {
        const options = accountResponse.data.data.map(
          (item: any, index: number) => ({
            id: String(index + 1),
            value: item.platformAccount,
            label: item.platformAccount,
          })
        );
        setFromOptions(options);
      } else {
        setFromOptions([]);
        console.warn('Message platform account data is null or undefined');
      }
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
      toast({
        title: 'Error',
        description: 'Failed to load dropdown options',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: any) => {
    if (data.date) {
      const selectedDate = new Date(data.date);
      const currentDate = new Date();

      if (selectedDate < currentDate) {
        toast({
          title: 'Time Error',
          description:
            'The sending time cannot be earlier than the current time',
          variant: 'error',
        });
        return;
      }
    }

    try {
      // 首先验证必填字段
      const requiredFields = [
        { name: 'broadcastName', label: 'Broadcast Name' },
        { name: 'from', label: 'From' },
        { name: 'messageTemplate', label: 'Message Template' },
        { name: 'date', label: 'Date' },
        { name: 'contactGroup', label: 'Contact Group' },
      ];

      const missingFields = requiredFields.filter((field) => !data[field.name]);

      if (missingFields.length > 0) {
        toast({
          title: 'Validation Error',
          description: `Please fill in all required fields: ${missingFields.map((f) => f.label).join(', ')}`,
          variant: 'error',
        });
        return;
      }

      setLoading(true);
      console.log('Form submitted:', data);

      // 1. 验证所有必需的图片是否都已上传
      if (templateData && templateData.components) {
        const missingImages: string[] = [];

        templateData.components.forEach((component, index) => {
          if (component.type === 'HEADER' && component.format === 'IMAGE') {
            const componentId = `header-${index}`;
            const hasNewImage = (headerImages as any)[componentId]?.url;
            const hasExistingImage = component.headerImage;

            if (!hasNewImage && !hasExistingImage) {
              missingImages.push(`Header Image ${index + 1}`);
            }
          }
        });

        if (missingImages.length > 0) {
          toast({
            title: 'Validation Error',
            description: `Please upload required images: ${missingImages.join(', ')}`,
            variant: 'error',
          });
          setLoading(false);
          return;
        }
      }

      // 2. 处理templateData的深拷贝，避免直接修改状态
      let processedTemplateData = null;

      if (templateData) {
        processedTemplateData = JSON.parse(JSON.stringify(templateData));

        if (
          processedTemplateData.components &&
          Array.isArray(processedTemplateData.components)
        ) {
          processedTemplateData.components =
            processedTemplateData.components.map(
              (component: any, index: any) => {
                if (
                  component.type === 'HEADER' &&
                  component.format === 'IMAGE'
                ) {
                  const componentId = `header-${index}`;

                  // 检查用户是否上传了该组件的图片
                  if ((headerImages as any)[componentId]) {
                    const uploadedImage = (headerImages as any)[componentId];

                    if (uploadedImage.isNewUpload) {
                      // 新上传的图片：同时设置两个字段
                      return {
                        ...component,
                        headerImage: uploadedImage.url, // 原始URL
                        headerImageProxy: uploadedImage.proxyUrl, // 代理URL
                      };
                    } else {
                      // 已有的图片：保持原来的headerImage，只在需要时新增headerImageProxy
                      return {
                        ...component,
                        // 如果已有 headerImageProxy 则保持不变，否则新增
                        ...(uploadedImage.proxyUrl &&
                          !component.headerImageProxy && {
                            headerImageProxy: uploadedImage.proxyUrl,
                          }),
                      };
                    }
                  }
                }
                return component;
              }
            );
        }
      }

      // 3. 构建请求数据
      const requestData = {
        name: data.broadcastName,
        from: data.from,
        template: processedTemplateData
          ? JSON.stringify(processedTemplateData)
          : data.messageTemplate,
        sendDate: data.date
          ? dayjs(data.date).format('YYYY-MM-DDTHH:mm:ss[Z]')
          : '',
        contactGroupId: data.contactGroup,
        optOutGroupId: data.optOutGroup || undefined,
        type: 'message',
        notificationRecipient: data.notificationEmail,
        notificationType: 'email',
      };

      if (!isCopyMode && !isNewBroadcast) {
        (requestData as any).id = broadcastId;
      }

      console.log('Sending request data:', requestData);
      const response = await saveOrUpdate(requestData, basePath);

      toast({
        title: 'Success',
        description: isCopyMode
          ? 'Broadcast copied successfully'
          : 'Broadcast saved successfully',
        variant: 'success',
      });

      toPath(`/campaign?refresh=${Date.now()}`);
    } catch (error) {
      console.error('Error saving broadcast:', error);
      toast({
        title: 'Error',
        description:
          'Failed to save broadcast: ' +
          ((error as any).response?.data.error || (error as any).message),
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Download contact group template
  const handleDownloadTemplate = async () => {
    try {
      setLoading(true);

      // Get the currently selected message template ID
      const messageTemplateId = methods.getValues('messageTemplate');

      // Check if message template is selected
      if (!messageTemplateId) {
        toast({
          title: 'Warning',
          description: 'Please select a message template first',
          variant: 'warning',
        });
        setLoading(false);
        return;
      }

      // Call the API with both basePath and messageTemplateId
      const response = await downloadContactGroupTemplate(
        basePath,
        messageTemplateId
      );

      const blob = new Blob([response.data], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'contact_group_template.csv';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: 'Success',
        description: 'Template downloaded successfully',
        variant: 'success',
      });
    } catch (error: any) {
      console.error('Error downloading template:', error);
      toast({
        title: 'Error',
        description: 'Failed to download template',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };
  // Upload contact group file

  const handleUploadContactGroup = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!event.target.files || event.target.files.length === 0) return;

    const file = event.target.files[0];
    if (!file) return;

    try {
      setLoading(true);

      // 保存上传前的组列表，用于对比
      const beforeUploadGroups = [...contactGroupOptions];
      console.log(
        'Groups before upload:',
        beforeUploadGroups.map((g: any) => g.label).join(', ')
      );

      // 保存文件名，用于后续匹配
      const fileName = file.name;
      const fileNameWithoutExtension = fileName.replace(/\.[^/.]+$/, '');
      console.log('Uploading file:', fileName);
      console.log('File name without extension:', fileNameWithoutExtension);

      // 上传文件
      await uploadContactGroup(file, basePath);

      // 延迟1秒后再获取列表，确保服务器有时间处理
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 重新获取联系人组列表
      const contactGroupResponse = await getContactGroupList(basePath);
      if ((contactGroupResponse as any).data?.data) {
        const newOptions = (contactGroupResponse as any).data.data.map(
          (item: any) => ({
            id: item.id,
            value: item.id,
            label: item.name,
          })
        );

        console.log(
          'Groups after upload:',
          newOptions.map((g: any) => g.label).join(', ')
        );

        // 先更新下拉列表选项
        setContactGroupOptions(newOptions);

        // 找到新增的组（通过对比上传前后的列表）
        const newGroups = newOptions.filter(
          (newGroup: any) =>
            !beforeUploadGroups.some(
              (oldGroup: any) => oldGroup.id === newGroup.id
            )
        );

        console.log(
          'New groups found:',
          newGroups.map((g: any) => g.label).join(', ')
        );

        // 使用setTimeout确保在下一个事件循环中设置值
        setTimeout(() => {
          let selectedGroup = null;

          // 优先策略1: 如果有新增的组，优先选择与文件名最匹配的新组
          if (newGroups.length > 0) {
            // 精确匹配文件名
            selectedGroup = newGroups.find(
              (group: any) =>
                group.label === fileName ||
                group.label === fileNameWithoutExtension
            );

            // 如果没有精确匹配，尝试模糊匹配
            if (!selectedGroup) {
              selectedGroup = newGroups.find((group: any) => {
                const groupName = group.label.toLowerCase();
                const fileNameLower = fileName.toLowerCase();
                const fileNameNoExtLower =
                  fileNameWithoutExtension.toLowerCase();

                return (
                  groupName.includes(fileNameNoExtLower) ||
                  groupName === fileNameLower ||
                  fileNameNoExtLower.includes(groupName)
                );
              });
            }

            // 如果还是没找到，就选第一个新组
            if (!selectedGroup) {
              selectedGroup = newGroups[0];
              console.log(
                'No exact match found, selecting first new group:',
                selectedGroup.label
              );
            }
          }

          // 策略2: 如果没有新组，在所有组中查找匹配
          if (!selectedGroup) {
            console.log('No new groups found, searching in all groups');

            // 精确匹配
            selectedGroup = newOptions.find(
              (group: any) =>
                group.label === fileName ||
                group.label === fileNameWithoutExtension
            );

            // 模糊匹配
            if (!selectedGroup) {
              selectedGroup = newOptions.find((group: any) => {
                const groupName = group.label.toLowerCase();
                const fileNameLower = fileName.toLowerCase();
                const fileNameNoExtLower =
                  fileNameWithoutExtension.toLowerCase();

                return (
                  groupName.includes(fileNameNoExtLower) ||
                  fileNameNoExtLower.includes(groupName)
                );
              });
            }

            // 最后的策略：选择最新的组（ID最大的）
            if (!selectedGroup && newOptions.length > 0) {
              selectedGroup = newOptions.reduce((latest: any, current: any) => {
                return current.id > latest.id ? current : latest;
              });
              console.log('Selecting latest group by ID:', selectedGroup.label);
            }
          }

          // 设置选中的组
          if (selectedGroup) {
            console.log(
              'Setting contactGroup value to:',
              selectedGroup.id,
              selectedGroup.label
            );

            methods.setValue('contactGroup', selectedGroup.id, {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });

            // 强制触发组件重新渲染
            methods.trigger('contactGroup');

            toast({
              title: 'Success',
              description: `Contact group "${selectedGroup.label}" uploaded and selected successfully`,
              variant: 'success',
            });
          } else {
            console.warn('No suitable group found for selection');
            toast({
              title: 'Success',
              description: 'Contact group uploaded successfully',
              variant: 'success',
            });
          }
        }, 200); // 增加延迟时间
      }

      // 清空文件输入框的值
      if (contactFileInputRef.current) {
        contactFileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('Error uploading contact group:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload contact group',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Trigger contact upload
  const triggerContactUpload = () => {
    if (contactFileInputRef.current) {
      contactFileInputRef.current.click();
    }
  };

  // Trigger opt-out upload
  const triggerOptOutUpload = () => {
    if (optOutFileInputRef.current) {
      optOutFileInputRef.current.click();
    }
  };

  // Upload opt-out group file
  const handleUploadOptOutGroup = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!event.target.files || event.target.files.length === 0) return;

    const file = event.target.files[0];
    if (!file) return;

    try {
      setLoading(true);

      // 保存上传前的组列表，用于对比
      const beforeUploadGroups = [...optOutGroupOptions];
      console.log(
        'OptOut groups before upload:',
        beforeUploadGroups.map((g: any) => g.label).join(', ')
      );

      // 保存文件名，用于后续匹配
      const fileName = file.name;
      const fileNameWithoutExtension = fileName.replace(/\.[^/.]+$/, '');
      console.log('Uploading OptOut file:', fileName);
      console.log(
        'OptOut file name without extension:',
        fileNameWithoutExtension
      );

      // 上传文件
      await uploadOptOutGroup(file, basePath);

      // 延迟1秒后再获取列表，确保服务器有时间处理
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 重新获取退订组列表
      const optOutResponse = await getOptOutList(basePath);
      if ((optOutResponse as any).data?.data) {
        const newOptions = (optOutResponse as any).data.data.map(
          (item: any) => ({
            id: item.id,
            value: item.id,
            label: item.name,
          })
        );

        console.log(
          'OptOut groups after upload:',
          newOptions.map((g: any) => g.label).join(', ')
        );

        // 先更新下拉列表选项
        setOptOutGroupOptions(newOptions);

        // 找到新增的组（通过对比上传前后的列表）
        const newGroups = newOptions.filter(
          (newGroup: any) =>
            !beforeUploadGroups.some(
              (oldGroup: any) => oldGroup.id === newGroup.id
            )
        );

        console.log(
          'New OptOut groups found:',
          newGroups.map((g: any) => g.label).join(', ')
        );

        // 使用setTimeout确保在下一个事件循环中设置值
        setTimeout(() => {
          let selectedGroup = null;

          // 优先策略1: 如果有新增的组，优先选择与文件名最匹配的新组
          if (newGroups.length > 0) {
            // 精确匹配文件名
            selectedGroup = newGroups.find(
              (group: any) =>
                group.label === fileName ||
                group.label === fileNameWithoutExtension
            );

            // 如果没有精确匹配，尝试模糊匹配
            if (!selectedGroup) {
              selectedGroup = newGroups.find((group: any) => {
                const groupName = group.label.toLowerCase();
                const fileNameLower = fileName.toLowerCase();
                const fileNameNoExtLower =
                  fileNameWithoutExtension.toLowerCase();

                return (
                  groupName.includes(fileNameNoExtLower) ||
                  groupName === fileNameLower ||
                  fileNameNoExtLower.includes(groupName)
                );
              });
            }

            // 如果还是没找到，就选第一个新组
            if (!selectedGroup) {
              selectedGroup = newGroups[0];
              console.log(
                'No exact match found, selecting first new OptOut group:',
                selectedGroup.label
              );
            }
          }

          // 策略2: 如果没有新组，在所有组中查找匹配
          if (!selectedGroup) {
            console.log('No new OptOut groups found, searching in all groups');

            // 精确匹配
            selectedGroup = newOptions.find(
              (group: any) =>
                group.label === fileName ||
                group.label === fileNameWithoutExtension
            );

            // 模糊匹配
            if (!selectedGroup) {
              selectedGroup = newOptions.find((group: any) => {
                const groupName = group.label.toLowerCase();
                const fileNameLower = fileName.toLowerCase();
                const fileNameNoExtLower =
                  fileNameWithoutExtension.toLowerCase();

                return (
                  groupName.includes(fileNameNoExtLower) ||
                  fileNameNoExtLower.includes(groupName)
                );
              });
            }

            // 最后的策略：选择最新的组（ID最大的）
            if (!selectedGroup && newOptions.length > 0) {
              selectedGroup = newOptions.reduce((latest: any, current: any) => {
                return current.id > latest.id ? current : latest;
              });
              console.log(
                'Selecting latest OptOut group by ID:',
                selectedGroup.label
              );
            }
          }

          // 设置选中的组
          if (selectedGroup) {
            console.log(
              'Setting optOutGroup value to:',
              selectedGroup.id,
              selectedGroup.label
            );

            methods.setValue('optOutGroup', selectedGroup.id, {
              shouldValidate: true,
              shouldDirty: true,
              shouldTouch: true,
            });

            // 强制触发组件重新渲染
            methods.trigger('optOutGroup');

            toast({
              title: 'Success',
              description: `Opt-out group "${selectedGroup.label}" uploaded and selected successfully`,
              variant: 'success',
            });
          } else {
            console.warn('No suitable OptOut group found for selection');
            toast({
              title: 'Success',
              description: 'Opt-out group uploaded successfully',
              variant: 'success',
            });
          }
        }, 200); // 增加延迟时间
      }

      // 清空文件输入框的值
      if (optOutFileInputRef.current) {
        optOutFileInputRef.current.value = '';
      }
    } catch (error: any) {
      console.error('Error uploading opt-out group:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload opt-out group',
        variant: 'error',
      });
    } finally {
      setLoading(false);
    }
  };
  // Custom component to create a time input field
  const CustomTimeInput = ({ field }: any) => {
    const getHoursValue = () => {
      if (!field.value) return '';
      try {
        const dateValue = field.value as unknown as Date;
        return dateValue.getHours();
      } catch (e) {
        return '';
      }
    };

    const getMinutesValue = () => {
      if (!field.value) return '';
      try {
        const dateValue = field.value as unknown as Date;
        return dateValue.getMinutes();
      } catch (e) {
        return '';
      }
    };

    const getSecondsValue = () => {
      if (!field.value) return '';
      try {
        const dateValue = field.value as unknown as Date;
        return dateValue.getSeconds();
      } catch (e) {
        return '';
      }
    };

    const handleTimeChange = (type: string, valueStr: string) => {
      const value = parseInt(valueStr) || 0;
      let newDate = new Date();

      if (field.value) {
        newDate = new Date((field.value as unknown as Date).getTime());
      }

      if (type === 'hours') {
        newDate.setHours(value);
      } else if (type === 'minutes') {
        newDate.setMinutes(value);
      } else if (type === 'seconds') {
        newDate.setSeconds(value);
      }

      field.onChange(newDate);
    };

    return (
      <div className="flex items-center space-x-1 py-2 px-1 border-t border-gray-200">
        <input
          type="number"
          min="0"
          max="23"
          placeholder="HH"
          className="w-12 p-1 border border-gray-300 rounded text-center"
          value={getHoursValue()}
          onChange={(e) => handleTimeChange('hours', e.target.value)}
        />
        <span>:</span>
        <input
          type="number"
          min="0"
          max="59"
          placeholder="MM"
          className="w-12 p-1 border border-gray-300 rounded text-center"
          value={getMinutesValue()}
          onChange={(e) => handleTimeChange('minutes', e.target.value)}
        />
        <span>:</span>
        <input
          type="number"
          min="0"
          max="59"
          placeholder="SS"
          className="w-12 p-1 border border-gray-300 rounded text-center"
          value={getSecondsValue()}
          onChange={(e) => handleTimeChange('seconds', e.target.value)}
        />
      </div>
    );
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit)}
        className="w-full bg-gray-50 min-h-screen"
      >
        <BroadcastHeader
          title={''}
          onSubmit={methods.handleSubmit(onSubmit)}
          loading={loading}
        />

        <div className="px-6 py-4">
          <div className="grid grid-cols-12 gap-6">
            {/* Left column */}
            <div className="col-span-12 lg:col-span-4">
              <div className="space-y-4">
                <Field
                  title="Broadcast Name"
                  isRequired
                >
                  <Controller
                    name="broadcastName"
                    control={methods.control}
                    render={({ field }) => (
                      <Input
                        {...field}
                        size="s"
                        placeholder="Enter broadcast name"
                        required
                      />
                    )}
                  />
                </Field>
                <Field
                  title="From"
                  isRequired
                >
                  <Controller
                    name="from"
                    control={methods.control}
                    render={({ field }) => (
                      <Select
                        options={fromOptions}
                        value={field.value}
                        onChange={(value) => {
                          field.onChange(value);
                          // When account is selected, fetch message templates
                          loadMessageTemplates(value);
                        }}
                        placeholder="Select"
                        mode="single"
                        isPagination={false}
                        triggerClassName="!h-8"
                        showSearch={true}
                      />
                    )}
                  />
                </Field>
                <Field
                  title="Date"
                  isRequired
                >
                  <Controller
                    name="date"
                    control={methods.control}
                    render={({ field }) => (
                      <div className="w-full relative">
                        <ReactDatePicker
                          calendarClassName="custom-calendar"
                          dateFormat="yyyy-MM-dd HH:mm:ss"
                          className="block w-full h-8 outline-none ring-0 p-2 border border-grey-300 rounded-md focus:border-primary"
                          selected={
                            (field.value as unknown as Date)
                              ? field.value
                              : null
                          }
                          minDate={new Date()}
                          onChange={(date) => field.onChange(date)}
                          showMonthDropdown
                          showYearDropdown
                          showTimeInput
                          timeInputLabel=""
                          dateFormatCalendar="MMMM yyyy"
                          placeholderText="YYYY-MM-DD HH:MM:SS"
                          onKeyDown={(e) => e.preventDefault()}
                          wrapperClassName="w-full"
                          customTimeInput={<CustomTimeInput field={field} />}
                          required
                        />
                      </div>
                    )}
                  />
                </Field>
                <div>
                  <Field
                    isRequired
                    title={
                      <span>
                        Contact Group
                        <span className="text-gray-500">
                          {' '}
                          (subs_number is required)
                        </span>
                      </span>
                    }
                  >
                    <div className="flex-1">
                      <Controller
                        name="contactGroup"
                        control={methods.control}
                        render={({ field }) => (
                          <Select
                            options={contactGroupOptions}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select"
                            mode="single"
                            isPagination={false}
                            triggerClassName="!h-8"
                            showSearch={true}
                          />
                        )}
                      />
                    </div>
                  </Field>
                </div>
                <div>
                  <Field
                    title={
                      <span>
                        Opt-out Group
                        <span className="text-gray-500">
                          {' '}
                          (subs_number is required)
                        </span>
                      </span>
                    }
                  >
                    <div className="flex-1">
                      <Controller
                        name="optOutGroup"
                        control={methods.control}
                        render={({ field }) => (
                          <Select
                            options={optOutGroupOptions}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select"
                            mode="single"
                            isPagination={false}
                            triggerClassName="!h-8"
                            showSearch={true}
                          />
                        )}
                      />
                    </div>
                  </Field>
                </div>
              </div>
            </div>

            <div className="col-span-12 lg:col-span-4">
              <div className="space-y-4">
                {/* Empty spacers to push buttons down to align with fields */}
                <div className="h-[38px] mb-4"></div>
                {/* Spacer for Broadcast Name field */}
                <div className="h-[38px] mb-4"></div>
                {/* Spacer for From field */}
                <div className="h-[38px] mb-4"></div>
                {/* Spacer for Date field */}
                <div className="h-[38px] mb-4"></div>

                <div className="h-[38px] mb-4"></div>

                {/* Contact Group buttons - now aligned with Contact Group field */}
                <div className="flex gap-2 h-[30px] flex items-end">
                  <Button
                    variant="secondary"
                    onClick={handleDownloadTemplate}
                    disabled={loading}
                    size="s"
                  >
                    Download Template
                  </Button>

                  <input
                    type="file"
                    ref={contactFileInputRef}
                    onChange={handleUploadContactGroup}
                    accept=".csv,.tsv"
                    className="hidden"
                  />
                  <Button
                    variant="primary"
                    onClick={triggerContactUpload}
                    disabled={loading}
                    size="s"
                    beforeIcon={
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        ></path>
                      </svg>
                    }
                  >
                    upload
                  </Button>
                </div>

                {/* Opt-out Group buttons - now aligned with Opt-out Group field */}
                <div className="flex gap-2 h-[58px] flex items-end">
                  <input
                    type="file"
                    ref={optOutFileInputRef}
                    onChange={handleUploadOptOutGroup}
                    accept=".csv,.tsv"
                    className="hidden"
                  />
                  <Button
                    variant="primary"
                    onClick={triggerOptOutUpload}
                    disabled={loading}
                    size="s"
                    beforeIcon={
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        ></path>
                      </svg>
                    }
                  >
                    upload
                  </Button>
                </div>
              </div>
            </div>
            {/* Right column */}
            <div className="col-span-12 lg:col-span-4">
              {/* Message Template Select */}
              <Field
                title="Message Template"
                isRequired
              >
                <Controller
                  name="messageTemplate"
                  control={methods.control}
                  render={({ field }) => (
                    <Select
                      options={messageTemplateOptions}
                      value={field.value}
                      onChange={(value) => {
                        field.onChange(value);
                        // When template is selected, fetch template details
                        if (value) {
                          loadMessageTemplateDetail(value);
                        }
                      }}
                      placeholder={templateLoading ? 'Loading...' : 'Select'}
                      mode="single"
                      isPagination={false}
                      triggerClassName="!h-8"
                      disabled={!methods.getValues('from') || templateLoading}
                      showSearch={true}
                    />
                  )}
                />
              </Field>

              {/* Template Preview Component */}
              <TemplateDetailPreview
                templateData={templateData}
                isLoading={templateLoading}
                className="mt-4"
                onImageUpload={handleImageUpload}
                isEditable={true}
                initialImages={headerImages}
              />
            </div>
          </div>
        </div>
      </form>
    </FormProvider>
  );
};
