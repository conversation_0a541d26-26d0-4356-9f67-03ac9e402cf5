import React from 'react';
import { useRoute<PERSON>and<PERSON> } from '@cdss-modules/design-system';

interface BroadcastHeaderProps {
  title?: string;
  onSubmit: () => void;
  buttonText?: string;
  loading?: boolean;
  buttonDisabled?: boolean; //按钮禁用属性
}

const BroadcastHeader: React.FC<BroadcastHeaderProps> = ({
  title = '',
  onSubmit,
  buttonText = 'Submit',
  loading = false,
  buttonDisabled = false, // 默认不禁用
}) => {
  const { toPath } = useRouteHandler();

  const handleBackClick = () => {
    toPath('/campaign');
  };

  return (
    <div className="w-full bg-white border-b border-gray-200 py-3 px-4 flex items-center justify-between">
      <div className="flex items-center">
        <button
          className="mr-2"
          type="button"
          onClick={handleBackClick}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M19 12H5M12 19l-7-7 7-7" />
          </svg>
        </button>
        <h1 className="text-xl font-bold">Broadcast Details</h1>
        <div className="ml-4 text-red-500 text-2xl">{title}</div>
      </div>
      <button
        className={`${loading || buttonDisabled ? 'bg-gray-500' : 'bg-black'} text-white px-4 py-2 rounded-md flex items-center`}
        onClick={onSubmit}
        type="button"
        disabled={loading || buttonDisabled}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
        )}
        {buttonText}
      </button>
    </div>
  );
};

export default BroadcastHeader;
