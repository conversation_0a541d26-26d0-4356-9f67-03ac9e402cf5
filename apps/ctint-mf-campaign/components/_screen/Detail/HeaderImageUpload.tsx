import React, { useEffect } from 'react';
import { Upload, Plus, Loader2 } from 'lucide-react';

interface HeaderImageProps {
  imagePreview?: string;
  onImageClick?: (e: React.MouseEvent) => void;
  onReselect?: (e: React.MouseEvent) => void;
  imageName?: string;
  isEditable?: boolean;
  className?: string;
  isUploading?: boolean;
}

const HeaderImageUpload: React.FC<HeaderImageProps> = ({
  imagePreview,
  onImageClick,
  onReselect,
  imageName,
  isEditable = true,
  className = '',
  isUploading = false,
}) => {
  useEffect(() => {
    console.log('imagePreview', imagePreview);
  }, [imagePreview]);

  // If there's no preview and we're in view mode, don't render anything
  if (!imagePreview && !isEditable) {
    return null;
  }
  // Upload placeholder state
  if (!imagePreview && isEditable && onImageClick) {
    return (
      <div
        onClick={isUploading ? undefined : onImageClick}
        className={`w-full h-40 bg-gray-50 border-2 border-dashed border-gray-200 rounded flex flex-col items-center justify-center ${
          !isUploading && 'cursor-pointer hover:border-orange-400'
        } transition-colors ${className}`}
        role={isUploading ? undefined : 'button'}
        tabIndex={isUploading ? undefined : 0}
        onKeyPress={(e) => {
          if (!isUploading && (e.key === 'Enter' || e.key === ' ')) {
            onImageClick(e as unknown as React.MouseEvent);
          }
        }}
      >
        {isUploading ? (
          <>
            <Loader2 className="w-8 h-8 text-orange-400 mb-2 animate-spin" />
            <span className="text-sm text-gray-500">Uploading...</span>
          </>
        ) : (
          <>
            <Plus className="w-8 h-8 text-gray-400 mb-2" />
            <span className="text-sm text-gray-500">Add Header Image</span>
            <span className="text-xs text-gray-400 mt-1">
              JPG, PNG (max. 5MB)
            </span>
          </>
        )}
      </div>
    );
  }

  // Preview state
  return (
    <div className={`space-y-2 ${className}`}>
      <div className={`relative ${isEditable && !isUploading ? 'group' : ''}`}>
        <img
          src={imagePreview}
          alt="Header Preview"
          className="w-full h-40 object-cover rounded"
        />
        {isUploading && (
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
        )}
        {isEditable && !isUploading && onReselect && (
          <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
            <button
              onClick={onReselect}
              className="px-3 py-1.5 bg-white rounded text-sm text-gray-800 hover:bg-gray-100 transition-colors flex items-center gap-1"
              type="button"
            >
              <Upload className="w-4 h-4" />
              Change Image
            </button>
          </div>
        )}
      </div>

      {isEditable && imageName && (
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500 truncate flex-1">
            {imageName}
          </span>
        </div>
      )}
    </div>
  );
};

export default HeaderImageUpload;
