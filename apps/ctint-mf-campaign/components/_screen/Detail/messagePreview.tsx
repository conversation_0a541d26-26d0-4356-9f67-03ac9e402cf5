import React, { useRef, useState } from 'react';
import HeaderImageUpload from './HeaderImageUpload';
import { toast, useRouteHandler } from '@cdss-modules/design-system';
import { uploadTemplateImg } from '../../../lib/api';

export interface ButtonComponent {
  text: string;
  type: string;
  flow_action?: string;
  flow_id?: number;
  navigate_screen?: string;
  url?: string;
  phone_number?: string;
}

export interface TemplateComponent {
  type: string;
  text?: string;
  format?: string;
  buttons?: ButtonComponent[];
  example?: any;
  headerImage?: string;
  headerImageProxy?: string;
}

export interface TemplateData {
  id: string;
  name: string;
  category: string;
  language: string;
  components: TemplateComponent[];
  created_at: string;
  modified_at: string;
}

export interface TemplateDetailPreviewProps {
  templateData: TemplateData | null;
  isLoading: boolean;
  className?: string;
  conversationId?: string;
  basePath?: string;
  onImageUpload?: (
    componentId: string,
    imageData: { url: string; proxyUrl?: string; name: string }
  ) => void;
  isEditable?: boolean;
  initialImages?: { [key: string]: { url: string; name: string } };
}

const TemplateDetailPreview: React.FC<TemplateDetailPreviewProps> = ({
  templateData,
  isLoading,
  className = '',
  conversationId = '',
  onImageUpload,
  isEditable = false,
  initialImages = {},
}) => {
  const { basePath, searchParams } = useRouteHandler();
  const [isUploading, setIsUploading] = useState(false);
  const [headerImages, setHeaderImages] = useState<{
    [key: string]: { url: string; proxyUrl?: string; name: string };
  }>(initialImages);
  const fileInputRefs = useRef<{ [key: string]: HTMLInputElement | null }>({});

  // 当 initialImages 改变时，更新 headerImages 状态
  React.useEffect(() => {
    console.log('Updating headerImages from initialImages:', initialImages);
    setHeaderImages(initialImages);
  }, [initialImages]);

  // 处理图片上传
  const handleImageUpload = async (
    event: React.ChangeEvent<HTMLInputElement>,
    componentId: string
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      toast({
        title: 'Error',
        description: 'Please select an image file',
        variant: 'error',
      });
      return;
    }

    // 验证文件大小
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: 'Error',
        description: 'Image size should be less than 5MB',
        variant: 'error',
      });
      return;
    }

    setIsUploading(true);

    try {
      // 调用图片上传API
      const response = await uploadTemplateImg(basePath, file);
      console.log('response.data', response);

      // 修改：创建包含两个URL的图片数据对象
      const imageData = {
        url: response.data.data.url, // 原始URL，用于保存
        proxyUrl: response.data.data.proxyUrl, // 代理URL，用于显示
        name: file.name,
        isNewUpload: true, // 标记为新上传
      };

      // 更新内部状态
      setHeaderImages((prev) => ({
        ...prev,
        [componentId]: imageData,
      }));

      // 调用父组件提供的回调，将图片数据传递给父组件
      if (onImageUpload) {
        onImageUpload(componentId, imageData);
      }

      toast({
        title: 'Success',
        description: 'Image uploaded successfully',
        variant: 'success',
      });
    } catch (error) {
      console.error('Upload failed:', error);
      toast({
        title: 'Error',
        description: 'Failed to upload image. Please try again.',
        variant: 'error',
      });
    } finally {
      setIsUploading(false);
    }
  };

  if (isLoading) {
    return (
      <div
        className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin h-8 w-8 border-4 border-orange-500 rounded-full border-t-transparent"></div>
        </div>
      </div>
    );
  }

  if (!templateData) {
    return (
      <div
        className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
      >
        <h3 className="text-sm font-medium text-gray-500 mb-2">
          Message Preview
        </h3>
        <div className="bg-gray-100 h-64 w-full rounded p-4 flex items-center justify-center text-gray-500">
          Select a template to preview
        </div>
      </div>
    );
  }

  // Render individual component based on type
  const renderComponent = (component: TemplateComponent, index: number) => {
    // 修复：统一使用原始索引生成componentId
    const componentId = `header-${index}`;

    switch (component.type) {
      case 'HEADER':
        return (
          <div
            key={`header-${index}`}
            className="mb-3"
          >
            {component.format === 'TEXT' && component.text && (
              <div className="font-bold text-lg">{component.text}</div>
            )}
            {component.format === 'IMAGE' && (
              <>
                <input
                  ref={(el) => (fileInputRefs.current[componentId] = el)}
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleImageUpload(e, componentId)}
                  className="hidden"
                />
                <HeaderImageUpload
                  imagePreview={
                    headerImages[componentId]?.proxyUrl ||
                    headerImages[componentId]?.url ||
                    component.headerImageProxy || // 新增：支持组件中的代理URL
                    component.headerImage ||
                    ''
                  }
                  imageName={headerImages[componentId]?.name}
                  isEditable={isEditable}
                  isUploading={isUploading}
                  onImageClick={(e) => {
                    e.preventDefault();
                    if (!isUploading) {
                      fileInputRefs.current[componentId]?.click();
                    }
                  }}
                  onReselect={(e) => {
                    e.preventDefault();
                    if (!isUploading) {
                      fileInputRefs.current[componentId]?.click();
                    }
                  }}
                />
              </>
            )}
          </div>
        );

      case 'BODY':
        return (
          <div
            key={`body-${index}`}
            className="mb-3 whitespace-pre-wrap"
          >
            {component.text || ''}
          </div>
        );

      case 'FOOTER':
        return (
          <div
            key={`footer-${index}`}
            className="text-sm text-gray-500 mt-2 border-t border-gray-200 pt-2"
          >
            {component.text || ''}
          </div>
        );

      case 'BUTTONS':
        return (
          <div
            key={`buttons-${index}`}
            className="mt-3 border-t border-gray-200"
          >
            {component.buttons?.map((button, buttonIndex) => (
              <div
                key={`button-${buttonIndex}`}
                className="py-2 px-3 text-center text-blue-600 border-b border-gray-200 last:border-b-0 hover:bg-blue-50 cursor-pointer"
              >
                <div>{button.text}</div>
                {button.type === 'URL' && button.url && (
                  <div className="text-xs text-red-500 mt-1">
                    URL: {button.url}
                  </div>
                )}
                {button.type === 'PHONE_NUMBER' && button.phone_number && (
                  <div className="text-xs text-red-500 mt-1">
                    Phone: {button.phone_number}
                  </div>
                )}
                {button.type === 'FLOW' && (
                  <div className="text-xs text-green-500 mt-1">
                    {button.flow_action}
                  </div>
                )}
              </div>
            ))}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}
    >
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-sm font-medium text-gray-700">Message Preview</h3>
        <div className="flex items-center space-x-2">
          <span className="px-2 py-1 bg-gray-100 text-xs rounded text-gray-600">
            {templateData.category}
          </span>
          <span className="px-2 py-1 bg-gray-100 text-xs rounded text-gray-600">
            {templateData.language}
          </span>
        </div>
      </div>

      <div className="rounded-lg border overflow-hidden">
        <div className="p-3 border-b">
          <div className="text-sm font-medium">
            {/* 渲染HEADER组件，保持原始索引 */}
            {templateData.components
              .map((component, index) => ({ component, index }))
              .filter(({ component }) => component.type === 'HEADER')
              .map(({ component, index }) => renderComponent(component, index))}
          </div>
        </div>

        <div className="p-4">
          {/* 渲染BODY组件 */}
          {templateData.components
            .map((component, index) => ({ component, index }))
            .filter(({ component }) => component.type === 'BODY')
            .map(({ component, index }) => renderComponent(component, index))}

          {/* 渲染FOOTER组件 */}
          {templateData.components
            .map((component, index) => ({ component, index }))
            .filter(({ component }) => component.type === 'FOOTER')
            .map(({ component, index }) => renderComponent(component, index))}

          {/* 渲染BUTTONS组件 */}
          {templateData.components
            .map((component, index) => ({ component, index }))
            .filter(({ component }) => component.type === 'BUTTONS')
            .map(({ component, index }) => renderComponent(component, index))}
        </div>
      </div>

      <div className="mt-2 text-xs text-gray-400">
        Last modified: {new Date(templateData.modified_at).toLocaleString()}
      </div>
    </div>
  );
};

export default TemplateDetailPreview;
