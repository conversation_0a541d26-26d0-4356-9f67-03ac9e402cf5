import React, { useState } from 'react';
import ReactDatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import dayjs from 'dayjs';
import {
  Popup,
  PopupContent,
  PopupClose,
} from '@cdss-modules/design-system/components/_ui/Popup';

// Interface for the component props
interface ResendFailedModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (date: Date | null, isScheduled: boolean) => void;
  broadcastId: string;
}

const ResendFailedModal = ({
  isOpen,
  onClose,
  onConfirm,
  broadcastId,
}: ResendFailedModalProps) => {
  // State for the selected date
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  // State for the sending option
  const [isSendNow, setIsSendNow] = useState(true);

  // Function to handle confirm action
  const handleConfirm = () => {
    onConfirm(selectedDate, !isSendNow);
    onClose();
  };

  // Custom time input component for the date picker
  const CustomTimeInput = () => {
    const getHoursValue = () => {
      if (!selectedDate) return '';
      return selectedDate.getHours();
    };

    const getMinutesValue = () => {
      if (!selectedDate) return '';
      return selectedDate.getMinutes();
    };

    const getSecondsValue = () => {
      if (!selectedDate) return '';
      return selectedDate.getSeconds();
    };

    const handleTimeChange = (type: string, valueStr: string) => {
      const value = parseInt(valueStr) || 0;
      let newDate = new Date();

      if (selectedDate) {
        newDate = new Date(selectedDate.getTime());
      }

      if (type === 'hours') {
        newDate.setHours(value);
      } else if (type === 'minutes') {
        newDate.setMinutes(value);
      } else if (type === 'seconds') {
        newDate.setSeconds(value);
      }
      console.log(`时间更新 (${type}): ${newDate}`); // 添加日志
      setSelectedDate(newDate);
    };

    return (
      <div className="flex items-center space-x-1 py-2 px-1 border-t border-gray-200">
        <input
          type="number"
          min="0"
          max="23"
          placeholder="HH"
          className="w-12 p-1 border border-gray-300 rounded text-center"
          value={getHoursValue()}
          onChange={(e) => handleTimeChange('hours', e.target.value)}
        />
        <span>:</span>
        <input
          type="number"
          min="0"
          max="59"
          placeholder="MM"
          className="w-12 p-1 border border-gray-300 rounded text-center"
          value={getMinutesValue()}
          onChange={(e) => handleTimeChange('minutes', e.target.value)}
        />
        <span>:</span>
        <input
          type="number"
          min="0"
          max="59"
          placeholder="SS"
          className="w-12 p-1 border border-gray-300 rounded text-center"
          value={getSecondsValue()}
          onChange={(e) => handleTimeChange('seconds', e.target.value)}
        />
      </div>
    );
  };

  return (
    <Popup
      open={isOpen}
      onOpenChange={onClose}
    >
      <PopupContent
        className="w-full max-w-md min-h-[400px]"
        headerClassName="text-black"
        title="Resend"
      >
        <div className="p-5">
          <div className="mb-5 flex gap-8">
            <div className="flex items-center">
              <input
                type="radio"
                id="sendNow"
                name="sendType"
                className="mr-2"
                checked={isSendNow}
                onChange={() => setIsSendNow(true)}
              />
              <label
                htmlFor="sendNow"
                className="font-medium"
              >
                Now
              </label>
            </div>
            <div className="flex items-center">
              <input
                type="radio"
                id="schedule"
                name="sendType"
                className="mr-2"
                checked={!isSendNow}
                onChange={() => setIsSendNow(false)}
              />
              <label
                htmlFor="schedule"
                className="font-medium"
              >
                Schedule
              </label>
            </div>
          </div>

          {!isSendNow && (
            <div className="mb-5">
              <div className="mb-2 font-medium">
                Sending time<span className="text-red-500">*</span>
              </div>
              <div className="w-full relative">
                <style jsx>{`
                  :global(.react-datepicker-popper) {
                    z-index: 9999 !important;
                  }
                  :global(.react-datepicker__time-container) {
                    z-index: 9999 !important;
                  }
                  :global(.react-datepicker__time-list) {
                    z-index: 9999 !important;
                  }
                  :global(.react-datepicker) {
                    z-index: 9999 !important;
                  }
                `}</style>
                <ReactDatePicker
                  calendarClassName="custom-calendar"
                  dateFormat="yyyy-MM-dd HH:mm:ss"
                  className="block w-full h-8 outline-none ring-0 p-2 border rounded-md focus:border-primary"
                  selected={selectedDate}
                  minDate={new Date()}
                  onChange={(date) => {
                    if (date) {
                      // 如果有选择日期，但需要确保时间部分也被设置
                      const newDate = new Date(date);

                      // 如果之前已有选择日期且有时间设置，保留原时间设置
                      if (selectedDate) {
                        newDate.setHours(selectedDate.getHours());
                        newDate.setMinutes(selectedDate.getMinutes());
                        newDate.setSeconds(selectedDate.getSeconds());
                      } else {
                        // 否则设置一个默认时间，比如当前时间
                        const now = new Date();
                        newDate.setHours(now.getHours());
                        newDate.setMinutes(now.getMinutes());
                        newDate.setSeconds(now.getSeconds());
                      }

                      setSelectedDate(newDate);
                    } else {
                      setSelectedDate(null);
                    }
                  }}
                  showMonthDropdown
                  showYearDropdown
                  showTimeInput
                  timeInputLabel=""
                  dateFormatCalendar="MMMM yyyy"
                  placeholderText="YYYY-MM-DD HH:MM:SS"
                  onKeyDown={(e) => e.preventDefault()}
                  wrapperClassName="w-full"
                  customTimeInput={<CustomTimeInput />}
                  popperProps={{
                    strategy: 'fixed',
                  }}
                  popperModifiers={
                    [
                      {
                        name: 'preventOverflow',
                        options: {
                          boundary: 'viewport',
                        },
                      },
                      {
                        name: 'flip',
                      },
                    ] as any
                  }
                  required
                />
              </div>
            </div>
          )}

          <div className="flex justify-end gap-2 mt-6">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-white border border-gray-300 rounded text-sm font-medium"
            >
              Cancel
            </button>
            <button
              onClick={handleConfirm}
              className="px-4 py-2 bg-black text-white rounded text-sm font-medium"
            >
              Confirm
            </button>
          </div>
        </div>
      </PopupContent>
    </Popup>
  );
};

export default ResendFailedModal;
