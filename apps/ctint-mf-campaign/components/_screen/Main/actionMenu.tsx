import React, { useState } from 'react';
import { Row } from '@tanstack/react-table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import { toast } from '@cdss-modules/design-system';
import ResendFailedModal from './resendFailedModalProps';
import { BroadcastData } from '../../../types/microfrontendsConfig';

interface ActionMenuProps {
  row: Row<BroadcastData>;
  onViewClick: (row: Row<BroadcastData>) => void;
  onResendFailedClick: (row: Row<BroadcastData>, date: Date) => void;
  onMakeCopyClick: (row: Row<BroadcastData>) => void;
  onEditClick?: (row: Row<BroadcastData>) => void;
  onCancelResendClick?: (row: Row<BroadcastData>) => void;
}

const ActionMenu: React.FC<ActionMenuProps> = ({
  row,
  onViewClick,
  onResendFailedClick,
  onMakeCopyClick,
  onEditClick,
  onCancelResendClick,
}) => {
  const [isResendModalOpen, setIsResendModalOpen] = useState(false);

  // Helper functions to determine action availability
  const canResendFailed = () => {
    return (
      row.original.status === 'Sent' &&
      parseInt(row.original.errorCount || '0', 10) > 0
    );
  };

  const canEdit = () => {
    return (
      row.original.status === 'Scheduled' || row.original.status === 'Draft'
    );
  };

  const canCancelResend = () => {
    return row.original.status === 'ScheduledResend';
  };

  const openResendModal = () => {
    // Check if resend failed is possible
    if (canResendFailed()) {
      setIsResendModalOpen(true);
    } else {
      toast({
        title: 'Action Unavailable',
        description:
          'This action is only available for sent broadcasts with errors',
        variant: 'error',
      });
    }
  };

  const handleResendConfirm = (date: Date | null, isScheduled: boolean) => {
    // Here you would implement the actual resend logic
    console.log('Resending failed broadcasts for', row.original.id);
    console.log('Is scheduled:', isScheduled);
    console.log('Selected date:', date);

    if (isScheduled && date) {
      // 检查选择的时间是否小于当前时间
      const now = new Date();
      if (date < now) {
        toast({
          title: 'Time Error',
          description: 'Resend time cannot be earlier than the current time',
          variant: 'error',
        });
        return; // 阻止继续执行
      }
    }

    if (date) {
      // Add 8 hours to compensate for the timezone difference
      const adjustedDate = new Date(date.getTime() + 8 * 60 * 60 * 1000);

      // Call the parent handler with the adjusted date
      onResendFailedClick(row, adjustedDate);
    } else {
      // If no date (now option), just use current time
      onResendFailedClick(row, new Date());
    }

    // Show toast notification
    toast({
      title: 'Success',
      description: isScheduled
        ? `Failed messages will be resent as scheduled`
        : 'Failed messages are being resent',
      variant: 'success',
    });
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="inline-flex items-center justify-center rounded-md p-2 text-sm font-medium">
            <svg
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10 6.25C10.6904 6.25 11.25 5.69036 11.25 5C11.25 4.30964 10.6904 3.75 10 3.75C9.30964 3.75 8.75 4.30964 8.75 5C8.75 5.69036 9.30964 6.25 10 6.25Z"
                fill="#001829"
                fillOpacity="0.88"
              />
              <path
                d="M10 11.25C10.6904 11.25 11.25 10.6904 11.25 10C11.25 9.30964 10.6904 8.75 10 8.75C9.30964 8.75 8.75 9.30964 8.75 10C8.75 10.6904 9.30964 11.25 10 11.25Z"
                fill="#001829"
                fillOpacity="0.88"
              />
              <path
                d="M10 16.25C10.6904 16.25 11.25 15.6904 11.25 15C11.25 14.3096 10.6904 13.75 10 13.75C9.30964 13.75 8.75 14.3096 8.75 15C8.75 15.6904 9.30964 16.25 10 16.25Z"
                fill="#001829"
                fillOpacity="0.88"
              />
            </svg>
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => onViewClick(row)}
          >
            View
          </DropdownMenuItem>

          {/* Edit option - always visible but disabled when not applicable */}
          <DropdownMenuItem
            className={`${canEdit() ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
            onClick={() => canEdit() && onEditClick && onEditClick(row)}
          >
            Edit
          </DropdownMenuItem>

          {/* Resend Failed option - always visible but disabled when not applicable */}
          <DropdownMenuItem
            className={`${canResendFailed() ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
            onClick={() => (canResendFailed() ? openResendModal() : null)}
          >
            Resend Failed
          </DropdownMenuItem>

          {/* Cancel Resend option - always visible but disabled when not applicable */}
          <DropdownMenuItem
            className={`${canCancelResend() ? 'cursor-pointer' : 'cursor-not-allowed opacity-50'}`}
            onClick={() =>
              canCancelResend() &&
              onCancelResendClick &&
              onCancelResendClick(row)
            }
          >
            Cancel Resend
          </DropdownMenuItem>

          <DropdownMenuItem
            className="cursor-pointer"
            onClick={() => onMakeCopyClick(row)}
          >
            Make a Copy
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Resend Failed Modal */}
      <ResendFailedModal
        isOpen={isResendModalOpen}
        onClose={() => setIsResendModalOpen(false)}
        onConfirm={handleResendConfirm}
        broadcastId={row.original.id}
      />
    </>
  );
};

export default ActionMenu;
