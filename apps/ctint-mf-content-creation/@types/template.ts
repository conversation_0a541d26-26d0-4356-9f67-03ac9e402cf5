export interface TemplateList {
  id: string;
  name: string;
  channelType: string;
  createBy: string;
  lastUpdateBy: string;
  createDate: string;
  lastUpdate: string;
  sortorder: number;
  status: (undefined | 'published' | 'draft') & string;
}

export interface EmailEditorRef {
  insertImage: (
    src: string,
    title?: string,
    alt?: string,
    width?: number,
    height?: number
  ) => void;
  setContent: (content: string) => void;
  getContent: () => string;
  isEmpty: () => boolean;
  insertContentAtCursor: (content: string) => void;
}

export interface EmailAttachment {
  id: string;
  emailId: string;
  attachmentName: string;
  attachmentId: string;
  contentId: string;
  fileType: string;
  fileSize: number;
  isInLine: boolean;
  createTime: string;
  updateTime: string;
  createBy: string | null;
  updateBy: string | null;
  platform: string | null;
  tenant: string | null;
  storageFileDir: string;
  storageFileName: string;
  storageShareName: string;
  url: string;
  success: boolean | null;
  error: string | '';
}

// 扩展EmailAttachment类型，添加删除标记
export interface ExtendedEmailAttachment extends EmailAttachment {
  isDeleted?: boolean;
}
