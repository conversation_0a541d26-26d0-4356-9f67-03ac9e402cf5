import { PageRenderer } from '@cdss-modules/design-system';
import Main from '../components/_screen/Main';
import Detail from '../components/_screen/Detail';
import { basePath } from '../lib/appConfig';

export const Page = () => {
  return (
    // <div>
    //   <div>mf-test</div>
    // </div>

    <div className="p-6 w-full h-screen">
      <PageRenderer
        routes={[
          {
            path: '/ctint-mf-content-creation',
            group: 'ctint-mf-content-creation',
            component: <Main />,
          },
          {
            path: '/ctint-mf-content-creation/detail',
            group: 'ctint-mf-content-creation',
            component: <Detail />,
          },
        ]}
        basePath={basePath}
      />
    </div>
  );
};

export const getServerSideProps = async () => {
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      publicEnvVars,
    },
  };
};

export default Page;
