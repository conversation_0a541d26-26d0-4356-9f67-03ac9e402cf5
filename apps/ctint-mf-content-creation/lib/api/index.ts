import { ExtendedEmailAttachment } from '../../@types/template';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

// init request config
export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-interaction',
    previousId: 'ctint-bff-cdss',
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

export const fireGetTabList = (basePath = '') => {
  return axiosInstance.get(`${basePath}${apiConfig.paths.tabList}`);
};

export const fireGetSpaceNameList = (searchConditions: any, basePath = '') => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.space.list}`,
    searchConditions
  );
  // return Promise.resolve({
  //   data: {
  //     data: [
  //       {
  //         id: '1',
  //         name: 'Space 1',
  //         channelType: 'Type 1',
  //         createBy: 'User 1',
  //         lastUpdateBy: 'User 1',
  //         createDate: '2023-01-01',
  //         lastUpdate: '2023-01-01',
  //         sortorder: 1,
  //       },
  //       {
  //         id: '2',
  //         name: 'Space 2',
  //         channelType: 'Type 2',
  //         createBy: 'User 2',
  //         lastUpdateBy: 'User 2',
  //         createDate: '2023-01-01',
  //         lastUpdate: '2023-01-01',
  //         sortorder: 2,
  //       },
  //     ],
  //   },
  // });
};

export const createSpaceName = (requestData: any, basePath = '') => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.space.create}`,
    requestData
  );
};

export const updateSpaceName = (requestData: any, basePath = '') => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.space.create}`, // 使用同一个端点
    requestData
  );
};

export const fireCreateCategory = (requestData: any, basePath = '') => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.category.create}`,
    requestData
  );
};

// 删除通用函数 - 支持删除space、category、template
export interface DeleteRequest {
  type: 'space' | 'category' | 'template';
  id: string;
}

export const fireDeleteItem = (requestData: DeleteRequest, basePath = '') => {
  return axiosInstance.delete(`${basePath}${apiConfig.paths.delete}`, {
    data: requestData,
  });
};

// 删除category的便捷函数
export const fireDeleteCategory = (id: string, basePath = '') => {
  return fireDeleteItem({ type: 'category', id }, basePath);
};

// 删除template的便捷函数
export const fireDeleteTemplate = (id: string, basePath = '') => {
  return fireDeleteItem({ type: 'template', id }, basePath);
};

// 提交模板数据
export interface SubmitTemplateRequest {
  name: string;
  spaceId: string;
  parentId: string;
  content: string;
  language: string;
  channelType: string;
  isPublished: boolean;
  type: string;
  id?: string;
  metadata?: any[];
}

export const submitTemplate = (
  requestData: SubmitTemplateRequest,
  basePath = ''
) => {
  return axiosInstance.patch(
    `${basePath}${apiConfig.paths.template.create}`,
    requestData
  );
};

// 获取模板详情
export interface TemplateDetailResponse {
  data: {
    id: string;
    name: string;
    content: string;
    contentType: string;
    language: string;
    status: string;
    metadata?: ExtendedEmailAttachment[] & undefined;
    createBy: string;
    updateBy: string;
    createTime: string;
    updateTime: string;
    channelType: string;
  };
  error: string;
  isSuccess: boolean;
}

export const getTemplateDetail = (id: string, basePath = '') => {
  console.log('getTemplateDetail', id);
  return axiosInstance.get<TemplateDetailResponse>(
    `${basePath}${apiConfig.paths.template.detail}?id=${id}`
  );
};

export const handleFileUpload = (basePath = '', data: FormData) => {
  console.log('附件data：', data);

  // 创建一个专门用于文件上传的 axios 实例，超时时间设置为 5 分钟
  const uploadAxiosInstance = axios.create({
    timeout: 300000, // 5分钟 = 300,000毫秒
    headers: {
      'Content-Type': 'multipart/form-data',
      traceId: uuidv4(),
      tenant: 'ctint',
      sourceId: 'ctint-mf-manual-queue',
      previousId: 'ctint-bff-cdss',
    },
  });

  // 添加与主 axiosInstance 相同的请求拦截器
  uploadAxiosInstance.interceptors.request.use(
    (config) => {
      const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
      const gcAccessToken = localStorage.getItem('gc-access-token') || '';
      const deviceId = localStorage.getItem('deviceId') || '';
      config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
      if (cdssAuthToken) {
        config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
      }
      if (gcAccessToken) {
        config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
      }
      if (deviceId) {
        config.headers['deviceId'] = deviceId;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return uploadAxiosInstance.post(
    `${basePath}${apiConfig.paths.template.upload}`,
    data
  );
};
