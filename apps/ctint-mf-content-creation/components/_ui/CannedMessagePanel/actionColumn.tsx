import { EllipsisVertical } from 'lucide-react';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useRouteHandler } from '@cdss-modules/design-system/context/RouteContext';
import * as Popover from '@radix-ui/react-popover';

interface ActionColumnProps {
  row: any;
  onViewHandle?: () => void;
  onEditHandle?: () => void;
  onDeleteHandle?: () => void;
}

const ActionColumn = ({
  row,
  onViewHandle,
  onEditHandle,
  onDeleteHandle,
}: ActionColumnProps) => {
  const { t } = useTranslation();
  const { toPath } = useRouteHandler();
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="flex items-center justify-center">
      <Popover.Root
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <Popover.Trigger
          className="cursor-pointer hover:text-primary-500 data-[state=open]:text-primary-500"
          onClick={(e) => {
            e.stopPropagation();
          }}
        >
          <EllipsisVertical />
        </Popover.Trigger>
        <Popover.Portal>
          <Popover.Content
            onOpenAutoFocus={(e) => e.preventDefault()}
            align="end"
            className="mt-1 bg-white shadow-[0px_2px_10px_0px_rgba(0,0,0,0.18)] z-50"
          >
            <button
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              onClick={(e) => {
                e.stopPropagation();
                setIsOpen(false);
                onViewHandle && onViewHandle();
              }}
            >
              {t('ctint-mf-content-creation.spacelist.view')}
            </button>
            {onEditHandle && (
              <button
                className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(false);
                  onEditHandle();
                }}
              >
                {t('ctint-mf-content-creation.spacelist.edit')}
              </button>
            )}
            {onDeleteHandle && (
              <button
                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsOpen(false);
                  onDeleteHandle();
                }}
              >
                {t('ctint-mf-content-creation.spacelist.delete')}
              </button>
            )}
          </Popover.Content>
        </Popover.Portal>
      </Popover.Root>
    </div>
  );
};

export default ActionColumn;
