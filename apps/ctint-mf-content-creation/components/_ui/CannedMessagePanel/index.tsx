import React, {
  useC<PERSON>back,
  useMemo,
  useTransition,
  useDeferredValue,
} from 'react';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import {
  Popup,
  PopupContent,
  Toaster,
  useRole,
  useRoute<PERSON>andler,
} from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useTranslation } from 'react-i18next';
import {
  createSpaceName,
  updateSpaceName,
  fireGetSpaceNameList,
} from '../../../lib/api';
import {
  TOrderByInput,
  TSortOrder,
} from '@cdss-modules/design-system/@types/common';
import { ICriteria } from '@cdss-modules/design-system/@types/config';
import { i18n, TFunction } from 'i18next';
import { Table as TableType } from '@tanstack/react-table';
import { SpaceNames } from '../../../@types/space';
import ActionColumn from './actionColumn';
import _ from 'lodash';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { GLOBAL_DATETIME_SECOND_FORMAT } from '@cdss-modules/design-system/lib/constants';
dayjs.extend(utc);

export const CannedMessageSpaceBody = (props: { parentId: string }) => {
  const { parentId } = props;
  const { i18n } = useTranslation();

  const { t } = i18n;
  const { globalConfig } = useRole();
  const { toPath, basePath } = useRouteHandler();

  const [total, setTotal] = React.useState<number>(0);
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const [perPage, setPerPage] = React.useState<number>(10);
  const [sortOrder, setSortOrder] = React.useState<TOrderByInput>();
  const [table, setTable] = React.useState<TableType<SpaceNames>>();
  const [open, setOpen] = React.useState(false);
  const [selectedChannelType, setSelectedChannelType] =
    React.useState('whatsapp');
  const [newSpaceName, setNewSpaceName] = React.useState('');
  const [filterChannelType, setFilterChannelType] = React.useState('all');

  // 编辑相关状态
  const [isEditMode, setIsEditMode] = React.useState(false);
  const [editingSpace, setEditingSpace] = React.useState<any>(null);

  // 搜索相关状态
  const [searchInput, setSearchInput] = React.useState('');
  const [searchQuery, setSearchQuery] = React.useState('');
  const [isPending, startTransition] = useTransition();

  // 使用useDeferredValue实现节流效果
  const deferredSearchQuery = useDeferredValue(searchQuery);

  // 动态构建搜索条件，根据filterChannelType和搜索查询决定包含的字段
  const searchConditions = React.useMemo(() => {
    const baseConditions: any = {
      page: currentPage,
      pageSize: Number(perPage),
      type: 'space',
      parentId: parentId,
    };

    // 如果有排序条件，则添加sort字段，格式为 { "columnKey": "ASC/DESC" }
    if (sortOrder && Object.keys(sortOrder).length > 0) {
      const sortKey = Object.keys(sortOrder)[0];
      const sortDirection = sortOrder[sortKey];
      baseConditions.sort = {
        [sortKey]: sortDirection?.toUpperCase() || 'ASC', // 转换为大写 ASC/DESC，添加可选链和默认值
      };
    }

    // 如果filterChannelType不是'all'，则添加channelType字段
    if (filterChannelType !== 'all') {
      baseConditions.channelType = filterChannelType;
    }

    // 如果有搜索查询，则添加keyword字段
    if (deferredSearchQuery.trim()) {
      baseConditions.keyword = deferredSearchQuery.trim();
    }

    return baseConditions;
  }, [
    currentPage,
    perPage,
    parentId,
    sortOrder,
    filterChannelType,
    deferredSearchQuery,
  ]);

  const { spaceColumn } =
    globalConfig &&
    globalConfig.microfrontends &&
    globalConfig.microfrontends['ctint-mf-content-creation']
      ? globalConfig.microfrontends['ctint-mf-content-creation']
      : [];

  console.log('CannedMessageSpaceBody => spaceColumn', spaceColumn);

  const {
    data,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: [
      'spaceNameList',
      searchConditions,
      filterChannelType,
      deferredSearchQuery,
    ],
    queryFn: () =>
      fireGetSpaceNameList(searchConditions, basePath)?.then((res) => {
        return res.data;
      }),
    enabled: !!searchConditions,
    // 添加staleTime以优化性能，避免频繁重新获取
    staleTime: 1000, // 1秒内的数据被认为是新鲜的
  });

  console.log('CannedMessageSpaceBody => spaceNameList', data);

  useMemo(() => {
    // 设置数据总数
    setTotal(data?.data?.total);
  }, [data]);

  const generateColumns = (
    columns: ICriteria[],
    sortOrder: TOrderByInput | undefined,
    setSortOrder: (input: TOrderByInput) => void,
    basePath = '',
    t: TFunction,
    i18n: i18n
  ) => {
    // 如果没有列配置，返回空数组
    if (!columns || !columns.length) return [];

    // 处理列配置，将ICriteria[]转换为DataTable所需的列定义
    const processedColumns = columns.map((column) => {
      // 列基本配置
      const columnDef = {
        accessorKey: column.value, // 用于访问数据的键
        header: i18n.language === 'en' ? column.labelEn : column.labelCh, // 显示的列标题，使用i18n翻译
        enableSorting: true, // 是否可排序，默认为true

        // 单元格渲染函数
        cell: ({ row }: { row: any }) => {
          const value = row.original[column.value];

          // 根据字段类型处理特殊渲染
          if (column.value === 'channelType') {
            // 根据渠道类型显示不同文本
            return (
              <div className="px-1">
                {t(`ctint-mf-content-creation.spacelist.${value}`)}
              </div>
            );
          }

          // 处理日期字段
          if (column.value === 'createDate' || column.value === 'lastUpdate') {
            return (
              <div className="px-1">
                {value
                  ? dayjs(value).format(GLOBAL_DATETIME_SECOND_FORMAT)
                  : '--'}
              </div>
            );
          }

          return <div className="px-1">{_.isEmpty(value) ? '--' : value}</div>;
        },
      };

      // 处理排序逻辑
      return {
        ...columnDef,
        // 点击表头时的排序处理
        header: ({ column: tableColumn }: { column: any }) => {
          return (
            <div
              className="cursor-pointer flex items-center"
              onClick={() => {
                // 计算新的排序方向
                const currentDirection = sortOrder?.[column.value];
                const isCurrentColumn = currentDirection !== undefined;

                // 切换排序方向：未排序 -> 升序 -> 降序 -> 未排序
                let newDirection: TSortOrder | undefined;
                if (!isCurrentColumn) {
                  newDirection = 'asc';
                } else if (currentDirection === 'asc') {
                  newDirection = 'desc';
                } else {
                  newDirection = undefined;
                }

                // 设置新的排序
                if (newDirection) {
                  const newSortOrder: TOrderByInput = {
                    [column.value]: newDirection,
                  };
                  setSortOrder(newSortOrder);
                } else {
                  // 清除排序
                  setSortOrder({});
                }
              }}
            >
              {/* 列标题 */}
              <span>{columnDef.header}</span>

              {/* 排序图标 */}
              {sortOrder?.[column.value] && (
                <span className="ml-1">
                  {sortOrder[column.value] === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </div>
          );
        },
      };
    });

    return processedColumns.concat({
      accessorKey: 'action',
      header: ({ column: tableColumn }: { column: any }) => {
        return (
          <div className="flex">
            <span>{t('ctint-mf-content-creation.spacelist.action')}</span>
          </div>
        );
      },
      enableSorting: false,
      cell: ({ row }: { row: any }) => {
        return (
          <ActionColumn
            row={row}
            onViewHandle={() => {
              toPath(
                `/canned/category?id=${row.original?.id}&type=${row.original?.channelType}&space=${row.original?.name}`
              );
            }}
            onEditHandle={() => handleEditSpace(row.original)}
          />
        );
      },
    });
  };

  const handleNext = () => {
    const totalPages = Math.ceil(total / perPage);
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  const onOpenChange = useCallback(() => {
    // 关闭弹窗时的处理逻辑
    setOpen(false);
    // 重置编辑状态
    setIsEditMode(false);
    setEditingSpace(null);
    setNewSpaceName('');
    setSelectedChannelType('whatsapp');
  }, []);

  // 处理搜索输入变化的函数，使用startTransition优化性能
  const handleSearchChange = useCallback(
    (value: string) => {
      // 立即更新输入框显示的值
      setSearchInput(value);

      // 使用startTransition将搜索查询的更新标记为非紧急更新
      startTransition(() => {
        setSearchQuery(value);
      });
    },
    [startTransition]
  );

  // 处理编辑space的函数
  const handleEditSpace = useCallback((space: any) => {
    setIsEditMode(true);
    setEditingSpace(space);
    setNewSpaceName(space.name || '');
    setSelectedChannelType(space.channelType || 'whatsapp');
    setOpen(true);
  }, []);

  return (
    <div className="w-full h-full p-4 flex flex-col overflow-hidden">
      {/* search bar & filter dropdown list */}
      <section className="flex gap-2 flex-shrink-0">
        <div className="relative w-1/4">
          <input
            className="w-full border-2 border-gray-300 rounded-md p-1 pr-8 focus:outline-none"
            type="text"
            placeholder={t('ctint-mf-content-creation.spacelist.search')}
            value={searchInput}
            onChange={(e) => {
              handleSearchChange(e.target.value);
            }}
          />
          {/* 搜索状态指示器 */}
          {isPending && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-600 rounded-full"></div>
            </div>
          )}
        </div>
        <select
          className="border-2 border-gray-300 rounded-md p-1 focus:outline-none"
          value={filterChannelType}
          onChange={(e) => {
            setFilterChannelType(e.target.value);
          }}
        >
          <option value="all">
            {t('ctint-mf-content-creation.spacelist.all')}
          </option>
          <option value="whatsapp">
            {t('ctint-mf-content-creation.spacelist.whatsapp')}
          </option>
          <option value="email">
            {t('ctint-mf-content-creation.spacelist.email')}
          </option>
        </select>

        <div className="flex gap-2 ml-auto">
          <button
            className="bg-black text-white rounded-md p-1 focus:outline-none active:bg-gray-800 active:scale-95 transition-transform duration-100"
            onClick={() => {
              // Handle create button click - 重置为创建模式
              setIsEditMode(false);
              setEditingSpace(null);
              setNewSpaceName('');
              setSelectedChannelType('whatsapp');
              setOpen(true);
              console.log('Create button clicked');
            }}
          >
            {t('ctint-mf-content-creation.spacelist.create')}
          </button>
        </div>
      </section>

      {/* table */}
      <section className="mt-4 flex-1 min-h-0 overflow-hidden">
        <DataTable<SpaceNames>
          emptyMessage={t('ctint-mf-content-creation.spacelist.noData')}
          data={loading ? [] : data?.data?.data ?? []}
          columns={generateColumns(
            spaceColumn,
            sortOrder,
            (input) => {
              setSortOrder(input);
            },
            basePath,
            t,
            i18n
          )}
          loading={loading}
          error={error?.message}
          onClickRow={(row) => {
            toPath(
              `/canned/category?id=${row.original?.id}&type=${row.original?.channelType}&space=${row.original?.name}`
            );
          }}
          onTableSetUp={(table) => setTable(table)}
          resize
        />
      </section>

      <section className="flex-none shrink-0">
        {Math.ceil(total / perPage) > 0 && (
          <section className="flex-row">
            <div>
              <Pagination
                current={currentPage}
                perPage={perPage}
                total={Math.ceil(total / perPage)}
                totalCount={total}
                onChange={(v) => setCurrentPage(v)}
                handleOnPrevious={() => handlePrevious()}
                handleOnNext={() => handleNext()}
                handlePerPageSetter={(p: number) => {
                  setPerPage(p);
                  setCurrentPage(1);
                }}
              />
            </div>
          </section>
        )}
      </section>

      <Popup
        open={open}
        onOpenChange={onOpenChange}
      >
        <PopupContent
          className="sm:max-w-[800px] shadow-md"
          title={
            isEditMode
              ? t('ctint-mf-content-creation.spacelist.popup.editTitle')
              : t('ctint-mf-content-creation.spacelist.popup.title')
          }
        >
          <div className="p-4 flex flex-col gap-1 px-12">
            <span className="font-bold text-[15px] leading-[2.2] text-black">
              {t('ctint-mf-content-creation.spacelist.popup.spaceName')}
              <span className="text-red-500">*</span>
            </span>
            <div className="flex flex-col gap-1">
              <input
                type="text"
                placeholder={t(
                  'ctint-mf-content-creation.spacelist.popup.spaceNamePlaceholder'
                )}
                value={newSpaceName}
                className="bg-white border border-[#DEDEDE] rounded px-2 py-3 text-black text-sm leading-[1.4] focus:outline-none focus:ring-2 focus:ring-blue-500"
                onChange={(e) => {
                  setNewSpaceName(e.target.value);
                }}
              />
            </div>
            <div>
              <div className="flex flex-col gap-1">
                <span className="font-bold text-sm leading-[1.4] text-black">
                  {t('ctint-mf-content-creation.spacelist.popup.channelType')}
                  <span className="text-red-500">*</span>
                </span>
                <div className="relative w-full">
                  <select
                    className={`bg-white border border-[#DEDEDE] rounded px-2 py-3 w-full text-black text-sm leading-[1.17] focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none pr-10 ${
                      isEditMode ? 'bg-gray-100 cursor-not-allowed' : ''
                    }`}
                    value={selectedChannelType}
                    disabled={isEditMode}
                    onChange={(e) => {
                      setSelectedChannelType(e.target.value);
                    }}
                  >
                    <option value="whatsapp">
                      {t('ctint-mf-content-creation.spacelist.popup.whatsapp')}
                    </option>
                    <option value="email">
                      {t('ctint-mf-content-creation.spacelist.popup.email')}
                    </option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="m6 9 6 6 6-6" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex flex-row items-center justify-end gap-2 mt-4">
              <button
                className="bg-gray-200 text-black rounded-md p-2 focus:outline-none active:bg-gray-300 active:scale-95 transition-transform duration-100"
                onClick={() => {
                  // Handle cancel button click
                  setOpen(false);
                  console.log('Cancel button clicked');
                }}
              >
                {t('ctint-mf-content-creation.spacelist.popup.cancel')}
              </button>
              <button
                className="bg-black text-white rounded-md p-2 focus:outline-none active:bg-gray-800 active:scale-95 transition-transform duration-100"
                onClick={() => {
                  if (_.isEmpty(newSpaceName)) return;

                  if (isEditMode) {
                    // 处理更新space
                    console.log('Update space clicked');
                    updateSpaceName(
                      {
                        id: editingSpace.id, // 更新时需要包含id字段
                        name: newSpaceName,
                        channelType: selectedChannelType,
                        type: 'space',
                        parentId: parentId,
                      },
                      basePath
                    ).then((res) => {
                      if (res.data.isSuccess) {
                        console.log('Update space success');
                        setOpen(false);
                        queryClient.invalidateQueries({
                          queryKey: ['spaceNameList'],
                        });
                      }
                    });
                  } else {
                    // 处理创建space
                    console.log('Create space clicked');
                    createSpaceName(
                      {
                        name: newSpaceName,
                        channelType: selectedChannelType,
                        type: 'space',
                        parentId: parentId,
                      },
                      basePath
                    ).then((res) => {
                      if (res.data.isSuccess) {
                        console.log('Create space success');
                        setOpen(false);
                        queryClient.invalidateQueries({
                          queryKey: ['spaceNameList'],
                        });
                      }
                    });
                  }
                }}
              >
                {isEditMode
                  ? t('ctint-mf-content-creation.spacelist.popup.update')
                  : t('ctint-mf-content-creation.spacelist.popup.create')}
              </button>
            </div>
          </div>
        </PopupContent>
      </Popup>
    </div>
  );
};

// Create a client
const queryClient = new QueryClient();

export const CannedMessageSpace = (props: { parentId: string }) => (
  <QueryClientProvider client={queryClient}>
    <CannedMessageSpaceBody parentId={props.parentId} />
    <Toaster />
  </QueryClientProvider>
);

export default CannedMessageSpace;
