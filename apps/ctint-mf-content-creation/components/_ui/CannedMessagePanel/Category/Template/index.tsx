import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  useTransition,
  useDeferredValue,
} from 'react';
import { QueryClient, useQuery } from '@tanstack/react-query';
import {
  Popup,
  PopupContent,
  useRole,
  useRouteH<PERSON>ler,
} from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { useTranslation } from 'react-i18next';
import {
  fireCreateCategory,
  fireGetSpaceNameList,
  fireDeleteTemplate,
} from '../../../../../lib/api';
import ConfirmationPopup from '../../../ConfirmationPopup';
import {
  TOrderByInput,
  TSortOrder,
} from '@cdss-modules/design-system/@types/common';
import { ICriteria } from '@cdss-modules/design-system/@types/config';
import { i18n, TFunction } from 'i18next';
import { Table as TableType } from '@tanstack/react-table';
import { Category } from '../../../../../@types/category';
import ActionColumn from '../../actionColumn';
import _, { set } from 'lodash';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { useSearchParams } from 'react-router-dom';
import { TemplateList } from '../../../../../@types/template';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { GLOBAL_DATETIME_SECOND_FORMAT } from '@cdss-modules/design-system/lib/constants';
dayjs.extend(utc);

export const CannedMessageTemplateBody = ({
  queryClient,
  setEditMode,
  setStatus,
  setSelectedTemplateItem,
  setEditable,
}: {
  queryClient: QueryClient;
  setEditMode?: (editMode: boolean) => void;
  setStatus?: (status: undefined | 'published' | 'draft') => void;
  setSelectedTemplateItem?: (template: TemplateList) => void;
  setEditable?: (editable: boolean) => void;
}) => {
  const { i18n } = useTranslation();
  const { t } = i18n;
  const { globalConfig } = useRole();
  const { toPath, basePath, activePath } = useRouteHandler();
  const [searchParams] = useSearchParams();
  console.log(
    'CannedMessageTemplateBody ===> searchParams',
    searchParams.get('id')
  );

  const [total, setTotal] = React.useState<number>(0);
  const [currentPage, setCurrentPage] = React.useState<number>(1);
  const [perPage, setPerPage] = React.useState<number>(10);
  const [sortOrder, setSortOrder] = React.useState<TOrderByInput>();
  const [table, setTable] = React.useState<TableType<TemplateList>>();

  // const [open, setOpen] = React.useState(false);

  const [selectedTemplate, setSelectedTemplate] = React.useState<
    TemplateList | undefined
  >();

  // 搜索相关状态
  const [searchInput, setSearchInput] = React.useState('');
  const [searchQuery, setSearchQuery] = React.useState('');
  const [isPending, startTransition] = useTransition();

  // 使用useDeferredValue实现节流效果
  const deferredSearchQuery = useDeferredValue(searchQuery);

  // 确认删除弹窗相关状态
  const [showDeleteConfirmation, setShowDeleteConfirmation] =
    React.useState(false);
  const [templateToDelete, setTemplateToDelete] =
    React.useState<TemplateList | null>(null);

  // 动态构建搜索条件，包含keyword字段和排序
  const searchConditions = useMemo(() => {
    const baseConditions: any = {
      page: currentPage,
      pageSize: Number(perPage),
      type: 'template',
      parentId: searchParams.get('id'),
    };

    // 如果有排序条件，则添加sort字段
    if (sortOrder && Object.keys(sortOrder).length > 0) {
      const sortKey = Object.keys(sortOrder)[0];
      const sortDirection = sortOrder[sortKey];
      baseConditions.sort = {
        [sortKey]: sortDirection?.toUpperCase() || 'ASC',
      };
    }

    // 如果有搜索查询，则添加keyword字段
    if (deferredSearchQuery.trim()) {
      baseConditions.keyword = deferredSearchQuery.trim();
    }

    return baseConditions;
  }, [currentPage, perPage, sortOrder, deferredSearchQuery, searchParams]);

  const { templateColumn } =
    globalConfig &&
    globalConfig.microfrontends &&
    globalConfig.microfrontends['ctint-mf-content-creation']
      ? globalConfig.microfrontends['ctint-mf-content-creation']
      : [];

  console.log(
    'CannedMessageTemplateBody => searchConditions',
    searchConditions
  );

  const {
    data,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ['templateList', searchConditions, deferredSearchQuery],
    queryFn: () =>
      fireGetSpaceNameList(searchConditions, basePath)?.then((res) => {
        return res.data;
      }),
    enabled: !!searchConditions,
    // 添加staleTime以优化性能，避免频繁重新获取
    staleTime: 1000, // 1秒内的数据被认为是新鲜的
  });

  console.log('CannedMessageTemplateBody => templateList', data);

  useMemo(() => {
    // 设置数据总数
    setTotal(data?.data?.total);
  }, [data]);

  // 处理搜索输入变化的函数，使用startTransition优化性能
  const handleSearchChange = useCallback(
    (value: string) => {
      // 立即更新输入框显示的值
      setSearchInput(value);

      // 使用startTransition将搜索查询的更新标记为非紧急更新
      startTransition(() => {
        setSearchQuery(value);
      });
    },
    [startTransition]
  );

  // 处理删除template的函数 - 显示确认弹窗
  const handleDeleteTemplate = useCallback((template: TemplateList) => {
    setTemplateToDelete(template);
    setShowDeleteConfirmation(true);
  }, []);

  // 确认删除template的函数
  const confirmDeleteTemplate = useCallback(() => {
    if (templateToDelete) {
      console.log('Confirmed delete template:', templateToDelete);

      fireDeleteTemplate(templateToDelete.id, basePath)
        .then((res) => {
          if (res.data.isSuccess) {
            console.log('Delete template success');
            // 刷新列表数据
            queryClient.invalidateQueries({
              queryKey: ['templateList'],
            });
          } else {
            console.error('Delete template failed:', res.data.error);
            // TODO: 显示错误提示
          }
        })
        .catch((error) => {
          console.error('Delete template error:', error);
          // TODO: 显示错误提示
        })
        .finally(() => {
          // 重置状态
          setTemplateToDelete(null);
          setShowDeleteConfirmation(false);
        });
    }
  }, [templateToDelete, basePath, queryClient]);

  const generateColumns = (
    columns: ICriteria[],
    sortOrder: TOrderByInput | undefined,
    setSortOrder: (input: TOrderByInput) => void,
    basePath = '',
    t: TFunction,
    i18n: i18n
  ) => {
    // 如果没有列配置，返回空数组
    if (!columns || !columns.length) return [];

    // 处理列配置，将ICriteria[]转换为DataTable所需的列定义
    const processedColumns = columns.map((column) => {
      // 列基本配置
      const columnDef = {
        accessorKey: column.value, // 用于访问数据的键
        header: i18n.language === 'en' ? column.labelEn : column.labelCh, // 显示的列标题，使用i18n翻译
        enableSorting: true, // 是否可排序，默认为true

        // 单元格渲染函数
        cell: ({ row }: { row: any }) => {
          const value = row.original[column.value];

          // 根据字段类型处理特殊渲染
          if (column.value === 'channelType') {
            // 根据渠道类型显示不同文本
            return (
              <div className="px-1">
                {t(`ctint-mf-content-creation.spacelist.${value}`)}
              </div>
            );
          }

          // 处理日期字段
          if (column.value === 'createDate' || column.value === 'lastUpdate') {
            return (
              <div className="px-1">
                {value
                  ? dayjs(value).format(GLOBAL_DATETIME_SECOND_FORMAT)
                  : '--'}
              </div>
            );
          }

          // 处理状态
          if (column.value === 'status') {
            console.log('row.original.status: ', row.original.status);
            const statusStyle =
              row.original.status === 'published'
                ? 'text-green-500 border-green-500 bg-green-100'
                : 'text-grey-500 border-grey-500 bg-grey-100';
            return (
              <div
                className={`w-fit px-1 border rounded-sm ${statusStyle} text-center`}
              >
                {_.isEmpty(value) ? '--' : value}
              </div>
            );
          }

          return <div className="px-1">{_.isEmpty(value) ? '--' : value}</div>;
        },
      };

      // 处理排序逻辑
      return {
        ...columnDef,
        // 点击表头时的排序处理
        header: ({ column: tableColumn }: { column: any }) => {
          return (
            <div
              className="cursor-pointer flex items-center"
              onClick={() => {
                // 计算新的排序方向
                const currentDirection = sortOrder?.[column.value];
                const isCurrentColumn = currentDirection !== undefined;

                // 切换排序方向：未排序 -> 升序 -> 降序 -> 未排序
                let newDirection: TSortOrder | undefined;
                if (!isCurrentColumn) {
                  newDirection = 'asc';
                } else if (currentDirection === 'asc') {
                  newDirection = 'desc';
                } else {
                  newDirection = undefined;
                }

                // 设置新的排序
                if (newDirection) {
                  const newSortOrder: TOrderByInput = {
                    [column.value]: newDirection,
                  };
                  setSortOrder(newSortOrder);
                } else {
                  // 清除排序
                  setSortOrder({});
                }
              }}
            >
              {/* 列标题 */}
              <span>{columnDef.header}</span>

              {/* 排序图标 */}
              {sortOrder?.[column.value] && (
                <span className="ml-1">
                  {sortOrder[column.value] === 'asc' ? '↑' : '↓'}
                </span>
              )}
            </div>
          );
        },
      };
    });

    return processedColumns.concat({
      accessorKey: 'action',
      header: ({ column: tableColumn }: { column: any }) => {
        return (
          <div className="flex items-center justify-center">
            <span>{t('ctint-mf-content-creation.spacelist.action')}</span>
          </div>
        );
      },
      enableSorting: false,
      cell: ({ row }: { row: any }) => {
        return (
          <ActionColumn
            row={row}
            onViewHandle={() => {
              setEditMode && setEditMode(true);
              setSelectedTemplateItem && setSelectedTemplateItem(row.original);
              setStatus && setStatus(row.original.status);
              setEditable && setEditable(false); // 设置为查看模式，不可编辑
            }}
            onEditHandle={
              row.original.status === 'published'
                ? undefined // published 状态不显示编辑按钮
                : () => {
                    setEditMode && setEditMode(true);
                    setSelectedTemplateItem &&
                      setSelectedTemplateItem(row.original);
                    setStatus && setStatus(row.original.status);
                    setEditable && setEditable(true); // 设置为编辑模式，可编辑
                  }
            }
            onDeleteHandle={() => handleDeleteTemplate(row.original)}
          />
        );
      },
    });
  };

  const handleNext = () => {
    const totalPages = Math.ceil(total / perPage);
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  return (
    <div className="w-full h-full p-4 flex flex-col overflow-hidden">
      {/* search bar & filter dropdown list */}
      <section className="flex gap-2 flex-shrink-0">
        <div className="relative w-1/4">
          <input
            className="w-full border-2 border-gray-300 rounded-md p-1 pr-8 focus:outline-none"
            type="text"
            placeholder={t('ctint-mf-content-creation.spacelist.search')}
            value={searchInput}
            onChange={(e) => {
              handleSearchChange(e.target.value);
            }}
          />
          {/* 搜索状态指示器 */}
          {isPending && (
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <div className="animate-spin h-4 w-4 border-2 border-gray-300 border-t-blue-600 rounded-full"></div>
            </div>
          )}
        </div>

        <div className="flex gap-2 ml-auto">
          <button
            className="bg-black text-white rounded-md p-1 focus:outline-none active:bg-gray-800 active:scale-95 transition-transform duration-100"
            onClick={() => {
              // Handle create button click
              setEditMode && setEditMode(true);
              setStatus && setStatus('draft');
              setEditable && setEditable(true); // 创建模式，可编辑
            }}
          >
            {t('ctint-mf-content-creation.spacelist.create')}
          </button>
        </div>
      </section>

      {/* table */}
      <section className="mt-4 flex-1 min-h-0 overflow-hidden">
        <DataTable<TemplateList>
          emptyMessage={t('ctint-mf-content-creation.spacelist.noData')}
          data={loading ? [] : data?.data?.data ?? []}
          columns={generateColumns(
            templateColumn,
            sortOrder,
            (input) => {
              setSortOrder(input);
            },
            basePath,
            t,
            i18n
          )}
          loading={loading}
          error={error?.message}
          onClickRow={(row) => {
            setEditMode && setEditMode(true);
            setSelectedTemplateItem && setSelectedTemplateItem(row.original);
            setStatus && setStatus(row.original.status);
            // 等于view mode
            setEditable && setEditable(false);
          }}
          onTableSetUp={(table) => setTable(table)}
          resize
        />
      </section>

      <section className="flex-none shrink-0">
        {Math.ceil(total / perPage) > 0 && (
          <section className="flex-row">
            <div>
              <Pagination
                current={currentPage}
                perPage={perPage}
                total={Math.ceil(total / perPage)}
                totalCount={total}
                onChange={(v) => setCurrentPage(v)}
                handleOnPrevious={() => handlePrevious()}
                handleOnNext={() => handleNext()}
                handlePerPageSetter={(p: number) => {
                  setPerPage(p);
                  setCurrentPage(1);
                }}
              />
            </div>
          </section>
        )}
      </section>

      {/* Delete Confirmation Popup */}
      <ConfirmationPopup
        isOpen={showDeleteConfirmation}
        onClose={() => {
          setShowDeleteConfirmation(false);
          setTemplateToDelete(null);
        }}
        onConfirm={confirmDeleteTemplate}
        message={
          templateToDelete
            ? t('ctint-mf-content-creation.template.deleteConfirmation', {
                name: templateToDelete.name,
              })
            : t('ctint-mf-content-creation.template.deleteConfirmationGeneric')
        }
        confirmText={t('ctint-mf-content-creation.spacelist.delete')}
        cancelText={t('ctint-mf-content-creation.spacelist.popup.cancel')}
      />
    </div>
  );
};
