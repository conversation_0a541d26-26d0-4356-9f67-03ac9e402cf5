import React, {
  useEffect,
  useImperativeHandle,
  forwardRef,
  useState,
  useRef,
} from 'react';
import { useEditor, EditorContent, Extension } from '@tiptap/react';
import { RawCommands, PasteRule } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextStyle from '@tiptap/extension-text-style';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Placeholder from '@tiptap/extension-placeholder';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import { Color } from '@tiptap/extension-color';
import OrderedList from '@tiptap/extension-ordered-list';
import BulletList from '@tiptap/extension-bullet-list';
import ListItem from '@tiptap/extension-list-item';
import { TiptapToolbar } from './index';
import { emailEditorStyles } from '@cdss-modules/design-system/lib/utils';

// 为自定义命令声明类型，解决TS2353错误
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    fontSize: {
      /**
       * 设置字体大小
       */
      setFontSize: (fontSize: string) => ReturnType;
      /**
       * 取消字体大小设置
       */
      unsetFontSize: () => ReturnType;
    };
  }
}

// 自定义的FontSize扩展
const FontSize = Extension.create({
  name: 'fontSize',

  addOptions() {
    return {
      types: ['textStyle'],
    };
  },

  addGlobalAttributes() {
    return [
      {
        types: this.options.types,
        attributes: {
          fontSize: {
            default: null,
            parseHTML: (element) => element.style.fontSize,
            renderHTML: (attributes) => {
              if (!attributes.fontSize) {
                return {};
              }

              return {
                style: `font-size: ${attributes.fontSize}`,
              };
            },
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setFontSize:
        (fontSize: string) =>
        ({ chain }) => {
          return chain().focus().setMark('textStyle', { fontSize }).run();
        },
      unsetFontSize:
        () =>
        ({ chain }) => {
          return chain()
            .focus()
            .setMark('textStyle', { fontSize: null })
            .removeEmptyTextStyle()
            .run();
        },
    };
  },
});

// 扩展组件的引用类型，以便在父组件访问编辑器实例
export interface EmailEditorRef {
  insertImage: (
    src: string,
    title?: string,
    alt?: string,
    width?: number,
    height?: number
  ) => void;
  setContent: (content: string) => void;
  getContent: () => string;
  isEmpty: () => boolean;
  insertContentAtCursor: (content: string) => void;
}

// 为自定义命令声明类型，解决背景色相关的类型问题
declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    // 文本背景色命令
    backgroundColor: {
      /**
       * 设置文本背景色
       */
      setBackgroundColor: (color: string) => ReturnType;
      /**
       * 取消文本背景色设置
       */
      unsetBackgroundColor: () => ReturnType;
    };

    // 表格背景色命令
    tableBackground: {
      /**
       * 设置表格背景色
       */
      setTableBackground: (color: string) => ReturnType;
      /**
       * 设置表格单元格背景色
       */
      setTableCellBackground: (color: string) => ReturnType;
    };
  }
}

const CustomTable = Table.extend({
  name: 'table',

  addAttributes() {
    return {
      ...this.parent?.(),
      // 背景色支持
      backgroundColor: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('data-original-bgcolor') ||
            element.getAttribute('bgcolor') ||
            (element.style && element.style.backgroundColor) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.backgroundColor) {
            return {};
          }

          return {
            style: `background-color: ${attributes.backgroundColor} !important`,
            bgcolor: attributes.backgroundColor,
            'data-original-bgcolor': attributes.backgroundColor,
            class: 'has-bgcolor',
          };
        },
      },
      // 宽度支持
      width: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('width') ||
            (element.style && element.style.width) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }

          return {
            style: `width: ${attributes.width}`,
            width: attributes.width,
            'data-original-width': attributes.width,
            class: 'has-width',
          };
        },
      },
      // 边框支持
      border: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('border') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.border) {
            return {};
          }

          return {
            border: attributes.border,
            'data-original-border': attributes.border,
            class: 'has-border',
          };
        },
      },
      // 边框颜色支持
      borderColor: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('bordercolor') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.borderColor) {
            return {};
          }

          return {
            style: `border-color: ${attributes.borderColor}`,
            bordercolor: attributes.borderColor,
            'data-original-bordercolor': attributes.borderColor,
            class: 'has-bordercolor',
          };
        },
      },
      // 单元格内边距支持
      cellPadding: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('cellpadding') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.cellPadding) {
            return {};
          }

          return {
            cellpadding: attributes.cellPadding,
            'data-original-cellpadding': attributes.cellPadding,
            class: 'has-cellpadding',
          };
        },
      },
      // 单元格间距支持
      cellSpacing: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('cellspacing') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.cellSpacing) {
            return {};
          }

          const style =
            attributes.cellSpacing === '0'
              ? `border-collapse: collapse; border-spacing: 0`
              : `border-collapse: separate; border-spacing: ${attributes.cellSpacing}px`;

          return {
            style,
            cellspacing: attributes.cellSpacing,
            'data-original-cellspacing': attributes.cellSpacing,
            class: 'has-cellspacing',
          };
        },
      },
      // 对齐方式支持
      align: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('align') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.align) {
            return {};
          }

          return {
            style: `text-align: ${attributes.align}`,
            align: attributes.align,
            'data-original-align': attributes.align,
            class: 'has-align',
          };
        },
      },
    };
  },
});

const CustomTableCell = TableCell.extend({
  name: 'tableCell',

  addAttributes() {
    return {
      ...this.parent?.(),
      // 背景色支持
      backgroundColor: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('data-original-bgcolor') ||
            element.getAttribute('bgcolor') ||
            (element.style && element.style.backgroundColor) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.backgroundColor) {
            return {};
          }

          return {
            style: `background-color: ${attributes.backgroundColor} !important`,
            bgcolor: attributes.backgroundColor,
            'data-original-bgcolor': attributes.backgroundColor,
            class: 'has-bgcolor',
          };
        },
      },
      // 宽度支持
      width: {
        default: null,
        parseHTML: (element) => {
          return (
            element.getAttribute('width') ||
            (element.style && element.style.width) ||
            null
          );
        },
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }

          return {
            style: `width: ${attributes.width}`,
            width: attributes.width,
            'data-original-width': attributes.width,
            class: 'has-width',
          };
        },
      },
      // 对齐方式支持
      align: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('align') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.align) {
            return {};
          }

          return {
            style: `text-align: ${attributes.align}`,
            align: attributes.align,
            'data-original-align': attributes.align,
            class: 'has-align',
          };
        },
      },
      // 垂直对齐支持
      valign: {
        default: null,
        parseHTML: (element) => {
          return element.getAttribute('valign') || null;
        },
        renderHTML: (attributes) => {
          if (!attributes.valign) {
            return {};
          }

          return {
            style: `vertical-align: ${attributes.valign}`,
            valign: attributes.valign,
            'data-original-valign': attributes.valign,
            class: 'has-valign',
          };
        },
      },
    };
  },
});

// 文本背景色扩展 - 修复类型问题
// 文本背景色扩展 - 修复选中文本应用背景色问题
const BackgroundColor = Extension.create({
  name: 'backgroundColor',
  addGlobalAttributes() {
    return [
      {
        types: ['textStyle'],
        attributes: {
          backgroundColor: {
            default: null,
            renderHTML: (attributes) => {
              if (!attributes.backgroundColor) return {};
              return {
                style: `background-color: ${attributes.backgroundColor} !important`,
                'data-bg-color': attributes.backgroundColor,
                class: 'has-bg-color',
              };
            },
            parseHTML: (element: HTMLElement): string | null =>
              element.style.backgroundColor || null,
          },
        },
      },
    ];
  },

  addCommands() {
    return {
      setBackgroundColor:
        (color: string) =>
        ({ chain, state }) => {
          // 判断是否有选中的文本
          if (state.selection.empty) {
            // 如果没有选中文本，直接设置背景色
            return chain()
              .focus()
              .setMark('textStyle', { backgroundColor: color })
              .run();
          } else {
            // 如果有选中文本，先移除现有的textStyle标记再应用新标记
            // 这是解决选中文本设置背景色不生效的关键
            return chain()
              .focus()
              .setMark('textStyle', { backgroundColor: null }) // 先清除现有背景色
              .setMark('textStyle', { backgroundColor: color }) // 然后设置新背景色
              .run();
          }
        },
      unsetBackgroundColor:
        () =>
        ({ chain }) => {
          return chain()
            .focus()
            .setMark('textStyle', { backgroundColor: null })
            .removeEmptyTextStyle()
            .run();
        },
    };
  },
});

// 表格背景色扩展 - 修复类型问题
const TableBackgroundExtension = Extension.create({
  name: 'tableBackground',

  addGlobalAttributes() {
    return [
      {
        types: ['table', 'tableCell', 'tableHeader', 'tableRow'],
        attributes: {
          backgroundColor: {
            default: null,
            parseHTML: (element: HTMLElement): string | null => {
              return (
                element.getAttribute('data-original-bgcolor') ||
                element.getAttribute('bgcolor') ||
                (element.style && element.style.backgroundColor) ||
                null
              );
            },
            renderHTML: (
              attributes: Record<string, any>
            ): Record<string, any> => {
              if (!attributes.backgroundColor) {
                return {};
              }

              return {
                style: `background-color: ${attributes.backgroundColor} !important`,
                'data-original-bgcolor': attributes.backgroundColor,
                bgcolor: attributes.backgroundColor,
                class: 'has-bgcolor',
              };
            },
          },
        },
      },
    ];
  },

  // 添加命令以设置表格背景色 - 修复类型问题
  addCommands() {
    return {
      setTableBackground:
        (color: string) =>
        ({ chain }) => {
          return chain()
            .updateAttributes('table', {
              backgroundColor: color,
            })
            .run() as any; // 添加类型断言
        },
      setTableCellBackground:
        (color: string) =>
        ({ chain }) => {
          return chain()
            .updateAttributes('tableCell', {
              backgroundColor: color,
            })
            .run() as any; // 添加类型断言
        },
    };
  },
});

// 处理MS邮件内容的扩展
const MsEmailPaste = Extension.create({
  name: 'msEmailPaste',
  addPasteRules() {
    return [
      {
        type: 'htmlToDoc',
        priority: 100,
        handler: (
          props: { html: string },
          next: (props: { html: string }) => any
        ) => {
          const { html } = props;
          if (
            html.includes('mso-') ||
            html.includes('urn:schemas-microsoft-com')
          ) {
            props.html = processHtmlForEditor(html);
          }
          return next(props);
        },
        // 添加find属性以满足PasteRule类型要求
        find: () => null,
      } as unknown as PasteRule,
    ];
  },
});

// 自定义扩展Image组件，添加width和height属性支持
const ResizableImage = Image.extend({
  addAttributes() {
    // 首先获取父类的属性
    const parentAttributes = this.parent?.() || {
      src: { default: null },
      alt: { default: null },
      title: { default: null },
    };

    // 添加width和height属性
    return {
      ...parentAttributes,
      width: {
        default: null,
        // 从DOM元素解析width属性
        parseHTML: (element) => element.getAttribute('width'),
        // 渲染width属性到HTML
        renderHTML: (attributes) => {
          if (!attributes.width) {
            return {};
          }
          return { width: attributes.width };
        },
      },
      height: {
        default: null,
        parseHTML: (element) => element.getAttribute('height'),
        renderHTML: (attributes) => {
          if (!attributes.height) {
            return {};
          }
          return { height: attributes.height };
        },
      },
      // 可选：添加style属性支持，确保内联样式也能被保留
      style: {
        default: null,
        parseHTML: (element) => element.getAttribute('style'),
        renderHTML: (attributes) => {
          if (!attributes.style) {
            return {};
          }
          return { style: attributes.style };
        },
      },
    };
  },
}).configure({
  inline: true,
  allowBase64: true,
  HTMLAttributes: {
    class: 'resizable-image',
  },
});

/**
 * 全面处理HTML内容，特别是针对邮件中的复杂表格样式
 * @param html 原始HTML内容
 * @returns 处理后的HTML内容
 */
export const processHtmlForEditor = (html: string): string => {
  if (!html) return '';

  // 使用DOMParser解析HTML
  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');

  // 处理所有表格元素（table, tr, td, th）
  const tableElements = doc.querySelectorAll('table, tr, td, th');
  tableElements.forEach((el) => {
    // 1. 处理bgcolor属性
    const bgcolor = el.getAttribute('bgcolor');
    if (bgcolor) {
      // 保存原始bgcolor值到自定义数据属性
      el.setAttribute('data-original-bgcolor', bgcolor);
      // 设置内联样式，使用!important确保优先级
      (el as HTMLElement).style.cssText +=
        `; background-color: ${bgcolor} !important;`;
      // 添加特殊标记类
      el.classList.add('has-bgcolor');
    }

    // 2. 处理宽度属性
    const width = el.getAttribute('width');
    if (width) {
      // 保存原始width值到自定义数据属性
      el.setAttribute('data-original-width', width);
      // 设置内联样式
      let widthValue = width;
      // 如果宽度是纯数字，添加px单位
      if (/^\d+$/.test(width)) {
        widthValue = `${width}px`;
      }
      (el as HTMLElement).style.cssText += `; width: ${widthValue} !important;`;
      el.classList.add('has-width');
    }

    // 3. 处理边框属性
    const border = el.getAttribute('border');
    if (border) {
      // 保存原始border值到自定义数据属性
      el.setAttribute('data-original-border', border);
      // 设置内联样式
      const borderWidth =
        border === '0' ? '0px' : parseInt(border) > 0 ? `${border}px` : '1px';
      (el as HTMLElement).style.cssText +=
        `; border-width: ${borderWidth} !important;`;
      if (border !== '0') {
        (el as HTMLElement).style.cssText +=
          `; border-style: solid !important;`;
      }
      el.classList.add('has-border');
    }

    // 4. 处理bordercolor属性
    const bordercolor = el.getAttribute('bordercolor');
    if (bordercolor) {
      // 保存原始bordercolor值到自定义数据属性
      el.setAttribute('data-original-bordercolor', bordercolor);
      // 设置内联样式
      (el as HTMLElement).style.cssText +=
        `; border-color: ${bordercolor} !important;`;
      el.classList.add('has-bordercolor');
    }

    // 5. 处理cellpadding属性
    const cellpadding = el.getAttribute('cellpadding');
    if (cellpadding && el.tagName.toLowerCase() === 'table') {
      // 保存原始cellpadding值到自定义数据属性
      el.setAttribute('data-original-cellpadding', cellpadding);
      // 为表格内的所有单元格添加内边距
      const cells = el.querySelectorAll('td, th');
      cells.forEach((cell) => {
        (cell as HTMLElement).style.cssText +=
          `; padding: ${cellpadding}px !important;`;
      });
      el.classList.add('has-cellpadding');
    }

    // 6. 处理cellspacing属性
    const cellspacing = el.getAttribute('cellspacing');
    if (cellspacing && el.tagName.toLowerCase() === 'table') {
      // 保存原始cellspacing值到自定义数据属性
      el.setAttribute('data-original-cellspacing', cellspacing);
      // 处理表格间距 - 这需要更复杂的CSS，但可以通过border-spacing属性实现
      (el as HTMLElement).style.cssText +=
        `; border-spacing: ${cellspacing}px !important;`;
      if (cellspacing === '0') {
        (el as HTMLElement).style.cssText +=
          `; border-collapse: collapse !important;`;
      } else {
        (el as HTMLElement).style.cssText +=
          `; border-collapse: separate !important;`;
      }
      el.classList.add('has-cellspacing');
    }

    // 7. 处理align属性
    const align = el.getAttribute('align');
    if (align) {
      // 保存原始align值到自定义数据属性
      el.setAttribute('data-original-align', align);
      // 设置内联样式
      (el as HTMLElement).style.cssText += `; text-align: ${align} !important;`;
      el.classList.add('has-align');
    }

    // 8. 处理valign属性
    const valign = el.getAttribute('valign');
    if (valign) {
      // 保存原始valign值到自定义数据属性
      el.setAttribute('data-original-valign', valign);
      // 设置内联样式
      (el as HTMLElement).style.cssText +=
        `; vertical-align: ${valign} !important;`;
      el.classList.add('has-valign');
    }

    // 9. 处理style中已有的CSS属性
    if ((el as HTMLElement).style) {
      // 背景色
      if ((el as HTMLElement).style.backgroundColor) {
        const bgColor = (el as HTMLElement).style.backgroundColor;
        (el as HTMLElement).style.setProperty(
          'background-color',
          bgColor,
          'important'
        );
        el.setAttribute('data-bg-color', bgColor);
        el.classList.add('has-bg-style');
      }

      // 边框样式
      if (
        (el as HTMLElement).style.borderWidth ||
        (el as HTMLElement).style.borderStyle ||
        (el as HTMLElement).style.borderColor
      ) {
        el.classList.add('has-border-style');
      }

      // 宽度
      if ((el as HTMLElement).style.width) {
        const widthValue = (el as HTMLElement).style.width;
        (el as HTMLElement).style.setProperty('width', widthValue, 'important');
        el.classList.add('has-width-style');
      }
    }
  });

  // 特殊处理Teams邮件
  if (
    html.includes('teams.microsoft.com') ||
    html.includes('style="background:revert') ||
    html.includes('microsoft.com')
  ) {
    // 查找Teams特定表格
    const teamsSpecificTables = doc.querySelectorAll(
      'table[style*="revert"], table[style*="table-layout:fixed"], table[align="left"]'
    );
    teamsSpecificTables.forEach((table) => {
      // 添加特殊标记类
      table.classList.add('teams-table');

      // 确保表格布局保持固定
      (table as HTMLElement).style.tableLayout = 'fixed';

      // 处理背景色单元格
      const cells = table.querySelectorAll(
        'td[bgcolor], td[style*="background-color"]'
      );
      cells.forEach((cell) => {
        const bgcolor = cell.getAttribute('bgcolor');
        if (bgcolor) {
          (cell as HTMLElement).style.backgroundColor = bgcolor;
          cell.setAttribute(
            'style',
            `${(cell as HTMLElement).getAttribute('style') || ''}; background-color: ${bgcolor} !important;`
          );
          cell.classList.add('teams-bg-cell');
        }

        // 处理内联背景色
        if (
          (cell as HTMLElement).style &&
          (cell as HTMLElement).style.backgroundColor
        ) {
          const bgColor = (cell as HTMLElement).style.backgroundColor;
          (cell as HTMLElement).style.setProperty(
            'background-color',
            bgColor,
            'important'
          );
          cell.classList.add('teams-bg-cell');
        }
      });
    });

    // 处理特殊边框
    const borderedElements = doc.querySelectorAll('[style*="border"]');
    borderedElements.forEach((el) => {
      el.classList.add('has-custom-border');
    });
  }

  // 添加内联样式表确保所有表格样式正确显示
  const styleEl = doc.createElement('style');
  styleEl.textContent = `
    /* 表格基本样式重置 */
    table.email-table {
      border-collapse: separate !important;
      border-spacing: inherit !important;
      width: auto !important;
      table-layout: auto !important;
    }

    /* 强制应用背景色 */
    [data-original-bgcolor] {
      background-color: attr(data-original-bgcolor) !important;
    }

    [bgcolor] {
      background-color: attr(bgcolor) !important;
    }

    .has-bgcolor {
      background-color: inherit !important;
    }

    /* 强制应用宽度 */
    [data-original-width] {
      width: attr(data-original-width) !important;
    }

    .has-width {
      width: inherit !important;
    }

    /* 强制应用边框样式 */
    [data-original-border] {
      border-width: attr(data-original-border) !important;
    }

    [data-original-bordercolor] {
      border-color: attr(data-original-bordercolor) !important;
    }

    .has-border {
      border-style: inherit !important;
      border-width: inherit !important;
    }

    .has-bordercolor {
      border-color: inherit !important;
    }

    /* 对齐方式 */
    [data-original-align] {
      text-align: attr(data-original-align) !important;
    }

    [data-original-valign] {
      vertical-align: attr(data-original-valign) !important;
    }

    /* Teams特殊表格 */
    .teams-table {
      border-collapse: separate !important;
      border-spacing: 0 !important;
      table-layout: fixed !important;
    }

    .teams-bg-cell {
      background-color: inherit !important;
    }

    /* 常见的Teams邮件背景色 */
    .teams-bg-cell[bgcolor="#A6A6A6"] {
      background-color: #A6A6A6 !important;
    }

    .teams-bg-cell[bgcolor="#EAEAEA"] {
      background-color: #EAEAEA !important;
    }

    .teams-bg-cell[bgcolor="#f7f7f7"] {
      background-color: #f7f7f7 !important;
    }

    .teams-bg-cell[bgcolor="#f8f8f8"] {
      background-color: #f8f8f8 !important;
    }

    /* 处理Microsoft Teams中的按钮样式 */
    [bgcolor="#6264a7"] {
      background-color: #6264a7 !important;
    }

    /* 确保内联样式优先 */
    [style*="background-color"] {
      background-color: inherit !important;
    }

    [style*="border"] {
      border: inherit !important;
    }

    [style*="width"] {
      width: inherit !important;
    }

    [style*="text-align"] {
      text-align: inherit !important;
    }

    [style*="vertical-align"] {
      vertical-align: inherit !important;
    }
  `;

  // 将样式添加到文档头部
  const headEl = doc.head || doc.getElementsByTagName('head')[0];
  if (headEl) {
    headEl.appendChild(styleEl);
  }

  // 标记所有表格
  doc.querySelectorAll('table').forEach((table) => {
    table.classList.add('email-table');
  });

  // 添加特殊标记类到文档
  doc.documentElement.classList.add('email-content');

  return doc.documentElement.outerHTML;
};

interface EmailEditorProps {
  initialContent?: string;
  onContentChange?: (html: string) => void;
  placeholder?: string;
  onAddLink?: () => void;
  onAddImage?: () => void;
  editable?: boolean; // 新增editable属性，控制编辑器是否可编辑
}

// 自定义的Email编辑器组件，使用forwardRef传递引用
const EmailEditor = forwardRef<EmailEditorRef, EmailEditorProps>(
  (
    {
      initialContent = '',
      onContentChange,
      placeholder = '撰写您的邮件...',
      onAddImage,
      editable = true, // 默认为可编辑状态
    },
    ref
  ) => {
    const [showLinkInput, setShowLinkInput] = useState(false);
    const [linkUrl, setLinkUrl] = useState('');
    const linkInputRef = useRef<HTMLInputElement>(null);
    const editor = useEditor({
      extensions: [
        // 不要使用整个StarterKit，而是分别引入所需扩展
        StarterKit.configure({
          heading: { levels: [1, 2, 3] },
          // 禁用默认的列表，我们将单独引入
          bulletList: false,
          orderedList: false,
          listItem: false,
        }),
        // 显式配置列表扩展
        OrderedList.configure({
          HTMLAttributes: {
            class: 'ordered-list',
          },
        }),
        BulletList.configure({
          HTMLAttributes: {
            class: 'bullet-list',
          },
        }),
        ListItem,
        Underline,
        TextAlign.configure({
          types: [
            'heading',
            'paragraph',
            'blockquote',
            'listItem',
            'tableCell',
            'tableHeader',
          ],
          alignments: ['left', 'center', 'right'],
        }),
        Link.configure({
          openOnClick: false,
          HTMLAttributes: {
            target: '_blank',
            rel: 'noopener noreferrer',
            class: 'email-editor-link',
          },
          validate: (url) => /^https?:\/\//.test(url),
        }),
        ResizableImage.configure({
          inline: false,
          allowBase64: true,
          HTMLAttributes: {
            class: 'resizable-image',
          },
        }),
        TableBackgroundExtension,
        // 使用自定义表格扩展
        CustomTable.configure({
          resizable: true,
          // 允许表格背景色
          HTMLAttributes: {
            class: 'custom-table',
          },
        }),
        TableRow,
        CustomTableCell,
        TableHeader,
        Placeholder.configure({
          placeholder,
        }),
        // 注意顺序：TextStyle需要在FontSize和BackgroundColor之前
        TextStyle,
        // 添加自定义字体大小扩展
        FontSize.configure({
          types: ['textStyle'],
        }),
        Color,
        Subscript,
        Superscript,
        BackgroundColor,
        MsEmailPaste,
      ],
      content: initialContent,
      editable: editable, // 设置编辑器的可编辑状态
      onUpdate: ({ editor }) => {
        // 自动调整高度
        const editorHeight = editor.view.dom.clientHeight;
        if (editorHeight < 150) {
          editor.view.dom.style.minHeight = '150px';
        }

        // 通知内容变化
        if (onContentChange) {
          onContentChange(editor.getHTML());
        }
      },
      parseOptions: {
        preserveWhitespace: 'full',
      },
      editorProps: {
        transformPastedHTML(html) {
          // 在这里可以预处理 HTML
          return html;
        },
      },
      injectCSS: true,
    });
    const handleAddLink = () => {
      // 显示链接输入界面
      setShowLinkInput(true);
      // 使用setTimeout确保在下一个渲染周期后获取焦点
      setTimeout(() => {
        linkInputRef.current?.focus();
      }, 0);
    };

    // 处理链接提交
    const handleLinkSubmit = () => {
      if (!editor) return;
      if (linkUrl.trim()) {
        // 检查URL是否已经有协议前缀，如果没有则添加
        let validUrl = linkUrl.trim();
        if (!/^https?:\/\//i.test(validUrl)) {
          validUrl = 'http://' + validUrl;
        }

        // 如果没有选中文本，创建一个链接节点直接插入
        if (editor?.state.selection.empty) {
          // 创建一个包含链接的HTML片段并插入
          const linkText = validUrl.replace(/^https?:\/\/(www\.)?/, ''); // 简化显示的链接文本
          const linkHTML = `<a href="${validUrl}" target="_blank" rel="noopener noreferrer" class="editor-link">${linkText}</a>`;

          editor.chain().focus().insertContent(linkHTML).run();
        } else {
          // 如果有选中文本，将选中的文本转换为链接
          editor
            .chain()
            .focus()
            .setLink({
              href: validUrl,
              target: '_blank',
              rel: 'noopener noreferrer',
            })
            .run();
        }

        // 重置和隐藏输入框
        setLinkUrl('');
        setShowLinkInput(false);
      }
    };
    // 在初始内容变化时更新编辑器
    useEffect(() => {
      if (editor && initialContent && editor.getHTML() !== initialContent) {
        editor.commands.setContent(initialContent);
      }
    }, [initialContent, editor]);

    // 监听editable属性变化，动态更新编辑器的可编辑状态
    useEffect(() => {
      if (editor) {
        editor.setEditable(editable);
      }
    }, [editor, editable]);

    useEffect(() => {
      if (!editor) return;

      const editorDom = editor.view.dom;

      let startX = 0;
      let startY = 0;
      let startWidth = 0;
      let startHeight = 0;
      let currentImage: HTMLImageElement | null = null;
      let resizeOverlay: HTMLDivElement | null = null;

      function onMouseDown(e: MouseEvent) {
        const target = e.target as HTMLElement;

        // Check if we clicked on an image or its resize handle
        const isImage = target.tagName === 'IMG';
        const isResizeHandle = target.classList.contains('resize-handle');

        const img = isImage
          ? (target as HTMLImageElement)
          : isResizeHandle
            ? (target.parentElement?.querySelector('img') as HTMLImageElement)
            : null;

        if (img) {
          // Check if we're in the bottom-right corner resize area
          const rect = img.getBoundingClientRect();
          const isInBottomRight =
            e.clientX >= rect.right - 20 &&
            e.clientX <= rect.right &&
            e.clientY >= rect.bottom - 20 &&
            e.clientY <= rect.bottom;

          if (isInBottomRight || isResizeHandle) {
            e.preventDefault();

            // Store initial position and size
            startX = e.clientX;
            startY = e.clientY;
            startWidth = img.width || img.offsetWidth;
            startHeight = img.height || img.offsetHeight;
            currentImage = img;

            // Add a class to indicate we're resizing
            document.body.classList.add('resizing-image');

            // Create a visual overlay to indicate resize operation
            if (!resizeOverlay) {
              resizeOverlay = document.createElement('div');
              resizeOverlay.style.position = 'fixed';
              resizeOverlay.style.top = '0';
              resizeOverlay.style.left = '0';
              resizeOverlay.style.right = '0';
              resizeOverlay.style.bottom = '0';
              resizeOverlay.style.zIndex = '9999';
              resizeOverlay.style.pointerEvents = 'none'; // Allow clicks to pass through
              document.body.appendChild(resizeOverlay);
            }
          }
        }
      }

      function onMouseMove(e: MouseEvent) {
        if (!currentImage || !editor) return;

        // Calculate how much the mouse has moved
        const dx = e.clientX - startX;
        const dy = e.clientY - startY;

        // Maintain aspect ratio
        const aspectRatio = startWidth / startHeight;
        const newWidth = Math.max(50, startWidth + dx);
        const newHeight = Math.max(50, newWidth / aspectRatio);

        // Update image size in the DOM
        currentImage.width = newWidth;
        currentImage.height = newHeight;

        // Also update the style to ensure it's visually updated
        currentImage.style.width = `${newWidth}px`;
        currentImage.style.height = `${newHeight}px`;

        // Update the model
        editor.commands.updateAttributes('image', {
          width: newWidth,
          height: newHeight,
        });
      }

      function onMouseUp() {
        if (currentImage && editor) {
          // 获取调整后的宽度和高度
          const newWidth = currentImage.width;
          const newHeight = currentImage.height;

          // 更新Image节点的width和height属性
          editor.commands.updateAttributes('image', {
            width: newWidth.toString(),
            height: newHeight.toString(),
            // 同时更新内联样式
            style: `width: ${newWidth}px; height: ${newHeight}px;`,
          });

          // 也可以显式更新DOM元素，但通常不需要，因为TipTap会自动更新
          currentImage.width = newWidth;
          currentImage.height = newHeight;
          currentImage.style.width = `${newWidth}px`;
          currentImage.style.height = `${newHeight}px`;

          // 重置状态
          currentImage = null;
          document.body.classList.remove('resizing-image');

          if (resizeOverlay && resizeOverlay.parentNode) {
            resizeOverlay.parentNode.removeChild(resizeOverlay);
            resizeOverlay = null;
          }
        }
      }

      // Add event listeners
      editorDom.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);

      return () => {
        // Clean up
        editorDom.removeEventListener('mousedown', onMouseDown);
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
        document.body.classList.remove('resizing-image');

        // Remove the overlay if it exists
        if (resizeOverlay && resizeOverlay.parentNode) {
          resizeOverlay.parentNode.removeChild(resizeOverlay);
        }
      };
    }, [editor]);

    // 使用 useImperativeHandle 暴露编辑器的方法给父组件
    useImperativeHandle(ref, () => ({
      insertImage: (
        src: string,
        title?: string,
        alt?: string,
        width?: number,
        height?: number
      ) => {
        if (editor) {
          editor
            .chain()
            .focus()
            .setImage({
              src,
              title,
              alt,
              // 直接传递width和height作为属性
              width: width ? width.toString() : undefined,
              height: height ? height.toString() : undefined,
              // 同时在HTMLAttributes中指定，以确保兼容性
              HTMLAttributes: {
                width: width ? width.toString() : undefined,
                height: height ? height.toString() : undefined,
                style:
                  width && height
                    ? `width: ${width}px; height: ${height}px;`
                    : undefined,
              },
            } as any)
            .run();
        }
      },
      setContent: (content: string) => {
        if (editor) {
          editor.commands.setContent(content);
        }
      },
      getContent: () => {
        return editor ? editor.getHTML() : '';
      },
      isEmpty: () => {
        return editor ? editor.isEmpty : true;
      },
      insertContentAtCursor: (content: string) => {
        if (editor) {
          // 如果编辑器为空，直接设置内容
          if (editor.isEmpty) {
            editor.commands.setContent(content);
          } else {
            // 否则，在当前光标位置插入内容
            editor.commands.insertContent(content);

            // 插入后保持焦点，确保光标位置正确
            editor.commands.focus();
          }
        }
      },
    }));
    return (
      <div className="email-editor">
        <div className="relative">
          {/* 始终显示工具栏，但传递editable状态来控制是否可操作 */}
          <TiptapToolbar
            editor={editor}
            onAddLink={handleAddLink}
            onAddImage={onAddImage}
            editable={editable}
          />

          {/* 链接输入界面 - 放在工具栏下方，只有在可编辑状态下才显示 */}
          {editable && showLinkInput && (
            <div className="absolute top-full left-0 right-0 z-10 bg-white border border-gray-200 p-3 flex items-center shadow-md rounded-b">
              <span className="mr-2 text-sm font-medium">Enter link URL:</span>
              <input
                ref={linkInputRef}
                type="text"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                className="flex-1 px-3 py-1.5 border rounded-l focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleLinkSubmit();
                  } else if (e.key === 'Escape') {
                    setShowLinkInput(false);
                    setLinkUrl('');
                  }
                }}
              />
              <button
                onClick={handleLinkSubmit}
                className="px-3 py-1.5 bg-blue-500 text-white rounded-r hover:bg-blue-600"
              >
                Confirm
              </button>
              <button
                onClick={() => {
                  setShowLinkInput(false);
                  setLinkUrl('');
                }}
                className="ml-2 px-3 py-1.5 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          )}
        </div>

        <div
          className={`border rounded p-4 mt-1 ${!editable ? 'bg-gray-50 cursor-not-allowed' : ''}`}
        >
          <style>{emailEditorStyles}</style>
          <EditorContent editor={editor} />
        </div>
      </div>
    );
  }
);

EmailEditor.displayName = 'EmailEditor';

export default EmailEditor;
