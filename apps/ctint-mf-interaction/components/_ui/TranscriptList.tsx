import { timeStringToSeconds } from '@cdss-modules/design-system/lib/utils';
import { RootObject } from '../../types/transcript';
import { useEffect, useMemo } from 'react';
import TranscriptItem from '@cdss-modules/design-system/components/_ui/TranscriptItem';
import { useQM } from '@cdss-modules/design-system';

interface TranscriptListProps {
  transcript: TTranscript;
}

type TTranscript = RootObject;

export const TranscriptList: React.FC<TranscriptListProps> = ({
  transcript,
}) => {
  const { currentPosition } = useQM();
  const focusedIndexes = useMemo(() => {
    return (
      transcript?.transcripts?.reduce((acc: number[], curr, index: number) => {
        const rangeStart = timeStringToSeconds(curr.timerStart);
        const rangeEnd = timeStringToSeconds(curr.timerEnd);

        const isMatched =
          currentPosition >= rangeStart && currentPosition < rangeEnd;
        if (isMatched) {
          return [...acc, index];
        }
        return acc;
      }, []) || []
    );
  }, [currentPosition, transcript?.transcripts]);

  // 当focusedIndex发生变化时,滚动到该元素
  useEffect(() => {
    if (focusedIndexes?.length >= 0) {
      const element = document.getElementById(
        `transcript-${focusedIndexes?.[0]}`
      );
      element?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }, [focusedIndexes]);

  return (
    <section>
      {transcript.transcripts.map((transcript, index: number) => (
        <TranscriptItem
          id={`transcript-${index}`}
          key={`${transcript.timerStart}-${transcript.timerEnd}-${transcript.content}-${index}`}
          content={transcript.content}
          timerStart={transcript.timerStart}
          timerEnd={transcript.timerEnd}
          participant={transcript.participant}
          isFocus={focusedIndexes?.includes(index)}
        />
      ))}
    </section>
  );
};
