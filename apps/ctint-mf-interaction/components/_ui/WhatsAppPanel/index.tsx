import { getWhatsappMessages } from '../../../lib/api/index';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Loader, useRouteHandler } from '@cdss-modules/design-system';
import MessageContent from '@cdss-modules/design-system/components/_ui/MessagePage/MessageContent';
import { CDSSMessage } from '@cdss-modules/design-system/@types/Message';
import { useTranslation } from 'react-i18next';
import { Search, X } from 'lucide-react';
import SessionRecordPage from '@cdss-modules/design-system/components/_ui/PopupInline/SessiongRecordPage';
import { Pagination } from '@cdss-modules/design-system/components/_ui/Pagination';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
interface WhatsAppPanelBodyProps {
  conversationId: string;
}

const WhatsAppPanelBody: React.FC<
  PropsWithChildren & WhatsAppPanelBodyProps
> = ({ children, conversationId }) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();

  const [isLoading, setIsLoading] = useState(false);
  const [messages, setMessages] = useState<CDSSMessage[] | []>([]);
  const [showedMessages, setShowedMessages] = useState<CDSSMessage[] | []>([]);
  const [isPopupVisible, setIsPopupVisible] = useState(false); // 控制弹窗的状态
  const [highlightedMessageId, setHighlightedMessageId] = useState<
    string | null
  >(null); // 高亮状态

  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  const totalPages = Math.ceil(totalCount / perPage);
  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  useEffect(() => {
    setTotalCount(messages ? messages.length : 0);
    setCurrentPage(1);
  }, [messages]);

  useEffect(() => {
    const startIndex = (currentPage - 1) * perPage;
    const endIndex = startIndex + perPage;
    setShowedMessages(
      messages
        ? messages.slice(
            startIndex,
            endIndex <= messages.length ? endIndex : messages.length
          )
        : []
    );
  }, [currentPage, perPage, messages]);

  useEffect(() => {
    setIsLoading(true);
    getWhatsappMessages(conversationId, basePath)
      .then((res) => {
        setMessages(res.data.data);
      })
      .finally(() => {
        setIsLoading(false);
      });

    return () => {
      setMessages([]);
    };
  }, []);

  const handleLocateToChat = (messageId: string) => {
    setHighlightedMessageId(messageId);
    if (
      showedMessages.findIndex((message) => message.id === messageId) === -1
    ) {
      const index = messages.findIndex((message) => message.id === messageId);
      console.log(
        'handleLocateToChat ==> should locate to chat page: ',
        Math.floor(index / perPage) + 1
      );
      setCurrentPage(Math.floor(index / perPage) + 1);
    } else {
      // scroll到message的位置
      const messageElement = document.getElementById(messageId);
      if (messageElement) {
        messageElement.scrollIntoView({ behavior: 'smooth' });
      }
    }
    setIsPopupVisible(false);

    // Set a timeout to remove highlight after 5 seconds
    const timeout = setTimeout(() => {
      setHighlightedMessageId(null);
    }, 5000);

    // Clear timeout on component unmount
    return () => clearTimeout(timeout);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center w-full h-full">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full h-full bg-white rounded-lg relative">
      {/* Title */}
      <div className="flex justify-between items-center w-full border-b border-gray-200 p-1">
        <h2 className="text-t6">
          {t('ctint-mf-interaction.details.whatsapp')}
        </h2>
        {showedMessages.length > 0 && (
          <div
            className="shrink-0"
            onClick={() => {
              setIsPopupVisible(true); // 显示弹窗
            }}
          >
            <Search
              size={16}
              className="mx-1 cursor-pointer text-gray-400 hover:text-gray-600"
            />
          </div>
        )}
      </div>
      {/* Message History */}
      <div className="flex flex-col w-full h-full overflow-y-auto p-4 space-y-4 hide-scrollbar">
        <div className="flex-1">
          {showedMessages && showedMessages.length > 0 ? (
            showedMessages?.map((message) => {
              const isCustomer = message.direction === 'inbound';
              return (
                <div
                  key={message.id}
                  id={message.id}
                  className={`flex ${isCustomer ? 'justify-start' : 'justify-end'} ${highlightedMessageId === message.id ? 'bg-primary-200 flashing' : ''}`} // 应用闪烁类
                >
                  <div
                    className={`flex flex-col max-w-3xl rounded-lg ${
                      isCustomer ? 'bg-white-100' : 'bg-white-100'
                    }`}
                  >
                    <span
                      className={`flex text-gray-400 ${isCustomer ? 'justify-start' : 'justify-end'}`}
                    >
                      {message.userName}
                    </span>
                    <MessageContent
                      message={message}
                      isCustomer={isCustomer}
                    />
                  </div>
                </div>
              );
            })
          ) : (
            <div className="w-full h-full flex flex-col items-center justify-center">
              <IconEmptyRecords size="78" />
              <div className="text-grey-500">
                {t('ctint-mf-interaction.whatsapp.noSelectedInteraction')}
              </div>
            </div>
          )}
        </div>
      </div>
      {messages && messages.length > 0 && (
        <section className="flex-row shrink-0">
          <div>
            <Pagination
              current={currentPage}
              perPage={perPage}
              total={totalPages}
              totalCount={totalCount}
              onChange={(v) => setCurrentPage(v)}
              handleOnPrevious={() => handlePrevious()}
              handleOnNext={() => handleNext()}
              handlePerPageSetter={(p: number) => {
                const pageSize = Number(p);
                if (!isNaN(pageSize)) {
                  setPerPage(pageSize);
                }
                setCurrentPage(1);
              }}
            />
          </div>
        </section>
      )}

      {/* 自定义弹窗 - 直接在父组件内渲染，不使用Portal */}
      {isPopupVisible && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0 z-50 flex items-center justify-center"
          style={{
            marginTop: '32px', // 为标题留出空间
            background: 'rgba(255, 255, 255, 0.5)', // 半透明背景
          }}
        >
          <div
            className="bg-white rounded-lg flex flex-col overflow-hidden shadow-lg"
            style={{
              width: '70%',
              maxHeight: 'calc(100% - 40px)', // 减去标题高度和一些间距
            }}
          >
            {/* 弹窗标题栏 */}
            <div className="px-4 py-2 bg-primary-500 text-black font-bold flex justify-between items-center">
              <p>{t('ctint-mf-interaction.details.searchRecord')}</p>
              <button
                className="outline-none"
                onClick={() => setIsPopupVisible(false)}
              >
                <X size={20} />
              </button>
            </div>

            {/* 弹窗内容 */}
            <div className="flex-1 overflow-auto">
              <SessionRecordPage
                currentMessages={messages}
                onLocateToChat={handleLocateToChat}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

const queryClient = new QueryClient();
export const WhatsAppPanel: React.FC<
  PropsWithChildren & WhatsAppPanelBodyProps
> = ({ children, conversationId }) => {
  return (
    <QueryClientProvider client={queryClient}>
      <WhatsAppPanelBody conversationId={conversationId}>
        {children}
      </WhatsAppPanelBody>
    </QueryClientProvider>
  );
};
