/* eslint-disable react-hooks/exhaustive-deps */
import { cn } from '@cdss-modules/design-system/lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Clipboard<PERSON>heck, Refresh<PERSON>w, User<PERSON>heck } from 'lucide-react';
import { Button, useQM, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';

import { memo, useEffect, useMemo, useState } from 'react';
import { DUMMY_CS_EVALUATION_FORM } from '../../lib/dummy/qa';
import { EvaluationItem } from '@cdss-modules/design-system/components/_ui/Evaluation';
import EvaluationResult from '@cdss-modules/design-system/components/_ui/EvaluationResult';
import EvaluationFinalResult from '@cdss-modules/design-system/components/_ui/EvaluationResult/EvaluationFinalResult';
import { EvaluationResult as TEvaluationResult } from '@cdss-modules/design-system/components/_ui/EvaluationResult/EvaluationResultItem/types/evaluation';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import { QmEvaluationListItem, QmResultItem } from '../../types/autoqm';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  fireDownloadReport,
  fireGetEvaluationResult,
  fireGetStandScriptResult,
  fireUpdateEvaluationResult,
  fireUpdateNLPEvaluationResult,
} from '../../lib/api';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

type TQMPanelProps = {
  data: QmEvaluationListItem[];
  defaultOpenedQA?: string;
  onAssign?: () => void;
  onReassign?: () => void;
  currentRecordingId: string;
};

const QMPanel = memo(
  ({
    data,
    onAssign,
    onReassign,
    defaultOpenedQA,
    currentRecordingId,
  }: TQMPanelProps) => {
    const { t } = useTranslation();
    const EVALUATION_RESULT_OPTIONS = [
      {
        id: 'all',
        label: t('ctint-mf-interaction.qmListPanel.allResult'),
        value: 'All',
      },
      {
        id: 'pass',
        label: t('ctint-mf-interaction.qmListPanel.passed'),
        value: 'Passed',
      },
      {
        id: 'fail',
        label: t('ctint-mf-interaction.qmListPanel.failed'),
        value: 'Failed',
      },
    ];
    const { basePath } = useRouteHandler();
    const [editable, setEditable] = useState<boolean>(false);
    const queryClient = useQueryClient();
    const [evaluationResultFilter, setEvaluationResultFilter] =
      useState<string>('All');
    const {
      updateQMActiveSection,
      qaFormAnswers,
      openedQA,
      formId,
      currentPosition,
      openQA,
      closeQA,
    } = useQM();

    useEffect(() => {
      if (defaultOpenedQA) {
        openQA(defaultOpenedQA);
      }
    }, [defaultOpenedQA]);

    const { searchParams } = useRouteHandler();
    const id = searchParams?.get('id') || '';

    const openQADetails: QmEvaluationListItem | undefined = useMemo(() => {
      return data.find((qa) => qa.evaluationId === openedQA);
    }, [openedQA, data]);

    // query evaluation result
    const { data: evaluationResult, isPending: isLoadingEvaluationResult } =
      useQuery({
        queryKey: ['evaluationResult', openedQA],
        queryFn: async () => {
          const res = await fireGetEvaluationResult(
            openedQA,
            currentRecordingId,
            basePath
          );
          return res?.data ? res.data : { data: [] };
        },
        enabled: !!openedQA,
        refetchInterval: 1000 * 60 * 5, // 5 minutes
      });

    // query strand script result by formId
    const { data: standScriptResult, isPending: isLoadingStandScriptResult } =
      useQuery({
        queryKey: ['standScriptResult', openedQA],
        queryFn: async () => {
          const res = await fireGetStandScriptResult(formId, basePath);
          return res?.data ? res.data : { data: [] };
        },
        enabled: !!openedQA,
      });

    // 根据 stepId 分组
    const groupByStepId = (data: QmResultItem[]) => {
      if (!data || data.length === 0) {
        return [];
      }
      const grouped = data.reduce(
        (acc: { [key: string]: QmResultItem[] }, item) => {
          const stepId = item.standardScript.stepId;
          if (!acc[stepId]) {
            acc[stepId] = [];
          }
          acc[stepId].push(item);
          return acc;
        },
        {}
      );

      return Object.keys(grouped).map((stepId) => ({
        stepId, // 添加 stepId 信息
        items: grouped[stepId], // 分组后的数据
      }));
    };

    // query evaluation results convert to display format
    const evaluationResultDisplay = useMemo(() => {
      if (!evaluationResult?.data && evaluationResult?.data?.length == 0)
        return [];
      const originData = evaluationResult?.data as QmResultItem[];
      const groupedData = groupByStepId(originData ? originData : []);

      const showData = groupedData.map(
        (resultGroup: { stepId: string; items: QmResultItem[] }) => {
          const subSteps = resultGroup.items.map((subStep: QmResultItem) => {
            return {
              subStepId: subStep.standardScript.scenarioId.replace('_', '.'),
              subStepName: subStep.displayContent || '',
              nlpResultId: subStep.nplResultId,
              autoRate: subStep.finalRate,
              autoResult: subStep.finalResult,
              manualResult: subStep.manualResult,
              manualComment: subStep.comment,
              notes: subStep.systemNote,
              recordingTimeLocation: subStep.standardScript.createTime,
              manualUpdateTime: subStep.updateTime,
              timerStart: subStep.timerStart,
              timerEnd: subStep.timerEnd,
              manualDetailList: subStep.manualDetailList,
              importantWordResult: subStep.importantWordResult,
              metaDataResult: subStep.metaDataResult,
              similarityResult: subStep.similarityResult,
              othersResult: subStep.othersResult,
            };
          });
          return {
            stepId: resultGroup.stepId,
            stepName: `Step ${resultGroup.stepId}`,
            subStep: subSteps,
          };
        }
      );

      return showData as unknown as TEvaluationResult[];
    }, [evaluationResult]);

    /**
     * 根据 autoResult 过滤 subStep
     * @param steps 需要过滤的步骤
     * @param result 过滤条件
     * @returns 过滤后的步骤
     */
    function filterSubStepsByAutoResult(
      steps: TEvaluationResult[],
      result: string
    ): TEvaluationResult[] {
      return steps.map((step) => ({
        ...step,
        subStep: step.subStep.filter((sub) => {
          // 如果手动评估结果不为空，则根据手动评估结果过滤
          if (sub.manualResult && sub.manualResult === result) {
            return true;
          }
          // 否则根据自动评估结果过滤
          return sub.autoResult === result;
        }),
      }));
    }

    // filter evaluation result
    const filterEvaluationResult = useMemo(() => {
      if (evaluationResultFilter === 'All') {
        return evaluationResultDisplay;
      }
      return filterSubStepsByAutoResult(
        evaluationResultDisplay,
        evaluationResultFilter
      );
    }, [evaluationResultDisplay, evaluationResultFilter]);

    // update result
    const updateNLPResult = useMutation({
      mutationFn: async (data: {
        evaluationId: string;
        nlpResultId: string;
        manualResult: string;
        comment: string;
      }) => {
        const res = await fireUpdateNLPEvaluationResult(data, basePath);
        return res.data;
      },
      onSuccess: () => {
        // update EvaluationResult
        queryClient.invalidateQueries({
          queryKey: ['evaluationResult', openedQA],
        });
      },
      onError: () => {
        // TODO: handle error
      },
    });

    const updateEvaluationResult = useMutation({
      mutationFn: async (data: {
        evaluationId: string;
        manualResult: string;
        comment: string;
      }) => {
        const res = await fireUpdateEvaluationResult(data, basePath);
        return res.data;
      },
      onSuccess: () => {
        // update EvaluationResult
        queryClient.invalidateQueries({
          queryKey: ['qmEvaluationList', currentRecordingId],
        });
      },
      onError: () => {
        // TODO: handle error
      },
    });

    // download report
    const onDownloadReport = () => {
      fireDownloadReport(openedQA || '', currentRecordingId, basePath);
    };

    useEffect(() => {
      if (openedQA) {
        setEditable(openQADetails?.status === 'completed');
        updateQMActiveSection(DUMMY_CS_EVALUATION_FORM?.questions?.[0]?.id);
      } else {
        updateQMActiveSection('');
      }
    }, [openedQA]);

    // QM Details Panel
    if (openedQA && openQADetails) {
      const getTranslatedResult = (result: string) => {
        const resultMap = {
          pass: t('ctint-mf-interaction.qmListPanel.passed'),
          passed: t('ctint-mf-interaction.qmListPanel.passed'),
          fail: t('ctint-mf-interaction.qmListPanel.failed'),
          failed: t('ctint-mf-interaction.qmListPanel.failed'),
          'to be reviewed': t('ctint-mf-interaction.qmListPanel.toBeReviewed'),
          'not evaluated': t('ctint-mf-interaction.qmListPanel.notEvaluated'),
        } as { [key: string]: string };

        return resultMap[result] || result;
      };

      return (
        <div className="flex flex-col h-full">
          <div className="bg-white z-10">
            <div className="flex items-center gap-4 py-2 px-4 border-b border-grey-200">
              <div className="w-full flex items-center justify-between">
                {/* Header: evaluation name and status */}
                <div className="flex flex-1 items-center gap-x-4">
                  <Button
                    onClick={() => {
                      closeQA();
                      setEvaluationResultFilter('All');
                    }}
                    variant="back"
                    beforeIcon={<Icon name="back" />}
                    bodyClassName="p-1 min-w-0"
                  />
                  <div className="flex items-center gap-x-2">
                    <UserCheck size={28} />
                    <span>
                      {openQADetails.formName +
                        ' (' +
                        openQADetails.formVersion +
                        ')'}
                    </span>
                  </div>
                  {/* Status */}
                  <div className="flex items-center gap-x-2">
                    <span
                      className={cn(
                        'w-2 h-2 rounded-full',
                        openQADetails.manualResult === 'Passed'
                          ? 'bg-status-success'
                          : openQADetails.finalResult === 'Passed'
                            ? 'bg-status-success'
                            : openQADetails.manualResult?.toLowerCase() ===
                                'To be reviewed'
                              ? 'bg-primary-500'
                              : openQADetails.finalResult?.toLowerCase() ===
                                  'to be reviewed'
                                ? 'bg-primary-500'
                                : 'bg-status-danger'
                      )}
                    ></span>
                    <span>
                      {openQADetails.manualResult &&
                      openQADetails.manualResult !== ''
                        ? getTranslatedResult(
                            openQADetails.manualResult.toLowerCase()
                          )
                        : getTranslatedResult(
                            openQADetails.finalResult.toLowerCase()
                          )}
                    </span>
                  </div>
                </div>

                <button
                  className="text-primary mx-2 my-1 px-2 py-0 border border-primary rounded-md"
                  onClick={onReassign}
                >
                  {t('ctint-mf-interaction.details.reGenerate')}
                </button>

                {/* QM Filter */}
                <Select
                  placeholder="All result"
                  triggerClassName="px-2 py-0"
                  options={EVALUATION_RESULT_OPTIONS}
                  mode="single"
                  isPagination={false}
                  onChange={(value) => {
                    setEvaluationResultFilter(value);
                  }}
                  value={evaluationResultFilter}
                />

                {/* Download report Button */}
                <button
                  className="text-primary mx-2 my-1 px-2 py-0 border border-primary rounded-md"
                  onClick={onDownloadReport}
                >
                  {t('ctint-mf-interaction.details.downloadReport')}
                </button>
                {/* dayone hidden Publish Button */}
                {/* <div
                  className="ml-4 p-2 flex-none bg-[#E0E0E0] rounded-full hover:bg-[#FFAC4A] cursor-pointer"
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    // TODO: add publish logic
                  }}
                >
                  <IconQMPublish
                    size="20"
                    alt="qm publish icon"
                  />
                </div> */}
              </div>
            </div>
          </div>
          <div className="px-4 py-4 flex flex-col gap-y-2">
            {/* <QMQuestionArea editable={editable} /> */}
            {filterEvaluationResult && filterEvaluationResult.length > 0 && (
              <>
                {filterEvaluationResult.map((item: TEvaluationResult) => {
                  return (
                    <EvaluationResult
                      key={item.stepId}
                      evaluationId={openedQA}
                      currentPosition={currentPosition}
                      onItemSubmit={(comment, manualResult, nlpResultId) => {
                        updateNLPResult.mutate({
                          evaluationId: openedQA,
                          nlpResultId,
                          manualResult,
                          comment,
                        });
                      }}
                      data={item}
                    />
                  );
                })}
                <div className="px-4">
                  <EvaluationFinalResult
                    evaluationId={openedQA}
                    result={
                      openQADetails.manualResult || openQADetails.finalResult
                    }
                    manualDetailList={openQADetails.manualDetailList ?? []}
                    onSubmit={(comment, manualResult) => {
                      updateEvaluationResult.mutate({
                        evaluationId: openedQA,
                        manualResult,
                        comment,
                      });
                    }}
                  />
                </div>
              </>
            )}
          </div>
        </div>
      );
    }

    // QM List Panel
    return (
      <div className={cn('relative')}>
        <div className="flex items-center gap-4 py-2 px-4 border-b border-grey-200 sticky top-0 bg-white z-10">
          <div className="flex items-center gap-x-6">
            <div className="flex items-center gap-x-2">
              <ClipboardCheck size={28} />
              <span>
                {t('ctint-mf-interaction.qmListPanel.evaluated')}:{' '}
                {/* {data.filter((qa) => qa.status === 'published').length} */}
                {data.filter((qa) => qa.status === 'completed').length}/
                {data.length}
              </span>
            </div>
            <div className="flex items-center gap-x-2">
              <span className="w-2 h-2 bg-status-success rounded-full"></span>
              <span>
                {t('ctint-mf-interaction.qmListPanel.passed')}{' '}
                {
                  data.filter((qa) => qa.finalResult.toLowerCase() === 'passed')
                    .length
                }
              </span>
              <span className="w-2 h-2 bg-status-danger rounded-full"></span>
              <span>
                {t('ctint-mf-interaction.qmListPanel.failed')}{' '}
                {
                  data.filter((qa) => qa.finalResult.toLowerCase() === 'failed')
                    .length
                }
              </span>
            </div>
          </div>
          <Button
            onClick={onAssign}
            variant="blank"
            className="self-end ml-auto"
            beforeIcon={
              <Icon
                name="plus"
                size={14}
              />
            }
          >
            <span>{t('ctint-mf-interaction.qmListPanel.generateNew')}</span>
          </Button>
          <Button
            className="shrink-0"
            onClick={() => {
              console.log('currentRecordingId', currentRecordingId);

              // update stand script result
              queryClient.invalidateQueries({
                queryKey: ['transcript', id],
              });
              // update EvaluationResult
              queryClient.invalidateQueries({
                queryKey: ['qmEvaluationList', currentRecordingId],
              });
            }}
            variant="blank"
          >
            <RefreshCw size={14} />
            <span className="active:text-other-orange">
              {t('ctint-mf-interaction.qmListPanel.refresh')}
            </span>
          </Button>
        </div>
        {data.map((qa) => {
          return (
            <EvaluationItem
              key={qa.evaluationId}
              title={qa.formName + ' (' + qa.formVersion + ')'}
              evaluator={qa.evaluatorName !== '' ? qa.evaluatorName : 'N/A'}
              releaseTime={qa.releasedTime}
              status={qa.status}
              result={
                qa.manualResult && qa.manualResult !== ''
                  ? qa.manualResult
                  : qa.finalResult
              }
              isAuto={qa.processBy === 'system'}
              showButton={false} // 不显示按钮, AutoQM 当前不支持手动发布评估和删除评估
              onItemClick={() => {
                openQA(qa.evaluationId, qa.formId);
              }}
            />
          );
        })}
      </div>
    );
  }
);

QMPanel.displayName = 'QMPanel';

export default QMPanel;
