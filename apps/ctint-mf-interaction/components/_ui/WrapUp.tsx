import { useState } from 'react';
import { Wrapup } from '../../types/wrapups';
import IconTriangleDown from '@cdss-modules/design-system/components/_ui/Icon/IconTriangleDown';

interface WrapUpProps {
  wrapup: Wrapup[];
}

const WrapUp: React.FC<WrapUpProps> = ({ wrapup }) => {
  const [showRemarkIndex, setShowRemarkIndex] = useState<number>(-1);

  const onFocus = (isExtend: boolean): string => {
    return isExtend ? 'font-bold' : '';
  };

  return (
    <>
      {/** Wrap up form */}
      <section className="my-2 border border-gray-300">
        <div className="flex gap-1 p-2 divide-x divide-geray-300">
          {wrapup
            .filter(
              (item: Wrapup) => item.wrapUpName !== '' && item.wrapUpCode !== ''
            )
            .map((item: Wrapup, index: number) => {
              return (
                <button
                  key={`${item.wrapUpName} + ${index}`}
                  onClick={() => {
                    if (showRemarkIndex === index) {
                      setShowRemarkIndex(-1);
                      return;
                    }
                    setShowRemarkIndex(index);
                  }}
                  className={`font-[#636363] flex flex-wrap items-center px-1 font-b ${onFocus(showRemarkIndex == index)}`}
                >
                  {item.wrapUpName}
                  {item.remark != null && item.remark.length > 0 && (
                    <IconTriangleDown
                      alt="wrap up name"
                      size="16"
                      className={showRemarkIndex == index ? 'rotate-180' : ''}
                    />
                  )}
                </button>
              );
            })}
        </div>
        {showRemarkIndex >= 0 && (
          <div className="px-3 border-t border-gray-300">
            {wrapup[showRemarkIndex].remark}
          </div>
        )}
      </section>
      {/** 整体评价 */}
      <section>
        {wrapup
          .filter(
            (item: Wrapup) =>
              item.wrapUpName === '' &&
              item.wrapUpCode === '' &&
              item.remark &&
              item.remark.length > 0
          )
          .map((item: Wrapup, index: number) => {
            return <div key={`${item.remark} + ${index}`}>{item.remark}</div>;
          })}
      </section>
    </>
  );
};

export default WrapUp;
