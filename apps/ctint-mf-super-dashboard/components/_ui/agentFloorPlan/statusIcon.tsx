import {
  Phone,
  MessageSquare,
  FileText,
  Coffee,
  X,
  Mail,
  PhoneOff,
  MailPlus,
  Voicemail,
  PhoneCall,
  Store,
  MonitorPlay,
} from 'lucide-react';
import { FC } from 'react';

type StatusType =
  | 'available'
  | 'complaint'
  | 'document'
  | 'meal'
  | 'away'
  | 'email'
  | 'busy'
  | 'outgoing'
  | 'voice-mail'
  | 'outbound'
  | 'counter'
  | 'after-call';

interface StatusIconProps {
  type: StatusType;
  className?: string;
}

export const StatusIcon: FC<StatusIconProps> = ({ type, className }) => {
  const icons = {
    available: Phone,
    complaint: MessageSquare,
    document: FileText,
    meal: Coffee,
    away: X,
    email: Mail,
    busy: PhoneOff,
    outgoing: MailPlus,
    'voice-mail': Voicemail,
    outbound: PhoneCall,
    counter: Store,
    'after-call': MonitorPlay,
  };

  const Icon = icons[type] || Phone;

  return <Icon className={className} />;
};
