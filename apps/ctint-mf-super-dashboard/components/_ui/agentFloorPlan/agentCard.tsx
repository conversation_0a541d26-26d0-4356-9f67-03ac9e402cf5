import { apiConfig } from '../../../lib/api/config';

import {
  Phone,
  Frown,
  Minus,
  X,
  Coffee,
  Presentation,
  GraduationCap,
  PhoneOff,
  LucideProps,
} from 'lucide-react';
import Time from './time';
import {
  ForwardRefExoticComponent,
  memo,
  RefAttributes,
  useEffect,
} from 'react';
import { useRouteHandler } from '@cdss-modules/design-system';

interface AgentCardProps {
  name: string;
  avatar?: string; // 可选，如果没有则显示首字母
  callsReceived: number;
  callsMade: number;
  type: string;
  subType: string;
  time: number;
  activity: string;
}
interface StatusConfigType {
  [key: string]: {
    icon: ForwardRefExoticComponent<
      Omit<LucideProps, 'ref'> & RefAttributes<SVGSVGElement>
    >;
    color: string;
    textColor: string;
    label: string;
  };
}
const AgentCard = memo(
  ({
    name,
    avatar,
    callsReceived,
    callsMade,
    type,
    subType,
    time,
    activity,
  }: AgentCardProps) => {
    const { basePath } = useRouteHandler();

    // console.log('AgentCard');
    // 状态配置
    const statusConfig: StatusConfigType = {
      AVAILABLE: {
        icon: Phone,
        color: 'bg-green-500',
        textColor: 'text-green-500',
        label: 'Available',
      },
      ON_QUEUE: {
        icon: Phone,
        color: 'bg-green-500',
        textColor: 'text-green-500',
        label: 'ON QUEUE',
      },
      // complaint: {
      //   icon: Frown,
      //   color: 'bg-green-500',
      //   textColor: 'text-green-500',
      //   label: 'Complaint',
      // },
      BUSY: {
        icon: Minus,
        color: 'bg-red-500',
        textColor: 'text-red-500',
        label: 'Busy',
      },
      AWAY: {
        icon: X,
        color: 'bg-yellow-500',
        textColor: 'text-yellow-500',
        label: 'Away',
      },
      BREAK: {
        icon: X,
        color: 'bg-yellow-500',
        textColor: 'text-yellow-500',
        label: 'Break',
      },
      MEAL: {
        icon: Coffee,
        color: 'bg-orange-500',
        textColor: 'text-orange-500',
        label: 'Meal',
      },
      MEETING: {
        icon: Presentation,
        color: 'bg-red-500',
        textColor: 'text-red-500',
        label: 'Meeting',
      },
      TRAINING: {
        icon: GraduationCap,
        color: 'bg-blue-500',
        textColor: 'text-blue-500',
        label: 'Training',
      },
      Offline: {
        icon: PhoneOff,
        color: 'bg-gray-500',
        textColor: 'text-gray-500',
        label: 'Offline',
      },
      OFFLINE: {
        icon: PhoneOff,
        color: 'bg-gray-500',
        textColor: 'text-gray-500',
        label: 'Offline',
      },
    };

    const StatusIcon = statusConfig[type]?.icon;
    useEffect(() => {
      // console.log(type);
      // console.log(statusConfig[type]);
    }, []);

    return (
      <div className="bg-white rounded-lg shadow-sm lg:p-2 lg:pt-2">
        {/* 头像和名字区域  sm:w-12 sm:h-12 md:w-14 md:h-14 lg:w-16 lg:h-16*/}
        <div className="flex items-start gap-1 sm:gap-2 md:gap-3">
          {avatar ? (
            <img
              src={basePath + apiConfig.paths.getImage + avatar}
              className="w-10 h-10 lg:w-16 lg:h-16 rounded-md object-cover"
            />
          ) : (
            <div className="w-6 h-6 lg:w-16 lg:h-16 lg:text-sm rounded-md bg-gray-200 flex items-center justify-center text-gray-600 overflow-clip">
              {name}
            </div>
          )}
          <div className="flex-1 min-w-0">
            <div className="font-medium text-left lg:text-l overflow-clip truncate">
              {name}
            </div>
            <div className="flex gap-2 text-lg bg-gray-200 p-1 pr-1 pl-2 sm:pl-3 rounded-md font-bold">
              <div className="">{callsReceived}</div>
              <div className="text-red-500 flex-1">{callsMade}</div>
            </div>
          </div>
        </div>

        {/* 状态区域 */}
        <div className="mt-2 flex items-center gap-2 sm:gap-3">
          <div
            className={`w-8 h-8 lg:w-14 lg:h-14 rounded-full ${
              statusConfig[type]?.color
            } flex items-center justify-center p-1.5 sm:p-2 md:p-2.5`}
          >
            <StatusIcon className="w-4 h-4 lg:w-6 lg:h-6 text-white" />
          </div>
          <div className="ml-2">
            <Time
              initialTime={Number(time)}
              status={type}
            />
            {/* <div className="text-base sm:text-lg md:text-xl lg:text-2xl">
            {formatDuration(Number(time))}
          </div> */}
            <div
              className={`text-sm sm:text-base md:text-lg lg:text-xl ${
                statusConfig[type]?.textColor
              }`}
            >
              {subType}
            </div>
          </div>
        </div>
        <div
          className={
            'text-sm sm:text-base md:text-lg lg:text-xl border-t-gray-300 border-t-2 mt-2  text-center'
          }
        >
          {activity}
        </div>
      </div>
    );
  }
);
AgentCard.displayName = 'AgentCard';

export default AgentCard;
