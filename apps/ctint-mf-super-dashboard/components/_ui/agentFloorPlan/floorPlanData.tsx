export interface AgentStatistics {
  agentId: string;
  callsReceived: number;
  callsMade: number;
  type: string;
  subType: string;
  activity: string;
  duration: number;
  label: string;
  color: string;
}

export interface AgentBaseInfo {
  id: string;
  isActive: boolean;
  name: string;
  avatar: string;
  position: number;
}

export interface AgentFullInfo extends AgentBaseInfo, AgentStatistics {
  // Additional properties can be defined here
}

export const agentBaseInfo: AgentBaseInfo = {
  id: '1',
  isActive: false,
  name: '',
  avatar: '',
  position: 0,
};

export const agentStatistics: AgentStatistics = {
  callsReceived: 0,
  callsMade: 1,
  type: 'Offline',
  subType: 'Offline',
  activity: 'OFF_LINE',
  duration: 0,
  label: 'offline',
  color: 'green',
  agentId: '1',
};

export const floorPlansInitData: FloorPlan[] = [
  {
    id: 'floor-1',
    name: 'Main Office Floor',
    rows: 5,
    cols: 7,
    agents: [],
  },
];

// 类型定义
export type Status = {
  type: string;
  duration: string;
  label: string;
  color: string;
};

export type FloorPlan = {
  id: string;
  name: string;
  rows: number;
  cols: number;
  agents: AgentFullInfo[];
};

export type FloorPlansDataInterface = {
  floorPlans: FloorPlan[];
};
