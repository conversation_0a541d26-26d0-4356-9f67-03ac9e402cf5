import { FC, memo, useEffect, useState } from 'react';
import AgentCard from './agentCard';
import {
  Coffee,
  Frown,
  GraduationCap,
  MessageSquare,
  Minus,
  Phone,
  Presentation,
  X,
} from 'lucide-react';
import { AgentFullInfo, FloorPlan } from './floorPlanData';
interface AgentFloorPlanProps {
  page: number;
  floorPlan: FloorPlan;
  rowSize: number;
}
const AgentFloorPlan: FC<AgentFloorPlanProps> = ({
  page,
  floorPlan,
  rowSize,
}: AgentFloorPlanProps) => {
  // console.log('AgentFloorPlan');
  return (
    <div
      className={`grid gap-4 p-3 bg-gray-200`}
      style={{ gridTemplateColumns: `repeat(${rowSize}, minmax(0, 1fr))` }}
    >
      {floorPlan.agents.map(
        (item, index) =>
          item.isActive &&
          (item.id.includes('blank') ? (
            <div
              key={index}
              className="bg-gray-100 rounded-lg"
            ></div>
          ) : (
            <AgentCard
              key={index}
              name={item.name}
              callsReceived={item.callsReceived}
              callsMade={item.callsMade}
              type={item.type}
              subType={item.subType}
              activity={item.activity}
              time={item.duration}
              avatar={item.avatar}
            />
          ))
      )}
    </div>
  );
};

export default AgentFloorPlan;
