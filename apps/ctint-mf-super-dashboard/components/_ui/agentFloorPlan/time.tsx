import { useEffect, useState, memo, useRef } from 'react';

const Time = ({
  initialTime = 0,
  status = '',
  isWaitingCall = false,
  waitingCall = 0,
}) => {
  const [time, setTime] = useState(Math.floor(initialTime / 1000));
  const intervalRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const prevWaitingCallRef = useRef(waitingCall); // 用来存储上一次的 waitingCall 值

  useEffect(() => {
    console.log('useEffect initialTime', initialTime);
    if (isWaitingCall && waitingCall == 0) {
      console.log('return false');
      return;
    }
    intervalRef.current = startInterval();
    // console.log('interval started1');
    // console.log('isWaitingCall && waitingCall', isWaitingCall && waitingCall);

    // 清理函数
    return () => {
      // 添加非空检查
      if (intervalRef.current !== null) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!isWaitingCall) {
      if (time != 0 && time != Math.floor(initialTime / 1000)) {
        setTime(0);
        if (intervalRef.current !== null) {
          clearInterval(intervalRef.current);
        }
        intervalRef.current = startInterval();
        // console.log('interval started2');
      }
    }
  }, [status]);

  useEffect(() => {
    if (isWaitingCall) {
      const prevWaitingCall = prevWaitingCallRef.current;
      prevWaitingCallRef.current = waitingCall;
      // console.log('prevWaitingCall', prevWaitingCall);
      // console.log('waitingCall', waitingCall);
      if (intervalRef.current) {
        // console.log('interval cleared');
        clearInterval(intervalRef.current);
      }
      if (isWaitingCall && waitingCall == 0) {
        setTime(0);
      }
      if (waitingCall != 0) {
        setTime(Math.floor(initialTime / 1000));
        intervalRef.current = startInterval();
        console.log('interval started3');
      }
    }
  }, [waitingCall]);

  const startInterval = () => {
    const interval = setInterval(() => {
      setTime((prevTime) => prevTime + 1);
    }, 1000);
    return interval;
  };
  //   console.log('Time component rendered', initialTime); // 只有 Time 组件会打印

  const formatDuration = (seconds: number) => {
    if (seconds === 0) return '00:00';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    let result = '';

    // 只有当天数大于0时才显示天数部分
    if (days > 0) {
      result += `${days}d `;
    }

    // 当有天数或小时大于0时才显示小时部分
    if (days > 0 || hours > 0) {
      result += `${hours.toString().padStart(2, '0')}:`;
    }

    // 分和秒始终显示，且保持两位数格式
    result += `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;

    return result;
  };
  return (
    <div className="text-base sm:text-lg md:text-xl lg:text-2xl">
      {formatDuration(time)}
      <span style={{ display: 'none' }}>{status}</span>
    </div>
  );
};

export default Time;
