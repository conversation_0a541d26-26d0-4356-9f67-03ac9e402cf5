import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  use<PERSON>out<PERSON><PERSON><PERSON><PERSON>,
} from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { InteractionProvider } from '@cdss-modules/design-system/context/InteractionContext';
import SupervisorDashboard from '../../_ui/SupervisorDashboard';
export function Main() {
  const { toPath } = useRouteHandler();
  const queryClient = new QueryClient();
  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-template'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-template.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      {/* <InteractionProvider> */}
      {/* <div className="custHeader">hello world</div> */}
      <SupervisorDashboard id={0} />
      {/* <TemplateDemo
            testId="home"
            titleI18n="ctint-mf-super-dashboard.templateHome.title"
            descI18n="ctint-mf-super-dashboard.templateHome.desc"
            btnLabelI18n="ctint-mf-super-dashboard.templateHome.btnLabel"
            onClickButton={() => toPath('/detail')}
          /> */}
      {/* </InteractionProvider> */}
    </AuthChecker>
  );
}

export default Main;
