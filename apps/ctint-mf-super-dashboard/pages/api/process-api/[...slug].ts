import { NextApiRequest, NextApiResponse } from 'next';
import httpProxy from 'http-proxy';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';

export const config = {
  api: {
    // Enable `externalResolver` option in Next.js
    externalResolver: true,
    bodyParser: false,
  },
};
const globalConfig = loadGlobalConfig('ctint-mf-super-dashboard') as any;

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // fix vulnerable issue: https://www.threads.net/@yuhaooo0x/post/DHit21AhKyU?xmt=AQGzTBVJjaZqgw3wSwQe38CoOe8Euj-WvvcCoVWwXzw4PQ
  if ('x-middleware-subrequest' in req.headers) {
    return new Response('Unauthorized', { status: 401 });
  }

  try {
    const services = globalConfig?.services ?? {};
    const servicesRewrites = Object.keys(services).map((key) => {
      const serviceHost = services?.[key]?.host ?? '';
      const serviceBasePath = services?.[key]?.basepath ?? '';
      return {
        source: `/api/process-api/${key}/`,
        destination: `${serviceHost}${serviceBasePath}`,
      };
    });
    const targetUrl = req?.url || '';
    const pathMatched = servicesRewrites.find((rewrite) =>
      targetUrl.startsWith(rewrite.source)
    );
    if (pathMatched) {
      let slugs = targetUrl.replace(pathMatched.source, '');
      if (slugs && !slugs.startsWith('/')) slugs = '/' + slugs;
      const target = pathMatched.destination + slugs;
      // res.status(404).json({
      //   isSuccess: false,
      //   headers: req.headers,
      //   error: 'Cannot find the matched service for the request',
      // });
      console.log('############# BFF Request ##################');
      console.log('BFF Request URL', target);
      console.log('BFF Request METHOD', req.method);
      console.log('BFF Request HEADERS', req.headers);
      console.log('BFF Request PAYLOAD', req.body);
      console.log('############# END BFF Request ##################');
      new Promise((resolve, reject) => {
        const proxy: httpProxy = httpProxy.createProxy();
        proxy.once('proxyRes', (proxyRes) => {
          console.log('proxyRes', proxyRes);
        });
        proxy.once('proxyRes', resolve).once('error', reject).web(req, res, {
          ignorePath: true,
          target,
        });
      });
    } else {
      res.status(404).json({
        isSuccess: false,
        error: 'Cannot find the matched service for the request',
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({
      isSuccess: false,
      error: 'Failed make a process-api request',
    });
  }
}
