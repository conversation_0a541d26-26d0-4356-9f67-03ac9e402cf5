import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

export const axiosInstance = axios.create({
  timeout: 50000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-super-dashboard',
    previousId: 'ctint-bff-cdss',
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'ofROc0Va1i74x3CKxPJ-et8g9jwo08wFkRgCzCcIYDwdAEmJMCIh-HDn9Sgw_U17WxTvtwnngeOksDWN8Sbatw'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    // console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export const GetTenantConfig = (basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.getTenantConfig}?key=adminSuperDashboad`
  );

export const UpdateTenantConfig = (basePath = '', data: any) =>
  axiosInstance.post(`${basePath}${apiConfig.paths.getTenantConfig}`, data);

export const GetQueues = (basePath = '', filterType = '', userId = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getQueues}`, {
    params: {
      filterType: filterType,
      userId: userId,
    },
  });
export const GetImage = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getImage}`, {
    responseType: 'blob',
  });

export const GetAgentStatusList = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getAgentStatusList}`);

export const GetQueuesStatisticsInitData = (basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.getQueuesStatisticsInitData}`
  );

export const GetActivityStatisticsInitData = (basePath = '') =>
  axiosInstance.get(
    `${basePath}${apiConfig.paths.getActivityStatisticsInitData}`
  );

export const GetAgentStatisticsInitData = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.getAgentStatisticsInitData}`);

export default axiosInstance;
