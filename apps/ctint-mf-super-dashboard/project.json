{"name": "ctint-mf-super-dashboard", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-super-dashboard", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-super-dashboard", "outputPath": "dist/apps/ctint-mf-super-dashboard"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-super-dashboard:build", "dev": true, "port": 4202, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-super-dashboard:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-super-dashboard:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-super-dashboard:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-super-dashboard"], "options": {"jestConfig": "apps/ctint-mf-super-dashboard/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-super-dashboard/**/*.{ts,tsx,js,jsx}"]}}}}