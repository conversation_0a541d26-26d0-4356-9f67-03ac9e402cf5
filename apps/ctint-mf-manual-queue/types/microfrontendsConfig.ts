// eslint-disable-next-line unused-imports/no-unused-imports

export interface microfrontends {
  'ctint-mf-cdss': CtintMF;
  'ctint-mf-cpp': CtintMF;
  'ctint-mf-user-admin': CtintMFUserAdmin;
  'ctint-mf-tts': CtintMF;
  'ctint-mf-wap': CtintMF;
  'ctint-mf-info': CtintMF;
  'ctint-mf-manual-queue': CtintMFManualQueue;
}

export interface CtintMF {
  auditTabNames: never[];
  host: string;
  basepath: null | string;
}

export interface CtintMFManualQueue {
  host: string;
  basepath: string;
  'manual-tab-names': ManualQueueTabName[];
  'email-tab-names': ManualQueueTabName[];
  'message-tab-names': ManualQueueTabName[];
  spaceId: SpaceId;
  permission: ManualQueuePermission;
  options?: {
    caseStatus?: OptionItem[];
    referenceStatus?: OptionItem[];
  };
}

export interface SpaceId {
  emailTemplate: string;
}

export interface ManualQueuePermission {
  'email-assign-to': string;
  'callback-assign-to': string;
  'voicemail-assign-to': string;
  'message-assign-to': string;
}

export interface ManualQueueTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}

export interface OptionItem {
  id: string;
  value: string;
  label: string;
}

export interface CtintMFUserAdmin {
  host: string;
  basepath: string;
  'user-tab-names': UserTabName[];
  'audit-tab-names': UserAuditTabName[];
}

export interface UserTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  require: boolean;
}
export interface UserAuditTabName {
  labelEn: string;
  labelCh: string;
  value: string;
  filterType: string;
  active: boolean;
  readOnly: boolean;
  sort: boolean;
}
export interface ShowColumn {
  key: string;
  value: string;
}

export interface TAdminRolesDataResp {
  data: TAdminRole[];
  error: string;
  isSuccess: boolean;
}

export interface TAdminRole {
  name: string;
  code: string;
  state: string;
  description: string;
  platform: string;
  tenant: string;
  createTime: Date;
  updateTime: Date;
  createBy: string;
  updateBy: string;
}

export interface TAdminUserGroupDataResp {
  data: TAdminUserGroup[];
  error: string;
  isSuccess: boolean;
}

export interface TAdminUserGroup {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  state: string;
  tenant: string;
  platform: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface CombinedRoleNGroupData {
  roleNames: TAdminRole[];
  groupNames: TAdminUserGroup[];
}

export interface userGroup {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  state: string;
  tenant: string;
  platform: string;
  createTime: Date;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface userRole {
  name: string;
  code: string;
  platform: string;
  tenant: string;
  state: string;
  description: string;
  createTime: Date;
  updateTime: string;
  createBy: string;
  updateBy: string;
}

export interface PrimaryContactInfo {
  address: string;
  mediaType: string;
  type: string;
}

export type ManualQueueData = {
  id?: string;
  name?: string;
  email?: string;
  divisionId?: string;
  divisionName?: string;
  state?: string;
  description?: string;
  type?: string;
  platform?: string;
  tenant?: string;
  organization?: string;
  primaryContactInfo?: PrimaryContactInfo[];
  createdBy?: string;
  createdAt?: string;
  updatedBy?: string;
  updatedAt?: string;
  isAllowEdit?: boolean;
  roles?: userRole[];
  roleNames?: string;
  groups?: userGroup[];
  groupNames?: string;
  conversationId?: string;
  conversationEnd?: string;
  conversationDuration?: string;
  from?: string;
  mp3URL?: string;
  wrapupRequired?: boolean;
  selfQueueId?: string;
  selfParticipantId?: string;
  queueName?: string;
  caseId?: string;
  subject?: string;
  isJunkmail?: string;
  queueId?: string;
  userId?: string;
  currentQueueId?: string;
  direction?: string;
};

export type ManualQueueDataResp = {
  data: {
    list: ManualQueueData[];
    total: number;
    page_size: number;
    current_page: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
    next_page: number;
    prev_page: number;
    from: number;
    to: number;
  };
  isSuccess: boolean;
};

export interface UserSearchReq {
  keyword?: string;
  queueId?: string;
  filterType?: string;
}

export interface AssignReq {
  isAssignToMe?: boolean;
  conversationId?: string;
  agentId?: string;
  type?: string;
  currentAssignUserId?: string;
  queueId?: string;
}

export interface ConversationCallBack {
  data: ManualQueueData;
  isSuccess: boolean;
}

export interface TransferReq {
  transferType?: string;
  conversationId?: string;
  id?: string;
  selfQueueId?: string;
  selfParticipantId?: string;
  state?: string;
  transferParticipantId?: string;
}

export type VoiceMailInfo = {
  conversationId?: string;
  from?: string;
  mp3URL?: string;
  selfParticipantId?: string;
  selfParticipantState?: string;
  selfQueueId?: string;
  voiceMailId?: string;
  selfParticipantStartTime?: string;
  selfParticipantEndTime?: string;
  selfParticipantWrapupRequired?: boolean;
  selfParticipantWrapupPrompt?: string;
  type?: string;
  transferParticipantId?: string;
  transferAgentName?: string;
  callbackNumber?: string;
};

export type User = {
  id: string;
  name: string;
  phone?: string;
  presence?: {
    presenceDefinition?: {
      systemPresence: string;
    };
  };
  primaryContactInfo?: PrimaryContactInfo[];
  presenceStatus?: string;
};
export interface PrimaryContactInfo {
  address: string;
  mediaType: string;
  type: string;
}

export interface QueueUser {
  id: string;
  name: string;
  divisionId: string;
  divisionName: string;
  email: string;
  state: string;
  joined: boolean;
  ringNumber: number;
  memberBy: string;
  routingStatus: string;
  presenceStatus: string;
  outOfOffice: boolean;
  acdAutoAnswer: boolean;
}

// 队列用户项类型
export interface QueueUserItem {
  queueId: string;
  items: QueueUser[];
}

// 队列用户响应类型
export interface QueueUserResponse {
  data: QueueUserItem[];
  error: string;
  isSuccess: boolean;
}

//email
export interface SendEmailRequest {
  gcConversationId?: string;
  outboundType?: string;
  to?: string[];
  cc?: string[];
  bcc?: string[];
  content?: string;
  subject?: string; //主题
  attachmentList?: EmailAttachment[];
  selfParticipantId?: string;
}

export interface EmailAttachment {
  id: string;
  emailId: string;
  attachmentName: string;
  attachmentId: string;
  contentId: string;
  fileType: string;
  fileSize: number;
  isInLine: boolean;
  createTime: string;
  updateTime: string;
  createBy: string | null;
  updateBy: string | null;
  platform: string | null;
  tenant: string | null;
  storageFileDir: string;
  storageFileName: string;
  storageShareName: string;
  url: string;
  success: boolean | null;
  error: string | '';
}

export interface CreateEmailPayLoad {
  queueId: string;
  toAddress: string;
  fromAddress: string | '';
}

export interface EmailTransferReq {
  conversationId?: string;
  agentParticipantId?: string;
  destinationUserId?: string;
  destinationQueueId?: string;
  transferType?: string;
}

export interface CopyEmailTemplateAttachmentRequest {
  templateAttachmentIds: string[];
  emailId: string;
}
