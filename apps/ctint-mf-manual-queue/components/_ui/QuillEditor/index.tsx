import React, { useEffect, useState, useRef } from 'react';
import { useF<PERSON>, Form<PERSON>rov<PERSON>, Controller } from 'react-hook-form';
import {
  getEmailNotes,
  saveEmailNote,
  updateEmailReference,
} from '../../../lib/api';
import { toast, useRole, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import { Download } from 'lucide-react';
import EmailAssignButton from '../AssignButton/EmailAssignButton';
import { Row } from '@tanstack/react-table';
import {
  ManualQueueData,
  OptionItem,
} from '../../../types/microfrontendsConfig';
import DOMPurify from 'dompurify';
import utc from 'dayjs/plugin/utc';
import dayjs from 'dayjs';
import { emailEditorStyles, sanitizeEmailBody } from '@cdss-modules/design-system/lib/utils';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
dayjs.extend(utc);

export interface EmailData {
  id?: string;
  subject?: string;
  from?: string;
  to?: string;
  bcc?: string;
  cc?: string;
  direction?: string;
  conversationId?: string;
  caseId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  notes?: string;
  body?: string;
  containsPii?: boolean | null;
  endTime?: string;
  attachments?: {
    items: Attachment[];
  };
  startTime?: string;
  conversationStartTime: string;
  conversationEndTime: string;
  isDraft?: number;
  caseStatus: string;
  referenceId: string;
  referenceStatus: string;
}

interface Note {
  id: string;
  createTime: string;
  updateTime: string;
  createBy: string;
  updateBy: string;
  tenant: string;
  node: string;
  gcConversationId: string;
  caseId: string;
  userName: string;
}

interface Attachment {
  id: string;
  attachmentName: string;
  fileType: string;
  fileSize: number;
  isInLine: boolean;
  url: string;
  contentId: string;
}

// 错误边界组件
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Email renderer crashed:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 text-red-500 bg-red-50 rounded">
          <h3 className="font-bold">
            Something went wrong displaying this email.
          </h3>
          <p>Please try again or contact support if the problem persists.</p>
          <button
            className="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
            onClick={() => this.setState({ hasError: false })}
          >
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

const EmailContainer: React.FC<{
  emailData: EmailData[];
  row: Row<ManualQueueData> | null;
  onAssignmentSuccessCallback?: () => void;
}> = ({ emailData, row, onAssignmentSuccessCallback }) => {
  const [notes, setNotes] = useState<Note[]>([]);
  const [noteText, setNoteText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingNotes, setIsFetchingNotes] = useState(false);
  const [emailsLoading, setEmailsLoading] = useState(true);
  const [isEditMode, setIsEditMode] = useState(true); // Initially start in edit mode
  const { basePath } = useRouteHandler();
  const notesLoadedRef = useRef(false);

  // 添加表单控制
  const methods = useForm({
    defaultValues: {
      referenceId: '',
      caseStatus: '',
      referenceStatus: '',
    },
  });

  const { globalConfig } = useRole();
  const microfrontendsConfig = globalConfig?.microfrontends || {};
  const manualQueueConfig = microfrontendsConfig['ctint-mf-manual-queue'] || {};
  const options = manualQueueConfig.options || {};

  const caseStatusOptions: OptionItem[] = options.caseStatus;
  const referenceStatusOptions: OptionItem[] = options.referenceStatus;

  const emailReference =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']?.emailReference ??
    false;

  // Use the conversation ID from the first email
  const conversationId = emailData[0]?.conversationId;
  const caseId = emailData[0]?.caseId;

  console.log('Row in EmailContainer:', row);
  console.log('Row state:', row?.original?.state);

  // 初始化emailsLoading
  useEffect(() => {
    if (emailData.length > 0) {
      setEmailsLoading(false);
      // 设置表单初始值
      methods.setValue('referenceId', emailData[0]?.referenceId || '');
      methods.setValue('caseStatus', emailData[0]?.caseStatus || '');
      methods.setValue('referenceStatus', emailData[0]?.referenceStatus || '');

      // 检查是否有已填写的值，如果有，则初始设置为非编辑模式
      const hasInitialValues =
        !isEmptyOrBlank(emailData[0]?.referenceId) ||
        !isEmptyOrBlank(emailData[0]?.caseStatus) ||
        !isEmptyOrBlank(emailData[0]?.referenceStatus);

      setIsEditMode(!hasInitialValues);
    }
  }, [emailData, methods]);

  const fetchNotes = () => {
    if (!conversationId || isFetchingNotes) return;

    setIsFetchingNotes(true);

    getEmailNotes(basePath, conversationId)
      .then((response) => {
        if (response.data.isSuccess) {
          setNotes(response.data.data);
        } else {
          console.error('Failed to fetch notes:', response.data.error);
        }
      })
      .catch((error) => {
        console.error('Error fetching notes:', error);
      })
      .finally(() => {
        setIsFetchingNotes(false);
      });
  };

  const handleSaveNote = () => {
    if (!conversationId || !noteText.trim()) return;

    setIsLoading(true);

    saveEmailNote(basePath, {
      gcConversationId: conversationId,
      caseId: caseId,
      note: noteText,
    })
      .then((response) => {
        if (response.data.isSuccess) {
          fetchNotes();
          setNoteText('');
        } else {
          console.error('Failed to save note:', response.data.error);
        }
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || '';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(() => {
        setIsLoading(false);
      });
  };

  // 解决重复调用问题
  useEffect(() => {
    if (conversationId && !notesLoadedRef.current) {
      notesLoadedRef.current = true;
      fetchNotes();
    }
  }, [conversationId]);

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string | undefined) => {
    if (!dateString || dateString === '-') return '-';

    // Parse the date string and convert to UTC+8
    const utcDate = dayjs(dateString).utc().add(8, 'hour');

    // Format the date
    return utcDate.format('YYYY-MM-DD HH:mm');
  };

  // Process HTML content, replace inline attachment src with robust error handling
  const processHtmlContent = (
    content: string,
    inlineAttachments: Attachment[]
  ): string => {
    try {
      let processedContent = content;
      if (inlineAttachments && inlineAttachments.length > 0) {
        inlineAttachments.forEach((attachment) => {
          if (attachment.contentId && attachment.url) {
            const pattern = new RegExp(`cid:${attachment.contentId}`, 'g');
            processedContent = processedContent.replace(
              pattern,
              attachment.url
            );
          }
        });
      }
      return processedContent;
    } catch (error) {
      console.error('Error processing HTML content:', error);
      return content; // 返回原始内容，避免处理失败
    }
  };

  // Format recipients
  const formatRecipients = (recipientsStr: string | ''): string => {
    if (!recipientsStr) return '-';
    return recipientsStr;
  };

  // 判断字段是否为空或空字符串
  const isEmptyOrBlank = (value: any) => {
    return value === undefined || value === null || value === '';
  };

  // 处理表单提交
  const handleFormSubmit = () => {
    if (isEditMode) {
      // 当前是编辑模式，需要保存
      const formValues = methods.getValues();

      // 验证 referenceId 不超过300个字符
      if (formValues.referenceId && formValues.referenceId.length > 300) {
        toast({
          title: 'error',
          description: 'Reference ID cannot exceed 300 characters',
          variant: 'error',
        });
        return; // 验证失败，直接返回，不执行后续保存操作
      }

      const formData = {
        caseId: caseId,
        caseStatus: formValues.caseStatus,
        gcConversationId: conversationId,
        referenceId: formValues.referenceId,
        referenceStatus: formValues.referenceStatus,
      };
      console.log('Form submitted:', formData);
      updateEmailReference(basePath, formData)
        .then((response) => {
          // 更新本地数据 - 将新值应用到邮件数据中
          const updatedEmails = sortedEmails.map((email) => ({
            ...email,
            referenceId: formValues.referenceId,
            caseStatus: formValues.caseStatus,
            referenceStatus: formValues.referenceStatus,
          }));
          toast({
            title: 'success',
            description: 'Successfully updated email reference',
            variant: 'success',
          });
          // 保存成功后，切换到非编辑模式
          setIsEditMode(false);
          if (onAssignmentSuccessCallback) {
            onAssignmentSuccessCallback();
          }
        })
        .catch((error) => {
          const errorMessage = error.response?.data?.error || '';
          toast({
            title: 'error',
            description: errorMessage,
            variant: 'error',
          });
        });
    } else {
      // 当前是非编辑模式，需要切换到编辑模式
      setIsEditMode(true);
    }
  };

  // Sort emails by date (newest first by default)
  const sortedEmails = [...emailData].sort((a, b) => {
    const dateA = a.startDate ? new Date(a.startDate).getTime() : 0;
    const dateB = b.startDate ? new Date(b.startDate).getTime() : 0;
    return dateB - dateA;
  });

  return (
    <div className="flex flex-col h-full max-h-[80vh]">
      <div className="flex flex-1 min-h-0">
        {/* Left side - email thread content */}
        <div className="w-[75%] overflow-y-auto">
          {emailsLoading ? (
            <div className="flex justify-center items-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-500"></div>
            </div>
          ) : (
            <div className="p-4">
              {/* Email thread - display all emails sorted by date */}
              <div className="space-y-6">
                {sortedEmails.map((email, index) => {
                  const inlineAttachments =
                    email.attachments?.items.filter((item) => item.isInLine) ||
                    [];
                  const externalAttachments =
                    email.attachments?.items.filter((item) => !item.isInLine) ||
                    [];

                  return (
                    <div
                      key={email.id || index}
                      className="email-message border rounded-lg overflow-hidden"
                    >
                      {/* Email header */}
                      <div className="bg-gray-50 p-3 border-b relative">
                        {email.isDraft === 1 && (
                          <div
                            className="absolute top-3 right-3 inline-flex items-center px-3 py-1 rounded text-xs font-medium"
                            style={{
                              backgroundColor: '#E6F7FF',
                              border: '1px solid #91D5FF',
                              color: '#1890FF',
                            }}
                          >
                            Draft
                          </div>
                        )}
                        <div className="space-y-2">
                          {/* 使用表格布局实现更好的对齐效果 */}
                          <table className="w-full text-sm">
                            <tbody>
                              {/* 主题行 */}
                              <tr>
                                <td className="align-top whitespace-nowrap pr-2 py-1 font-medium w-[120px]">
                                  Subject:
                                </td>
                                <td className="break-words py-1">
                                  {email.subject}
                                </td>
                              </tr>

                              {/* 发送时间行 */}
                              <tr>
                                <td className="align-top whitespace-nowrap pr-2 py-1 font-medium w-[120px]">
                                  SentDateTime:
                                </td>
                                <td className="break-words py-1">
                                  {formatDate(email.startDate)}
                                </td>
                              </tr>

                              {/* 发件人行 */}
                              <tr>
                                <td className="align-top whitespace-nowrap pr-2 py-1 font-medium w-[120px]">
                                  From:
                                </td>
                                <td className="break-words py-1">
                                  {email.from}
                                </td>
                              </tr>

                              {/* 收件人行 */}
                              <tr>
                                <td className="align-top whitespace-nowrap pr-2 py-1 font-medium w-[120px]">
                                  To:
                                </td>
                                <td className="break-words py-1">
                                  {formatRecipients(email?.to || '')}
                                </td>
                              </tr>

                              {/* 抄送行 - 条件渲染 */}
                              {email.cc && email.cc !== '-' && (
                                <tr>
                                  <td className="align-top whitespace-nowrap pr-2 py-1 font-medium w-[120px]">
                                    Cc:
                                  </td>
                                  <td className="break-words py-1">
                                    {formatRecipients(email.cc)}
                                  </td>
                                </tr>
                              )}

                              {/* 密送行 - 条件渲染 */}
                              {email.bcc && email.bcc !== '-' && (
                                <tr>
                                  <td className="align-top whitespace-nowrap pr-2 py-1 font-medium w-[120px]">
                                    Bcc:
                                  </td>
                                  <td className="break-words py-1">
                                    {formatRecipients(email.bcc)}
                                  </td>
                                </tr>
                              )}
                            </tbody>
                          </table>
                        </div>
                      </div>
                      {/* External attachments */}
                      {externalAttachments.length > 0 && (
                        <div className="p-3 bg-gray-50 border-b">
                          <div className="text-xs font-medium mb-2">
                            Attachments:
                          </div>
                          <div className="flex flex-wrap gap-2">
                            {externalAttachments.map((attachment) => (
                              <div
                                key={attachment.id}
                                className="flex items-center p-2 bg-white rounded-md text-sm border"
                              >
                                <div className="flex-1 mr-2">
                                  <div className="font-medium truncate max-w-[200px]">
                                    {attachment.attachmentName}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {formatFileSize(attachment.fileSize)}
                                  </div>
                                </div>
                                <a
                                  href={attachment.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="p-1 hover:bg-gray-100 rounded-full transition-colors"
                                >
                                  <Download
                                    size={14}
                                    className="text-gray-600"
                                  />
                                </a>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                      {/*Email body*/}
                      <div>
                        <ErrorBoundary>
                          {email.body ? (
                            <EmailViewer
                              content={processHtmlContent(
                                email.body,
                                inlineAttachments
                              )}
                            />
                          ) : (
                            <div className="p-4 text-gray-500 text-center">
                              No email content available
                            </div>
                          )}
                        </ErrorBoundary>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Right side - details panel */}
        <div className="w-[25%] border-l overflow-y-auto">
          <FormProvider {...methods}>
            <div className="p-4">
              {sortedEmails.length > 0 && (
                <>
                  {/* 右上角的 PII 指示器和分配按钮 */}
                  <div className="flex justify-end items-center gap-2 mb-4">
                    <div
                      className="inline-flex items-center px-3 py-1 rounded text-xs font-medium"
                      style={{
                        backgroundColor: sortedEmails.some(
                          (email) => email.containsPii === true
                        )
                          ? '#FFF1F0'
                          : '#F6FFED',
                        border: sortedEmails.some(
                          (email) => email.containsPii === true
                        )
                          ? '1px solid #FFA39E'
                          : '1px solid #B7EB8F',
                        color: sortedEmails.some(
                          (email) => email.containsPii === true
                        )
                          ? '#FF271C'
                          : '#1CC500',
                      }}
                    >
                      {sortedEmails.some((email) => email.containsPii === true)
                        ? 'Contains PII'
                        : 'No PII'}
                    </div>
                    <EmailAssignButton
                      row={row}
                      onAssignmentSuccess={() => {
                        if (onAssignmentSuccessCallback) {
                          onAssignmentSuccessCallback();
                        }
                        console.log('Email assigned successfully');
                      }}
                      variant="orange"
                      inModal={true}
                    />
                  </div>
                  <div className="mb-4 space-y-4">
                    {[
                      { label: 'Conversation ID', value: conversationId },
                      { label: 'Case ID', value: caseId },
                      { label: 'Status', value: sortedEmails[0].status },
                      {
                        label: 'Start Date',
                        value: formatDate(
                          sortedEmails[sortedEmails.length - 1]
                            ?.conversationStartTime
                        ),
                      },
                      {
                        label: 'End Date',
                        value: formatDate(sortedEmails[0]?.conversationEndTime),
                      },
                    ].map(({ label, value }) => (
                      <div
                        key={label}
                        className="flex"
                      >
                        <span className="w-36 text-black font-medium">
                          {label}
                        </span>
                        <span className="text-sm break-all">
                          {value || '-'}
                        </span>
                      </div>
                    ))}
                    {emailReference && (
                      <>
                        {/* Ref ID 输入框 */}
                        <div>
                          <div className="flex">
                            <span className="w-36 text-black font-medium">
                              Ref ID
                            </span>
                          </div>
                          <div className="mt-1">
                            <Controller
                              name="referenceId"
                              control={methods.control}
                              render={({ field }) => (
                                <Input
                                  {...field}
                                  size="s"
                                  placeholder="Inputed text"
                                  disabled={!isEditMode}
                                  className="w-full break-all whitespace-normal overflow-visible text-wrap"
                                  style={{
                                    wordBreak: 'break-all',
                                    minHeight:
                                      field.value && field.value.length > 30
                                        ? 'auto'
                                        : '32px',
                                  }}
                                />
                              )}
                            />
                          </div>
                          {methods.getValues('referenceId') &&
                            methods.getValues('referenceId').length > 30 &&
                            !isEditMode && (
                              <div className="mt-1 text-xs text-gray-600 break-all">
                                {methods.getValues('referenceId')}
                              </div>
                            )}
                        </div>

                        {/* Reference Status */}
                        <div>
                          <div className="flex">
                            <span className="w-36 text-black font-medium">
                              Reference Status
                            </span>
                          </div>
                          <div className="mt-1">
                            <Controller
                              name="referenceStatus"
                              control={methods.control}
                              render={({ field }) => (
                                <Select
                                  options={referenceStatusOptions}
                                  value={field.value}
                                  onChange={field.onChange}
                                  placeholder="Select"
                                  mode="single"
                                  isPagination={false}
                                  triggerClassName="!h-8"
                                  disabled={!isEditMode}
                                />
                              )}
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {/* Case status */}
                    <div>
                      <div className="flex">
                        <span className="w-36 text-black font-medium">
                          Case status
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mb-1">
                        Case status will be associated with case ID({caseId})
                      </div>
                      <Controller
                        name="caseStatus"
                        control={methods.control}
                        render={({ field }) => (
                          <Select
                            options={caseStatusOptions}
                            value={field.value}
                            onChange={field.onChange}
                            placeholder="Select"
                            mode="single"
                            isPagination={false}
                            triggerClassName="!h-8"
                            disabled={!isEditMode}
                          />
                        )}
                      />
                    </div>

                    {/* Edit/Save 按钮 */}
                    <div className="flex justify-end mt-4 gap-2">
                      {isEditMode ? (
                        <>
                          <button
                            className="px-4 py-2 bg-white text-black border border-gray-300 rounded hover:bg-gray-100 text-sm"
                            onClick={() => {
                              // 重置表单值为初始值
                              methods.setValue(
                                'referenceId',
                                emailData[0]?.referenceId || ''
                              );
                              methods.setValue(
                                'caseStatus',
                                emailData[0]?.caseStatus || ''
                              );
                              methods.setValue(
                                'referenceStatus',
                                emailData[0]?.referenceStatus || ''
                              );
                              // 切换回非编辑模式
                              setIsEditMode(false);
                            }}
                          >
                            Cancel
                          </button>
                          <button
                            className="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 text-sm"
                            onClick={handleFormSubmit}
                          >
                            Save
                          </button>
                        </>
                      ) : (
                        <button
                          className="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 text-sm"
                          onClick={handleFormSubmit}
                        >
                          Edit
                        </button>
                      )}
                    </div>
                  </div>
                </>
              )}

              {/* Notes section */}
              <div className="mt-6">
                <span className="text-gray-600 block mb-2">Notes</span>
                <div className="mb-4 space-y-2">
                  {notes
                    .slice() // Create a copy of the array to avoid mutating the original
                    .sort((a, b) => {
                      // Sort by creation time in descending order (newest first)
                      return (
                        new Date(b.createTime).getTime() -
                        new Date(a.createTime).getTime()
                      );
                    })
                    .map((note) => (
                      <div
                        key={note.id}
                        className="p-2 bg-gray-50 rounded text-sm"
                      >
                        <div className="whitespace-pre-wrap break-words">
                          {note.node}
                        </div>
                        <div className="flex justify-between text-xs text-gray-500 mt-2">
                          <span>{note.userName}</span>
                          <span>{formatDate(note.createTime)}</span>
                        </div>
                      </div>
                    ))}
                </div>

                <textarea
                  className="w-full min-h-[120px] p-3 border border-gray-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white text-sm"
                  placeholder="Enter notes here..."
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                />

                <div className="flex justify-end mt-4">
                  <button
                    className="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 text-sm disabled:bg-gray-400"
                    onClick={handleSaveNote}
                    disabled={isLoading || !noteText.trim()}
                  >
                    {isLoading ? 'Saving...' : 'Save'}
                  </button>
                </div>
              </div>
            </div>
          </FormProvider>
        </div>
      </div>
    </div>
  );
};

export default EmailContainer;
const EmailViewer: React.FC<{ content: string }> = ({ content }) => {
  const [viewMode, setViewMode] = useState<'simple' | 'text'>('simple');

  // 内容为空时的处理
  if (!content || content.trim() === '') {
    return <div className="p-4 text-gray-500">无内容可显示</div>;
  }

  // 检查内容是否太大或太复杂
  // const isComplexContent =
  //   content.length > 50000 ||
  //   content.includes('height:100%!important') ||
  //   (content.match(/<table/g) || []).length > 10;

  const isComplexContent = false;
  // 简化HTML的函数 - 移除所有可能导致问题的元素和属性
  const simplifyHtml = (html: string): string => {
    try {
      // 移除所有script标签及其内容
      let result = html.replace(
        /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
        ''
      );

      // 移除所有style标签及其内容
      result = result.replace(
        /<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi,
        ''
      );

      // 移除所有内联样式
      result = result.replace(/ style="[^"]*"/gi, '');

      // 移除所有class属性
      result = result.replace(/ class="[^"]*"/gi, '');

      // 移除所有事件处理程序
      result = result.replace(/ on\w+="[^"]*"/gi, '');

      // 确保图片有最大宽度
      result = result.replace(
        /<img/gi,
        '<img style="max-width:100%;height:auto"'
      );

      // 将表格替换为简单的div
      result = result.replace(
        /<table[^>]*>/gi,
        '<div class="table-replacement">'
      );
      result = result.replace(/<\/table>/gi, '</div>');
      result = result.replace(/<tr[^>]*>/gi, '<div class="tr-replacement">');
      result = result.replace(/<\/tr>/gi, '</div>');
      result = result.replace(/<td[^>]*>/gi, '<div class="td-replacement">');
      result = result.replace(/<\/td>/gi, '</div>');

      return result;
    } catch (error) {
      console.error('简化HTML时出错:', error);
      return ''; // 出错时返回空字符串
    }
  };

  // 获取纯文本内容 - 移除所有HTML标签
  const getPlainText = (html: string): string => {
    return html
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  };

  return (
    <div className="email-viewer">
      <style>{emailEditorStyles}</style>
      {/*<div className="mb-2 flex justify-end">*/}
      {/*  <button*/}
      {/*    onClick={() => setViewMode(viewMode === 'simple' ? 'text' : 'simple')}*/}
      {/*    className="text-xs bg-gray-100 hover:bg-gray-200 px-2 py-1 rounded"*/}
      {/*  >*/}
      {/*    {viewMode === 'simple' ? '查看纯文本' : '查看简化HTML'}*/}
      {/*  </button>*/}
      {/*</div>*/}

      {/*{isComplexContent && (*/}
      {/*  <div className="p-2 bg-yellow-50 border border-yellow-200 rounded mb-2">*/}
      {/*    <p className="text-yellow-700 text-sm">*/}
      {/*      此邮件包含复杂内容，已简化显示以确保稳定性。*/}
      {/*    </p>*/}
      {/*  </div>*/}
      {/*)}*/}

      {viewMode === 'simple' ? (
        <div
          className="email-content p-3 border rounded bg-white ProseMirror "
          dangerouslySetInnerHTML={{
            __html: sanitizeEmailBody(
              isComplexContent ? simplifyHtml(content) : content
            ),
          }}
        />
      ) : (
        <pre className="p-3 bg-gray-50 border rounded text-sm overflow-auto whitespace-pre-wrap">
          {getPlainText(content)}
        </pre>
      )}
    </div>
  );
};
