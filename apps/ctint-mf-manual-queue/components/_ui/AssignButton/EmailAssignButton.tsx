import React, { useRef, useState } from 'react';
import { Row } from '@tanstack/react-table';
import {
  AssignReq,
  ManualQueueData,
  ManualQueuePermission,
  microfrontends,
  SendEmailRequest,
  User,
} from '../../../types/microfrontendsConfig';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import {
  THEME_FONT_COLORS,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import {
  assignTo,
  replyDisconnected,
  toggleSpamStatus,
  reassign,
  secureSuffix,
} from '../../../lib/api';
import UserByQueueSelector from '../UserSelector/UserByQueue';
import QueueSelector from '../UserSelector/QueueSelector';
import {
  Popup,
  PopupContent,
} from '@cdss-modules/design-system/components/_ui/Popup';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@cdss-modules/design-system/components/_ui/DropdownMenu';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';

export interface EmailAssignButtonProps {
  row: Row<ManualQueueData> | null;
  onAssignmentSuccess: () => void;
  variant?: 'default' | 'orange';
  inModal?: boolean;
}

const EmailAssignButton: React.FC<EmailAssignButtonProps> = ({
  row,
  onAssignmentSuccess,
  variant = 'default',
  inModal = false,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [showUserSelector, setShowUserSelector] = useState(false);
  const [showQueueSelector, setShowQueueSelector] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedQueue, setSelectedQueue] = useState<{
    id: string;
    name: string;
  } | null>(null);
  const [assignToMe, setAssignToMe] = useState(false);
  const [isReply, setIsReply] = useState(false);
  const [isReassignTo, setIsReassignTo] = useState(false);
  const [isReassignQueue, setIsReassignQueue] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const { toast } = useToast();

  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueuePermission: ManualQueuePermission =
    microfrontendsConfig['ctint-mf-manual-queue']['permission'];

  const assignToPermission = manualQueuePermission?.['email-assign-to'];
  const canAssignTo =
    !assignToPermission || permissions.includes(assignToPermission);

  const forceEnabled = inModal;

  const isEnabled =
    row?.original.state === 'WAITING' || row?.original.state === 'ALERTING';

  const isReplyEnabled =
    row?.original.state === 'DISCONNECTED' || row?.original.state === 'WRAP-UP';

  const isConnected = row?.original.state === 'CONNECTED';

  const handleAssignToMe = (e: React.MouseEvent): void => {
    console.log('Assign to me clicked');
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);
    setAssignToMe(true);
    setSelectedUser(null);
    setSelectedQueue(null);
    setIsReply(false);
    setIsReassignTo(false);
    setIsReassignQueue(false);
    setShowConfirmModal(true);
  };

  const handleUserSelected = (user: User): void => {
    console.log('User selected:', user.name);
    setShowUserSelector(false);
    setSelectedUser(user);
    setSelectedQueue(null);
    setAssignToMe(false);
    setIsReply(false);
    setShowConfirmModal(true);
  };

  const handleQueueSelected = (queue: { id: string; name: string }): void => {
    console.log('Queue selected:', queue.name);
    setShowQueueSelector(false);
    setSelectedQueue(queue);
    setSelectedUser(null);
    setAssignToMe(false);
    setIsReply(false);
    setShowConfirmModal(true);
  };

  const handleAssignTo = (e: React.MouseEvent): void => {
    console.log('Assign to clicked');
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);
    setIsReassignTo(false);
    setIsReassignQueue(false);
    setTimeout(() => {
      setShowUserSelector(true);
    }, 100);
  };

  const handleReply = (e: React.MouseEvent): void => {
    console.log('Reply clicked');
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);
    setIsReply(true);
    setIsReassignTo(false);
    setIsReassignQueue(false);
    setShowConfirmModal(true);
  };

  const handleReassignQueue = (e: React.MouseEvent): void => {
    console.log('ReAssign Queue clicked');
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);

    // Show queue selector
    setIsReassignQueue(true);
    setIsReassignTo(false);
    setIsReply(false);
    setAssignToMe(false);
    setSelectedUser(null);
    setSelectedQueue(null);

    // Open the queue selector with a small delay
    setTimeout(() => {
      setShowQueueSelector(true);
    }, 100);
  };

  const handleReassignTo = (e: React.MouseEvent): void => {
    console.log('ReAssign To clicked');
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);
    setIsReassignTo(true);
    setIsReassignQueue(false);
    setTimeout(() => {
      setShowUserSelector(true);
    }, 100);
  };

  const handleConfirmAssign = (): void => {
    if (isReply) {
      // Handle reply email API call
      const replyData: SendEmailRequest = {
        gcConversationId: row?.original.conversationId,
      };

      replyDisconnected(basePath, replyData)
        .then(() => {
          toast({
            title: 'Success',
            description: 'Email reply submitted successfully',
            variant: 'success',
          });
          setTimeout(() => {
            onAssignmentSuccess();
          }, 1000);
        })
        .catch((error) => {
          const errorMessage = error.response?.data?.error || 'Reply Error';
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'error',
          });
        })
        .finally(() => {
          setShowConfirmModal(false);
          setIsReply(false);
        });
    } else if (isReassignQueue && selectedQueue) {
      // Handle reassign to queue API call
      const reassignQueueReq: AssignReq = {
        conversationId: row?.original.conversationId,
        type: 'email',
        queueId: selectedQueue.id,
        currentAssignUserId: row?.original.userId,
      };

      reassign(basePath, reassignQueueReq)
        .then(() => {
          toast({
            title: 'Success',
            description: `Email reassigned to ${selectedQueue.name} queue successfully`,
            variant: 'success',
          });
          setTimeout(() => {
            onAssignmentSuccess();
          }, 1000);
        })
        .catch((error) => {
          const errorMessage =
            error.response?.data?.error || 'Reassign Queue Error';
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'error',
          });
        })
        .finally(() => {
          setShowConfirmModal(false);
          setIsReassignQueue(false);
          setSelectedQueue(null);
        });
    } else if (isReassignTo && selectedUser) {
      // Handle reassign to user API call
      const reassignToUserReq: AssignReq = {
        conversationId: row?.original.conversationId,
        type: 'email',
        agentId: selectedUser.id,
        currentAssignUserId: row?.original.userId,
      };

      reassign(basePath, reassignToUserReq)
        .then(() => {
          toast({
            title: 'Success',
            description: `Email reassigned to ${selectedUser.name} successfully`,
            variant: 'success',
          });
          setTimeout(() => {
            onAssignmentSuccess();
          }, 1000);
        })
        .catch((error) => {
          const errorMessage =
            error.response?.data?.error || 'Reassign To Error';
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'error',
          });
        })
        .finally(() => {
          setShowConfirmModal(false);
          setSelectedUser(null);
          setIsReassignTo(false);
        });
    } else {
      // Handle regular assignment
      const assignReq: AssignReq = {
        conversationId: row?.original.conversationId,
        type: 'email',
        ...(assignToMe
          ? { isAssignToMe: true }
          : { agentId: selectedUser?.id }),
      };

      assignTo(basePath, assignReq)
        .then(() => {
          toast({
            title: 'Success',
            description: 'Email assigned successfully',
            variant: 'success',
          });
          setTimeout(() => {
            onAssignmentSuccess();
          }, 1000);
        })
        .catch((error) => {
          const errorMessage = error.response?.data?.error || 'Assign Error';
          toast({
            title: 'Error',
            description: errorMessage,
            variant: 'error',
          });
        })
        .finally(() => {
          setShowConfirmModal(false);
          setSelectedUser(null);
          setAssignToMe(false);
        });
    }
  };

  const emailData = {
    interactionId: row?.original.conversationId || '',
    caseId: row?.original.caseId || '',
    fromAddress: row?.original.from || '',
    subject: row?.original.subject || '',
    queueName: row?.original.queueName || '',
    assignTo: isReply
      ? 'Reply'
      : isReassignQueue && selectedQueue
        ? `Queue: ${selectedQueue.name}`
        : isReassignTo && selectedUser
          ? `Agent: ${selectedUser.name}`
          : assignToMe
            ? 'Myself'
            : selectedUser?.name || '',
  };

  // Render orange button icon
  const renderOrangeButton = () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="24"
        height="24"
        rx="12"
        fill="#F7971D"
      />
      <path
        d="M14.6808 8.50684C14.6808 6.57325 13.1077 5.00012 11.1741 5.00012C9.24048 5.00012 7.66735 6.57325 7.66735 8.50684C7.66813 9.68872 8.26454 10.7906 9.25376 11.4376C6.71313 12.7273 4.98657 15.3739 4.98657 18.3423C4.98657 18.7054 5.28095 18.9998 5.64407 18.9998C6.0072 18.9998 6.30157 18.7054 6.30157 18.3423C6.30157 15.1789 8.65767 12.4467 11.7817 11.9873C11.8628 11.9754 11.9369 11.9475 12.0044 11.9101C13.5375 11.5357 14.6808 10.154 14.6808 8.50684ZM11.1744 6.31512C12.3828 6.31512 13.3661 7.2984 13.3661 8.50684C13.3661 9.71528 12.3828 10.6986 11.1744 10.6986C9.96595 10.6986 8.98267 9.71528 8.98267 8.50684C8.98267 7.2984 9.96595 6.31512 11.1744 6.31512ZM18.5969 14.6212L13.4858 12.6075C13.3668 12.5608 13.2368 12.5498 13.1116 12.5759C12.9865 12.6019 12.8716 12.6638 12.7811 12.754C12.6906 12.8443 12.6283 12.9589 12.6018 13.084C12.5754 13.2091 12.586 13.3391 12.6324 13.4582L14.6339 18.5817C14.7317 18.8306 14.9702 18.9959 15.237 18.9998H15.2463C15.5102 18.9998 15.7486 18.8425 15.8516 18.5987L16.6766 16.652L18.6105 15.8393C18.7311 15.7888 18.8339 15.7034 18.9057 15.5941C18.9775 15.4848 19.0151 15.3565 19.0136 15.2257C19.0122 15.095 18.9717 14.9676 18.8975 14.8599C18.8233 14.7522 18.7186 14.6691 18.5969 14.6212ZM15.9203 15.5434C15.7625 15.6095 15.6367 15.7354 15.5697 15.8932L15.2703 16.5984L14.4045 14.3831L16.6111 15.2523L15.9203 15.5434Z"
        fill="white"
      />
    </svg>
  );

  // Render default three-dot button icon
  const renderDefaultButton = () => (
    <svg
      width="4"
      height="15"
      viewBox="0 0 4 15"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        className={`mr-1 ${!buttonEnabled ? 'opacity-50' : ''}`}
        d="M2.00139 14.543C1.50908 14.543 1.03694 14.3463 0.688825 13.9962C0.34071 13.6462 0.145142 13.1714 0.145142 12.6763C0.145142 12.1812 0.34071 11.7064 0.688825 11.3564C1.03694 11.0063 1.50908 10.8096 2.00139 10.8096C2.4937 10.8096 2.96585 11.0063 3.31396 11.3564C3.66207 11.7064 3.85764 12.1812 3.85764 12.6763C3.85764 13.1714 3.66207 13.6462 3.31396 13.9962C2.96585 14.3463 2.4937 14.543 2.00139 14.543ZM2.00139 9.20964C1.50908 9.20964 1.03694 9.01297 0.688825 8.6629C0.34071 8.31283 0.145142 7.83804 0.145142 7.34297C0.145142 6.8479 0.34071 6.3731 0.688825 6.02304C1.03694 5.67297 1.50908 5.4763 2.00139 5.4763C2.4937 5.4763 2.96585 5.67297 3.31396 6.02304C3.66207 6.3731 3.85764 6.8479 3.85764 7.34297C3.85764 7.83804 3.66207 8.31283 3.31396 8.6629C2.96585 9.01297 2.4937 9.20964 2.00139 9.20964ZM0.145142 2.00963C0.145142 1.51456 0.34071 1.03977 0.688825 0.689701C1.03694 0.339634 1.50908 0.142968 2.00139 0.142968C2.4937 0.142968 2.96585 0.339634 3.31396 0.689701C3.66207 1.03977 3.85764 1.51456 3.85764 2.00963C3.85764 2.50471 3.66207 2.9795 3.31396 3.32957C2.96585 3.67964 2.4937 3.8763 2.00139 3.8763C1.50908 3.8763 1.03694 3.67964 0.688825 3.32957C0.34071 2.9795 0.145142 2.50471 0.145142 2.00963Z"
        style={{
          fill: isDropdownOpen
            ? THEME_FONT_COLORS.primary[500]
            : THEME_FONT_COLORS.secondary[500],
        }}
      />
    </svg>
  );

  const handleToggleSpam = (e: React.MouseEvent): void => {
    console.log('Toggle spam clicked');
    e.preventDefault();
    e.stopPropagation();
    setIsDropdownOpen(false);

    const isCurrentlySpam = row?.original.isJunkmail === 'Yes';
    const newSpamValue = isCurrentlySpam ? 0 : 1;

    toggleSpamStatus(basePath, {
      gcConversationId: row?.original.conversationId,
      isJunkmail: newSpamValue,
    })
      .then(() => {
        toast({
          title: 'Success',
          description: isCurrentlySpam
            ? 'Email unmarked as spam'
            : 'Email marked as spam',
          variant: 'success',
        });
        onAssignmentSuccess();
      })
      .catch((error) => {
        const errorMessage = error.response?.data?.error || 'Operation failed';
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'error',
        });
      });
  };

  // const handleSecureSuffix = (e: React.MouseEvent): void => {
  //   console.log('Secure suffix clicked');
  //   e.preventDefault();
  //   e.stopPropagation();
  //   setIsDropdownOpen(false); // 关闭下拉菜单
  //
  //   // 调用安全后缀API
  //   secureSuffix(basePath, {
  //     conversationId: row?.original.conversationId,
  //   })
  //     .then(() => {
  //       toast({
  //         title: 'Success',
  //         description: 'Email secure suffix applied successfully',
  //         variant: 'success',
  //       });
  //       // 刷新页面数据
  //       onAssignmentSuccess();
  //     })
  //     .catch((error) => {
  //       const errorMessage =
  //         error.response?.data?.error || 'Secure suffix operation failed';
  //       toast({
  //         title: 'Error',
  //         description: errorMessage,
  //         variant: 'error',
  //       });
  //     });
  // };

  const buttonEnabled =
    isEnabled || isReplyEnabled || isConnected || forceEnabled;

  return (
    <>
      <div
        ref={buttonRef}
        className="relative inline-flex"
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
        onDoubleClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        <DropdownMenu
          open={isDropdownOpen}
          onOpenChange={setIsDropdownOpen}
        >
          <DropdownMenuTrigger
            disabled={!buttonEnabled && !forceEnabled}
            className={`flex items-center justify-center hover:bg-transparent ${
              variant === 'default'
                ? 'px-5 min-w-[50px] h-[25px]'
                : 'min-w-[50px] h-[25px]'
            } ${!buttonEnabled && !forceEnabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={(e) => e.stopPropagation()}
          >
            {variant === 'orange'
              ? renderOrangeButton()
              : renderDefaultButton()}
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className={`z-[10000] ${inModal ? 'right-0' : ''}`}
            onClick={(e) => e.stopPropagation()}
            sideOffset={5}
            align={inModal ? 'end' : 'start'}
          >
            {isEnabled && (
              <>
                <DropdownMenuItem onClick={handleAssignToMe}>
                  <span className="text-common-black">
                    {t('assign-to-me', 'Assign to me')}
                  </span>
                </DropdownMenuItem>
                {canAssignTo && (
                  <DropdownMenuItem onClick={handleAssignTo}>
                    <span className="text-common-black">
                      {t('assign-to', 'Assign to...')}
                    </span>
                  </DropdownMenuItem>
                )}
              </>
            )}
            {isReplyEnabled && (
              <DropdownMenuItem onClick={handleReply}>
                <span className="text-common-black">{t('reply', 'Reply')}</span>
              </DropdownMenuItem>
            )}
            {/* Spam related options */}
            <DropdownMenuItem onClick={handleToggleSpam}>
              <span className="text-common-black">
                {row?.original.isJunkmail === 'Yes'
                  ? t('unmark-junk-mail', 'Unmark junk-mail')
                  : t('mark-as-junk-mail', 'Mark as junk mail')}
              </span>
            </DropdownMenuItem>
            {/* Show reassign options in CONNECTED state */}
            {isConnected && canAssignTo && (
              <>
                <DropdownMenuItem onClick={handleReassignQueue}>
                  <span className="text-common-black">
                    {t('reassignQueue', 'Reassign Queue')}
                  </span>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleReassignTo}>
                  <span className="text-common-black">
                    {t('reassign', 'Reassign To..')}
                  </span>
                </DropdownMenuItem>
              </>
            )}
            {/*{emailSecureSuffix && (*/}
            {/*  <DropdownMenuItem onClick={handleSecureSuffix}>*/}
            {/*    <span className="text-common-black">*/}
            {/*      {t('secure-suffix', 'Secure suffix')}*/}
            {/*    </span>*/}
            {/*  </DropdownMenuItem>*/}
            {/*)}*/}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Show UserSelector when needed */}
        {showUserSelector && !isDropdownOpen && (
          <UserByQueueSelector
            isOpen={showUserSelector}
            onClose={() => setShowUserSelector(false)}
            anchorRef={buttonRef}
            onSelectUser={handleUserSelected}
            inModal={inModal}
            queueId={row?.original.currentQueueId}
          />
        )}

        {/* Show QueueSelector when needed */}
        {showQueueSelector && !isDropdownOpen && (
          <QueueSelector
            isOpen={showQueueSelector}
            onClose={() => setShowQueueSelector(false)}
            anchorRef={buttonRef}
            onSelectQueue={handleQueueSelected}
            inModal={inModal}
          />
        )}
      </div>

      {/* Confirmation Modal */}
      <Popup
        open={showConfirmModal}
        onOpenChange={setShowConfirmModal}
      >
        <PopupContent
          title={
            isReply
              ? 'Reply to Email'
              : isReassignQueue
                ? 'Reassign Email to Queue'
                : isReassignTo
                  ? 'Reassign Email to Agent'
                  : 'Assign Email to Agent'
          }
          onClick={(e: React.MouseEvent) => e.stopPropagation()}
        >
          <div
            className="p-4"
            onClick={(e: React.MouseEvent) => e.stopPropagation()}
          >
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="text-sm text-gray-600">Interaction ID</div>
              <div className="text-sm">{emailData.interactionId}</div>

              <div className="text-sm text-gray-600">Case ID</div>
              <div className="text-sm">{emailData.caseId}</div>

              <div className="text-sm text-gray-600">From Address</div>
              <div className="text-sm">{emailData.fromAddress}</div>

              <div className="text-sm text-gray-600">Subject</div>
              <div className="text-sm">{emailData.subject}</div>

              {isReassignQueue && (
                <>
                  <div className="text-sm text-gray-600">Current Queue</div>
                  <div className="text-sm">{emailData.queueName}</div>

                  <div className="text-sm text-gray-600">Reassign To Queue</div>
                  <div className="text-sm">{selectedQueue?.name || ''}</div>
                </>
              )}

              {!isReply && !isReassignQueue && (
                <>
                  <div className="text-sm text-gray-600">
                    {isReassignTo ? 'Reassign To' : 'Assign To'}
                  </div>
                  <div className="text-sm">{emailData.assignTo}</div>
                </>
              )}
            </div>

            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button
                variant="blank"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  setShowConfirmModal(false);
                  setIsReassignQueue(false);
                  setSelectedQueue(null);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  handleConfirmAssign();
                }}
              >
                Confirm
              </Button>
            </div>
          </div>
        </PopupContent>
      </Popup>
    </>
  );
};

export default EmailAssignButton;
