import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRouteHandler } from '@cdss-modules/design-system';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { getQueueInfo } from '../../../lib/api';

// Queue API response interface
interface QueueResponse {
  data: { id: string; name: string }[];
  error: string;
  isSuccess: boolean;
}

export interface QueueSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  anchorRef: React.RefObject<HTMLDivElement>;
  onSelectQueue: (queue: { id: string; name: string }) => void;
  inModal?: boolean;
}

export const QueueSelector: React.FC<QueueSelectorProps> = ({
  isOpen,
  onClose,
  anchorRef,
  onSelectQueue,
  inModal = false,
}) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const [allQueues, setAllQueues] = useState<{ id: string; name: string }[]>(
    []
  ); // Store all queues cache
  const [displayedQueues, setDisplayedQueues] = useState<
    { id: string; name: string }[]
  >([]); // Displayed queues (filtered)
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [selectedQueue, setSelectedQueue] = useState<{
    id: string;
    name: string;
  } | null>(null);

  // Window size state for recalculating position
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  const handleSelectQueue = (queue: { id: string; name: string }) => {
    console.log('Selecting queue:', queue.name);
    setSelectedQueue(queue);
    onSelectQueue(queue);
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inModal) return;

      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        anchorRef.current &&
        !anchorRef.current.contains(event.target as Node)
      ) {
        console.log('Click outside, closing selector');
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose, inModal]);

  // Listen for search query changes, filter queues from cache
  useEffect(() => {
    if (allQueues.length > 0) {
      // If search box is empty, show all queues
      if (!searchQuery.trim()) {
        setDisplayedQueues(allQueues);
        return;
      }

      // Filter matching queues from cache
      const filtered = allQueues.filter((queue) =>
        queue.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setDisplayedQueues(filtered);
    }
  }, [searchQuery, allQueues]);

  // Fetch queues on initial load
  useEffect(() => {
    fetchQueues();
  }, []);

  const fetchQueues = async () => {
    setLoading(true);
    setError(null);
    try {
      const queueListData = await getQueueInfo(basePath, 'email');

      const queues = queueListData.data.data;
      setAllQueues(queues);
      setDisplayedQueues(queues);
    } catch (error) {
      setError(t('Error loading queues. Please try again.'));
      console.error('Error fetching queues:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !anchorRef.current) return null;

  // Get button position info
  const rect = anchorRef.current.getBoundingClientRect();

  // Calculate best position on screen
  // Check if we need to align to the left (when near right edge)
  const nearRightEdge = rect.left + 240 > windowSize.width - 20; // 240px is selector width, 20px is right margin

  // Check if we need to expand upwards (when bottom space is insufficient)
  const bottomSpace = windowSize.height - rect.bottom;
  const needsToExpandUp = bottomSpace < 300; // 300px is estimated selector height

  // Position logic inside modal
  const inModalStyle = nearRightEdge
    ? {
        position: 'absolute' as const,
        top: needsToExpandUp ? 'auto' : '100%',
        bottom: needsToExpandUp ? '100%' : 'auto',
        right: '0',
        zIndex: 20000,
      }
    : {
        position: 'absolute' as const,
        top: needsToExpandUp ? 'auto' : '100%',
        bottom: needsToExpandUp ? '100%' : 'auto',
        left: '0',
        zIndex: 20000,
      };

  // Position logic in page (fixed position)
  // Calculate best position based on screen space
  const inPageStyle = {
    position: 'fixed' as const,
    top: needsToExpandUp ? 'auto' : `${rect.bottom + window.scrollY + 5}px`,
    bottom: needsToExpandUp
      ? `${windowSize.height - rect.top + window.scrollY - 5}px`
      : 'auto',
    left: nearRightEdge ? 'auto' : `${rect.left + window.scrollX}px`,
    right: nearRightEdge
      ? `${windowSize.width - rect.right - window.scrollX}px`
      : 'auto',
    zIndex: 10000,
  };

  // Choose style based on whether in modal or not
  const style = inModal ? inModalStyle : inPageStyle;

  return (
    <div
      ref={dropdownRef}
      className={cn(
        'bg-white rounded-sm shadow-lg border border-grey-200',
        'min-w-[240px] w-[240px] max-h-[300px] overflow-hidden flex flex-col',
        'data-[state=open]:animate-in data-[state=closed]:animate-out',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0'
      )}
      style={style}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      {/* Search Input */}
      <div className="p-2 border-b border-grey-200 flex-shrink-0">
        <div className="relative">
          <Input
            isSearch
            size="s"
            beforeIcon={<Icon name="search" />}
            placeholder={t('Search queues...')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e as string)}
            allowClear
            style={{ zIndex: 9999 }}
          />
        </div>
      </div>

      {/* Queues List */}
      <div
        className="w-full flex flex-col overflow-y-auto flex-grow"
        style={{ maxHeight: '250px', zIndex: 9999 }}
      >
        {loading ? (
          <div className="py-6 w-full text-center text-grey-400">
            {t('Loading...')}
          </div>
        ) : error ? (
          <div className="py-6 w-full text-center text-status-danger">
            {error}
          </div>
        ) : displayedQueues.length === 0 ? (
          <div className="py-6 w-full text-center text-grey-400">
            {searchQuery
              ? t('No queues found')
              : t('Start typing to search queues')}
          </div>
        ) : (
          displayedQueues.map((queue) => (
            <div
              key={queue.id}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                handleSelectQueue(queue);
              }}
              className={cn(
                'relative flex w-full cursor-pointer select-none items-center',
                'py-2 px-3 text-body outline-none',
                'hover:bg-primary-100',
                selectedQueue?.id === queue.id &&
                  'bg-primary-100 hover:bg-primary-200'
              )}
            >
              <div className="flex items-center justify-between w-full">
                <span className="text-sm font-medium">{queue.name}</span>
                {selectedQueue?.id === queue.id && (
                  <Icon
                    name="check"
                    className="text-primary-500"
                  />
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default QueueSelector;
