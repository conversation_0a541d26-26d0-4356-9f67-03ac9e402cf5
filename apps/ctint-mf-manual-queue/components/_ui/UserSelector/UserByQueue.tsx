import React, { useState, useEffect, useRef } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRole, useRouteHandler } from '@cdss-modules/design-system';
import { queueMember, userSearchByQueue } from '../../../lib/api';
import {
  UserSearchReq,
  QueueUser,
  QueueUserResponse,
} from '../../../types/microfrontendsConfig';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { UserSelectorProps } from './index';

// 你可能需要更新UserSelectorProps接口来使用QueueUser而不是User
// 如果无法直接修改UserSelectorProps，则可以使用类型断言或适配器模式

export const UserByQueueSelector: React.FC<UserSelectorProps> = ({
  isOpen,
  onClose,
  anchorRef,
  onSelectUser,
  inModal = false,
  queueId,
}) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const [allUsers, setAllUsers] = useState<QueueUser[]>([]); // 存储所有用户的缓存列表
  const [displayedUsers, setDisplayedUsers] = useState<QueueUser[]>([]); // 显示的用户列表（可能经过过滤）
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const [selectedUser, setSelectedUser] = useState<QueueUser | null>(null);
  const { globalConfig } = useRole();
  // 窗口尺寸状态，用于重新计算位置
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  const handleSelectUser = (user: QueueUser) => {
    console.log('Selecting user:', user.name);
    setSelectedUser(user);
    onSelectUser(user);
  };

  const emailSkillFlow =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']?.emailSkillFlow ??
    false;

  // 处理窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 处理点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inModal) return;

      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        anchorRef.current &&
        !anchorRef.current.contains(event.target as Node)
      ) {
        console.log('Click outside, closing selector');
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose, inModal]);

  // 监听搜索查询变化，从缓存列表中过滤用户
  useEffect(() => {
    if (allUsers.length > 0) {
      // 如果搜索框为空，显示所有用户
      if (!searchQuery.trim()) {
        setDisplayedUsers(allUsers);
        return;
      }

      // 在缓存列表中过滤匹配用户
      const filtered = allUsers.filter((user) =>
        user.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setDisplayedUsers(filtered);
    }
  }, [searchQuery, allUsers]);

  // 初次加载时获取用户数据
  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    setError(null);
    try {
      const searchRequest: UserSearchReq = {
        queueId: queueId,
      };
      let response: any;
      if (emailSkillFlow) {
        response = await userSearchByQueue(`${basePath}`, searchRequest);
      } else {
        response = await queueMember(`${basePath}`, searchRequest);
      }

      // 处理新的返回值结构
      let userData: QueueUser[] = [];
      if (response.data && response.data.isSuccess) {
        // 找到匹配queueId的数据
        const queueData = response.data.data.find(
          (item: any) => item.queueId === queueId
        );
        if (queueData && queueData.items) {
          userData = queueData.items;
        }
      }
      let filteredUsers: QueueUser[];

      if (emailSkillFlow) {
        filteredUsers = userData.filter((user) => {
          const isActive = user?.state?.toLowerCase() === 'active';
          return isActive;
        });
      } else {
        filteredUsers = userData.filter((user) => {
          const isActive =
            user?.state?.toLowerCase() === 'active' &&
            user?.presenceStatus?.toLowerCase() === 'on queue';
          return isActive;
        });
      }

      setAllUsers(filteredUsers || []);
      setDisplayedUsers(filteredUsers || []);
    } catch (error) {
      setError(t('Error loading users. Please try again.'));
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !anchorRef.current) return null;

  // 获取按钮位置信息
  const rect = anchorRef.current.getBoundingClientRect();

  // 计算在屏幕上的最佳位置
  // 检查是否需要向左对齐（当接近屏幕右侧时）
  const nearRightEdge = rect.left + 240 > windowSize.width - 20; // 240px是选择器宽度，20px是右边距

  // 检查是否需要向上展开（当底部空间不足时）
  const bottomSpace = windowSize.height - rect.bottom;
  const needsToExpandUp = bottomSpace < 300; // 300px是选择器的预估高度

  // 在模态框内的定位逻辑
  const inModalStyle = nearRightEdge
    ? {
        position: 'absolute' as const,
        top: needsToExpandUp ? 'auto' : '100%',
        bottom: needsToExpandUp ? '100%' : 'auto',
        right: '0',
        zIndex: 20000,
      }
    : {
        position: 'absolute' as const,
        top: needsToExpandUp ? 'auto' : '100%',
        bottom: needsToExpandUp ? '100%' : 'auto',
        left: '0',
        zIndex: 20000,
      };

  // 在页面中的定位逻辑（固定定位）
  // 根据屏幕空间计算最佳位置
  const inPageStyle = {
    position: 'fixed' as const,
    top: needsToExpandUp ? 'auto' : `${rect.bottom + window.scrollY + 5}px`,
    bottom: needsToExpandUp
      ? `${windowSize.height - rect.top + window.scrollY - 5}px`
      : 'auto',
    left: nearRightEdge ? 'auto' : `${rect.left + window.scrollX}px`,
    right: nearRightEdge
      ? `${windowSize.width - rect.right - window.scrollX}px`
      : 'auto',
    zIndex: 10000,
  };

  // 根据是否在模态框内选择样式
  const style = inModal ? inModalStyle : inPageStyle;

  return (
    <div
      ref={dropdownRef}
      className={cn(
        'bg-white rounded-sm shadow-lg border border-grey-200',
        'min-w-[240px] w-[240px] max-h-[300px] overflow-hidden flex flex-col',
        'data-[state=open]:animate-in data-[state=closed]:animate-out',
        'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0'
      )}
      style={style}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      {/* Search Input */}
      <div className="p-2 border-b border-grey-200 flex-shrink-0">
        <div className="relative">
          <Input
            isSearch
            size="s"
            beforeIcon={<Icon name="search" />}
            placeholder={t('Search users...')}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e as string)}
            allowClear
            style={{ zIndex: 9999 }}
          />
        </div>
      </div>

      {/* Users List */}
      <div
        className="w-full flex flex-col overflow-y-auto flex-grow"
        style={{ maxHeight: '250px', zIndex: 9999 }}
      >
        {loading ? (
          <div className="py-6 w-full text-center text-grey-400">
            {t('Loading...')}
          </div>
        ) : error ? (
          <div className="py-6 w-full text-center text-status-danger">
            {error}
          </div>
        ) : displayedUsers.length === 0 ? (
          <div className="py-6 w-full text-center text-grey-400">
            {searchQuery
              ? t('No users found')
              : t('Start typing to search users')}
          </div>
        ) : (
          displayedUsers.map((user) => (
            <div
              key={user.id}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                handleSelectUser(user);
              }}
              className={cn(
                'relative flex w-full cursor-pointer select-none items-center',
                'py-2 px-3 text-body outline-none',
                'hover:bg-primary-100',
                selectedUser?.id === user.id &&
                  'bg-primary-100 hover:bg-primary-200'
              )}
            >
              <div className="flex items-center justify-between w-full">
                <span className="text-sm font-medium">{user.name}</span>
                {selectedUser?.id === user.id && (
                  <Icon
                    name="check"
                    className="text-primary-500"
                  />
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default UserByQueueSelector;
