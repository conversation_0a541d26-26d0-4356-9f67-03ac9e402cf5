import React, { useRef, useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Search, Loader2 } from 'lucide-react';
import {
  getQueueInfo,
  userSearch,
  queueMember,
  userSearchByQueue,
} from '../../../lib/api';
import {
  UserSearchReq,
  User,
  QueueUser,
} from '../../../types/microfrontendsConfig';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRole, useRouteHandler } from '@cdss-modules/design-system';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import { TWorkgroup } from '@cdss-modules/design-system/@types/Interaction';

type TransferType = 'user' | 'workgroup';

interface TransferButtonProps {
  onTransfer?: (id: string, type: TransferType) => void;
  onCancelTransfer?: () => void;
  disabled?: boolean;
  isLoading?: boolean;
  type?: string;
  transferTargetName?: string;
  transferTargetId?: string;
  queueId?: string;
}

const TransferButton: React.FC<TransferButtonProps> = ({
  onTransfer,
  onCancelTransfer,
  disabled,
  isLoading,
  type,
  transferTargetName,
  transferTargetId,
  queueId,
}) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const { globalConfig } = useRole();
  const [isOpen, setIsOpen] = useState(false);
  const [showTransferringPanel, setShowTransferringPanel] = useState(false);
  const [transferType, setTransferType] = useState<TransferType>('user');
  const [users, setUsers] = useState<User[]>([]);
  const [workgroups, setWorkgroups] = useState<TWorkgroup[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const buttonRef = useRef<HTMLDivElement>(null);
  const panelRef = useRef<HTMLDivElement>(null);
  const strokeColor = isOpen ? '#FFAC4A' : 'currentColor';
  const { userConfig } = useRole();

  const emailSkillFlow =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']?.emailSkillFlow ??
    false;

  // 面板位置状态
  const [panelPosition, setPanelPosition] = useState({
    top: 0,
    left: 0,
  });

  // 更新面板位置函数
  const updatePanelPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();

      // 检查尺寸是否有效
      if (rect.width > 0 && rect.height > 0) {
        setPanelPosition({
          top: rect.bottom + 5,
          left: rect.left,
        });
      }
    }
  };

  // 监听窗口大小变化和滚动事件
  useEffect(() => {
    const handleResize = () => {
      if (isOpen || showTransferringPanel) {
        updatePanelPosition();
      }
    };

    const handleScroll = () => {
      if (isOpen || showTransferringPanel) {
        updatePanelPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [isOpen, showTransferringPanel]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      // Ensure any open panels are closed when the component unmounts
      const transferringPanel = document.getElementById('transferring-panel');
      if (transferringPanel && transferringPanel.parentElement) {
        document.body.removeChild(transferringPanel.parentElement);
      }

      const selectionPanel = document.getElementById(
        'transfer-selection-panel'
      );
      if (selectionPanel && selectionPanel.parentElement) {
        document.body.removeChild(selectionPanel.parentElement);
      }
    };
  }, []);

  // 当状态变化为禁用时关闭面板
  useEffect(() => {
    if (disabled) {
      setIsOpen(false);
      setShowTransferringPanel(false);
    }
  }, [disabled]);

  // 显示转接面板时
  useEffect(() => {
    if (isLoading && transferTargetName) {
      setIsOpen(false);
      setShowTransferringPanel(true);

      // 添加小延迟确保面板已渲染
      setTimeout(() => {
        if (buttonRef.current) {
          updatePanelPosition();
        }
      }, 50);
    } else if (!isLoading) {
      setShowTransferringPanel(false);
    }
  }, [isLoading, transferTargetName]);

  // 打开面板后更新面板位置
  useEffect(() => {
    if (isOpen || showTransferringPanel) {
      updatePanelPosition();
    }
  }, [isOpen, showTransferringPanel]);

  // 获取workgroup数据的函数
  const fetchWorkgroups = async () => {
    try {
      const queueListData = await getQueueInfo(basePath, 'email');
      const queues = queueListData.data.data || [];
      // 转换数据格式以匹配 TWorkgroup 接口
      const formattedWorkgroups = queues.map((queue: any) => ({
        id: queue.id,
        name: queue.name,
      }));
      return formattedWorkgroups;
    } catch (error) {
      console.error('Error fetching workgroups:', error);
      return [];
    }
  };

  const handleSearch = async (query: string) => {
    setLoading(true);
    try {
      if (transferType === 'user') {
        const searchRequest: UserSearchReq = {
          keyword: query,
        };
        if (type !== 'email') {
          const response = await userSearch(`${basePath}`, searchRequest);
          const result = await response.data.data;
          setUsers(result || []);
        } else {
          const searchRequest: UserSearchReq = {
            queueId: queueId,
          };
          let response: any;
          if (emailSkillFlow) {
            response = await userSearchByQueue(`${basePath}`, searchRequest);
          } else {
            response = await queueMember(`${basePath}`, searchRequest);
          }

          // 处理新的返回值结构
          let userData: QueueUser[] = [];
          if (response.data && response.data.isSuccess) {
            // 找到匹配queueId的数据
            const queueData = response.data.data.find(
              (item: any) => item.queueId === queueId
            );
            if (queueData && queueData.items) {
              userData = queueData.items;
            }
          }

          // 过滤用户（保留"active"状态的用户）
          const filteredUsers = userData.filter((user) => {
            const isActive = user?.state?.toLowerCase() === 'active';
            return isActive;
          });

          setUsers(filteredUsers || []);
        }
      } else {
        // Workgroup search using getQueueInfo
        const allWorkgroups: any = await fetchWorkgroups();
        const filteredWorkgroups = query
          ? allWorkgroups.filter((workgroup: any) =>
              workgroup.name.toLowerCase().includes(query.toLowerCase())
            )
          : allWorkgroups;
        setWorkgroups(filteredWorkgroups);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTypeChange = async (type: TransferType) => {
    setTransferType(type);
    setSearchQuery('');
    if (type === 'workgroup') {
      // Load workgroups immediately without waiting for search
      const allWorkgroups = await fetchWorkgroups();
      setWorkgroups(allWorkgroups);
    } else {
      handleSearch('');
    }
  };

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      handleSearch(searchQuery);
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, transferType]);

  useEffect(() => {
    // Preload data based on type
    if (type === 'email') {
      // Preload workgroup data, but don't automatically switch to workgroup tab
      fetchWorkgroups().then((workgroups) => {
        setWorkgroups(workgroups);
      });
    }
  }, [type, basePath]);

  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };

  // Handle transfer action for both users and workgroups
  const handleTransferAction = (id: string, type: TransferType) => {
    onTransfer?.(id, type);
    setIsOpen(false);
    setShowTransferringPanel(true); // Show the transferring panel immediately
  };

  const renderUserActionButtons = (user: User) => {
    let isOnQueue =
      user.presence?.presenceDefinition?.systemPresence?.toLowerCase() ===
      'on queue';

    if (type === 'email') {
      if (emailSkillFlow) {
        isOnQueue = true;
      } else {
        isOnQueue = user.presenceStatus?.toLowerCase() === 'on queue';
      }
    }
    const isNotCurrentUser = user.id !== userConfig?.id;

    // Check if user has phone as primary contact method
    const hasPhoneContact = true;

    if (
      // Only render action buttons if user is on queue, not current user, and has phone number
      isOnQueue &&
      isNotCurrentUser &&
      hasPhoneContact
    ) {
      return (
        <button
          className="p-1 hover:bg-gray-100 rounded"
          onClick={(e) => {
            e.stopPropagation();
            handleTransferAction(user.id, 'user');
          }}
        >
          <TransferIcon />
        </button>
      );
    }
    return null;
  };

  const renderWorkgroupActionButtons = (workgroup: TWorkgroup) => {
    return (
      <button
        className="p-1 hover:bg-gray-100 rounded"
        onClick={(e) => {
          e.stopPropagation();
          handleTransferAction(workgroup.id, 'workgroup');
        }}
      >
        <TransferIcon />
      </button>
    );
  };

  const TransferIcon = () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="12"
        cy="12"
        r="12"
        fill="#2A69FF"
      />
      <path
        d="M14.2123 7.18349C14.441 6.94771 14.8229 6.93789 15.0642 7.16139L18.625 10.4672C19.125 10.9314 19.125 11.7125 18.625 12.1767L15.0642 15.4825C14.8229 15.706 14.441 15.6962 14.2123 15.4604C13.9836 15.2246 13.9937 14.8513 14.2349 14.6278L17.7957 11.322L14.2349 8.0161C13.9937 7.7926 13.9836 7.41927 14.2123 7.18349ZM10.6289 7.78523C10.6289 7.47576 10.8149 7.19332 11.1063 7.06806C11.3978 6.9428 11.7346 6.99437 11.9708 7.20068L15.9914 10.7374C16.1598 10.8872 16.2578 11.0985 16.2578 11.322C16.2578 11.5455 16.1598 11.7567 15.9914 11.9065L11.9708 15.4432C11.7346 15.652 11.3953 15.7036 11.1063 15.5759C10.8174 15.4481 10.6289 15.1682 10.6289 14.8587V13.2868H9.82478C8.49294 13.2868 7.41239 14.3429 7.41239 15.6446C7.41239 16.3913 7.73404 16.8211 7.97025 17.0372C8.10845 17.1625 8.21651 17.3319 8.21651 17.5161C8.21651 17.7838 7.99537 18 7.72147 18C7.65111 18 7.58075 17.9852 7.51793 17.9533C7.04801 17.7053 5 16.4625 5 13.6798C5 11.2925 6.98017 9.35711 9.42271 9.35711H10.6289V7.78523Z"
        fill="white"
      />
    </svg>
  );

  // Render transferring panel
  const renderTransferringPanel = () => {
    if (!showTransferringPanel) return null;

    return createPortal(
      <div
        id="transferring-panel"
        ref={panelRef}
        className="fixed bg-white rounded-lg shadow-lg border border-gray-200 z-50"
        style={{
          top: `${panelPosition.top}px`,
          left: `${panelPosition.left}px`,
          width: '20rem',
        }}
      >
        <div className="p-4">
          <div className="text-base font-medium mb-4">Transferring Call</div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              <div>
                <div className="text-sm font-medium">{transferTargetName}</div>
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCancelTransfer?.();
                setShowTransferringPanel(false);
              }}
              className="px-3 py-1 text-sm text-red-500 hover:bg-red-50 rounded-md transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>,
      document.body
    );
  };

  const renderTransferPanel = () => {
    if (!isOpen) return null;

    return createPortal(
      <div
        id="transfer-selection-panel"
        className="fixed bg-white rounded-lg shadow-lg border border-gray-200 z-50"
        style={{
          top: `${panelPosition.top}px`,
          left: `${panelPosition.left}px`,
          width: '20rem',
        }}
      >
        <div className="p-3 border-b border-gray-100">
          <div className="text-base font-medium mb-2">Transfer</div>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchInput}
              placeholder="Search"
              className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded-lg"
            />
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
        </div>

        {/* Tab display logic: show both tabs for email type, only User for non-email */}
        <div className="flex border-b border-gray-200">
          <button
            className={`flex-1 h-9 text-sm ${
              transferType === 'user'
                ? 'border-b-2 border-blue-500 text-blue-500 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => handleTypeChange('user')}
          >
            User
          </button>
          {type === 'email' && (
            <button
              className={`flex-1 h-9 text-sm ${
                transferType === 'workgroup'
                  ? 'border-b-2 border-blue-500 text-blue-500 font-medium'
                  : 'text-gray-500'
              }`}
              onClick={() => handleTypeChange('workgroup')}
            >
              Workgroup
            </button>
          )}
        </div>

        <div className="max-h-80 overflow-y-auto">
          {loading ? (
            <div className="p-4 text-center text-gray-500">
              {t('Loading...')}
            </div>
          ) : transferType === 'user' ? (
            // User list rendering
            users.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                {searchQuery
                  ? t('No users found')
                  : t('Start typing to search users')}
              </div>
            ) : (
              users.map((user) => (
                <div
                  key={user.id}
                  className="px-3 py-2 flex items-center justify-between hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center gap-2">
                    <div
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: getStatusColor(user) }}
                    />
                    <span className="text-sm">{user.name}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    {user.phone && (
                      <span className="text-xs text-gray-500">
                        {user.phone}
                      </span>
                    )}
                    {renderUserActionButtons(user)}
                  </div>
                </div>
              ))
            )
          ) : // Workgroup list rendering
          workgroups.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              {searchQuery
                ? t('No workgroups found')
                : t('No workgroups available')}
            </div>
          ) : (
            workgroups.map((workgroup) => (
              <div
                key={workgroup.id}
                className="px-3 py-2 flex items-center justify-between hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
              >
                <div className="flex items-center gap-2">
                  <span className="text-sm">{workgroup.name}</span>
                </div>
                {renderWorkgroupActionButtons(workgroup)}
              </div>
            ))
          )}
        </div>
      </div>,
      document.body
    );
  };

  useEffect(() => {
    const handleDocumentClick = (e: MouseEvent) => {
      // 如果点击的是按钮本身，不做任何处理（已在按钮的onClick事件中处理）
      if (buttonRef.current && buttonRef.current.contains(e.target as Node)) {
        return;
      }

      // 检查是否点击在各个面板内部
      const selectionPanel = document.getElementById(
        'transfer-selection-panel'
      );
      const transferringPanel = document.getElementById('transferring-panel');

      // 如果点击不在任何面板内部，关闭所有面板
      if (
        !(selectionPanel && selectionPanel.contains(e.target as Node)) &&
        !(transferringPanel && transferringPanel.contains(e.target as Node))
      ) {
        setIsOpen(false);
        setShowTransferringPanel(false);
      }
    };

    // 使用click而不是mousedown
    document.addEventListener('click', handleDocumentClick);
    return () => {
      document.removeEventListener('click', handleDocumentClick);
    };
  }, [isOpen, showTransferringPanel, isLoading]);

  const handleButtonClick = () => {
    if (disabled) return;

    if (isLoading) {
      // 当正在转接中时，点击按钮只显示转接中面板，不打开选择面板
      setIsOpen(false);
      setShowTransferringPanel(!showTransferringPanel);
    } else {
      // 正常状态下点击按钮打开选择面板
      setIsOpen(!isOpen);
      setShowTransferringPanel(false);
      // 如果打开选择面板，触发搜索
      if (!isOpen) {
        handleSearch(''); // 初始空搜索加载所有结果
      }
    }
  };

  const getStatusColor = (user: User) => {
    const systemPresence =
      user.presence?.presenceDefinition?.systemPresence?.toLowerCase();
    return systemPresence !== undefined && systemPresence !== null
      ? AGENT_STATUS_COLOR_MAP[systemPresence]
      : AGENT_STATUS_COLOR_MAP.default;
  };

  return (
    <div
      ref={buttonRef}
      className="relative"
    >
      <button
        onClick={handleButtonClick}
        className={`p-2 rounded-lg transition-colors relative ${
          disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
        }`}
        disabled={disabled}
      >
        {isLoading ? (
          <div className="w-6 h-6 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-t-transparent border-yellow-500 rounded-full animate-spin" />
          </div>
        ) : (
          <svg
            width="25"
            height="25"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`${disabled ? 'opacity-50' : ''}`}
            stroke={strokeColor}
          >
            <path
              d="M4.66667 4.66667H11.3333V11.3333"
              stroke={strokeColor}
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M4.66667 11.3333L11.3333 4.66667"
              stroke={strokeColor}
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </button>
      {!disabled && (
        <>
          {renderTransferringPanel()}
          {renderTransferPanel()}
        </>
      )}
    </div>
  );
};

export default TransferButton;
