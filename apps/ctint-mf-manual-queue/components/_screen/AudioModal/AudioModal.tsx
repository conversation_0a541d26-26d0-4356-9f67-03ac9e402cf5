import React from 'react';
import {
  Popup,
  PopupContent,
  PopupTrigger,
} from '@cdss-modules/design-system/components/_ui/Popup';
import {
  AudioFileLoader,
  AudioControls,
  AudioSeekBar,
} from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import { ManualQueueData } from '../../../types/microfrontendsConfig';
import { cn } from '@cdss-modules/design-system/lib/utils';

export const AudioModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  audioUrl: string;
  callInfo: ManualQueueData | undefined;
}> = ({ isOpen, onClose, audioUrl, callInfo }) => {
  if (!isOpen) return null;

  return (
    <Popup
      open={isOpen}
      onOpenChange={onClose}
    >
      <PopupContent
        className="sm:max-w-[800px] shadow-md"
        title={callInfo?.queueName}
      >
        {/* Info Section */}
        <div className={cn('px-4 py-3 border-b-2 border-primary-600')}>
          <div className={cn('font-medium text-black')}>
            From: {callInfo?.from}
          </div>
          <div className="text-grey-600 text-sm mt-1">
            {callInfo?.createdAt}
          </div>
        </div>

        {/* Audio Player */}
        <div className={cn('px-4 py-4')}>
          <AudioFileLoader audioSrc={audioUrl} />
          <div className="mt-2">
            <AudioControls />
            <AudioSeekBar />
          </div>
          <div className="mt-4 flex justify-center" />
        </div>
      </PopupContent>
    </Popup>
  );
};

export default AudioModal;
