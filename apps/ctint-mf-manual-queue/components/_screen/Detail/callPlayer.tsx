import React, { useState } from 'react';
import {
  AudioControls,
  AudioFileLoader,
  AudioSeekBar,
} from '@cdss-modules/design-system/components/_ui/AudioPlayer';
import {
  CallBack,
  toast,
  Toaster,
  useRole,
  useRouteHandler,
} from '@cdss-modules/design-system';
import { saveConversationRel } from '../../../lib/api';
import { VoiceMailInfo } from '../../../types/microfrontendsConfig';

interface CallPlayerProps {
  phoneNumber: string;
  audioUrl: string;
  conversation: VoiceMailInfo;
  isConnected: boolean;
}

interface ConversationRel {
  referenceConversationId: string;
  sourceConversationId: string;
}

const CallPlayer: React.FC<CallPlayerProps> = ({
  phoneNumber,
  audioUrl,
  conversation,
  isConnected,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { basePath } = useRouteHandler();

  const [error, setError] = useState<string | null>(null);

  const [hasActiveCall, setHasActiveCall] = useState(false);

  const { userConfig } = useRole();

  const handleCallBack = (conversationId: string) => {
    setIsLoading(true);
    setError(null);
    const conversationRel = {
      referenceConversationId: conversationId,
      sourceConversationId: conversation.conversationId,
    };
    saveConversationRel(basePath, conversationRel).catch((error) => {
      const errorMessage = error?.response?.data?.error || 'Operation Failed';
      toast({
        title: 'error',
        description: errorMessage,
        variant: 'error',
      });
    });
  };

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="text-center pt-16 pb-8">
        <h1 className="text-4xl font-medium tracking-wider">{phoneNumber}</h1>
      </div>

      <div className="px-8 mb-8">
        <AudioFileLoader
          key={audioUrl}
          audioSrc={audioUrl}
        />
        <div className="mt-6">
          <AudioControls />
          <AudioSeekBar />
        </div>
      </div>

      <div className="flex-1 flex flex-col items-center justify-center px-8">
        {conversation.callbackNumber && (
          <div className="w-[50%] mb-2 bg-gray-100 p-3 rounded-md text-center">
            <div className="font-medium text-gray-700">
              Callback Number : +{conversation.callbackNumber}
            </div>
          </div>
        )}
        <CallBack
          className="w-full"
          triggerClassName="w-[50%]"
          number={conversation.callbackNumber || phoneNumber}
          onCallBack={(res) => {
            handleCallBack(res);
          }}
        />
      </div>
      <Toaster />
    </div>
  );
};

export default CallPlayer;
