import React, { useEffect, useState } from 'react';
import {
  Loader,
  Toaster,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import {
  TransferReq,
  VoiceMailInfo,
} from '../../../types/microfrontendsConfig';
import { transfer, wrapUp } from '../../../lib/api';
import CallHeader from './callHeader';
import CallPlayer from './callPlayer';
import WrapUp from '@cdss-modules/design-system/components/_ui/WrapUp';
import {
  TWrapUpCodeQueueData,
  TWrapUpFormState,
  TWrapUpOption,
} from '@cdss-modules/design-system/@types/Interaction';
import { fireGetWrapupCategory } from '@cdss-modules/design-system/lib/api';
import CallBack from './callBack';
import CallBackDetail from './callBack';

interface CallDetailViewProps {
  voiceMailInfo: VoiceMailInfo;
  onRefresh: () => Promise<void>;
}

const CallDetailView: React.FC<CallDetailViewProps> = ({
  voiceMailInfo,
  onRefresh,
}) => {
  const { basePath } = useRouteHandler();
  const [showWrapUp, setShowWrapUp] = useState(false);
  const { toast } = useToast();
  const [isTransferring, setIsTransferring] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { userConfig } = useRole();
  const phoneNumber = voiceMailInfo?.from || '';
  const audioUrl = voiceMailInfo?.mp3URL || '';

  const type = voiceMailInfo?.type || '';

  useEffect(() => {
    if (voiceMailInfo) {
      setIsConnected(voiceMailInfo.selfParticipantState === 'connected');
      console.log('ParticipantState：', voiceMailInfo.selfParticipantState);
      console.log('Connected:', isConnected);

      setShowWrapUp(!(voiceMailInfo.selfParticipantState === 'connected'));
    }
  }, []);

  // 存储 WrapUp 表单状态
  const [wrapUpFormState, setWrapUpFormState] = useState<TWrapUpFormState>({
    selectedItems: [],
    notes: '',
    expandedItems: [],
    itemRemarks: [],
    activeRemarkItems: [],
  });
  const [wrapUpData, setWrapUpData] = useState<TWrapUpCodeQueueData[]>([]);
  const [wrapUpLoading, setWrapUpLoading] = useState(false);
  const [wrapUpError, setWrapUpError] = useState<string | null>(null);

  //转接
  const handleTransfer = async (userId: string, type: string) => {
    if (isTransferring) return;
    setIsTransferring(true);

    toast({
      title: 'Transfer in progress',
      description: 'Please wait while we process your transfer request...',
      variant: 'loading',
    });

    const transferReq: TransferReq = {
      transferType: type,
      conversationId: voiceMailInfo.conversationId,
      id: userId,
      selfQueueId: voiceMailInfo.selfQueueId,
      selfParticipantId: voiceMailInfo.selfParticipantId,
    };
    const result = await transfer(basePath, transferReq)
      .then((response) => {
        toast({
          title: 'Success',
          description: 'Transfer request has been successfully sent',
          variant: 'success',
        });
      })
      .catch((error) => {
        const errorMessage = error?.response?.data?.error || 'Operation Failed';
        toast({
          title: 'error',
          description: errorMessage,
          variant: 'error',
        });
      })
      .finally(async () => {
        await onRefresh();
        setIsTransferring(false);
      });
  };

  //查WrapUpCode
  useEffect(() => {
    if ((!showWrapUp && isConnected) || !userConfig?.id) return;
    setWrapUpLoading(true);
    fireGetWrapupCategory(userConfig.id, basePath)
      .then((response) => {
        const data = response.data.data;
        const filteredData = data.find(
          (queue: { queueId: string }) =>
            queue.queueId === voiceMailInfo?.selfQueueId
        );
        setWrapUpData(filteredData ? [filteredData] : []);
      })
      .catch((err) => {
        setWrapUpError('Failed to fetch wrap-up data');
        toast({
          title: 'Error',
          description: err?.response?.data?.error,
          variant: 'error',
        });
      })
      .finally(() => {
        setWrapUpLoading(false);
      });
  }, [voiceMailInfo?.selfQueueId]);
  // }, [showWrapUp, userConfig?.id, basePath]);

  //头部按钮 控制 wrapup组件是否打开
  const handleWrapUpClick = () => {
    if (!showWrapUp) {
      setShowWrapUp(true);
    }
  };

  //wrapup组件 回调函数。关闭用
  const handleWrapUpClose = () => {
    setShowWrapUp(false);
  };

  //处理 WrapUp 表单状态更新
  const handleWrapUpStateChange = (newState: TWrapUpFormState) => {
    setWrapUpFormState(newState);
  };

  //提交wrapup表单
  const handleWrapUpSubmit = async () => {
    if (!voiceMailInfo || !userConfig?.id) return;
    try {
      setLoading(true);
      setIsSubmitting(true);
      const findItemById = (
        items: TWrapUpOption[],
        targetId?: string
      ): TWrapUpOption | undefined => {
        if (!targetId) return undefined;
        for (const item of items) {
          if (item.id === targetId) return item;
          if (item.items) {
            const found = findItemById(item.items, targetId);
            if (found) return found;
          }
        }
        return undefined;
      };
      const wrapUpList = wrapUpFormState.selectedItems
        .map((itemId) => {
          const item = findItemById(wrapUpData[0]?.items || [], itemId);
          // 删除这里的 type 判断，所有被选中的项目都会被包含
          if (!item) return null;
          const itemRemark = wrapUpFormState.itemRemarks.find(
            (r) => r.itemId === itemId
          );
          return {
            wrapUpCode: item.code,
            wrapUpName: item.name,
            remark: itemRemark?.remark || '',
          };
        })
        .filter(Boolean);

      const payload = {
        wrapUpList,
        remark: wrapUpFormState.notes,
        participantId: voiceMailInfo.selfParticipantId,
        userId: userConfig.id,
        conversationId: voiceMailInfo.conversationId,
        queueId: voiceMailInfo.selfQueueId,
        state: 'disconnected',
      };
      await wrapUp(basePath, payload);
      setShowWrapUp(false);
      onRefresh();
      toast({
        title: 'Success',
        description: 'Wrap-up submitted successfully',
        variant: 'success',
      });
    } catch (error) {
      const errorMessage = 'Operation failed';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'error',
      });
    } finally {
      setLoading(false);
      setIsSubmitting(false);
    }
  };

  const onEndCall = () => {
    setIsConnected(false);
    const payload = {
      participantId: voiceMailInfo.selfParticipantId,
      state: 'disconnected',
      conversationId: voiceMailInfo.conversationId,
    };
    wrapUp(basePath, payload)
      .then((response) => {
        setIsConnected(false);
        setShowWrapUp(true);
        if (
          voiceMailInfo.selfParticipantWrapupPrompt === 'agentRequested' &&
          voiceMailInfo.selfParticipantWrapupRequired === false
        ) {
          onRefresh();
        }
      })
      .catch((err) => {
        setIsConnected(true);
        toast({
          title: 'Error',
          description: err?.response?.data?.error,
          variant: 'error',
        });
      });
  };

  if (!voiceMailInfo?.conversationId) {
    return <div className="text-gray-500">No conversation data available</div>;
  }

  return (
    <>
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={64} />
        </div>
      )}
      <div className="h-full flex flex-col bg-white border-r">
        <CallHeader
          phoneNumber={phoneNumber}
          onTransfer={handleTransfer}
          onWrapUp={handleWrapUpClick}
          showWrapUp={showWrapUp}
          isConnected={isConnected}
          voiceMailInfo={voiceMailInfo}
          onEndCall={onEndCall}
          cancelTransfer={() => {}}
        />
        <div className="flex-1 overflow-auto">
          {showWrapUp ? (
            <WrapUp
              onRevert={handleWrapUpClose}
              onSubmit={handleWrapUpSubmit}
              formState={wrapUpFormState}
              onFormStateChange={handleWrapUpStateChange}
              wrapUpData={wrapUpData}
              loading={wrapUpLoading}
              error={wrapUpError}
              isSubmit={!isConnected}
              className="h-full"
              isSubmitting={isSubmitting}
            />
          ) : type === 'callback' ? (
            <CallBackDetail
              phoneNumber={phoneNumber}
              conversation={voiceMailInfo}
              isConnected={isConnected}
            />
          ) : (
            <CallPlayer
              conversation={voiceMailInfo}
              phoneNumber={phoneNumber}
              audioUrl={audioUrl}
              isConnected={isConnected}
            />
          )}
        </div>
        <Toaster />
      </div>
    </>
  );
};

export default CallDetailView;
