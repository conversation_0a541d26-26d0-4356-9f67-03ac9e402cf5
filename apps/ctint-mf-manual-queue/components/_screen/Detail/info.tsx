import React, { useState } from 'react';
import {ManualQueueData, VoiceMailInfo} from '../../../types/microfrontendsConfig';

interface InfoPanelProps {
  voiceMailInfo: VoiceMailInfo | null;
}

const InfoPanel: React.FC<InfoPanelProps> = ({ voiceMailInfo }) => {
  const [activeTab, setActiveTab] = useState<'info' | 'history'>('info');

  return (
    <div className="bg-white">
      {/* 标签页头部 */}
      <div className="flex border-b">
        <button
          className={`flex-1 py-4 text-center font-medium ${
            activeTab === 'info'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500'
          }`}
          onClick={() => setActiveTab('info')}
        >
          Info
        </button>
        <button
          className={`flex-1 py-4 text-center font-medium ${
            activeTab === 'history'
              ? 'text-blue-600 border-b-2 border-blue-600'
              : 'text-gray-500'
          }`}
          onClick={() => setActiveTab('history')}
        >
          History
        </button>
      </div>

      {/* 内容区域 */}
      <div className="p-6">
        {activeTab === 'info' ? (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-gray-500">Customer Name</div>
                <div className="mt-1 text-gray-900">{'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Gender</div>
                <div className="mt-1 text-gray-900">{'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Preferred Title</div>
                <div className="mt-1 text-gray-900">{'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Contact Number</div>
                <div className="mt-1 text-gray-900">{'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Member Since</div>
                <div className="mt-1 text-gray-900">{'N/A'}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Email</div>
                <div className="mt-1 text-gray-900">
                  { 'N/A'}
                </div>
              </div>
            </div>

            {/* SAA 部分 */}
            <div className="mt-8">
              <h3 className="text-lg font-medium text-gray-900">SAA</h3>
              <div className="mt-4 space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">
                    1. First question content
                  </h4>
                  <p className="mt-1 text-gray-500">
                    The corresponding answer to the first question...
                  </p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">
                    2. Second question content
                  </h4>
                  <p className="mt-1 text-gray-500">
                    The corresponding answer to the second question...
                  </p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="p-4 border rounded-lg">
              <div className="text-sm text-gray-500">Previous Call History</div>
              {/* 这里可以添加历史记录列表 */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default InfoPanel;
