import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React, { useEffect, useRef, useState } from 'react';
import {
  Loader,
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  THEME_FONT_COLORS,
  useRouteHandler,
  MiniWallboard,
  Toaster,
  toast,
} from '@cdss-modules/design-system';
import { VoiceMailInfo } from '../../../../types/microfrontendsConfig';
import {
  getConversationInfo,
  getEmailConversationInfo,
} from '../../../../lib/api';
import EmailDetailView from './emailDetailView';
import CallInfo from '@cdss-modules/design-system/components/_ui/CallInfo';
import { TbarProvider } from '@cdss-modules/design-system/context/TBarContext';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import Saa from '@cdss-modules/design-system/components/_ui/Saa';
import { useCustomerHistory } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import { useRole } from '@cdss-modules/design-system';

const queryClient = new QueryClient();

const EmailContent = () => {
  const { searchParams, basePath, activePath } = useRouteHandler();
  const { globalConfig } = useRole();

  const [conversationId, setConversationId] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [data, setData] = useState<VoiceMailInfo>({});
  const [error, setError] = useState<boolean>(false);
  const { lastMessage, eventData } = useInteractionContext();
  const [isFullResizablePanel, setIsFullResizablePanel] =
    useState<boolean>(false);
  const { SAADetail, getSAADetail, SAADetailLoading } = useCustomerHistory();
  const activeTransferRef = useRef<{
    participantId: string | null;
    name: string | null;
  }>({
    participantId: null,
    name: null,
  });

  const hiddenMiniWallboard =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']
      ?.hiddenMiniWallboard ?? false;

  const enableSAA =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']?.enableSAA ?? false;
  const enableRightWindow =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']
      ?.enableRightWindow ?? false;
  const enableCallInfo =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']?.enableCallInfo ??
    false;

  const fetch = async () => {
    if (!conversationId) {
      return;
    }
    try {
      setIsLoading(true);
      const response = await getEmailConversationInfo(basePath, conversationId);
      setData(response.data.data);
      setError(false);
    } catch (err) {
      setError(true);
      console.error('Failed to fetch conversation:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    try {
      await fetch();
    } catch (err) {
      console.error('Failed to refresh conversation data:', err);
    }
  };

  useEffect(() => {
    if (activePath?.includes('/email')) {
      const urlConversationId = searchParams.get('conversationId');
      if (urlConversationId) {
        // 如果 conversationId 发生变化才设置新值
        if (urlConversationId !== conversationId) {
          setConversationId(urlConversationId);
        } else {
          //如果 conversationId 没变，但是进入了这个页面，也需要获取最新数据
          fetch();
        }
      }
    }
    console.log('email:', conversationId);
  }, [activePath, conversationId, searchParams]);

  //  WebSocket 事件处理器
  useEffect(() => {
    const messageData = lastMessage?.data;
    // 如果没有消息数据、消息是心跳检测或没有会话ID，则直接返回
    if (!messageData || messageData === 'ping' || !conversationId) return;

    try {
      // 解析 WebSocket 消息
      const formattedEvent = JSON.parse(messageData);

      // 验证事件格式并解析事件类型
      const eventString = formattedEvent.event;
      if (!eventString) return; // 如果没有事件字符串，直接返回

      // 将事件字符串按点分割为数组
      const eventParts = eventString.split('.');
      if (eventParts.length < 4) return; // 确保事件格式正确

      // 提取会话ID和事件类型
      const eventConvId = eventParts[1]; // 会话ID
      const eventType = eventParts[3]; // 事件类型 (如 connected, disconnected 等)

      // 只处理当前会话的事件
      if (eventConvId !== conversationId) return;

      // 获取事件数据
      const eventData = formattedEvent.eventData?.data;

      // 检查是否是当前用户的参与者ID
      const isSelfParticipant = (
        participantId: string | undefined
      ): boolean => {
        return participantId === data.selfParticipantId;
      };

      // 根据不同事件类型处理
      switch (eventType) {
        case 'connected': {
          // 检查是否为转接相关事件
          const activeConsultParticipant = eventData?.activeConsultParticipant;
          const currentParticipant = eventData?.currentParticipant;

          // 确认当前参与者是自己，只处理与当前用户相关的事件
          if (currentParticipant && !isSelfParticipant(currentParticipant.id)) {
            console.log('忽略不属于当前用户的连接事件');
            return; // 不是当前用户的事件，忽略
          }

          if (activeConsultParticipant) {
            // 这很可能是一个转接发起事件
            const transferParticipantId = activeConsultParticipant.id;
            const transferAgentName = activeConsultParticipant.name;

            // 确保这是一个从当前用户发起的转接
            if (
              currentParticipant &&
              isSelfParticipant(currentParticipant.id) &&
              transferParticipantId &&
              transferAgentName
            ) {
              console.log('检测到由当前用户发起的转接事件');

              // 直接更新本地数据中的转接信息
              setData((prevData) => ({
                ...prevData,
                transferParticipantId: transferParticipantId,
                transferAgentName: transferAgentName,
              }));

              // 更新我们的引用以跟踪活动的转接
              activeTransferRef.current = {
                participantId: transferParticipantId,
                name: transferAgentName,
              };

              // 显示转接开始的通知（使用英文）
              toast({
                title: 'Transfer Initiated',
                description: `Transferring to ${transferAgentName}`,
                variant: 'success',
              });

              return; // 不需要完整刷新
            }
          } else if (
            activeTransferRef.current.participantId &&
            currentParticipant
          ) {
            // 如果我们之前有活动的转接，但新事件中不包含它
            // 这很可能意味着转接已完成或被拒绝

            // 确保这是当前用户的事件
            if (isSelfParticipant(currentParticipant.id)) {
              console.log('检测到转接完成或拒绝事件');

              // 检查这个事件是否包含完成信号
              if (!eventData.activeConsultParticipant) {
                // 这表明转接完成/拒绝（使用英文）
                toast({
                  title: 'Transfer Completed',
                  description: `${
                    activeTransferRef.current.name
                      ? `Transfer to ${activeTransferRef.current.name}`
                      : 'Transfer'
                  } has been completed`,
                  variant: 'success',
                });

                // 清除转接信息 - 使用正确的TypeScript类型
                setData((prevData) => ({
                  ...prevData,
                  transferParticipantId: undefined,
                  transferAgentName: undefined,
                }));

                // 重置活动转接引用
                activeTransferRef.current = {
                  participantId: null,
                  name: null,
                };

                // 在这种情况下，应该进行完整刷新以获取更新的会话状态
                onRefresh();
                return;
              }
            }
          }

          // 对于其他连接事件，执行完整刷新
          onRefresh();
          break;
        }

        case 'disconnected': {
          // 获取断开连接的参与者ID
          const participantId = eventData?.participant?.id;

          // 如果是当前用户断开连接或没有指定参与者ID，刷新UI
          if (!participantId || isSelfParticipant(participantId)) {
            console.log('当前用户断开连接，更新UI');
            onRefresh();
          } else if (
            participantId === activeTransferRef.current.participantId
          ) {
            // 如果是转接目标断开连接，可能需要取消转接（使用英文）
            console.log('转接目标断开连接，可能需要取消转接');

            toast({
              title: 'Transfer Cancelled',
              description: `${activeTransferRef.current.name || 'Target agent'} has disconnected`,
              variant: 'warning',
            });

            // 清除转接信息 - 使用正确的TypeScript类型
            setData((prevData) => ({
              ...prevData,
              transferParticipantId: undefined,
              transferAgentName: undefined,
            }));

            // 重置活动转接引用
            activeTransferRef.current = {
              participantId: null,
              name: null,
            };

            onRefresh();
          }
          break;
        }

        default:
          // 对于任何其他事件，只有当它涉及当前用户时才刷新
          if (eventData?.participant?.id === data.selfParticipantId) {
            console.log(`处理与当前用户相关的事件：${eventType}`);
            onRefresh();
          }
          break;
      }
    } catch (error) {
      console.error('处理 WebSocket 消息时出错:', error);
    }
  }, [lastMessage]);

  if (isLoading) {
    return (
      <div
        style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 999,
        }}
      >
        <Loader size={64} />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ color: THEME_FONT_COLORS.status.success }}>
        {/*wrap up successfully*/}
      </div>
    );
  }

  return (
    <div className="relative flex flex-col h-full gap-4 justify-between">
      <ResizablePanelGroup
        autoSaveId="call-detail-panel"
        direction="horizontal"
        className="h-full flex-1 flex w-full gap-x-3 "
      >
        <ResizablePanel
          minSize={30}
          defaultSize={70}
        >
          <EmailDetailView
            key={conversationId}
            emailInfo={data}
            onRefresh={onRefresh}
          />
        </ResizablePanel>
        <ResizableHandle />
        {enableRightWindow && (
          <>
            <ResizablePanel
              minSize={30}
              defaultSize={70}
            >
              <ResizablePanelGroup
                autoSaveId="SAA-detail-panel"
                direction="vertical"
                className="h-full flex-1 flex w-full h-0 gap-x-3 "
              >
                {enableCallInfo && (
                  <>
                    <ResizablePanel
                      style={
                        isFullResizablePanel
                          ? {
                              flexGrow: 2000,
                              flexShrink: 1,
                              flexBasis: '0px',
                            }
                          : undefined
                      }
                    >
                      <CallInfo
                        customerData={{
                          conversationId: conversationId,
                          phoneNumber: data?.from || '',
                        }}
                        headerClass="p-4"
                      />
                    </ResizablePanel>
                  </>
                )}
                {enableSAA && (
                  <>
                    <ResizableHandle />
                    <ResizablePanel minSize={50}>
                      <Saa
                        data={{
                          SAADetail: SAADetail,
                          getSAADetail: getSAADetail,
                          convId: conversationId,
                          SAADetailLoading: SAADetailLoading,
                        }}
                        useSAAv2={true}
                        setIsFullResizablePanel={setIsFullResizablePanel}
                        isShowCopy={true}
                      />
                    </ResizablePanel>
                  </>
                )}
              </ResizablePanelGroup>
            </ResizablePanel>
          </>
        )}
      </ResizablePanelGroup>
      <TbarProvider>
        <MiniWallboard hidden={hiddenMiniWallboard} />
      </TbarProvider>
      <Toaster />
    </div>
  );
};

export default function Main() {
  return (
    <QueryClientProvider client={queryClient}>
      <EmailContent />
    </QueryClientProvider>
  );
}
