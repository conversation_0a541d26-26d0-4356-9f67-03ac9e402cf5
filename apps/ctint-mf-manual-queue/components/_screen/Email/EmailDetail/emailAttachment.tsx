import { useState } from 'react';
import {
  ExtendedEmailAttachment,
  AttachmentValidationResult,
  validateAttachment,
  validateEmailSize,
  getTotalAttachmentSize,
  formatFileSize,
  EMAIL_SIZE_LIMITS,
} from './emailAttachmentValidator';

// 修改接口，确保类型一致性
interface AttachmentAddResult {
  success: boolean;
  error?: string;
  needsUpload?: boolean;
}

// Define the hook return interface
interface UseEmailAttachmentsResult {
  attachments: ExtendedEmailAttachment[];
  setAttachments: React.Dispatch<
    React.SetStateAction<ExtendedEmailAttachment[]>
  >;
  deletedAttachmentIds: string[];
  setDeletedAttachmentIds: React.Dispatch<React.SetStateAction<string[]>>;
  addAttachment: (
    newAttachment: File | ExtendedEmailAttachment
  ) => AttachmentAddResult;
  removeAttachment: (id: string) => void;
  totalSize: number;
  attachmentWarning: string | null;
  clearWarning: () => void;
  validateBeforeSend: (emailContent: string) => {
    valid: boolean;
    error?: string;
  };
}

/**
 * 自定义React钩子，用于管理邮件附件和验证逻辑
 * @param initialAttachments 初始附件数组
 * @returns 附件管理方法和状态
 */
const useEmailAttachments = (
  initialAttachments: ExtendedEmailAttachment[] = []
): UseEmailAttachmentsResult => {
  // 附件相关状态
  const [attachments, setAttachments] =
    useState<ExtendedEmailAttachment[]>(initialAttachments);
  const [deletedAttachmentIds, setDeletedAttachmentIds] = useState<string[]>(
    []
  );
  const [attachmentWarning, setAttachmentWarning] = useState<string | null>(
    null
  );

  // 获取有效的（未删除的）附件
  const getValidAttachments = () => attachments.filter((att) => !att.isDeleted);

  // 计算总附件大小
  const totalSize = getTotalAttachmentSize(attachments);

  /**
   * 添加新附件并进行验证
   */
  const addAttachment = (
    newAttachment: File | ExtendedEmailAttachment
  ): AttachmentAddResult => {
    // 创建用于验证的文件对象
    const fileObj =
      newAttachment instanceof File
        ? newAttachment
        : {
            name:
              (newAttachment as ExtendedEmailAttachment).attachmentName ||
              'file',
            size:
              (newAttachment as ExtendedEmailAttachment).fileSize ||
              (newAttachment as ExtendedEmailAttachment).contentSizeBytes ||
              0,
          };

    // 验证附件
    const validation = validateAttachment(fileObj, getValidAttachments());

    if (!validation.valid) {
      // 确保返回的error是string类型，而不是null
      return {
        success: false,
        error: validation.error || 'Invalid attachment',
      };
    }

    // 如果有警告，设置警告信息
    if (validation.warning) {
      setAttachmentWarning(validation.warning);
    }

    // 如果已经是完整的附件对象，直接添加
    if (!(newAttachment instanceof File)) {
      setAttachments((prev) => [...prev, newAttachment]);
      return { success: true };
    }

    // 如果是File对象，表示需要上传
    return { success: true, needsUpload: true };
  };

  /**
   * 移除附件
   */
  const removeAttachment = (id: string) => {
    // 查找要删除的附件
    const attachmentToDelete = attachments.find((item) => item.id === id);

    if (attachmentToDelete) {
      // 如果需要，跟踪ID以便通知后端删除
      if (!attachmentToDelete.isDeleted) {
        setDeletedAttachmentIds((prev) => [...prev, id]);
      }

      // 在UI中标记为已删除
      setAttachments((prev) =>
        prev.map((item) =>
          item.id === id ? { ...item, isDeleted: true } : item
        )
      );

      // 根据新的附件列表更新警告
      updateWarnings(getValidAttachments().filter((att) => att.id !== id));
    }
  };

  /**
   * 根据当前附件列表更新警告
   */
  const updateWarnings = (attachmentList: ExtendedEmailAttachment[]) => {
    const currentTotalSize = getTotalAttachmentSize(attachmentList);

    if (currentTotalSize > EMAIL_SIZE_LIMITS.SIZE_WARNINGS.CRITICAL_THRESHOLD) {
      setAttachmentWarning(
        `Total attachment size (${formatFileSize(currentTotalSize)}) is approaching the limit. Consider using a file sharing service instead.`
      );
    } else if (
      currentTotalSize > EMAIL_SIZE_LIMITS.SIZE_WARNINGS.WARNING_THRESHOLD
    ) {
      setAttachmentWarning(
        `Total attachment size (${formatFileSize(currentTotalSize)}) may trigger spam filters.`
      );
    } else {
      setAttachmentWarning(null);
    }
  };

  /**
   * 发送邮件前的最终验证
   */
  const validateBeforeSend = (emailContent: string) => {
    const validAttachments = getValidAttachments();
    const sizeValidation = validateEmailSize(emailContent, validAttachments);

    if (!sizeValidation.valid) {
      return {
        valid: false,
        error:
          sizeValidation.error || 'The email exceeds the maximum size limit.',
      };
    }

    if (sizeValidation.warning) {
      setAttachmentWarning(sizeValidation.warning);
    }

    return { valid: true };
  };

  return {
    attachments,
    setAttachments,
    deletedAttachmentIds,
    setDeletedAttachmentIds,
    addAttachment,
    removeAttachment,
    totalSize,
    attachmentWarning,
    clearWarning: () => setAttachmentWarning(null),
    validateBeforeSend,
  };
};

export default useEmailAttachments;
