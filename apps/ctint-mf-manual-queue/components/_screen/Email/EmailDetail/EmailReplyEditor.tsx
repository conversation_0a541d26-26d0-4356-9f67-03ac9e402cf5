import React, { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';
import {
  copyEmailTemplateAttachment,
  handleFileUpload,
  saveEmailNote,
} from '../../../../lib/api';
import {
  toast,
  Toaster,
  useRole,
  useRouteHandler,
  useBlocking,
} from '@cdss-modules/design-system';
import DropzoneAttachmentButton from '@cdss-modules/design-system/components/_ui/DropzoneAttachmentButton';
import DropzoneAttachmentPage from '@cdss-modules/design-system/components/_ui/DropzoneAttachmentPage';
import {
  EmailAttachment,
  microfrontends,
  SendEmailRequest,
} from '../../../../types/microfrontendsConfig';
import { EmailItem } from './emailDetailView';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import RecipientField from './RecipientField';
import EmailEditor, {
  processHtmlForEditor,
  EmailEditorRef,
} from '../../../_ui/TiptapEditor/EmailEditor';
import TemplateSelector from '@cdss-modules/design-system/components/_ui/CannedResponses';
import DOMPurify from 'dompurify';
import { useToast } from '@cdss-modules/design-system';
import useEmailAttachments from './emailAttachment';
import { FileRejection } from 'react-dropzone';
import { log } from 'next/dist/server/typescript/utils';
import { sanitizeEmailBody } from '@cdss-modules/design-system/lib/utils';

// 扩展SendEmailRequest接口，添加删除附件IDs
interface ExtendedSendEmailRequest extends SendEmailRequest {
  deletedAttachmentIds?: string[];
}

// 扩展EmailAttachment类型，添加删除标记
interface ExtendedEmailAttachment extends EmailAttachment {
  isDeleted?: boolean;
}

// 添加 FileItem 类型定义，根据 DropzoneAttachmentPage 组件的需求
interface FileItem {
  id: string;
  filename: string;
  mime: string;
  contentSizeBytes: number;
  url?: string;
  isDeleted?: boolean;
}

interface EmailReplyEditorProps {
  emailData: EmailItem[];
  type: 'reply' | 'forward' | 'new' | 'reply-disconnected';
  onSaveDraft: (
    draftData: ExtendedSendEmailRequest,
    isDraft: boolean
  ) => Promise<void>;
  onSend: (emailData: ExtendedSendEmailRequest) => Promise<void>;
  onRefresh: () => Promise<void>;
}

const EmailReplyEditor: React.FC<EmailReplyEditorProps> = ({
  emailData,
  type,
  onSaveDraft,
  onSend,
  onRefresh,
}) => {
  const [subject, setSubject] = useState('');
  const [to, setTo] = useState<string[]>([]);
  const [cc, setCc] = useState<string[]>([]);
  const [bcc, setBcc] = useState<string[]>([]);
  const { basePath } = useRouteHandler();
  const [attachments, setAttachments] = useState<ExtendedEmailAttachment[]>([]);
  const [loading, setLoading] = useState(false);
  const [editorContent, setEditorContent] = useState('');
  const { toast } = useToast();
  const { handleTriggerBlocking } = useBlocking();
  // 存储已删除附件的ID，用于通知后端
  const [deletedAttachmentIds, setDeletedAttachmentIds] = useState<string[]>(
    []
  );
  const { globalConfig } = useRole();
  const [isSending, setIsSending] = useState(false);

  const [isAutoSavePending, setIsAutoSavePending] = useState(false); // 自动保存是否正在进行
  const autoSaveTimerRef = useRef<NodeJS.Timeout | null>(null);

  const [sessionFiles, setSessionFiles] = useState<File[]>([]);

  // 控制附件上传页面的显示
  const [showAttachmentPage, setShowAttachmentPage] = useState(false);

  // email模板模态框控制状态
  const [showEmailTemplateModal, setShowEmailTemplateModal] = useState(false);

  // 创建一个引用，以便访问EmailEditor组件的方法
  const editorRef = useRef<EmailEditorRef>(null);

  // 查找原始邮件和草稿邮件
  const originalEmail = emailData?.find(
    (email) => email.emailDirection === 'IN'
  );
  const outboundEmail = emailData?.find(
    (email) => email.emailDirection === 'OUT' && email.detail?.isDraft === 1
  );

  // 处理 HTML 内容转换为 Microsoft 邮件格式
  const convertToMicrosoftEmailFormat = (htmlContent: string) => {
    const template = `<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <style type="text/css">
      /* 默认样式 */
      body, p, div, span, a, table, td {
        font-family: 'Calibri', 'Segoe UI', Arial, sans-serif;
        font-size: 12pt;
      }
      p { margin:0; padding:0 0 1em 0; }
      table { border-collapse: collapse; }
      img { border: 0; line-height: 100%; outline: none; text-decoration: none; }
    </style>
  </head>
  <body>
    ${htmlContent}
  </body>
</html>`;
    return template.trim();
  };

  // 处理原始邮件内容，用于显示在邮件下方
  const getProcessedEmailContent = () => {
    if (!originalEmail) return '';
    const sender = JSON.parse(originalEmail.detail.emailSender);
    const toRecipients = JSON.parse(originalEmail.detail.emailToRecipient);
    const ccRecipients = originalEmail.detail.emailCcRecipient
      ? JSON.parse(originalEmail.detail.emailCcRecipient)
      : [];

    // 格式化原始邮件的日期
    const sentDate = new Date(originalEmail.detail.sentDateTime);
    const formattedDate = sentDate.toLocaleString();

    // 使用英文标签的邮件头部信息
    const headerHtml = `
    <div style="margin-bottom: 20px;">
      <div><strong>From:</strong> ${sender.emailAddress.address}</div>
      <div><strong>Subject:</strong> ${originalEmail.emailSubject}</div>
      <div><strong>Date:</strong> ${formattedDate}</div>
      <div><strong>To:</strong> ${toRecipients.map((r: { emailAddress: { address: any } }) => r.emailAddress.address).join(', ')}</div>
      ${ccRecipients.length > 0 ? `<div><strong>Cc:</strong> ${ccRecipients.map((r: { emailAddress: { address: any } }) => r.emailAddress.address).join(', ')}</div>` : ''}
    </div>
  `;

    let processedBody = originalEmail.detail.emailBody;
    const inlineAttachments =
      originalEmail.attachments?.items.filter((item) => item.isInLine) || [];

    // 处理内联图片，将CID引用替换为URL
    inlineAttachments.forEach((attachment) => {
      if (attachment.contentId && attachment.url) {
        const regex = new RegExp(`cid:${attachment.contentId}`, 'g');
        processedBody = processedBody.replace(regex, attachment.url);
      }
    });
    return headerHtml + processedBody;
  };

  // 处理引用的邮件内容，用于编辑器初始内容
  const getInitProcessedEmailContent = () => {
    console.log('originalEmail', originalEmail);
    if (!originalEmail) return '';

    const sender = JSON.parse(originalEmail.detail.emailSender);
    const toRecipients: any = JSON.parse(originalEmail.detail.emailToRecipient);
    const ccRecipients: any = originalEmail.detail.emailCcRecipient
      ? JSON.parse(originalEmail.detail.emailCcRecipient)
      : [];

    // 格式化日期
    const sentDate = new Date(originalEmail.detail.sentDateTime);
    const formattedDate = sentDate.toLocaleString();

    // 创建更适合TipTap的头部格式
    const headerHtml = `
  <div>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <p>&nbsp;</p>
    <hr style="border: none; border-top: 1px solid #e5e5e5; margin: 10px 0;" />
    <p style="margin: 0; color: #5a5a5a; font-size: 11pt;">
      <strong>From:</strong> ${sender.emailAddress.address}<br/>
      <strong>Sent:</strong> ${formattedDate}<br/>
      <strong>To:</strong> ${toRecipients.map((r: { emailAddress: { address: any } }) => r.emailAddress.address).join('; ')}<br/>
      ${ccRecipients.length > 0 ? `<strong>Cc:</strong> ${ccRecipients.map((r: { emailAddress: { address: any } }) => r.emailAddress.address).join('; ')}<br/>` : ''}
      <strong>Subject:</strong> ${originalEmail.emailSubject}
    </p>
    <p>&nbsp;</p>
  </div>
  `;

    let processedBody = originalEmail.detail.emailBody;

    // 处理内联图片
    const inlineAttachments =
      originalEmail.attachments?.items.filter((item) => item.isInLine) || [];

    inlineAttachments.forEach((attachment) => {
      if (attachment.contentId && attachment.url) {
        const imgTagRegex = new RegExp(
          `<img[^>]*src=["']cid:${attachment.contentId}["'][^>]*>`,
          'gi'
        );

        processedBody = processedBody.replace(imgTagRegex, (match) => {
          return match
            .replace(`cid:${attachment.contentId}`, attachment.url)
            .replace('<img', `<img title="${attachment.contentId}"`);
        });

        // 处理简单cid引用
        const simpleRegex = new RegExp(`cid:${attachment.contentId}`, 'g');
        processedBody = processedBody.replace(simpleRegex, attachment.url);
      }
    });

    // 在处理之前保存原始背景颜色
    // 解析HTML获取背景颜色
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = processedBody;

    // 处理带有背景颜色的元素
    const elementsWithBg = tempDiv.querySelectorAll(
      '[style*="background-color"]'
    );
    elementsWithBg.forEach((el: Element) => {
      const htmlEl = el as HTMLElement;
      const bgColor = htmlEl.style.backgroundColor;
      if (bgColor) {
        // 将背景颜色存储在数据属性中
        el.setAttribute('data-original-bgcolor', bgColor);
        // 确保背景色足够强烈
        (el as HTMLElement).setAttribute(
          'style',
          `${(el as HTMLElement).getAttribute('style') || ''}; background-color: ${bgColor} !important;`
        );
        // 添加特殊类
        el.classList.add('has-bg-color');
      }
    });

    // 同时处理具有bgcolor属性的元素
    const elementsWithBgAttr = tempDiv.querySelectorAll('[bgcolor]');
    elementsWithBgAttr.forEach((el) => {
      const bgColor = el.getAttribute('bgcolor');
      if (bgColor) {
        // 确保同时有两种属性以提高兼容性
        el.setAttribute('data-original-bgcolor', bgColor);
        // 直接设置内联样式
        (el as HTMLElement).style.backgroundColor = bgColor;
        // 添加特殊类
        el.classList.add('has-bg-color');
      }
    });

    // 获取修改后的HTML
    processedBody = tempDiv.innerHTML;

    // 现在使用你现有的函数处理HTML
    processedBody = processHtmlForEditor(processedBody);

    return headerHtml + `<div>${processedBody}</div>`;
  };

  // 处理已保存的草稿内容
  const getInitDraftProcessedEmailContent = () => {
    if (!outboundEmail) return '';

    let processedBody = outboundEmail.detail.emailBody;

    if (!processedBody) {
      return '';
    }
    const inlineAttachments =
      outboundEmail?.attachments?.items.filter((item) => item.isInLine) || [];

    // 处理内联图片，将CID引用替换为URL
    inlineAttachments.forEach((attachment) => {
      if (attachment.contentId && attachment.url) {
        const regex = new RegExp(`cid:${attachment.contentId}`, 'g');
        processedBody = processedBody.replace(regex, attachment.url);
      }
    });

    return processHtmlForEditor(processedBody);
  };

  // 图片处理方法
  const addImage = () => {
    const input = document.createElement('input');
    input.setAttribute('type', 'file');
    input.setAttribute('accept', 'image/*');
    input.click();

    input.onchange = async () => {
      if (!input.files || !input.files[0]) return;

      const formData = new FormData();
      formData.append('files', input.files[0]);

      try {
        setLoading(true);
        const response = await handleFileUpload(basePath, formData);
        if (response.data?.data?.[0]?.success) {
          // 将上传的图片添加到附件列表，并设置为内联
          const attachmentData = response.data.data[0];
          attachmentData.isInLine = true;
          attachmentData.contentId = uuidv4();
          setAttachments((prev) => [...prev, attachmentData]);

          // 创建一个临时图片对象，获取自然尺寸
          const tempImg = new Image();
          tempImg.onload = () => {
            const naturalWidth = tempImg.naturalWidth;
            const naturalHeight = tempImg.naturalHeight;

            // 使用editorRef直接插入图片到编辑器，包含宽高属性
            if (editorRef.current) {
              editorRef.current.insertImage(
                attachmentData.url,
                attachmentData.contentId,
                attachmentData.attachmentName || 'image',
                naturalWidth, // 传递宽度
                naturalHeight // 传递高度
              );
            }
          };
          tempImg.src = attachmentData.url;
        }
      } catch (error) {
        console.error('Upload failed:', error);
      } finally {
        setLoading(false);
      }
    };
  };

  // 在组件加载时初始化数据
  useEffect(() => {
    if (emailData) {
      if (outboundEmail) {
        // 如果有草稿邮件，使用它的所有信息
        setSubject(outboundEmail.emailSubject || '');

        if (outboundEmail.detail?.emailToRecipient) {
          const toRecipients = JSON.parse(
            outboundEmail.detail.emailToRecipient
          );
          setTo(toRecipients.map((r: any) => r.emailAddress.address));
        }

        if (outboundEmail.detail?.emailCcRecipient) {
          const ccRecipients = JSON.parse(
            outboundEmail.detail.emailCcRecipient
          );
          setCc(ccRecipients.map((r: any) => r.emailAddress.address));
        }

        if (outboundEmail.detail?.emailBccRecipient) {
          const bccRecipients = JSON.parse(
            outboundEmail.detail.emailBccRecipient
          );
          setBcc(bccRecipients.map((r: any) => r.emailAddress.address));
        }

        if (outboundEmail.attachments) {
          setAttachments(outboundEmail.attachments.items);
        }
        if (outboundEmail.detail.emailBody !== '') {
          // 初始化编辑器内容为草稿内容
          setEditorContent(getInitDraftProcessedEmailContent());
        } else {
          setEditorContent(getInitProcessedEmailContent());
        }
      } else if (originalEmail) {
        // 如果是回复/转发但没有草稿，则创建新的回复内容
        if (type === 'reply' || type === 'forward') {
          // 设置回复的主题
          const prefix = type === 'reply' ? 'Re: ' : 'Fw: ';
          setSubject(prefix + (originalEmail.emailSubject || ''));

          // 如果是回复，自动填充收件人(原发件人)
          if (type === 'reply') {
            const sender = JSON.parse(originalEmail.detail.emailSender);
            setTo([sender.emailAddress.address]);
          }

          // 添加引用原始邮件内容
          setEditorContent(getInitProcessedEmailContent());
        }
      }
    }
  }, [emailData, type]);

  // 处理编辑器内容变化
  // const handleContentChange = (html: string) => {
  //   setEditorContent(html);
  // };
  const handleContentChange = (html: string) => {
    setEditorContent(html);
    console.log('有变化');
    // 清除之前的定时器
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
    }

    // 设置新的定时器，3秒后自动保存
    autoSaveTimerRef.current = setTimeout(() => {
      console.log('有变化----》自动保存');
      // 设置保存进行中标志
      setIsAutoSavePending(true);
      console.log('有变化----》自动保存--->saveDraft');
      saveDraft(true);
      setIsAutoSavePending(false);
    }, 1000); // 1秒后触发
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
  };
  const ensureImageDimensions = (html: string): string => {
    if (!html) return html;

    // 更全面的正则表达式，可以捕获所有img标签，不管属性顺序如何
    const imgRegex = /<img\s[^>]*>/g;

    return html.replace(imgRegex, (imgTag) => {
      // 提取所有可能的尺寸信息
      const widthMatch = imgTag.match(/width="([^"]+)"/);
      const heightMatch = imgTag.match(/height="([^"]+)"/);
      const styleMatch = imgTag.match(/style="([^"]+)"/);

      // 如果存在，从style属性中提取尺寸
      const styleWidthMatch =
        styleMatch && styleMatch[1].match(/width:\s*([\d.]+)(px|%|em|rem)?/i);
      const styleHeightMatch =
        styleMatch && styleMatch[1].match(/height:\s*([\d.]+)(px|%|em|rem)?/i);

      // 从任何可用来源获取尺寸，优先使用属性而非样式
      const width = widthMatch
        ? widthMatch[1]
        : styleWidthMatch
          ? `${styleWidthMatch[1]}${styleWidthMatch[2] || 'px'}`
          : null;
      const height = heightMatch
        ? heightMatch[1]
        : styleHeightMatch
          ? `${styleHeightMatch[1]}${styleHeightMatch[2] || 'px'}`
          : null;

      if (!width && !height) return imgTag; // 没有尺寸需要保留

      // 在保持现有标签结构的同时确保尺寸存在
      let newImgTag = imgTag;

      // 确保宽度和高度属性存在
      if (width && !widthMatch) {
        newImgTag = newImgTag.replace(/<img/, `<img width="${width}"`);
      }

      if (height && !heightMatch) {
        newImgTag = newImgTag.replace(/<img/, `<img height="${height}"`);
      }

      // 确保尺寸也在style属性中，以提高电子邮件客户端兼容性
      if ((width || height) && styleMatch) {
        let styleContent = styleMatch[1];
        if (width && !styleContent.includes('width:')) {
          styleContent += `; width: ${width}`;
        }
        if (height && !styleContent.includes('height:')) {
          styleContent += `; height: ${height}`;
        }
        newImgTag = newImgTag.replace(
          /style="([^"]+)"/,
          `style="${styleContent}"`
        );
      } else if ((width || height) && !styleMatch) {
        // 如果缺少style属性，添加它
        let style = '';
        if (width) style += `width: ${width}; `;
        if (height) style += `height: ${height}; `;
        newImgTag = newImgTag.replace(/<img/, `<img style="${style.trim()}"`);
      }

      return newImgTag;
    });
  };
  const handleSend = async () => {
    // 取消任何等待中的自动保存定时器
    if (autoSaveTimerRef.current) {
      clearTimeout(autoSaveTimerRef.current);
      autoSaveTimerRef.current = null;
    }
    setIsSending(true);
    // 重置自动保存进行中标志
    setIsAutoSavePending(false);

    const allEmails = [...to, ...cc, ...bcc].join(';');

    // 如果有邮箱地址，检查是否被阻止
    if (allEmails) {
      const isBlocked = await handleTriggerBlocking(allEmails, 'email');
      if (isBlocked) {
        // 如果被阻止，handleTriggerBlocking 已经显示了弹窗，直接返回
        return;
      }
    }

    // 确保从编辑器获取最新内容
    let processedContent = editorRef.current
      ? editorRef.current.getContent()
      : editorContent;

    // 处理内联图片，将URL替换为CID格式
    // 改进的正则表达式，可以捕获更多图片标签的情况
    const imgRegex = /<img\s[^>]*>/g;
    const matches = [...processedContent.matchAll(imgRegex)];

    // 提取所有内联图片的contentId
    const existingContentIds = new Set();
    matches.forEach((match) => {
      const imgTag = match[0];
      const titleMatch = imgTag.match(/title="([^"]+)"/);
      const contentId = titleMatch ? titleMatch[1] : null;
      if (contentId) {
        existingContentIds.add(contentId);
      }
    });

    // 处理附件和删除的附件
    const validAttachments: any[] = [];
    const deletedIds = [...deletedAttachmentIds];

    // 标记已删除的内联图片
    const updatedAttachments = attachments.map((attachment) => {
      const attachmentCopy = { ...attachment };

      if (
        attachmentCopy.isInLine &&
        attachmentCopy.contentId &&
        !existingContentIds.has(attachmentCopy.contentId)
      ) {
        attachmentCopy.isDeleted = true;

        if (!deletedIds.includes(attachmentCopy.id)) {
          deletedIds.push(attachmentCopy.id);
        }
      }

      return attachmentCopy;
    });

    // 筛选有效附件
    updatedAttachments.forEach((attachment) => {
      if (!attachment.isDeleted) {
        validAttachments.push(attachment);
      }
    });

    // 验证邮件大小，限制不能超过150MB
    const totalAttachmentSize = validAttachments.reduce(
      (total, attachment) => total + (attachment.fileSize || 0),
      0
    );

    const MAX_ATTACHMENT_SIZE = 150 * 1024 * 1024;

    if (totalAttachmentSize > MAX_ATTACHMENT_SIZE) {
      toast({
        variant: 'error',
        title: 'ERROR',
        description: `Total attachment size (${formatFileSize(totalAttachmentSize)}) exceeds the maximum limit (150MB). Please remove some attachments before sending.`,
      });
      return;
    }

    // 处理每个内联图片，替换URL为CID格式，但保留所有其他属性
    processedContent = processedContent.replace(imgRegex, (imgTag) => {
      const srcMatch = imgTag.match(/src="([^"]+)"/);
      const titleMatch = imgTag.match(/title="([^"]+)"/);

      if (!srcMatch || !titleMatch) return imgTag;

      const imgSrc = srcMatch[1];
      const contentId = titleMatch[1];

      if (!imgSrc || !contentId) return imgTag;

      const attachment = updatedAttachments.find(
        (item) => item.contentId === contentId && !item.isDeleted
      );

      if (attachment) {
        // 只替换src属性，保留所有其他属性包括宽高和样式
        return imgTag.replace(`src="${imgSrc}"`, `src="cid:${contentId}"`);
      }

      return imgTag;
    });

    // 确保图片尺寸属性被保留
    processedContent = ensureImageDimensions(processedContent);

    // 转换为Microsoft邮件格式
    const microsoftFormattedContent =
      convertToMicrosoftEmailFormat(processedContent);

    const processedValidAttachments = validAttachments.map((attachment) => {
      // 如果是临时ID，移除ID属性
      if (attachment.id && attachment.id.startsWith('temp-')) {
        const attachmentCopy = { ...attachment };
        delete attachmentCopy.id; // 删除id属性
        return attachmentCopy;
      }
      return attachment;
    });

    // 更新附件状态
    setAttachments(updatedAttachments);

    const filteredDeletedIds = deletedIds.filter(
      (id) => !id.startsWith('temp-')
    );
    setDeletedAttachmentIds(deletedIds);

    // 发送邮件
    onSend({
      outboundType: type,
      subject: subject,
      content: microsoftFormattedContent,
      to: to,
      cc: cc,
      bcc: bcc,
      attachmentList: processedValidAttachments,
      deletedAttachmentIds: filteredDeletedIds,
    })
      .then(onRefresh)
      .finally(() => {
        // 无论成功还是失败，都重新启用按钮
        setIsSending(false);
      });
  };
  const saveDraft = (isAutoSave = false) => {
    // 确保从编辑器获取最新内容
    let processedContent = editorRef.current
      ? editorRef.current.getContent()
      : editorContent;

    console.log('处理前 email body', processedContent);

    // 处理内联图片，将URL替换为CID格式
    // 改进的正则表达式，可以捕获更多图片标签的情况
    const imgRegex = /<img\s[^>]*>/g;
    const matches = [...processedContent.matchAll(imgRegex)];

    // 提取所有内联图片的contentId
    const existingContentIds = new Set();
    matches.forEach((match) => {
      const imgTag = match[0];
      const titleMatch = imgTag.match(/title="([^"]+)"/);
      const contentId = titleMatch ? titleMatch[1] : null;
      if (contentId) {
        existingContentIds.add(contentId);
      }
    });

    // 处理附件和删除的附件
    const validAttachments: any[] = [];
    const deletedIds = [...deletedAttachmentIds];

    // 标记已删除的内联图片
    const updatedAttachments = attachments.map((attachment) => {
      const attachmentCopy = { ...attachment };

      if (
        attachmentCopy.isInLine &&
        attachmentCopy.contentId &&
        !existingContentIds.has(attachmentCopy.contentId)
      ) {
        attachmentCopy.isDeleted = true;

        if (!deletedIds.includes(attachmentCopy.id)) {
          deletedIds.push(attachmentCopy.id);
        }
      }

      return attachmentCopy;
    });

    // 筛选有效附件
    updatedAttachments.forEach((attachment) => {
      if (!attachment.isDeleted) {
        validAttachments.push(attachment);
      }
    });

    const processedValidAttachments = validAttachments.map((attachment) => {
      // 如果是临时ID，移除ID属性
      if (attachment.id && attachment.id.startsWith('temp-')) {
        const attachmentCopy = { ...attachment };
        delete attachmentCopy.id; // 删除id属性
        return attachmentCopy;
      }
      return attachment;
    });

    // 处理每个内联图片，替换URL为CID格式，但保留所有其他属性
    processedContent = processedContent.replace(imgRegex, (imgTag) => {
      const srcMatch = imgTag.match(/src="([^"]+)"/);
      const titleMatch = imgTag.match(/title="([^"]+)"/);

      if (!srcMatch || !titleMatch) return imgTag;

      const imgSrc = srcMatch[1];
      const contentId = titleMatch[1];

      if (!imgSrc || !contentId) return imgTag;

      const attachment = updatedAttachments.find(
        (item) => item.contentId === contentId && !item.isDeleted
      );

      if (attachment) {
        // 只替换src属性，保留所有其他属性包括宽高和样式
        return imgTag.replace(`src="${imgSrc}"`, `src="cid:${contentId}"`);
      }

      return imgTag;
    });
    console.log('处理每个内联图片 email body', processedContent);

    // 确保图片尺寸属性被保留
    processedContent = ensureImageDimensions(processedContent);

    console.log('确保图片尺寸属性被保留 email body', processedContent);

    // 转换为Microsoft邮件格式
    const microsoftFormattedContent =
      convertToMicrosoftEmailFormat(processedContent);

    // 更新附件状态
    setAttachments(updatedAttachments);

    const filteredDeletedIds = deletedIds.filter(
      (id) => !id.startsWith('temp-')
    );
    setDeletedAttachmentIds(deletedIds);

    // 保存草稿
    const savePromise = onSaveDraft(
      {
        outboundType: type,
        subject: subject,
        content: microsoftFormattedContent,
        to: to,
        cc: cc,
        bcc: bcc,
        attachmentList: processedValidAttachments,
        deletedAttachmentIds: filteredDeletedIds,
      },
      isAutoSave
    );

    if (!isAutoSave) {
      savePromise.then(onRefresh);
    } else {
      // For auto-save, we might want to log or show a subtle notification instead
      savePromise.then(() => {
        console.log('Auto-save completed successfully');
      });
    }
  };

  // 将上传的文件转换为EmailAttachment格式
  const convertFileToAttachment = (
    file: File
  ): Promise<ExtendedEmailAttachment> => {
    return new Promise((resolve, reject) => {
      const formData = new FormData();
      formData.append('files', file);

      handleFileUpload(basePath, formData)
        .then((response) => {
          if (response.data?.data?.[0]?.success) {
            resolve(response.data.data[0]);
          } else {
            reject(new Error('文件上传失败'));
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  const handleSelectFiles = (files: File[]) => {
    if (files.length === 0) return;
    // 将用户选择的文件添加到临时会话中
    setSessionFiles((prevFiles) => [...prevFiles, ...files]);
  };

  const handleUploadComplete = async () => {
    if (sessionFiles.length === 0) {
      setShowAttachmentPage(false);
      return;
    }

    setLoading(true);
    try {
      // 上传所有文件到服务器
      const uploadPromises = sessionFiles.map((file) =>
        convertFileToAttachment(file)
      );
      const newAttachments = await Promise.all(uploadPromises);

      // 确保所有新附件都有ID，如果没有，添加一个临时ID
      const processedAttachments = newAttachments.map((attachment) => {
        if (!attachment.id) {
          // 使用uuidv4生成一个临时ID
          return {
            ...attachment,
            id: `temp-${uuidv4()}`,
          };
        }
        return attachment;
      });

      // 添加到附件列表
      setAttachments((prev) => [...prev, ...processedAttachments]);

      // 清空会话文件列表
      setSessionFiles([]);

      // 关闭附件上传页面
      setShowAttachmentPage(false);
    } catch (error) {
      console.error('Upload failed:', error);
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Failed to upload files. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFileRejected = (fileRejections: FileRejection[]) => {
    const errorMessages = fileRejections.map(
      (rejection) =>
        `${rejection.file.name}: ${rejection.errors[0]?.message || 'Unknown error'}`
    );

    // 显示英文错误提示
    toast({
      variant: 'error',
      title: 'File Validation Failed',
      description: errorMessages.join('\n'),
    });
  };

  // 附件上传取消处理函数
  const handleCancel = () => {
    // 清空会话文件
    setSessionFiles([]);
    setShowAttachmentPage(false);
  };

  const convertSessionFilesToFileItems = (): FileItem[] => {
    return sessionFiles.map((file, index) => ({
      id: `temp-${index}`, // 临时ID，真正上传后会被替换
      filename: file.name,
      mime: file.type || 'application/octet-stream',
      contentSizeBytes: file.size || 0,
      url: URL.createObjectURL(file), // 创建临时URL用于预览
      isDeleted: false,
    }));
  };

  // 更新临时文件的删除函数
  const handleRemoveSessionFile = (id: string) => {
    // 从临时ID中提取索引
    const index = parseInt(id.replace('temp-', ''));
    if (!isNaN(index) && index >= 0 && index < sessionFiles.length) {
      setSessionFiles((prevFiles) => prevFiles.filter((_, i) => i !== index));
    }
  };

  // 处理文件删除
  const handleRemoveFile = (id: string) => {
    console.log('handleRemoveFile', id);
    // 查找要删除的附件
    const attachmentToDelete = attachments.find((item) => item.id === id);

    if (attachmentToDelete) {
      // 如果附件是从服务器已有的文件中删除的（不是刚刚上传的），添加到删除ID列表
      if (!attachmentToDelete.isDeleted) {
        setDeletedAttachmentIds((prev) => [...prev, id]);
      }

      // 将附件标记为已删除，但不从列表中移除
      setAttachments((prev) =>
        prev.map((item) =>
          item.id === id ? { ...item, isDeleted: true } : item
        )
      );
    } else {
      // 处理临时附件的情况 - 检查是否是以 'temp-' 开头的ID
      if (id.startsWith('temp-')) {
        // 这是一个临时文件 ID，使用 handleRemoveSessionFile 函数来处理它
        handleRemoveSessionFile(id);
      } else {
        console.warn(`未找到要删除的附件: ${id}`);
      }
    }
  };

  // 渲染附件列表
  const renderAttachments = () => {
    // 过滤出 isInLine == false 且 isDeleted != true 的附件
    const nonInlineAttachments = attachments.filter(
      (attachment) => !attachment.isInLine && !attachment.isDeleted
    );

    // 如果没有有效的非内联附件，则不渲染任何内容
    if (nonInlineAttachments.length === 0) return null;

    return (
      <div className="px-4 py-2 border-t">
        <div className="flex flex-wrap items-center gap-2">
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M15.6253 2.98299C14.6457 2.00371 13.0558 2.00371 12.0762 2.98299L4.68887 10.3677C2.99863 12.0574 2.99863 14.7946 4.68887 16.4842C6.37912 18.1739 9.11724 18.1739 10.8075 16.4842L16.91 10.3838C17.3477 9.94632 18.0623 9.94632 18.4999 10.3838C18.9375 10.8213 18.9375 11.5356 18.4999 11.9731L12.3974 18.0735C9.82786 20.6422 5.66849 20.6422 3.099 18.0735C0.529501 15.5049 0.529501 11.347 3.099 8.77841L10.4863 1.39367C12.3452 -0.464556 15.3563 -0.464556 17.2152 1.39367C19.074 3.25189 19.074 6.26198 17.2152 8.1202L10.1491 15.1839C9.00081 16.3317 7.13792 16.3317 5.98968 15.1839C4.84144 14.036 4.84144 12.1738 5.98968 11.0259L11.771 5.24658C12.2087 4.80911 12.9233 4.80911 13.3609 5.24658C13.7985 5.68404 13.7985 6.39844 13.3609 6.8359L7.57956 12.6153C7.31056 12.8842 7.31056 13.3256 7.57956 13.5945C7.84855 13.8634 8.29018 13.8634 8.55918 13.5945L15.6253 6.53088C16.6049 5.5516 16.6049 3.96227 15.6253 2.98299Z"
              fill="black"
            />
          </svg>
          {nonInlineAttachments.map((attachment, index) => (
            <div
              key={index}
              className="flex items-center gap-2 px-3 py-1 bg-gray-50 rounded-lg"
            >
              <span className="text-sm text-gray-600">
                {attachment.attachmentName}
              </span>
              <button
                type="button"
                onClick={() => handleRemoveFile(attachment.id)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 处理模板选择
  const handleSelectTemplate = async (
    content: string,
    templateId: string,
    attachments: any[] | null
  ) => {
    // 首先处理模板内容
    const processedContent = processHtmlForEditor(content);

    // 如果存在附件，先处理它们
    if (attachments && attachments.length > 0) {
      // 获取当前邮件ID
      const emailId = outboundEmail?.id;

      if (emailId) {
        try {
          // 准备请求数据
          const templateAttachmentIds = attachments.map(
            (attachment) => attachment.id
          );

          // 使用导入的API函数复制邮件模板附件
          const response = await copyEmailTemplateAttachment(basePath, {
            templateAttachmentIds: templateAttachmentIds,
            emailId: emailId,
          });

          // 处理响应结果
          if (response.data?.isSuccess && response.data?.data) {
            // 获取新复制的附件列表
            const newAttachments: EmailAttachment[] = response.data.data;

            // 将新附件添加到当前附件列表，避免重复
            setAttachments((prevAttachments: ExtendedEmailAttachment[]) => [
              ...prevAttachments,
              ...newAttachments.filter(
                (newAtt: EmailAttachment) =>
                  !prevAttachments.some(
                    (existingAtt: ExtendedEmailAttachment) =>
                      existingAtt.id === newAtt.id
                  )
              ),
            ]);

            // 处理内联附件
            let contentWithInlineImages = processedContent;

            // 识别内联附件
            const inlineAttachments = newAttachments.filter(
              (attachment) => attachment.isInLine && attachment.contentId
            );

            // 将内容中的CID引用替换为实际URL
            inlineAttachments.forEach((attachment) => {
              if (attachment.contentId && attachment.url) {
                // 替换所有cid:{contentId}为实际URL
                const cidRegex = new RegExp(
                  `src="cid:${attachment.contentId}"`,
                  'g'
                );
                contentWithInlineImages = contentWithInlineImages.replace(
                  cidRegex,
                  `src="${attachment.url}" title="${attachment.contentId}"`
                );
              }
            });

            // 修改：使用insertContentAtCursor在光标位置插入内容
            if (editorRef.current) {
              editorRef.current.insertContentAtCursor(contentWithInlineImages);
            } else {
              // 如果没有editor引用，则使用备选方案
              setEditorContent(
                (prevContent) => prevContent + contentWithInlineImages
              );
            }

            console.log('成功复制模板附件:', response.data.data);
          } else {
            console.error('复制模板附件失败:', response.data?.error);

            // 如果附件处理失败，仍然插入原始内容
            if (editorRef.current) {
              editorRef.current.insertContentAtCursor(processedContent);
            } else {
              setEditorContent((prevContent) => prevContent + processedContent);
            }
          }
        } catch (error) {
          console.error('复制模板附件时发生错误:', error);

          // 如果出现异常，仍然插入原始内容
          if (editorRef.current) {
            editorRef.current.insertContentAtCursor(processedContent);
          } else {
            setEditorContent((prevContent) => prevContent + processedContent);
          }
        }
      } else {
        console.warn('无法复制附件: 邮件ID不可用');

        // 如果邮件ID不可用，仍然插入内容
        if (editorRef.current) {
          editorRef.current.insertContentAtCursor(processedContent);
        } else {
          setEditorContent((prevContent) => prevContent + processedContent);
        }
      }
    } else {
      // 如果没有附件，直接在光标位置插入内容
      if (editorRef.current) {
        editorRef.current.insertContentAtCursor(processedContent);
      } else {
        setEditorContent((prevContent) => prevContent + processedContent);
      }
    }

    // 关闭模板选择模态框
    setShowEmailTemplateModal(false);
    saveDraft(true);
  };

  const emailSecureSuffix: string =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']
      ?.emailSecureSuffix ?? '';

  const emailSecureSuffixButtonName: string =
    globalConfig?.microfrontends?.['ctint-mf-manual-queue']
      ?.emailSecureSuffixButtonName ?? '';

  const isSecureSuffix: boolean = emailSecureSuffix !== '';

  const handleSecureEmail = () => {
    // 检查 subject 是否已经包含 [Secure] 标记，避免重复添加
    if (!subject.includes('[Secure]')) {
      // 在 subject 末尾添加 [Secure] 标记
      setSubject((prevSubject) =>
        prevSubject.trim() ? `${prevSubject.trim()} [Secure]` : '[Secure]'
      );
    }
  };

  return (
    <div className="flex flex-col h-full bg-white relative">
      {/* 主滚动容器 - 添加padding-bottom来为固定按钮留出空间 */}
      <div className="h-full overflow-auto pb-16">
        {/* 附件列表 */}
        {renderAttachments()}

        {/* Email Form - 邮件头部 */}
        <div className="p-4 space-y-3">
          <RecipientField
            label="From"
            recipients={[
              outboundEmail?.detail?.emailSender
                ? JSON.parse(outboundEmail.detail.emailSender).emailAddress
                    .address
                : originalEmail?.detail?.emailSender
                  ? JSON.parse(originalEmail.detail.emailSender).emailAddress
                      .address
                  : '',
            ]}
            disabled={true}
          />

          <RecipientField
            label="To"
            recipients={to}
            onAdd={(email: string) => setTo([...to, email])}
            onRemove={(index: number) =>
              setTo(to.filter((_, i) => i !== index))
            }
          />

          <RecipientField
            label="Cc"
            recipients={cc}
            onAdd={(email: string) => setCc([...cc, email])}
            onRemove={(index: number) =>
              setCc(cc.filter((_, i) => i !== index))
            }
          />

          <RecipientField
            label="Bcc"
            recipients={bcc}
            onAdd={(email: string) => setBcc([...bcc, email])}
            onRemove={(index: number) =>
              setBcc(bcc.filter((_, i) => i !== index))
            }
          />

          <div className="flex items-center gap-2">
            <span className="w-16 text-sm text-gray-600">Subject:</span>
            <input
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="flex-1 px-2 py-1.5 border rounded-md outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* 使用改进的 EmailEditor 组件 */}
        <div className="mx-4 mb-4">
          <EmailEditor
            ref={editorRef}
            initialContent={editorContent}
            onContentChange={handleContentChange}
            onAddImage={addImage}
            placeholder="Compose your email..."
          />
        </div>

        {/* 原始邮件内容（在回复/转发时显示） */}
        {originalEmail && (type === 'reply' || type === 'forward') && (
          <div className="pt-8">
            <div className="border-t-[16px] border-gray-100"></div>
            <div className="p-4">
              <div
                className="prose max-w-none"
                dangerouslySetInnerHTML={{
                  __html: sanitizeEmailBody(getProcessedEmailContent()),
                }}
              />
            </div>
          </div>
        )}
      </div>

      {/* 固定在底部的按钮区域 */}
      <div className="absolute bottom-0 left-0 right-0 bg-white border-t">
        <div className="flex justify-between items-center p-4">
          <div className="flex gap-2">
            <div className="relative group">
              <button
                onClick={() => saveDraft(false)}
                className="flex items-center justify-center w-9 h-9 hover:bg-gray-50 rounded"
                disabled={loading}
                type="button"
              >
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15.2 3C15.7275 3.00751 16.2307 3.22317 16.6 3.6L20.4 7.4C20.7768 7.76926 20.9925 8.27246 21 8.8V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H15.2Z"
                    stroke="black"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M17 21V14C17 13.7348 16.8946 13.4804 16.7071 13.2929C16.5196 13.1054 16.2652 13 16 13H8C7.73478 13 7.48043 13.1054 7.29289 13.2929C7.10536 13.4804 7 13.7348 7 14V21"
                    stroke="black"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M7 3V7C7 7.26522 7.10536 7.51957 7.29289 7.70711C7.48043 7.89464 7.73478 8 8 8H15"
                    stroke="black"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </button>
              <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition pointer-events-none whitespace-nowrap">
                Save Draft
              </div>
            </div>
            <div className="relative group">
              <DropzoneAttachmentButton
                onToggleAttachmentPage={() => setShowAttachmentPage(true)}
                buttonClassName="hover:bg-gray-50"
              />
              <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition pointer-events-none whitespace-nowrap">
                Add Attachment
              </div>
            </div>
            <div className="relative group">
              <button
                onClick={() => setShowEmailTemplateModal(true)}
                className="flex items-center justify-center hover:bg-gray-50"
                disabled={loading}
                type="button"
              >
                <svg
                  width="36"
                  height="36"
                  viewBox="0 0 36 36"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M27 21C27 21.5304 26.7893 22.0391 26.4142 22.4142C26.0391 22.7893 25.5304 23 25 23H13L9 27V11C9 10.4696 9.21071 9.96086 9.58579 9.58579C9.96086 9.21071 10.4696 9 11 9H25C25.5304 9 26.0391 9.21071 26.4142 9.58579C26.7893 9.96086 27 10.4696 27 11V21Z"
                    stroke="black"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M14 18C14.5304 18 15.0391 17.7893 15.4142 17.4142C15.7893 17.0391 16 16.5304 16 16V14H14"
                    stroke="black"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                  <path
                    d="M20 18C20.5304 18 21.0391 17.7893 21.4142 17.4142C21.7893 17.0391 22 16.5304 22 16V14H20"
                    stroke="black"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  />
                </svg>
              </button>
              <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition pointer-events-none whitespace-nowrap">
                Email Template
              </div>
            </div>
            {isSecureSuffix && (
              <div className="relative group">
                <button
                  onClick={() => handleSecureEmail()}
                  className="flex items-center justify-center w-9 h-9 hover:bg-gray-50 rounded"
                  disabled={loading}
                  type="button"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="lucide lucide-shield-ellipsis"
                  >
                    <path d="M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z" />
                    <path d="M8 12h.01" />
                    <path d="M12 12h.01" />
                    <path d="M16 12h.01" />
                  </svg>
                </button>
                <div className="absolute left-1/2 -translate-x-1/2 bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition pointer-events-none whitespace-nowrap">
                  {emailSecureSuffixButtonName}
                </div>
              </div>
            )}
          </div>
          <div className="relative group">
            <Button
              variant="primary"
              size="m"
              onClick={handleSend}
              disabled={
                loading ||
                isSending ||
                !to.length ||
                !subject.trim() ||
                (editorRef.current
                  ? editorRef.current.isEmpty()
                  : !editorContent.trim()) ||
                false
              }
              testId="send-email-button"
            >
              {isSending ? 'Sending...' : 'Send'}
            </Button>
            <div className="absolute right-0 bottom-full mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition pointer-events-none whitespace-nowrap">
              SendEmail
            </div>
          </div>
        </div>
      </div>

      {/* 附件上传模态框 */}
      {showAttachmentPage && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
          {loading ? (
            <div className="bg-white p-8 rounded-xl shadow-lg flex flex-col items-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
              <p className="text-gray-700">Uploading file...</p>
            </div>
          ) : (
            <DropzoneAttachmentPage
              attachmentList={convertSessionFilesToFileItems()}
              onRemoveFile={handleRemoveSessionFile}
              onAddFiles={handleSelectFiles}
              onSubmit={handleUploadComplete}
              onCancel={handleCancel}
              onFileRejected={handleFileRejected}
              title="Attach File"
              submitLabel="Done"
              cancelLabel="Cancel"
              allowAllTypes={true}
              defaultMaxSize={25 * 1024 * 1024}
              maxSizeByType={{
                'image/jpeg': 8 * 1024 * 1024,
                'image/png': 8 * 1024 * 1024,
                'image/gif': 8 * 1024 * 1024,
                'image/webp': 8 * 1024 * 1024,
                'image/bmp': 10 * 1024 * 1024,
                'application/pdf': 50 * 1024 * 1024,
                'application/x-pdf': 50 * 1024 * 1024,
                'application/msword': 30 * 1024 * 1024,
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                  30 * 1024 * 1024,
                'application/vnd.ms-excel': 30 * 1024 * 1024,
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
                  30 * 1024 * 1024,
                'application/vnd.ms-powerpoint': 30 * 1024 * 1024,
                'application/vnd.openxmlformats-officedocument.presentationml.presentation':
                  30 * 1024 * 1024,
                'application/zip': 50 * 1024 * 1024,
                'application/x-rar-compressed': 50 * 1024 * 1024,
                'application/x-7z-compressed': 50 * 1024 * 1024,
                'text/plain': 15 * 1024 * 1024,
                'text/csv': 25 * 1024 * 1024,
                'application/json': 15 * 1024 * 1024,
                'application/xml': 15 * 1024 * 1024,
                '*': 25 * 1024 * 1024,
              }}
              maxSizeText="Images: 5MB, Documents: 50-100MB, Other files: 25MB"
              uploadInstructionText="Drop files here or click to select files (size limits vary by file type)"
            />
          )}
        </div>
      )}

      {/* 邮件模板选择模态框 */}
      <TemplateSelector
        isOpen={showEmailTemplateModal}
        onClose={() => setShowEmailTemplateModal(false)}
        onSelectTemplate={handleSelectTemplate}
        contentType={'html'}
        showNotes={true}
        onSaveNotes={(notes) => {
          try {
            const conversationId = emailData[0]?.gcConversationId;
            const caseId = emailData[0]?.caseId;
            if (!conversationId) {
              console.error('Conversation ID is missing');
              toast({
                variant: 'error',
                title: 'Error',
                description: 'Cannot save notes: Missing conversation ID',
              });
              return;
            }

            saveEmailNote(basePath, {
              gcConversationId: conversationId,
              caseId: caseId,
              note: notes,
            })
              .then((response) => {
                if (response.data?.isSuccess) {
                  toast({
                    variant: 'success',
                    title: 'Success',
                    description: 'notes saved successfully.',
                  });
                  setShowEmailTemplateModal(false);
                } else {
                  toast({
                    variant: 'error',
                    title: 'Error',
                    description:
                      response.data?.error || 'Failed to save notes.',
                  });
                }
              })
              .catch((error) => {
                console.error('Error saving template notes:', error);
                toast({
                  variant: 'error',
                  title: 'Error',
                  description: 'An error occurred while saving template notes.',
                });
              });
          } catch (error) {
            console.error('Error in template notes save handler:', error);
            toast({
              variant: 'error',
              title: 'Error',
              description: 'An unexpected error occurred.',
            });
          }
        }}
      />
      <Toaster />
    </div>
  );
};

export default EmailReplyEditor;
