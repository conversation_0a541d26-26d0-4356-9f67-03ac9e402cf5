import { EmailAttachment } from '../../../../types/microfrontendsConfig';

// Extended EmailAttachment interface with additional properties
export interface ExtendedEmailAttachment extends EmailAttachment {
  isDeleted?: boolean;
  contentSizeBytes?: number; // For compatibility with different size representations
}

// Interface for validation results
export interface AttachmentValidationResult {
  valid: boolean;
  error: string | null;
  warning: string | null;
}

// Interface for file-like objects (useful for non-File attachment objects)
export interface FileObject {
  name: string;
  size: number;
}

// Email size constraints based on Microsoft Exchange limits
export const EMAIL_SIZE_LIMITS = {
  // Maximum total attachment size: 25MB (Microsoft Exchange standard)
  MAX_TOTAL_SIZE: 25 * 1024 * 1024,

  // Maximum email size including body and encodings: 35MB
  MAX_EMAIL_SIZE: 35 * 1024 * 1024,

  // Maximum number of attachments: 30 (reasonable limit)
  MAX_FILE_COUNT: 30,

  // Blocked file extensions for security
  BLOCKED_EXTENSIONS: [
    '.exe',
    '.bat',
    '.cmd',
    '.vbs',
    '.js',
    '.wsf',
    '.msi',
    '.com',
    '.pif',
    '.application',
    '.gadget',
    '.msp',
    '.scr',
    '.hta',
    '.cpl',
    '.msc',
    '.jar',
    '.reg',
    '.ps1',
    '.vbe',
    '.jse',
    '.dll',
  ],

  // Size warning thresholds
  SIZE_WARNINGS: {
    // 10MB - Can trigger spam filters
    WARNING_THRESHOLD: 10 * 1024 * 1024,

    // 20MB - Close to the limit
    CRITICAL_THRESHOLD: 20 * 1024 * 1024,
  },
};

/**
 * Formats file size in human-readable format
 * @param bytes Size in bytes
 * @returns Formatted size string (e.g., "2.5 MB")
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return bytes + ' B';
  if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
  return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
};

/**
 * Validates if an attachment meets the size and security requirements
 * @param file File or file-like object to validate
 * @param currentAttachments Current list of attachments
 * @returns Validation result with status and messages
 */
export const validateAttachment = (
  file: File | FileObject,
  currentAttachments: ExtendedEmailAttachment[]
): AttachmentValidationResult => {
  const result: AttachmentValidationResult = {
    valid: true,
    error: null,
    warning: null,
  };

  // 检查文件扩展名（安全性检查）
  const fileName = file.name.toLowerCase();
  const fileExtension = '.' + fileName.split('.').pop();
  if (EMAIL_SIZE_LIMITS.BLOCKED_EXTENSIONS.includes(fileExtension)) {
    result.valid = false;
    result.error = `File type "${fileExtension}" is blocked for security reasons.`;
    return result;
  }

  // 计算当前所有附件的总大小
  const currentTotalSize = currentAttachments.reduce(
    (total, attachment) =>
      total + (attachment.fileSize || attachment.contentSizeBytes || 0),
    0
  );

  // 检查添加此文件是否会超过最大附件大小限制
  const newTotalSize = currentTotalSize + file.size;
  if (newTotalSize > EMAIL_SIZE_LIMITS.MAX_TOTAL_SIZE) {
    result.valid = false;
    result.error = `Adding this file would exceed the maximum attachment size limit of ${formatFileSize(EMAIL_SIZE_LIMITS.MAX_TOTAL_SIZE)}.`;
    return result;
  }

  // 检查附件数量限制
  if (currentAttachments.length >= EMAIL_SIZE_LIMITS.MAX_FILE_COUNT) {
    result.valid = false;
    result.error = `You cannot attach more than ${EMAIL_SIZE_LIMITS.MAX_FILE_COUNT} files.`;
    return result;
  }

  // 当接近限制时提供警告
  if (newTotalSize > EMAIL_SIZE_LIMITS.SIZE_WARNINGS.CRITICAL_THRESHOLD) {
    result.warning = `Total attachment size (${formatFileSize(newTotalSize)}) is approaching the limit. Consider using a file sharing service instead.`;
  } else if (newTotalSize > EMAIL_SIZE_LIMITS.SIZE_WARNINGS.WARNING_THRESHOLD) {
    result.warning = `Total attachment size (${formatFileSize(newTotalSize)}) may trigger spam filters.`;
  }

  return result;
};

/**
 * 计算估计的RFC 2822邮件大小（包括编码开销）
 * 邮件使用base64编码，会使大小增加约33%
 * @param content 邮件的HTML内容
 * @param attachments 附件列表
 * @returns 估计的总大小（字节）
 */
export const calculateEstimatedEmailSize = (
  content: string,
  attachments: ExtendedEmailAttachment[]
): number => {
  // 估计内容大小（HTML内容加编码开销）
  const contentSize = content.length * 1.33; // ~33%的base64编码开销

  // 附件总大小加编码开销
  const attachmentsSize = attachments
    .filter((attachment) => !attachment.isDeleted)
    .reduce(
      (total, attachment) =>
        total +
        (attachment.fileSize || attachment.contentSizeBytes || 0) * 1.33,
      0
    );

  // 邮件头通常会增加一些额外开销
  const estimatedHeadersSize = 2 * 1024; // 约2KB的头部信息

  return contentSize + attachmentsSize + estimatedHeadersSize;
};

/**
 * Validates the entire email (content + attachments) against size limits
 * @param content Email HTML content
 * @param attachments List of attachments
 * @returns Validation result
 */
export const validateEmailSize = (
  content: string,
  attachments: ExtendedEmailAttachment[]
): AttachmentValidationResult => {
  const result: AttachmentValidationResult = {
    valid: true,
    error: null,
    warning: null,
  };

  // Calculate estimated total email size
  const estimatedSize = calculateEstimatedEmailSize(content, attachments);

  // Check if it exceeds the maximum email size
  if (estimatedSize > EMAIL_SIZE_LIMITS.MAX_EMAIL_SIZE) {
    result.valid = false;
    result.error = `The email exceeds the maximum size limit of ${formatFileSize(EMAIL_SIZE_LIMITS.MAX_EMAIL_SIZE)}. Please reduce the content or remove some attachments.`;
    return result;
  }

  // Provide warnings if approaching limits
  if (estimatedSize > EMAIL_SIZE_LIMITS.MAX_EMAIL_SIZE * 0.9) {
    result.warning = `The email size (${formatFileSize(estimatedSize)}) is very close to the maximum limit. Consider reducing content or attachments.`;
  } else if (estimatedSize > EMAIL_SIZE_LIMITS.MAX_EMAIL_SIZE * 0.75) {
    result.warning = `The email size (${formatFileSize(estimatedSize)}) is approaching the limit. Large emails may have delivery issues.`;
  }

  return result;
};

/**
 * Returns the total size of all non-deleted attachments
 * @param attachments Array of attachments
 * @returns Total size in bytes
 */
export const getTotalAttachmentSize = (
  attachments: ExtendedEmailAttachment[]
): number => {
  return attachments
    .filter((attachment) => !attachment.isDeleted)
    .reduce(
      (total, attachment) =>
        total + (attachment.fileSize || attachment.contentSizeBytes || 0),
      0
    );
};
