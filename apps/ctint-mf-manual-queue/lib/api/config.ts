// eslint-disable-next-line unused-imports/no-unused-imports

import { emailTransfer, replyDisconnected, updateEmailReference } from './index';

export const apiConfig = {
  paths: {
    conversation: {
      conversations: '/api/conversations',
      conversationActiveList:
        '/api/process-api/ctint-conv/conversation/activeList',
    },
    message: {
      messages: '/api/messages',
      page: '/api/process-api/ctint-manual-queue/api/v1/message/page',
    },
    detail: {
      info: '/api/process-api/ctint-conv/interaction/detail',
      media: '/api/process-api/ctint-conv/interaction/recordings',
      transcript:
        '/api/process-api/ctint-conv/interaction/recordings/transcript',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      evaluation_list: '/api/process-api/ctint-qm/inspection/evaluation/list',
      evaluation_assign: '/api/process-api/ctint-qm/inspection/evaluation',
      evaluation_nlp_result_update:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail',
      evaluation_nlp_result_list:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail/list',
      evaluation_result_update:
        '/api/process-api/ctint-qm/inspection/evaluation/detail',
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
    },
    recordings: '/api/process-api/ctint-conv/recordings',
    export: '/api/process-api/ctint-conv/interaction',
    gc_recordings: '/api/process-api/ctint-conv/interactions',
    sort: 'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings',
    config: '/api/process-api/ctint-config/userconfig',
    transcript:
      'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings/transcript',
    manual_queue: {
      page: '/api/process-api/ctint-manual-queue/api/v1/voicemail/page',
      getAudioUrl:
        '/api/process-api/ctint-manual-queue/api/v1/voicemail/get/mp3Url',
      userSearch: '/api/process-api/ctint-manual-queue/api/v1/users/search',
      assign: '/api/process-api/ctint-manual-queue/api/v1/voicemail/assign',
      getQueueInfo:
        '/api/process-api/ctint-manual-queue/api/v1/voicemail/get/queue-info',
      conversationCallback:
        '/api/process-api/ctint-manual-queue/api/v1/voicemail/voiceMailInfo',
      transfer: '/api/process-api/ctint-manual-queue/api/v1/voicemail/transfer',
      saveConversationRel:
        '/api/process-api/ctint-manual-queue/api/v1/voicemail/saveConversationRel',
    },
    email: {
      page: '/api/process-api/ctint-manual-queue/api/v1/cdss-email/page',
      detail: '/api/process-api/ctint-manual-queue/api/v1/cdss-email/detail',
      emailNotes:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/get/notes',
      saveNotes:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/save/note',
      getHandleEmail:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/get/handle/email',
      saveDraft:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/save-draft',
      handleFileUpload:
        '/api/process-api/ctint-manual-queue/api/v1/file/upload',
      sendEmail: '/api/process-api/ctint-manual-queue/api/v1/cdss-email/send',
      getEmailTemplate:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/get/email/template',
      replyDisconnected:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/reply-disconnected',
      copyEmailTemplateAttachment:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/copy/email-template-attachment',
      updateEmailReference:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/update-case-status',
      toggleSpamStatus:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/handleToggleSpam',
      reassign:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/reassign',
      secureSuffix:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/secure-suffix',
      queueMember:
        '/api/process-api/ctint-manual-queue/api/v1/user/queueMember',
    },
    users: {
      searchUsers: '/api/process-api/ctint-user/users/search',
      getAllUsers: '/api/process-api/ctint-user/users',
    },
    callControl: {
      callbackNow: '/api/process-api/ctint-call-control/conversations/call',
      wrapUp:
        '/api/process-api/ctint-call-control/conversations/callbacks/participants',
      stationsUsers: '/api/process-api/ctint-call-control/stations/users/',
      emailTransfer:
        '/api/process-api/ctint-call-control/conversations/emails/blind/transfer',
      emailCancelTransfer:
        '/api/process-api/ctint-call-control/conversations/emails/{conversationId}/participants/{participantId}/disconnect',
    },
  },
};
