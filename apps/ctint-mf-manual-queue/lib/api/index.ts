// eslint-disable-next-line unused-imports/no-unused-imports
import { addLoggingToAxios } from '@cdss-modules/design-system/lib/logging';
import { apiConfig } from './config';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import {
  AssignReq,
  ConversationCallBack,
  CopyEmailTemplateAttachmentRequest,
  SendEmailRequest,
  TransferReq,
  UserSearchReq,
} from '../../types/microfrontendsConfig';
import { useQuery } from '@tanstack/react-query';

type UserConfigItems = {
  name: string;
  value: string;
};

type UserConfigProps = {
  filters?: UserConfigItems[];
  columns?: UserConfigItems[];
};

export const axiosInstance = axios.create({
  timeout: 20000,
  headers: {
    'Content-Type': 'application/json',
    traceId: uuidv4(),
    tenant: 'ctint',
    sourceId: 'ctint-mf-manual-queue',
    previousId: 'ctint-bff-cdss',
  },
});

export const getConversationsResult = (
  basePath = 'https://apifoxmock.com/m1/5561471-5238589-default'
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.conversation.conversations}`
  );
};

export const getAudioUrl = async (basePath = '', conversationId = '') => {
  //return "https://api-downloads.mypurecloud.com.au/MediaCache/29e1074213590dce17d626b7822f75f59e948f61/AUDIO_0.MP3?response-content-disposition=attachment%3Bfilename%3D%22MediaCache%2F29e1074213590dce17d626b7822f75f59e948f61%2FAUDIO_0.MP3%22&Expires=1734509935&Signature=lInBPgddP3chC6h8~D5XL53TetRoAMTmGp65xOi3C-J0aIH3Exlw7w45CZFUXUXqdpa8WNEAOqLmMAMqMGOlIOAbgzdUGAPJ100j7YjE-IvFTRr2U-nRiLBNpHYxIHUjPfkda4UOxC7RFOiH3vQoBf2JuS14IB48bC5CFC~5dql8vfWdDGFwgMXPjqbhy0~0MROIyZeb4oBL3DoTcrivIzi1MyAe6Pi0TTnYE7OyTS-QO~s7MKf7bxfCJxU5Rvu0rUYmsl3XeKhSxaLIH3tViG6zsOdTw0d~Iacjea2mIYYksxa55lJE6fDNWT1-Ez-PSLRJctWp47nGiP6GTBkD9Q__&Key-Pair-Id=K21FSGH5HMHI4N"
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manual_queue.getAudioUrl}?conversationId=${conversationId}`
  );
};
export const assignTo = (basePath = '', data: AssignReq) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manual_queue.assign}`,
    data
  );
};
export const replyDisconnected = (basePath = '', data: SendEmailRequest) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.replyDisconnected}`,
    data
  );
};

export interface FilterParams {
  [key: string]: any;
}

export const getManualQueuePage = (
  basePath = '',
  pageNumber: number,
  pageSize: number,
  filterParams: FilterParams
) => {
  const queryParams = new URLSearchParams({
    page: pageNumber.toString(),
    page_size: pageSize.toString(),
    ...filterParams,
  });
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manual_queue.page}?${queryParams}`
  );
};

export const getMessagePage = (
  basePath = '',
  pageNumber: number,
  pageSize: number,
  filterParams: FilterParams
) => {
  const queryParams = new URLSearchParams({
    page: pageNumber.toString(),
    page_size: pageSize.toString(),
    ...filterParams,
  });
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.message.page}?${queryParams}`
  );
};


export const getEmailPage = (
  basePath = '',
  pageNumber: number,
  pageSize: number,
  filterParams: FilterParams
) => {
  const queryParams = new URLSearchParams({
    page: pageNumber.toString(),
    page_size: pageSize.toString(),
    ...filterParams,
  });
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.email.page}?${queryParams}`
  );
};

export const getQueueInfo = async (basePath = '', type: string) => {
  const queryParams = new URLSearchParams({
    type: type,
  });
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manual_queue.getQueueInfo}?${queryParams}`
  );
};

export const userSearch = (basePath = '', data: UserSearchReq) => {
  console.log(data);
  if (data.keyword) {
    return axiosInstance.post(
      `${basePath}${apiConfig.paths.users.searchUsers}`,
      data
    );
  } else {
    const params = new URLSearchParams();
    params.append('pageNum', '1');
    params.append('filterType', 'all');
    return axiosInstance.get(
      `${basePath}${apiConfig.paths.users.getAllUsers}?${params}`
    );
  }
};

export const queueMember = (basePath = '', data: UserSearchReq) => {
  const params = new URLSearchParams();
  params.append('pageNum', '1');
  params.append('queueId', data.queueId || '');
  params.append('filterType', 'queue');
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.email.queueMember}?${params}`
  );
};

export const userSearchByQueue = (basePath = '', data: UserSearchReq) => {
  const params = new URLSearchParams();
  params.append('pageNum', '1');
  params.append('queueId', data.queueId || '');
  params.append('filterType', 'queue');
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.users.getAllUsers}?${params}`
  );
};

export const useConversation = (
  basePath = '',
  conversationId: string | null
) => {
  return useQuery({
    queryKey: ['conversationId', conversationId],
    queryFn: async () => {
      if (!conversationId) {
        throw new Error('No conversation ID provided');
      }
      const response = await axiosInstance.get(
        `${basePath}${apiConfig.paths.manual_queue.conversationCallback}?conversationId=${conversationId}`
      );
      return response.data;
    },
    enabled: !!conversationId,
  });
};

export const getConversationInfo = (
  basePath = '',
  conversationId: string | null
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.manual_queue.conversationCallback}?conversationId=${conversationId}`
  );
};

export const getEmailConversationInfo = (
  basePath = '',
  conversationId: string | null
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.email.getHandleEmail}?gcConversationId=${conversationId}`
  );
};

export const transfer = (basePath = '', data: TransferReq) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manual_queue.transfer}`,
    data
  );
};
export const emailTransfer = (basePath = '', data: TransferReq) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.emailTransfer}`,
    data
  );
};

export const emailCancelTransfer = (basePath = '', data: TransferReq) => {
  const conversationId = data.conversationId || '';
  const transferParticipantId = data.transferParticipantId || '';
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.emailCancelTransfer.replace('{conversationId}', conversationId).replace('{participantId}', transferParticipantId)}`
  );
};

export const reassign = (basePath = '', data: AssignReq) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.reassign}`,
    data
  );
};

export const wrapUp = (basePath = '', data: TransferReq) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.wrapUp}`,
    data
  );
};

export const callbackNow = (
  basePath = '',
  payload: { phoneNumber?: string; callUserId?: string; callQueueId?: string }
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.callControl.callbackNow}`,
    payload
  );
};

export const stationsUsers = (basePath = '', userId = '') => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.callControl.stationsUsers}/${userId}`
  );
};

export const conversationActiveList = (
  basePath = '',
  userId = '',
  deviceId = ''
) => {
  const params = new URLSearchParams();
  params.append('userId', userId);
  params.append('deviceId', deviceId);
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.conversation.conversationActiveList}?${params}`
  );
};

export const saveConversationRel = (
  basePath = '',
  payload: { referenceConversationId?: string; sourceConversationId?: string }
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.manual_queue.saveConversationRel}`,
    payload
  );
};

export const getEmailDetail = (
  basePath = '',
  conversationId: string | null
) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.email.detail}?gcConversationId=${conversationId}`
  );
};

export const getEmailNotes = (basePath = '', conversationId: string | null) => {
  return axiosInstance.get(
    `${basePath}${apiConfig.paths.email.emailNotes}?gcConversationId=${conversationId}`
  );
};

export const saveEmailNote = (
  basePath = '',
  payload: { gcConversationId?: string; caseId?: string; note?: string }
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.saveNotes}`,
    payload
  );
};

export const updateEmailReference = (
  basePath = '',
  payload: {
    caseId?: string;
    caseStatus?: string;
    gcConversationId?: string;
    referenceId?: string;
    referenceStatus?: string;
  }
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.updateEmailReference}`,
    payload
  );
};

export const toggleSpamStatus = (
  basePath = '',
  payload: {
    gcConversationId?: string;
    isJunkmail?: number;
  }
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.toggleSpamStatus}`,
    payload
  );
};
export const secureSuffix = (
  basePath = '',
  payload: {
    conversationId?: string;
  }
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.secureSuffix}`,
    payload
  );
};

export const saveDraft = (basePath = '', data: SendEmailRequest) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.saveDraft}`,
    data
  );
};

export const sendEmail = (basePath = '', data: SendEmailRequest) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.sendEmail}`,
    data
  );
};

export const handleFileUpload = (basePath = '', data: FormData) => {
  console.log('附件data：', data);

  // 创建一个专门用于文件上传的 axios 实例，超时时间设置为 5 分钟
  const uploadAxiosInstance = axios.create({
    timeout: 300000, // 5分钟 = 300,000毫秒
    headers: {
      'Content-Type': 'multipart/form-data',
      traceId: uuidv4(),
      tenant: 'ctint',
      sourceId: 'ctint-mf-manual-queue',
      previousId: 'ctint-bff-cdss',
    },
  });

  // 添加与主 axiosInstance 相同的请求拦截器
  uploadAxiosInstance.interceptors.request.use(
    (config) => {
      const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
      const gcAccessToken = localStorage.getItem('gc-access-token') || '';
      const deviceId = localStorage.getItem('deviceId') || '';
      config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
      if (cdssAuthToken) {
        config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
      }
      if (gcAccessToken) {
        config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
      }
      if (deviceId) {
        config.headers['deviceId'] = deviceId;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  return uploadAxiosInstance.post(
    `${basePath}${apiConfig.paths.email.handleFileUpload}`,
    data
  );
};

export const copyEmailTemplateAttachment = (
  basePath = '',
  data: CopyEmailTemplateAttachmentRequest
) => {
  return axiosInstance.post(
    `${basePath}${apiConfig.paths.email.copyEmailTemplateAttachment}`,
    data
  );
};

export const fireGetUserConfig = (basePath = '') =>
  axiosInstance.get(`${basePath}${apiConfig.paths.config}`);

export const fireCreateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.post(`${basePath}${apiConfig.paths.config}`, data);

export const fireUpdateUserConfig = (data: UserConfigProps, basePath = '') =>
  axiosInstance.put(`${basePath}${apiConfig.paths.config}`, data);

axiosInstance.interceptors.request.use(
  (config) => {
    // TODO: should call the auth api and set the token when program initialize
    if (
      process.env.NODE_ENV === 'development' &&
      localStorage.getItem('cdss-auth-token') === null
    ) {
      localStorage.setItem('deviceId', uuidv4());
      localStorage.setItem(
        'gc-access-token',
        'ZoICpoNdyYLZpUEM2AqHGWSc3DGPiDmzrm-HaAAIdVjprS9frf76aFPHtKxjewfZ-3PDMQu6pUEDeksTiFLmWA'
      );
      localStorage.setItem(
        'cdss-auth-token',
        'EdOImMlG47o8McBVtqY2QKHtIoT7OPznIInDv7cOXG+0DnbPZAcG6Ra+604VzhdT+fX3I2pbhPKWfQSrGpd+hZ5OuZOcptbvJ01GpJkpzCbiiT6TpuU='
      );
    }
    const cdssAuthToken = localStorage.getItem('cdss-auth-token') || '';
    const gcAccessToken = localStorage.getItem('gc-access-token') || '';
    const deviceId = localStorage.getItem('deviceId') || '';
    config.headers['tenant'] = localStorage.getItem('tenant') || 'ctint';
    if (cdssAuthToken) {
      config.headers['cdss_authorization'] = 'Bearer ' + cdssAuthToken;
    }
    if (gcAccessToken) {
      config.headers['gc_authorization'] = 'Bearer ' + gcAccessToken;
    }
    if (deviceId) {
      config.headers['deviceId'] = deviceId;
    }

    if (config.url?.includes('/interaction/recordings/transcript/')) {
      config.headers['requester'] = 'agent1';
    }

    console.log('axiosInstance.interceptors.request', config);
    return config;
  },
  (error) => {
    Promise.reject(error);
  }
);

axiosInstance.interceptors.response.use(
  (response) => {
    // Return response as is if the request is successful
    return response;
  },
  (error) => {
    const errorCode = (localStorage.getItem('errorCode') || '').split(',');
    if (errorCode && errorCode.includes(error?.response?.status.toString())) {
      const currUrl = window.location.href;
      if (currUrl?.indexOf('login') === -1) {
        const basePath = (window as any)?.GLOBAL_BASE_PATH;
        window.location.href = `${basePath}/login`;
      }
      localStorage.removeItem('cdss-auth-token');
      localStorage.removeItem('gc-access-token');
      localStorage.removeItem('permissions');
    }
    return Promise.reject(error);
  }
);

addLoggingToAxios(axiosInstance);

export const fireGetSortedRecordings = (queryParams: string, basePath = '') =>
  axiosInstance.get(`${apiConfig.paths.sort}?${queryParams}`);

export default axiosInstance;
