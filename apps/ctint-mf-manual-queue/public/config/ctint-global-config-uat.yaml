auth:
  headers:
    - name: traceId
    - name: tenant
    - name: sourceId
    - name: previousId
    - name: cdss_authorization
logger:
  headers:
    - name: traceId
    - name: tenant
    - name: sourceId
    - name: previousId
pureengage:
  host: http://*************:8090
  recordsLimit: 200
genesys-cloud:
  environment: mypurecloud.com.au
  apiHost: https://api.mypurecloud.com.au
  authHost: https://login.mypurecloud.com.au
  clientId: ${genesysCloudClientId}
  clientSecret: ${genesysCloudClientSecret}
ctint-dab:
  auth:
    host: http://ctint-dab-auth-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  config:
    host: http://ctint-dab-config-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
  conv:
    host: http://ctint-dab-conv-service.cdss-data-ctint.svc.cluster.local:5000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /graphql
    active: true
ctint-state:
  auth:
    host: http://ctint-state-auth-service.cdss-data-ctint.svc.cluster.local:35000 #<service-name>.<namespace>.svc.cluster.local:port
    basepath: /v1.0/state/ctint-state-auth
    active: true
  cdssmf:
    host: http://ctint-state-cdssmf-service.cdss-data-ctint.svc.cluster.local:35010
    basepath: /v1.0/state/ctint-state-cdssmf
    active: true
portals:
  - ctint-mf-cpp
  - ctint-mf-tts
  - ctint-mf-wap
  - ctint-mf-template
  - ctint-mf-message
  - ctint-mf-manual-queue
microfrontends:
  ctint-mf-cdss:
    host: http://localhost:4400
    basepath: /ctint/mf-cdss
  ctint-mf-cpp:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/mf-cpp
  ctint-mf-template:
    host: http://localhost:4000
    basepath: /ctint/mf-template
  ctint-mf-message:
    host: http://localhost:4200
    basepath: /ctint/mf-message
  ctint-mf-manual-queue:
    host: http://localhost:4301
    basepath: /ctint/mf-manual-queue
    manual-tab-names:
    - labelEn: Conversation ID
      labelCh: 對話 ID
      value: conversationId
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Type
      labelCh: 媒體類型
      value: mediaType
      filterType: select
      active: true
      require: false
      isMetaData: false
    - labelEn: State
      labelCh: 狀態
      value: state
      active: true
      require: false
      isMetaData: false
    - labelEn: From
      labelCh: 來自
      value: from
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Assign
      labelCh: 委派
      value: assign
      filterType: select
      active: true
      isMetaData: false
    - labelEn: QueueName
      labelCh: 佇列
      value: queueName
      filterType: select
      active: true
      isMetaData: true
    - labelEn: Created At
      labelCh: 開始時間
      value: createdAt
      filterType: date
      active: true
      isMetaData: false
      checked: false
      require: true
    - labelEn: EndData
      labelCh: 结束時間
      value: createdAtToEnd
      filterType: date
      active: true
      isMetaData: false
      checked: false
      require: true
    - labelEn: ConversationDuration
      labelCh: 對話持續期間
      value: conversationDuration
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Ended
      labelCh: 已結束
      value: ended
      filterType: select
      active: true
      isMetaData: false
    email-tab-names:
    - labelEn: Conversation ID
      labelCh: 對話 ID
      value: conversationId
      filterType: input
      active: false
      isMetaData: false
    - labelEn: Type
      labelCh: 媒體類型
      value: mediaType
      filterType: select
      active: true
      require: false
      isMetaData: false
    - labelEn: Case ID
      labelCh: 案例編號
      value: caseId
      filterType: select
      active: true
      require: false
      isMetaData: false
    - labelEn: Subject
      labelCh: 主體
      value: subject
      filterType: select
      active: true
      require: false
      isMetaData: false
    - labelEn: State
      labelCh: 狀態
      value: state
      active: true
      require: false
      isMetaData: false
    - labelEn: From
      labelCh: 來自
      value: from
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Assign
      labelCh: 委派
      value: assign
      filterType: select
      active: true
      isMetaData: false
    - labelEn: QueueName
      labelCh: 佇列
      value: queueName
      filterType: select
      active: true
      isMetaData: true
    - labelEn: Created At
      labelCh: 開始時間
      value: createdAt
      filterType: date
      active: true
      isMetaData: false
      checked: false
      require: true
    - labelEn: ConversationDuration
      labelCh: 對話持續期間
      value: conversationDuration
      filterType: input
      active: true
      isMetaData: false
    - labelEn: Ended
      labelCh: 已結束
      value: ended
      filterType: select
      active: true
      isMetaData: false
  ctint-mf-tts:
    host: http://localhost:4600
    basepath:
  ctint-mf-wap:
    host: http://localhost:4700
    basepath:
  ctint-mf-info:
    host: http://localhost:4800
    basepath:
languages:
  supportedLanguages:
    - en
    - zh-HK
  defaultLanguage: zh-HK
services:
  ctint-stt:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/stt
    active: false
    provider:
      - astri
    healthcheck: /healthcheck
  ctint-auth:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/auth
    active: true
    provider: # pure-engage / genesys-cloud / ctint-dab-auth / ctint-state-auth
      - pure-engage
      - ctint-dab-auth
      - ctint-state-auth
    healthcheck: /healthcheck
  ctint-conv:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/conv
    active: true
    provider:
      - pureengage
      - ctint-dab-conv
    healthcheck: /healthcheck
  ctint-config:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/config
    active: true
    provider:
      - pureengage
      - ctint-dab-config
    healthcheck: /healthcheck
  ctint-session:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/session
    active: true
    provider:
      - ctint-state-cdssmf
    healthcheck: /healthcheck
  ctint-call-control:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/call-control
    active: true
    healthcheck: /healthcheck
  ctint-user:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/user
    active: true
    healthcheck: /healthcheck
  ctint-manual-queue:
    host: http://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com
    basepath: /ctint/cdss-manual-queue
    active: true
    provider:
      - ctint-state-cdssmf
    healthcheck: /healthcheck
