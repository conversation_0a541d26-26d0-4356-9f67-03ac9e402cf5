// WebSocket 事件的类型定义
interface WebSocketMessageData {
  id: string;
  conversationId: string;
  participantId: string;
  originalPlatform: string;
  platform: string;
  platformMessageId: string;
  externalMessageId: string;
  channelId: string;
  direction: string;
  messengerType: string;
  category: string;
  type: string;
  userName: string;
  userId: string;
  fromAddress: string;
  fromName: string;
  toAddress: string;
  toName: string;
  timestamp: string;
  textBody: string;
  status: string;
  operator: string;
  metadata: string;
  rawPayload?: string;
  tenant: string;
}

interface WebSocketEventData {
  data: WebSocketMessageData[];
  deviceId: string;
  eventId: number;
}

interface WebSocketEvent {
  event: string;
  eventData: WebSocketEventData;
}
