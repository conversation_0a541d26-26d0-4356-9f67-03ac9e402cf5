import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { subscribeWithSelector } from 'zustand/middleware';
import { ConversationItem } from '@cdss-modules/design-system/@types/Conversation';
import {
  CDSSMessage,
  MessageData,
} from '@cdss-modules/design-system/@types/Message';
import { now } from 'lodash';
import { getUatMessage } from '../lib/api';
import {
  SAAContentSection,
  SAASearchTerm,
} from '@cdss-modules/design-system/@types/SAA';

const getMessagesByConversationIdList = async (
  conversationIds: string[]
): Promise<MessageData[]> => {
  await new Promise((resolve) => setTimeout(resolve, 500));
  // const { basePath } = useRouteHandler();
  // Convert the mock messages format to MessageData[]
  const messagesList: MessageData[] = [];
  for (const id of conversationIds) {
    try {
      const response = await getUatMessage('/ctint/mf-cdss', id);
      if (response) {
        //修改timestamp
        messagesList.push({
          ...response.data.data,
          messages: response.data.data.messages?.map((cdssMessage: any) => ({
            ...cdssMessage,
            timestamp: new Date(cdssMessage.timestamp),
          })),
        });
      }
    } catch (error) {
      console.error(
        `Error fetching messages for conversation ID ${id}:`,
        error
      );
    }
  }

  return messagesList;
};

interface ConversationState {
  conversations: ConversationItem[];
  redothints: string[];
  messages: MessageData[];
  currentConversationId: string | null;
  isLoading: boolean;
  error: string | null;
  // 用于将SAA组件的搜索结果传送到聊天输入框中
  SAAContent: string | null;
  // 搜索结果记录
  SAAResult: Record<string, SAAContentSection[]>;
  //用于将聊天框的内容作为搜索条件传送到SAA组件中
  SAAManualSearchTerm: SAASearchTerm;
  referenceMessage: CDSSMessage | null; // 修改类型，允许 null
  rollingGCId: string;
  // Actions
  setConversations: (conversations: ConversationItem[]) => void;
  setMessages: (messages: MessageData[]) => void;
  setCurrentConversationId: (id: string | null) => void;
  setIsLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentActiveConversation: (conversationId: string) => void;
  setLastMessage: (conversationId: string, message: string) => void;
  // 添加 SAAContent 相关方法
  setSAAContent: (content: string) => void;
  clearSAAContent: () => void;
  // 添加 SAAResult 相关方法
  getSAAResult: (key: string) => SAAContentSection[] | undefined;
  setSAAResult: (key: string, result: SAAContentSection[]) => void;
  clearSAAResult: (key: string) => void;
  setSAAManualSearchTerm: (manualSearchTerm: SAASearchTerm) => void;
  setReferenceMessage: (message: CDSSMessage | null) => void;
  clearReferenceMessage: () => void; // 新增清除方法
  setRollingGCID: (id: string) => void;

  // API Actions
  fetchMessagesByConversationIds: (conversationIds: string[]) => Promise<void>;
  handleSingleMessageData: (messageData: MessageData) => void;
  handleRealTimeMessage: (
    conversationId: string,
    messages: CDSSMessage[]
  ) => void;
  updateMessageStatus: (
    conversationId: string,
    reqId: string,
    status: string
  ) => void;
  updateMessageReference: (
    conversationId: string,
    messageId: string,
    reference: CDSSMessage
  ) => void;

  addNewMessagesConversations: (conversationId: string) => void;
  readNewMessagesConversations: (deleteId: string) => void;
}

export const useConversationStore = create<ConversationState>()(
  subscribeWithSelector(
    immer((set, get) => ({
      conversations: [],
      redothints: [],
      messages: [],
      currentConversationId: null,
      isLoading: false,
      error: null,
      // 添加 SAAContent 初始值
      SAAContent: null,
      // 添加 SAAResult 初始值
      SAAResult: {},
      SAAManualSearchTerm: {} as SAASearchTerm,
      referenceMessage: null, // 修改初始值
      rollingGCId: '',

      // 添加 SAAContent 相关方法
      setSAAContent: (content: string) =>
        set((state) => {
          console.log('设置 SAA 内容:', content);
          state.SAAContent = content;
        }),

      clearSAAContent: () =>
        set((state) => {
          state.SAAContent = null;
        }),

      // 添加 SAAResult 相关方法
      getSAAResult: (key: string): SAAContentSection[] => {
        const state = get();
        return state.SAAResult[key];
      },

      setSAAResult: (key: string, result: SAAContentSection[]) =>
        set((state) => {
          console.log(
            `设置 SAA 搜索结果, key: ${key}, 结果数量: ${result.length}`
          );
          state.SAAResult[key] = result;
        }),

      clearSAAResult: (key: string) =>
        set((state) => {
          const { [key]: _, ...rest } = state.SAAResult;
          state.SAAResult = rest;
        }),

      setSAAManualSearchTerm: (manualSearchTerm: SAASearchTerm) =>
        set((state) => {
          state.SAAManualSearchTerm = manualSearchTerm;
        }),

      setReferenceMessage: (message: CDSSMessage | null) =>
        set((state) => {
          console.log('设置参考消息:', message);
          state.referenceMessage = message;
        }),

      // 新增清除引用消息的方法
      clearReferenceMessage: () =>
        set((state) => {
          console.log('清除参考消息');
          state.referenceMessage = null;
        }),

      setRollingGCID: (id: string) =>
        set((state) => {
          console.log('设置滚动 GC ID:', id);
          state.rollingGCId = id;
        }),
      addNewMessagesConversations: (conversationId: string) =>
        set((state) => {
          state.redothints = [...state.redothints, conversationId];
        }),

      readNewMessagesConversations: (deleteId: string) =>
        set((state) => ({
          redothints: state.redothints.filter(
            (conversationId: string) => conversationId !== deleteId
          ),
        })),

      setConversations: (conversations) =>
        set((state) => {
          state.conversations = conversations;
        }),

      setMessages: (messages) =>
        set((state) => {
          state.messages = messages;
        }),

      setLastMessage: (conversationId: string, message: string) =>
        set((state) => ({
          conversations: state.conversations.map(
            (item) =>
              item.id === conversationId
                ? { ...item, latestMessage: message } // 修改匹配项的属性
                : item // 保持其他项不变
          ),
        })),

      setCurrentConversationId: (id) =>
        set((state) => {
          state.currentConversationId = id;
        }),

      setIsLoading: (loading) =>
        set((state) => {
          state.isLoading = loading;
        }),

      setError: (error) =>
        set((state) => {
          state.error = error;
        }),
      setCurrentActiveConversation: (conversationId) => {
        set((state) => ({
          conversations: state.conversations.map((conv) => ({
            ...conv,
            isActive: conv.id === conversationId,
          })),
          currentConversationId: conversationId,
        }));
      },

      fetchMessagesByConversationIds: async (conversationIds) => {
        const { setIsLoading, setError, setMessages } = get();
        try {
          setIsLoading(true);
          setError(null);
          const messages =
            await getMessagesByConversationIdList(conversationIds);
          setMessages(messages);
        } catch (error) {
          setError(
            error instanceof Error
              ? error.message
              : 'Failed to fetch messages store'
          );
        } finally {
          setIsLoading(false);
        }
      },

      //可能有多条message 处理messages层面
      //先看reqId 如果存在则用此匹配 如存在则是api返回数据 如匹配则是等待send message的response的数据
      //不存在reqId 是websocket数据
      handleRealTimeMessage: (
        conversationId: string,
        realTimeMessages: CDSSMessage[]
      ) => {
        set((state) => {
          // const conversationId = realTimeMessages[0]?.conversationId || 'conv_1';
          // Find the MessageData object for this conversation
          const conversationMessages = state.messages.find(
            (m) => m.conversationId === conversationId
          );
          console.log('handleRealTimeMessage:', realTimeMessages);
          console.log('handleRealTimeMessage conversationId:', conversationId);
          if (conversationMessages && conversationMessages.messages) {
            realTimeMessages.forEach((messageItem) => {
              // find with reqId for api response
              if (messageItem.reqId) {
                const existingMessageIndex =
                  conversationMessages.messages.findIndex(
                    (msg) => msg.reqId === messageItem.reqId
                  );
                if (existingMessageIndex !== -1) {
                  // Update existing message
                  conversationMessages.messages[existingMessageIndex] = {
                    ...messageItem,
                    timestamp: new Date(messageItem.timestamp),
                  };
                  // Skip to the next messageItem
                  return;
                }
              }
              const existingMessageIndex =
                conversationMessages.messages.findIndex(
                  (msg) => msg.id === messageItem.id
                );
              if (existingMessageIndex !== -1) {
                // Update existing message for status ws
                conversationMessages.messages[existingMessageIndex] =
                  messageItem;
              } else {
                // Add new message
                conversationMessages.messages.push(messageItem);
              }
            });
          } else {
            // Create new MessageData if conversation doesn't exist
            state.messages.push({
              startTime: now().toLocaleString(),
              conversationId,
              messages: realTimeMessages,
            });
          }
        });
      },
      //用于查询单个conversation下的message detail 的更新
      handleSingleMessageData: (messageData: MessageData) => {
        console.log('handleSingleMessageData', messageData);

        // 先处理 messageData 中所有消息的 timestamp
        const processedMessageData = {
          ...messageData,
          messages:
            messageData.messages?.map((msg) => ({
              ...msg,
              timestamp: new Date(msg.timestamp),
            })) || [],
          whatsappConversationWindow: messageData.whatsappConversationWindow
            ? {
                ...messageData.whatsappConversationWindow,
                serviceWindowStartTime: new Date(
                  messageData.whatsappConversationWindow.serviceWindowStartTime
                ),
              }
            : undefined,
        };

        set((state) => {
          const messageIndex = state.messages.findIndex(
            (msg) => msg.conversationId === messageData.conversationId
          );
          if (messageIndex !== -1) {
            // 找到匹配的消息，创建新的数组并替换对应位置的消息
            const newMessages: MessageData[] = [...state.messages];
            newMessages[messageIndex] = processedMessageData as MessageData;
            return { messages: newMessages };
          } else {
            // 没找到匹配的消息，添加到数组末尾
            return { messages: [...state.messages, processedMessageData] };
          }
        });
      },
      updateMessageStatus: (conversationId, reqId, status) => {
        console.log('updateMessageStatus', conversationId, reqId, status);
        set((state) => {
          const conversationMessages = state.messages.find(
            (m) => m.conversationId === conversationId
          );
          if (conversationMessages && conversationMessages.messages) {
            const thisIndex = conversationMessages.messages.findIndex(
              (m) => m.reqId === reqId
            );
            if (thisIndex !== -1) {
              const oldMessage = conversationMessages.messages[thisIndex];
              conversationMessages.messages[thisIndex] = {
                ...oldMessage,
                status: status,
              };
            }
          }
        });
      },

      updateMessageReference: (
        conversationId: string,
        messageId: string,
        reference: CDSSMessage
      ) => {
        console.log(
          'updateMessageReference',
          conversationId,
          messageId,
          reference
        );
        set((state) => {
          const conversationMessages = state.messages.find(
            (m) => m.conversationId === conversationId
          );
          if (conversationMessages && conversationMessages.messages) {
            const thisIndex = conversationMessages.messages.findIndex(
              (m) => m.id === messageId
            );
            if (thisIndex !== -1) {
              const oldMessage = conversationMessages.messages[thisIndex];
              conversationMessages.messages[thisIndex] = {
                ...oldMessage,
                reference: reference,
                referenceId: reference.id,
              };
            }
          }
        });
      },
    }))
  )
);
