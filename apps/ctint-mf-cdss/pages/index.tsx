import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  TglobalConfig,
} from '@cdss-modules/design-system';
import dynamic from 'next/dynamic';
import MainLayout from '../components/_ui/MainLayout';
import { basePath, mfName } from '../lib/appConfig';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import UIBuilder from '../components/_screen/UIBuilder';
import UIBuilderPreview from '../components/_screen/UIBuilder/preview';
import DemoToolBar from '../components/_screen/DemoToolbar';
import DemoQMAdmin from '../components/_screen/DemoQMAdmin';
import AdminUserScreen from '../components/_screen/Admn/_screen/Main';
import AdminAuditScreen from '../components/_screen/Admn/_screen/Audit';
import { CommonPermissionWrapper } from '@cdss-modules/design-system/components/_other/PermissionWrapper/CommonPermissionWrapper';
import { <PERSON>Permission } from '@cdss-modules/design-system/@types/CommonPermission';
import QMAdminScreen from '@cdss/components/_screen/Admn/_screen/QM';
import { InteractionProvider } from '@cdss-modules/design-system/context/InteractionContext';
import { AdminSuperDashboardComponent } from '@cdss/components/_screen/Admn/ui/AdminSuperDashboad';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { OpenStationProvider } from '@cdss-modules/design-system/context/StationContext';
import { OpenBlockingProvider } from '@cdss-modules/design-system/context/BlockingContext';
import { TbarProvider } from '@cdss-modules/design-system/context/TBarContext';
import AgentPage from '@cdss/components/_screen/AgentPage';
import AutoReplyAdmin from '../components/_screen/AutoReplyAdmin';
import DNCAdminScreen from '@cdss/components/_screen/Admn/_screen/DNC';
import { EmailAdminPage } from '../components/_screen/EmailAdmin/_screen';
import { useEffect } from 'react';
import React from 'react';

const queryClient = new QueryClient();

// @ts-expect-error: Can't type check dynamic imports
export const PlaybackModule = dynamic(() => import('interaction/module'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const PlaybackModuleDetail = dynamic(() => import('interaction/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const ManualQueueModule = dynamic(() => import('manualQueue/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const ManualQueueDetailModule = dynamic(() => import('manualQueue/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
const CallDetail = dynamic(
  () => import('../components/_screen/AgentPage/_screen/Detail/CallDetail'),
  {
    ssr: false,
    loading: () => <LoadingBlock />,
  }
) as any;
// @ts-expect-error: Can't type check dynamic imports
const EmailDetailModule = dynamic(() => import('manualQueue/emailDetail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// // @ts-expect-error: Can't type check dynamic imports
// const TTSModule = dynamic(() => import('tts/module'), {
//     ssr: false,
//     loading: () => <LoadingBlock />
// }) as any;

// // @ts-expect-error: Can't type check dynamic imports
// const MsgModule = dynamic(() => import('wap/module'), {
//     ssr: false,
//     loading: () => <LoadingBlock />
// }) as any;

// @ts-expect-error: Can't type check dynamic imports
const UserAdminModule = dynamic(() => import('userAdmin/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const UserAdminDetail = dynamic(() => import('userAdmin/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

// @ts-expect-error: Can't type check dynamic imports
const TemplateModule = dynamic(() => import('template/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const TemplateDetail = dynamic(() => import('template/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const MsgModule = dynamic(() => import('msg/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const MsgDetail = dynamic(() => import('msg/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const Info = dynamic(() => import('info/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

// @ts-expect-error: Can't type check dynamic imports
const SuperDashboard = dynamic(() => import('superDashboard/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

const StyleGuide = dynamic(() => import('../components/_screen/StyleGuide'), {
  ssr: false,
  loading: () => <LoadingBlock />,
});
// @ts-expect-error: Can't type check dynamic imports
const CallModule = dynamic(() => import('call/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

// const Tdc = dynamic(() => import('tdc/main'), {
//   ssr: false,
//   loading: () => <LoadingBlock />,
// });

// @ts-expect-error: Can't type check dynamic imports
const ReportMain = dynamic(() => import('report/module'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const CampaignModule = dynamic(() => import('campaign/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;
// @ts-expect-error: Can't type check dynamic imports
const CampaignModuleDetailModule = dynamic(() => import('campaign/detail'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

// @ts-expect-error: Can't type check dynamic imports
const ContentCreationMain = dynamic(() => import('contentCreation/main'), {
  ssr: false,
  loading: () => <LoadingBlock />,
}) as any;

const ContentCreationCategory = dynamic(
  // @ts-expect-error: Can't type check dynamic imports
  () => import('contentCreation/category'),
  {
    ssr: false,
    loading: () => <LoadingBlock />,
  }
) as any;

const ContentCreationTemplate = dynamic(
  // @ts-expect-error: Can't type check dynamic imports
  () => import('contentCreation/template'),
  {
    ssr: false,
    loading: () => <LoadingBlock />,
  }
) as any;

export const Page = (props: any) => {
  useEffect(() => {
    // TODO: 通过配置决定是否需要登出, 目前是针对CCB,刷新和關閉頁面都會登出
    console.log(
      'Page ==> logoutOnRefresh',
      props.globalConfig.microfrontends.logoutOnRefresh
    );
    if (props.globalConfig.microfrontends.logoutOnRefresh) {
      const handleBeforeUnload = async () => {
        localStorage.removeItem('cdss-auth-token');
        localStorage.removeItem('gc-access-token');
        localStorage.removeItem('permissions');
        localStorage.removeItem('userName');
      };
      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('unload', handleBeforeUnload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        window.removeEventListener('unload', handleBeforeUnload);
      };
    }
  }, []);

  // home page maybe components
  const homeComponents: Record<string, React.ComponentType<any>> = {
    PlaybackModule,
    AdminUserScreen,
    AdminAuditScreen,
    QMAdminScreen,
    AdminSuperDashboardComponent,
    DNCAdminScreen,
    EmailAdminPage,
    AgentPage,
    ReportMain,
    CampaignModule,
    SuperDashboard,
  };

  const homePageComponent =
    props.globalConfig.microfrontends.homePageConfig.component;

  return (
    <PageRenderer
      routes={[
        {
          path: '/',
          group: 'ctint-mf-cdss',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  const permissionString =
                    globalConfig.microfrontends.homePageConfig.permission;
                  const permissionArgs = permissionString.split('.');
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled.apply(null, permissionArgs);
                }}
              >
                {/* {React.createElement('PlaybackModule')} */}
                {React.createElement(homeComponents[homePageComponent])}
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/interaction',
          group: 'ctint-mf-interaction',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-interaction',
                    'application',
                    'visit'
                  );
                }}
              >
                <PlaybackModule />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(
                        'ctint-mf-interaction',
                        'detail',
                        'visit'
                      );
                    }}
                  >
                    <PlaybackModuleDetail />
                  </CommonPermissionWrapper>
                </AuthLoginChecker>
              ),
            },
          ],
        },
        {
          path: '/call-patch',
          group: 'ctint-mf-agent',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-agent', 'listing', 'visit');
                }}
              >
                <AgentPage />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <CommonPermissionWrapper
                    customerPermissionHandler={(
                      globalConfig: TglobalConfig,
                      permissions: string[]
                    ) => {
                      return new CommonPermission(
                        globalConfig,
                        permissions
                      ).isPermissionEnabled(
                        'ctint-mf-agent',
                        'detail',
                        'visit'
                      );
                    }}
                  >
                    <CallDetail />
                  </CommonPermissionWrapper>
                </AuthLoginChecker>
              ),
            },
          ],
        },
        {
          path: '/demo-toolbar',
          group: 'ctint-mf-toolbar',
          component: (
            <AuthLoginChecker>
              <DemoToolBar />
            </AuthLoginChecker>
          ),
        },
        {
          path: '/demo-qm-admin',
          group: 'ctint-mf-qm',
          component: <DemoQMAdmin />,
        },
        {
          path: '/admin/qm',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                    // TODO: 需要確認修改权限名称
                  ).isPermissionEnabled('ctint-mf-admin', 'qm', 'visit');
                }}
              >
                <QMAdminScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/audit',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-admin', 'audit', 'visit');
                }}
              >
                <AdminAuditScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/user',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-admin', 'user', 'visit');
                }}
              >
                <AdminUserScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/super-dashboard',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-admin',
                    'super-dashboard',
                    'visit'
                  );
                }}
              >
                <AdminSuperDashboardComponent />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/super-dashboard',
          group: 'ctint-mf-super-dashboard',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-super-dashboard',
                    'user',
                    'visit'
                  );
                }}
              >
                <SuperDashboard />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/ctint-mf-template',
          group: 'ctint-mf-template',
          component: <TemplateModule />,
          subroutes: [
            {
              path: '/detail',
              component: <TemplateDetail />,
            },
          ],
        },
        {
          path: '/message',
          group: 'ctint-mf-msg',
          component: <MsgModule />,
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <MsgDetail />
                </AuthLoginChecker>
              ),
            },
          ],
        },
        {
          path: '/email',
          group: 'ctint-mf-email',
          component: (
            <AuthLoginChecker>
              <EmailDetailModule />
            </AuthLoginChecker>
          ),
        },
        {
          path: '/callback',
          group: 'ctint-mf-callback',
          component: (
            <AuthLoginChecker>
              <ManualQueueDetailModule />
            </AuthLoginChecker>
          ),
        },
        {
          path: '/manualQueue',
          group: 'ctint-mf-manualQueue',
          component: (
            <AuthLoginChecker>
              <ManualQueueModule />
            </AuthLoginChecker>
          ),
        },
        {
          path: '/info',
          group: 'ctint-mf-info',
          component: <Info />,
        },
        {
          path: '/style-guide',
          group: 'style-guide',
          component: <StyleGuide />,
        },
        {
          path: '/ui-builder',
          group: 'ui-builder',
          component: <UIBuilder />,
        },
        {
          path: '/ui-preview',
          group: 'ui-builder',
          component: <UIBuilderPreview />,
        },
        {
          path: '/user-admin',
          group: 'ctint-mf-user-admin',
          component: <UserAdminModule />,
          subroutes: [
            {
              path: '/detail',
              component: <UserAdminDetail />,
            },
          ],
        },
        {
          path: '/call',
          group: 'ctint-mf-call',
          component: (
            <AuthLoginChecker>
              <CallModule />
            </AuthLoginChecker>
          ),
        },
        {
          path: '/report',
          group: 'ctint-mf-report',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-report',
                    'application',
                    'visit'
                  );
                }}
              >
                <ReportMain />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/dnc',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-report',
                    'application',
                    'visit'
                  );
                }}
              >
                <DNCAdminScreen />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/campaign',
          group: 'ctint-mf-campaign',
          component: (
            <AuthLoginChecker>
              <CampaignModule />
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/detail',
              component: (
                <AuthLoginChecker>
                  <CampaignModuleDetailModule />
                </AuthLoginChecker>
              ),
            },
          ],
        },
        // add the new router for remote component
        {
          path: '/canned',
          group: 'ctint-mf-content-creation',
          component: (
            <AuthLoginChecker>
              {/* <div>---</div> */}
              <ContentCreationMain />
              {/* <div>---</div> */}
            </AuthLoginChecker>
          ),
          subroutes: [
            {
              path: '/category',
              component: (
                <AuthLoginChecker>
                  <ContentCreationCategory />
                </AuthLoginChecker>
              ),
            },
            {
              path: '/template',
              component: (
                <AuthLoginChecker>
                  <ContentCreationTemplate />
                </AuthLoginChecker>
              ),
            },
          ],
        },
        {
          path: '/admin/auto-reply',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled(
                    'ctint-mf-admin',
                    'auto-reply',
                    'visit'
                  );
                }}
              >
                <AutoReplyAdmin />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
        {
          path: '/admin/email',
          group: 'ctint-mf-admin',
          component: (
            <AuthLoginChecker>
              <CommonPermissionWrapper
                customerPermissionHandler={(
                  globalConfig: TglobalConfig,
                  permissions: string[]
                ) => {
                  return new CommonPermission(
                    globalConfig,
                    permissions
                  ).isPermissionEnabled('ctint-mf-admin', 'email', 'visit');
                }}
              >
                <EmailAdminPage />
              </CommonPermissionWrapper>
            </AuthLoginChecker>
          ),
        },
      ]}
      basePath={basePath}
    >
      <QueryClientProvider client={queryClient}>
        <InteractionProvider>
          <OpenStationProvider>
            <OpenBlockingProvider>
              <TbarProvider>
                <MainLayout>
                  {/*<Tdc />*/}
                  <PageBody basePath={basePath} />
                </MainLayout>
              </TbarProvider>
            </OpenBlockingProvider>
          </OpenStationProvider>
        </InteractionProvider>
      </QueryClientProvider>
    </PageRenderer>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default Page;
