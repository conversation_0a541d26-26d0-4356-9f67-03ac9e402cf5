import type { NextApiRequest, NextApiResponse } from 'next';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 只允许GET请求
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // 从查询参数中获取id和gc-token
    const { id, 'gc-token': gcToken } = req.query;

    // 验证必需参数
    if (!id || !gcToken) {
      return res.status(400).json({
        error: 'Missing required parameters: id and gc-token',
      });
    }

    // 确保参数是字符串类型
    const downloadId = Array.isArray(id) ? id[0] : id;
    const token = Array.isArray(gcToken) ? gcToken[0] : gcToken;

    // 构建请求URL
    const apiUrl = `https://api.mypurecloud.com.au/api/v2/downloads/${downloadId}`;

    // 发送GET请求到PureCloud API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    // 获取响应的buffer数据
    const buffer = await response.arrayBuffer();

    // 设置响应头
    res.status(response.status);
    // 复制原始响应的headers
    response.headers.forEach((value, key) => {
      res.setHeader(key, value);
    });
    // 返回原始响应数据
    return res.end(Buffer.from(buffer));
  } catch (error) {
    console.error('Error proxying request:', error);
    return res.status(500).json({
      error: 'Internal server error',
    });
  }
}
