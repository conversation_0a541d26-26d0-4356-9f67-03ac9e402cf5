import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { url, cdssAuthToken } = req.query;

  const Authorization = `Bearer ${cdssAuthToken}`;
  try {
    const result = await axios.get(url as string, {
      responseType: 'stream', // Set the response type to 'stream' to handle binary data
      headers: {
        traceId: uuidv4(),
        tenant: 'ccba',
        sourceId: 'ctint-mf-cdss',
        previousId: 'ctint-bff-cdss',
        'cdss-authorization': Authorization,
      },
    });

    const headers = result.headers;

    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader(
      'Access-Control-Allow-Methods',
      'GET, POST, PUT, DELETE, OPTIONS'
    );
    res.setHeader(
      'Access-Control-Allow-Headers',
      'X-Requested-With, Content-Type, Accept'
    );
    res.setHeader(
      'Cache-Control',
      'no-store, no-cache, must-revalidate, proxy-revalidate'
    );
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Content-Type', 'audio/mpeg');
    res.setHeader('Content-Disposition', 'attachment; filename=audio.mp3');
    res.setHeader('Content-Length', headers['content-length']);
    res.setHeader('Content-Range', 'bytes');
    res.setHeader('Accept-Ranges', 'bytes');

    (await result).data.pipe(res);
    // return result;
  } catch (error: any) {
    // If decryption fails or sourceId does not match
    res.status(403).json({
      data: Authorization,
      isSuccess: false,
      error: `Failed to get mp3 file: ${error?.message}`,
    });
  }
}
