@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /* width */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }

  /* Track */
  ::-webkit-scrollbar-track {
    @apply bg-common-divider rounded-full;
  }

  /* Handle */
  ::-webkit-scrollbar-thumb {
    @apply bg-primary rounded-full;
    background-clip: padding-box;
  }

  /* Handle on hover */
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-black;
  }
}

@layer components {
  .saa-keyword {
    font-weight: bold;
    text-decoration: underline;
    cursor: pointer;
  }
  .saa-keyword:hover {
    @apply text-primary;
  }
  .saa-article h2{
    @apply text-body font-bold mb-4;
  }
  .saa-article p{
    @apply text-remark mb-4;
  }
  @keyframes spinner-animation {
    0% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -105;
    }
    50% {
      stroke-dasharray: 80 10;
      stroke-dashoffset: -160;
    }
    100% {
      stroke-dasharray: 1 98;
      stroke-dashoffset: -300;
    }
  }
  .spinner-animation {
    transform-origin: center;
    animation-name: animation;
    animation: spinner-animation 1.2s infinite;
  }

  .custom-calendar .react-datepicker__header {
    @apply !bg-white border-b-0;
  }
  .custom-calendar .react-datepicker__current-month {
    display:none;
  }

  .day {
    @apply w-[25px] h-[25px] rounded-full hover:bg-transparent active:bg-primary-500 active:border-0;
  }

  /* .react-datepicker__day {
    @apply w-[25px] h-[25px] rounded-full hover:bg-transparent hover:text-red active:bg-primary-500 active:border-0;
  } */
  .react-datepicker__day.day:hover {
    @apply rounded-full;
  }

  .react-datepicker__day--selected.day {
    @apply !bg-primary-200 text-black hover:rounded-full;
  }
  .size-container {
    position: relative;
    height: 100%;
  }
  .call-panel {
    height: 100%;
    container-type: size;
    container-name: call-panel;
    display: flex;
    align-items: center;
    column-gap: 32px;
  }
  .call-panel__info {
    @container call-panel (max-width: 480px) {
      display: none;
    }
    @container call-panel (max-height: 180px) and (max-width: 880px) {
      display: none;
    }
  }

  .call-info {
    padding: 16px;
    height: 100%;
    overflow: auto;
    container-type: size;
    container-name: call-info;
    @container call-panel (max-height: 120px) {
      height: 100%;
      display: flex;
      align-items: center;
    }
  }
  .call-info__head {
    display: flex;
    align-items: center;
    column-gap: 16px;
    @container call-panel (max-height: 120px) {
      height: 100%;
    }
  }
  .call-info__all-infos {
    @container call-panel (max-height: 120px) {
      display: none;
    }
  }
  .call-info__name {
    display: flex;
    align-items: center;
  }
  .call-info__phone {
    display: none;
    @container call-panel (max-height: 120px) and (min-width: 321px) {
      display: block;
    }
  }
  .call-info__other-infos {
    @container call-panel (max-height: 320px) {
      display: none;
    }
    @container call-info (max-width: 320px) {
      display: none;
    }
  }
  .call-info__infos {
    padding-top: 16px;
    padding-bottom: 16px;
    display: flex;
    flex-wrap: wrap;
    row-gap: 16px;
  }
  .call-info__info {
    width: 33.3333%;
    @container call-info (max-width: 320px) {
      width: 50%;
    }
    @container call-info (max-width: 240px) {
      width: 100%;
    }
  }

  .call-buttons {
    height: 100%;
    container-type: size;
    container-name: call-buttons;
    display: flex;
    align-items: center;
    flex-direction: column;
    overflow: auto;
    @container (max-height: 320px) {
      flex-direction: row;
      overflow-y: hidden;
      overflow-x: auto;
    }
    @container (max-width: 240px) {
      flex-direction: row;
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  .call-buttons__info {
    width: 100%;
    padding: 16px;
    height: 0;
    flex: 1;
    @container call-panel (max-height: 320px) {
      display: none;
    }
    @container call-buttons (max-width: 320px) {
      display: none;
    }
  }
  .call-buttons__body {
    width: 100%;
    display: flex;
    padding: 16px;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    column-gap: 64px;
    border-top: 4px solid #ffac4a;
    @container call-panel (max-height: 320px) {
      border-top: 0;
      display: flex;
      align-items: center;
    }
    @container call-buttons (max-width: 320px) {
      border-top: 0;
    }
  }
  .call-buttons__buttons {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    overflow: auto;
    @container call-buttons (min-width: 320px) {
      flex-direction: row;
    }
    @container call-buttons (max-height: 120px) {
      flex-direction: row;
      flex-wrap: nowrap;
    }
    @container call-buttons (max-height: 120px) and (max-width: 240px) {
      flex-direction: column;
    }
  }
  .call-buttons__button {
    font-size: 24px;
    @container call-buttons (min-height: 90px) {
      font-size: 32px;
    }
  }
  .call-buttons__line {
    display: block;
    margin: 16px 0;
    @container call-buttons (max-height: 180px) {
      display: none;
    }
    @container call-buttons (max-width: 320px) {
      display: none;
    }
  }
  .call-buttons__label {
    display: block;
    @container call-buttons (max-height: 240px) {
      display: none;
    }
    @container call-buttons (max-width: 240px) {
      display: none;
    }
    @container call-buttons (min-width: 241px) and (max-height: 480px) {
      display: none;
    }
  }

  .toolbar-panel {
    height: 100%;
    /* container-type: size;
    container-name: toolbar-panel; */
    display: flex;
  }

  /* .profile-panel__info {
   d
    @container profile-panel (max-height: 180px) and (max-width: 880px) {
      display: none !important;
    }
  } */

  .toolbar-panel__toolbar {
    @container toolbar-panel (min-height: 240px) {
      display: none !important;
    }
  }

  .toolbar-panel__fullscreen {
    @container toolbar-panel (min-height: 240px) {
      display: flex !important;
    }
  }
}

/**** UI Builder ****/
#gjs {
  border: 3px solid #444;
}

/* Reset some default styling */
.gjs-cv-canvas {
  top: 0;
  width: 100%;
  height: 100%;
}

.gjs-mdl-container textarea {
  color: #000;
}

/*** Toolbar Demo ***/
.cdssctr-toolbar {
  padding-left: 32px;
  container-type: size;
  container-name: cdssctr-toolbar;
}

@container cdssctr-toolbar (max-width: 480px) {
  .cdssctr-toolbar__inputs,
  .cdssctr-toolbar__btns {
    display: none !important;
  }
}
@container cdssctr-toolbar (min-width: 481px) {
  .cdssctr-toolbar__sm-btn {
    display: none !important;
  }
}
.cdssctr-controlbar {
  container-type: size;
  container-name: cdssctr-controlbar;
}

@container cdssctr-controlbar (max-width: 640px) {
  .cdssctr-toolbar {
    padding-left: 16px;
  }
  .cdssctr-controlbar__call-btns {
    column-gap: 16px;
  }
}

@media  (max-height: 640px) {
  .toolbar-devbtn {
    display: none !important;
  }
}


@keyframes progress-animation {
  from {
    stroke-dashoffset: 251.2;
  }
  to {
    stroke-dashoffset: calc(251.2px - (251.2px * var(--score)) / 100);
  }
}

.progress-ring__ani {
  animation: progress-animation 1s ease-out forwards;
}


.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes highlight {
  0% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
  20% {
    background-color: rgba(233, 120, 20, 0.8);
    transform: scale(1.015);
    box-shadow: 0 0 10px 3px rgba(233, 120, 20, 0.3);
  }
  35% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
  65% {
    background-color: rgba(233, 120, 20, 0.8);
    transform: scale(1.015);
    box-shadow: 0 0 10px 3px rgba(233, 120, 20, 0.3);
  }
  80% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
  100% {
    background-color: transparent;
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(233, 120, 20, 0);
  }
}

.highlight {
  animation: highlight 2.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}






.heartbeat {
	-webkit-animation: heartbeat 1.5s ease-in-out 1s infinite both;
	        animation: heartbeat 1.5s ease-in-out 1s infinite both;
}
@-webkit-keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
            transform: scale(1);
    -webkit-transform-origin: center center;
            transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
            animation-timing-function: ease-out;
  }
  10% {
    -webkit-transform: scale(0.91);
            transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
            animation-timing-function: ease-in;
  }
  17% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
            animation-timing-function: ease-out;
  }
  33% {
    -webkit-transform: scale(0.87);
            transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
            animation-timing-function: ease-in;
  }
  45% {
    -webkit-transform: scale(1);
            transform: scale(1);
    -webkit-animation-timing-function: ease-out;
            animation-timing-function: ease-out;
  }
}
@keyframes heartbeat {
  from {
    -webkit-transform: scale(1);
            transform: scale(1);
    -webkit-transform-origin: center center;
            transform-origin: center center;
    -webkit-animation-timing-function: ease-out;
            animation-timing-function: ease-out;
  }
  10% {
    -webkit-transform: scale(0.91);
            transform: scale(0.91);
    -webkit-animation-timing-function: ease-in;
            animation-timing-function: ease-in;
  }
  17% {
    -webkit-transform: scale(0.98);
            transform: scale(0.98);
    -webkit-animation-timing-function: ease-out;
            animation-timing-function: ease-out;
  }
  33% {
    -webkit-transform: scale(0.87);
            transform: scale(0.87);
    -webkit-animation-timing-function: ease-in;
            animation-timing-function: ease-in;
  }
  45% {
    -webkit-transform: scale(1);
            transform: scale(1);
    -webkit-animation-timing-function: ease-out;
            animation-timing-function: ease-out;
  }
}
