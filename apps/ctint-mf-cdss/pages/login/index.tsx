import Icon from '@cdss-modules/design-system/components/_ui/Icon';

import { useEffect, useState } from 'react';
import { LoadingBlock, toast } from '@cdss-modules/design-system';
import { useRouter } from 'next/router';
import { useWindowDimensions } from '@cdss-modules/design-system/lib/hooks/useWindowDimension';
import {
  cn,
  extractErrorMessage,
  removeDeviceId,
} from '@cdss-modules/design-system/lib/utils';
import { fireLogin, fireCheckAuthUser } from '../../lib/api';
import CDSSImage from '@cdss-modules/design-system/components/_ui/CDSSImage';
import { fireSaveSession } from '@cdss-modules/design-system/lib/api';
import { LoginForm } from '../../components/_ui/_login/LoginForm';
import { UserCheckerForm } from '../../components/_ui/_login/UserCheckerForm';
import { basePath, mfName } from '../../lib/appConfig';
import { v4 as uuidv4 } from 'uuid';
import { ArrowRight } from 'lucide-react';
import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { ResetPasswordForm } from '@cdss/components/_ui/_login/ResetPasswordForm';
const LoginPage = (props: any) => {
  const { globalConfig } = props;

  // Per https://ctint-jira.atlassian.net/browse/CCBAP-251
  // Clients reported they want to see the password form directly, as they only have password-login accounts
  // So we add checking here to show username & password form directly in one step for the type of clients
  // TODO: to find reliable config to determine if the client only have password-login accounts
  const shouldShowPasswordFormDirectly =
    globalConfig?.microfrontends?.passwordLoginOnly;
  const [step, setStep] = useState<number>(
    shouldShowPasswordFormDirectly ? 1 : 0
  );
  const [isLoading, setIsLoading] = useState(true);
  const [userName, setUserName] = useState('');
  const [provider, setProvider] = useState(''); // "pure_engage" | "cdss" | "genesys_cloud"

  const [cdssToken, setCdssToken] = useState('');
  const { setPermissions } = usePermission();

  const { push, query } = useRouter();

  const height = useWindowDimensions();
  const authCode = query?.code as string;

  const isGC = provider === 'genesys-cloud';
  const deviceId = uuidv4();
  const cdssAuthToken =
    typeof localStorage !== 'undefined' &&
    localStorage.getItem('cdss-auth-token');
  let existedDeviceId: string;
  // (localStorage?.getItem('deviceId') as string) ?? deviceId;

  if (typeof localStorage !== 'undefined') {
    existedDeviceId = (localStorage?.getItem('deviceId') as string) ?? deviceId;
  }

  const handleLoginError = (title: string, msg?: string, error?: any) => {
    localStorage.removeItem('cdss-auth-redirect');
    localStorage.removeItem('cdss-auth-token');
    localStorage.removeItem('gc-access-token');
    localStorage.removeItem('userName');
    setIsLoading(false);
    removeDeviceId();

    let description = msg || 'There is an error';
    if (error) {
      description = `${description}: ${extractErrorMessage(error)}`;
    } else {
      description = `${description}.`;
    }

    toast({
      variant: 'error',
      title,
      description,
    });
  };

  // Step 1: Submit and username (for its validity and provider)
  const onSubmitUserName = async (data: any) => {
    setIsLoading(true);
    setTimeout(() => {
      // Store username in localstorage because the page will be redirected to Genesys Cloud
      localStorage.setItem('cdss-gc-username', data.username);
      if (!localStorage.getItem('deviceId')) {
        localStorage.setItem('deviceId', deviceId);
      }
      fireCheckAuthUser(data.username, basePath)
        .then((res) => {
          localStorage.setItem('loginPlatform', res?.data?.data?.platform);
          setUserName(data.username);
          setProvider(res?.data?.data?.platform);
          setIsLoading(false);
        })
        .catch((err) => {
          handleLoginError(
            'Failed to recognize user',
            'There is an error',
            err
          );
          // // TODO: remove after reset pw done
          // setUserName(data.username);
          // setStep(1);
        });
    }, 1500);
  };

  // Step 1.1: Handle flow depending on user's provider
  // GC - redirect to GC login page
  // Others - proceed to password input
  useEffect(() => {
    if (!provider) {
      return;
    } else if (isGC) {
      const redirectUrl = query.redirect
        ? decodeURIComponent(query.redirect as string)
        : '/';
      localStorage.setItem('cdss-auth-redirect', redirectUrl);

      const port = window?.location?.port ? `:${window.location.port}` : '';
      const redirectUri = window
        ? `${window?.location?.protocol}//${window?.location?.hostname}${port}${basePath}/login`
        : '';

      const gcAuthUrlConfig: string =
        props.globalConfig.microfrontends['gc-auth-url'] ||
        'https://login.mypurecloud.com.au/oauth/authorize';
      const gcAuthUrl =
        gcAuthUrlConfig +
        '?' +
        'response_type=code' +
        '&client_id=' +
        props.globalConfig.microfrontends['gc-client-id'] +
        `&redirect_uri=${redirectUri}`;
      window.location.href = gcAuthUrl;
    } else {
      setStep(1);
    }
  }, [isGC, provider, query.redirect]);

  useEffect(() => {
    if (cdssAuthToken) {
      setCdssToken(cdssAuthToken);
      const redirectUrl = query.redirect
        ? decodeURIComponent(query.redirect as string)
        : '/';
      push(redirectUrl);
      setIsLoading(false);
    }
  }, []);

  // Step 2A: Login Handling for Engage and CDSS - submit password
  const onSubmit = async (data: any) => {
    setIsLoading(true);
    if (!localStorage.getItem('deviceId')) {
      localStorage.setItem('deviceId', deviceId);
    }
    fireLogin(
      data.username,
      data.password,
      existedDeviceId,
      undefined,
      basePath
    )
      .then((res) => {
        if (res?.data.isSuccess) {
          // Store Token
          const token =
            res?.data?.data?.bearerToken?.replace('Bearer ', '') ?? '';
          if (token) {
            localStorage.setItem('cdss-auth-token', token);
          }
          const isFirstLogin = res?.data?.data?.isFirstLogin || true;
          if (isFirstLogin) {
            setStep(2);
          }

          // set permissions
          setPermissions(res?.data?.data?.permissions ?? []);

          // Store User Data
          const userData = res?.data?.data;
          if (userData) {
            // TODO: 设置用户名到localStorage
            localStorage.setItem('userName', userData.userName);
            fireSaveSession(existedDeviceId, userData, basePath)
              .then(() => {
                const redirectUrl = query.redirect
                  ? decodeURIComponent(query.redirect as string)
                  : '/';
                push(redirectUrl);
              })
              .catch((e) =>
                handleLoginError(
                  'Failed Saving Session',
                  'There is an error saving session',
                  e
                )
              );
          }
        } else {
          handleLoginError('Failed', 'Login failed');
        }
      })
      .catch((err) => {
        handleLoginError('Failed', 'Login failed', err);
        // // TODO: remove after reset pw done
        // setStep(2);
      });
  };

  // Step 2B: Login Handling for GC - submit password
  useEffect(() => {
    if (authCode) {
      const gcUserName = localStorage.getItem('cdss-gc-username');
      const cdssAuthRedirect =
        localStorage.getItem('cdss-auth-redirect') || '/';
      const redirectUrlParam =
        cdssAuthRedirect && cdssAuthRedirect !== '/'
          ? `?redirect=${cdssAuthRedirect}`
          : '';
      if (!gcUserName) {
        handleLoginError('Failed', 'Login failed: No username found');
        push(`/login${redirectUrlParam}`);
        return () => {
          setIsLoading(false);
        };
      }
      setUserName(gcUserName);
      fireLogin(gcUserName, null, existedDeviceId, authCode, basePath)
        .then((res) => {
          if (res?.data.isSuccess) {
            // Store Token
            const cdssAuthToken =
              res?.data?.data?.bearerToken?.replace('Bearer ', '') ?? '';
            // const gcAuthToken =
            //   res?.data?.data?.accessToken?.replace('Bearer ', '') ?? '';
            const gcAuthToken =
              res?.data?.data?.multiServiceTokens?.genesysCloud?.accessToken?.replace(
                'Bearer ',
                ''
              ) ?? '';

            // set permissions
            setPermissions(res?.data?.data?.permissions ?? []);

            if (cdssAuthToken) {
              localStorage.setItem('cdss-auth-token', cdssAuthToken);
            }
            if (gcAuthToken) {
              localStorage.setItem('gc-access-token', gcAuthToken);
            }
            // Store User Data
            const userData = res?.data?.data;

            if (userData) {
              localStorage.setItem('userName', userData.userName);
              fireSaveSession(existedDeviceId, userData, basePath)
                .then(() => {
                  push(cdssAuthRedirect);
                })
                .catch((e) => {
                  handleLoginError(
                    'Failed Saving Session',
                    'There is an error saving session',
                    e
                  );
                  push(`/login${redirectUrlParam}`);
                  push(cdssAuthRedirect);
                });
            }
          } else {
            handleLoginError('Failed', 'Login failed');
            push(`/login${redirectUrlParam}`);
          }
        })
        .catch((err) => {
          handleLoginError('Failed', 'Login failed', err);
          push(`/login${redirectUrlParam}`);
        });
    } else {
      setIsLoading(false);
    }
    return () => {
      setIsLoading(false);
      localStorage.removeItem('cdss-auth-redirect');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [authCode]);

  // Step 3: Reset Password (First Login after acc creation or admin pw reset)
  const onSubmitReset = async (data: any) => {
    setIsLoading(true);
    console.log('onSubmitReset data', data);

    // On success
    localStorage.removeItem('cdss-auth-token');
    localStorage.removeItem('permissions');
    toast({
      variant: 'success',
      title: 'Success',
      description:
        'Password reset successfully. Please login with your new password',
    });
    setStep(1);
    setIsLoading(false);
  };
  // Login Logo handling
  const loginLogo = globalConfig?.microfrontends?.loginLogo;
  const noLoginLogo = loginLogo === 'none';

  if (cdssToken !== '') {
    return (
      <div className="w-full h-screen flex items-center justify-center py-12">
        <LoadingBlock />
      </div>
    );
  }

  return (
    <div
      className={cn(
        'relative w-screen h-screen flex bg-no-repeat bg-center md:bg-[length:100%_100%]',
        height < 630 && 'items-center'
      )}
      style={{ backgroundImage: `url('${basePath}/images/curve.svg')` }}
    >
      <div className="w-full absolute p-6 top-0 flex items-center gap-x-6">
        <Icon
          name="fullLogo"
          className="!w-[125px] !h-10"
        />
      </div>
      <div
        className={cn(
          'relative w-4/5 md:w-1/2 md:max-w-[605px] max-h-[80%] mx-auto md:mx-auto bg-[rgba(255,255,255,0.4)] backdrop-blur-sm shadow-[0_4px_30px_0px_rgba(0,0,0,0.02)] rounded-2xl flex flex-col items-center pt-10',
          height < 630
            ? 'py-6 px-2'
            : noLoginLogo
              ? 'mt-[170px] md:mt-[210px] py-4 md:py-10 px-2 md:px-5 h-fit'
              : 'mt-[120px] md:mt-[160px] py-4 md:py-10 px-2 md:px-5 h-fit'
        )}
      >
        {height >= 400 && !noLoginLogo && (
          <CDSSImage
            basePath={basePath}
            src={loginLogo ?? '/images/CTINT-logo.svg'}
            width={height >= 631 ? 299 : 240}
            alt={''}
          />
        )}
        {isGC ? (
          <>
            <div className="flex justify-center items-center gap-x-6">
              <LoadingBlock />
              <div className="flex items-center">
                <div className="animate-bounce-hr">
                  <ArrowRight size={36} />
                </div>
              </div>
              <CDSSImage src={`${basePath}/images/logo-gc.svg`} />
            </div>
            <p className="text-body font-bold -mt-4">
              Redirecting you to Genesys Cloud to login...
            </p>
          </>
        ) : (
          <>{isLoading && <LoadingBlock />}</>
        )}
        <div
          className={cn(
            'flex flex-col w-full gap-10 h-full',
            height < 630 ? 'overflow-auto mt-[20px]' : 'mt-[50px]',
            (height < 400 || noLoginLogo) && 'mt-0',
            (isLoading || isGC || authCode) && 'hidden'
          )}
        >
          {step === 0 ? (
            <UserCheckerForm
              isLoading={isLoading}
              setIsLoading={(loading: boolean) => setIsLoading(loading)}
              onSubmit={onSubmitUserName}
            />
          ) : step === 1 ? (
            <LoginForm
              isLoading={isLoading}
              setIsLoading={(loading: boolean) => setIsLoading(loading)}
              defaultValues={{ username: userName, password: '' }} // reset password value when login fail
              readonlyFields={
                shouldShowPasswordFormDirectly ? [] : ['username']
              }
              onSubmit={onSubmit}
              onBack={
                shouldShowPasswordFormDirectly
                  ? undefined
                  : () => {
                      setStep(0);
                      setProvider('');
                    }
              }
            />
          ) : (
            <ResetPasswordForm
              isLoading={false}
              setIsLoading={(loading: boolean) => setIsLoading(loading)}
              onSubmit={onSubmitReset}
              onBack={() => {
                setStep(0);
                setProvider('');
              }}
            />
          )}
        </div>
        {/* <a
          className="w-fit mx-auto mt-4 origin-center bottom-6 left-0 right-0 italic text-[#0075FF]"
          href="/forget-password"
        >
          Forget password
        </a> */}
      </div>
      {/* <TipsSlider /> */}
    </div>
  );
};

export const getServerSideProps = async () => {
  const globalConfig = loadGlobalConfig(mfName);
  const publicEnvVars = Object.keys(process.env).reduce(
    (publicVars: any, key) => {
      if (key.startsWith('CDSS_PUBLIC_')) {
        publicVars[key] = process.env[key];
      }
      return publicVars;
    },
    {}
  ) as any;
  return {
    props: {
      globalConfig,
      publicEnvVars,
    },
  };
};

export default LoginPage;
