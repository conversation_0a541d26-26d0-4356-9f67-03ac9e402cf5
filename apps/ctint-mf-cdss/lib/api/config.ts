const isUAT = process.env.NEXT_PUBLIC_ENVIRONMENT !== 'dev';
const isRemoteEndpoint = isUAT;

export const apiConfig = {
  isUAT,
  paths: {
    login: isRemoteEndpoint
      ? `/api/process-api/ctint-auth/token`
      : `/api/login/ctint-auth/token`,
    checkAuthUser: isRemoteEndpoint
      ? `/api/process-api/ctint-auth/checkAuthUser`
      : `/api/login/ctint-auth/checkAuthUser`,
    geVerifyUser: '/api/process-api/ctint-auth/verify/user',
    userList: '/api/process-api/ctint-user/admin/user?site=supervisordashboard',
    userAuditLogs: '/api/process-api/ctint-audit-log/user/auditLog',
    userCreate: '/api/process-api/ctint-user/admin/user/create',
    userUpdate: '/api/process-api/ctint-user/admin/user/update',
    getRoles: '/api/process-api/ctint-user/admin/role',
    getUserGroups: '/api/process-api/ctint-user/admin/usergroup',
    session: isUAT
      ? '/api/process-api/ctint-session/ctint-bff-cdss/session'
      : '/api/session/ctint-session/ctint-bff-cdss/session',
    logout: `/api/process-api/ctint-auth/logout`,
    sendMessage:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}/messages',
    callControl: {
      getAllActiveConversations:
        '/api/process-api/ctint-conv/conversation/activeList',
      getAllConversations: '/api/process-api/ctint-conv/conversation',
      action: '/api/process-api/ctint-call-control/conversations/calls',
      userRoutingStatus:
        '/api/process-api/ctint-call-control/routingstatus/users',
      updateRoutingStatus:
        '/api/process-api/ctint-call-control/presences/users',
      getAllAgentStatus: '/api/process-api/ctint-call-control/presences',
      getAllStations: '/api/process-api/ctint-call-control/stations',
      call: '/api/process-api/ctint-call-control/conversations/call',
      blindTransfer:
        '/api/process-api/ctint-call-control/conversations/calls/blind/transfer',
      consult:
        '/api/process-api/ctint-call-control/conversations/calls/consult',
      consultDisconnect:
        '/api/process-api/ctint-call-control/conversations/calls/consult/disconnect',
      consultCancel:
        '/api/process-api/ctint-call-control/conversations/calls/consult/cancel',
      conference:
        '/api/process-api/ctint-call-control/conversations/calls/conference',
      getWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapupcodes',
      submitWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapup/add',
      updateWrapup:
        '/api/process-api/ctint-call-control/conversations/calls/wrapup/update',
      attributes:
        '/api/process-api/ctint-call-control/conversations/calls/attributes',
      getWrapupCategory: '/api/process-api/ctint-call-control/wrapUp',
      getCustomerInfo: '/api/process-api/ctint-conv/conversation/customer/info', ///api/process-api/ctint-conv/conversation/customer/info?customerId=123
      blocking: '/api/process-api/ctint-call-control/blocking/rule',
    },
    users: {
      getAllUsers: '/api/process-api/ctint-user/users', // /api/process-api/ctint-user/users?filterType=all
      getCurrentUser: '/api/process-api/ctint-user/user/me',
      getAllWorkgroups: '/api/process-api/ctint-user/queues', // /api/process-api/ctint-user/queues?filterType=all
      searchUsers: '/api/process-api/ctint-user/users/search',
      searchWorkgroups: '/api/process-api/ctint-user/queues/search',
      getWorkGroupsByUser: '/api/process-api/ctint-user/queues',
      getDncList: '/api/process-api/ctint-user/admin/dnc',
      getTemplate: '/api/process-api/ctint-user/admin/dnc/template',
      createDnc: '/api/process-api/ctint-user/admin/dnc/create',
      deleteDnc: '/api/process-api/ctint-user/admin/dnc/delete',
      importDnc: '/api/process-api/ctint-user/admin/dnc/import',
      exportDnc: '/api/process-api/ctint-user/admin/dnc/export',
      getChannelData: '/api/process-api/ctint-user/admin/dnc/channelType/data',
    },
    miniWallboard: {
      getUserAggregates:
        '/api/process-api/ctint-user/users/conversations/aggregates?site=miniwallboard',
      getQueueAggregates:
        '/api/process-api/ctint-user/queues/aggregates?site=miniwallboard',
    },
    config: {
      getTenatConfig: '/api/process-api/ctint-config/tenantconfig',
      updateTenantConfig: '/api/process-api/ctint-config/tenantconfig',
    },
    aboutAllGroupApi: '/api/process-api/ctint-user/admin/group', //user/role/queue/station
    qm: {
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      update_standard_script_score:
        '/api/process-api/ctint-qm/sop/standardScript/detail',
      upload_dictionary: '/api/process-api/ctint-qm/sop/dataDict/upload',
      meta_data_mapping:
        '/api/process-api/ctint-qm/sop/metadataTranslation/list',
    },
    recordings: '/api/process-api/ctint-conv/recordings',
    export: '/api/process-api/ctint-conv/interaction',
    gc_recordings: '/api/process-api/ctint-conv/interactions',
    sort: 'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings',
    userconfig: '/api/process-api/ctint-config/userconfig',
    transcript:
      'http://localhost:4400/ctint/mf-cdss/api/mock/ctint-conv/recordings/transcript',
    detail: {
      info: '/api/process-api/ctint-conv/interaction/detail',
      media: '/api/process-api/ctint-conv/interaction/recordings',
      transcript:
        '/api/process-api/ctint-conv/interaction/recordings/transcript',
      evaluation_form: '/api/process-api/ctint-qm/sop/form/list',
      evaluation_list: '/api/process-api/ctint-qm/inspection/evaluation/list',
      evaluation_assign: '/api/process-api/ctint-qm/inspection/evaluation',
      evaluation_nlp_result_update:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail',
      evaluation_nlp_result_list:
        '/api/process-api/ctint-qm/inspection/nlpResult/detail/list',
      evaluation_result_update:
        '/api/process-api/ctint-qm/inspection/evaluation/detail',
      stand_script_result: '/api/process-api/ctint-qm/sop/standardScript/list',
      download_nlp_report:
        '/api/process-api/ctint-qm/inspection/excel/download',
    },
    completed_calls_pureconnect: '/api/process-api/ctint-ccba-api/calls',
    completed_calls_pureconnect_detail: {
      info: '/api/process-api/ctint-ccba-api/calls/detail',
      update: '/api/process-api/ctint-ccba-api/calls/detail/update',
    },
    getActiveConversations:
      '/api/process-api/ctint-conv/conversation/activeList',
    getAutoReplyTemplateList: '/api/process-api/ctint-conv/contents',
    getAutoReplyTemplateDetail: '/api/process-api/ctint-conv/contents/detail',
    getQueues: '/api/process-api/ctint-user/queues?site=supervisordashboard',
    getQueueOptions: '/api/process-api/ctint-user/queues',
    conversationAlertAction:
      '/api/process-api/ctint-call-control/conversations/calls',
    userRoutingStatus:
      '/api/process-api/ctint-call-control/routingstatus/users',
    // updateRoutingStatus: '/api/process-api/ctint-call-control/presences/users',
    updateRoutingStatus:
      '/api/process-api/ctint-call-control/routingstatus/idle',

    getAllAgentStatus: '/api/process-api/ctint-call-control/presences',
    getUatMessage:
      '/api/process-api/ctint-message/api/v1/cdss/conversations/messages/{conversationId}',
    getTenantConfig: '/api/process-api/ctint-session/ctint-bff-cdss/session',
    uploadAvatar: '/api/process-api/ctint-user/images/upload',
    getImage: '/api/process-api/ctint-user/images/',
    manualQueue: {
      emailQueue:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/get/email/queue',
      newEmailConversation:
        '/api/process-api/ctint-manual-queue/api/v1/cdss-email/new/email/conversations',
      getEmailRules:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/page',
      getAutoReplyFilterRule:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/autoReplyFilterRule/page',
      getAutoReplyFilterRuleDetail:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/autoReplyFilterRule/detail',
      updateAutoReplyFilterRule:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/autoReplyFilterRule/patch',
      deleteAutoReplyFilterRule:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/autoReplyFilterRule/delete',
      getEmailRuleDetail:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/detail',
      // getAutoReplyTemplateList:
      //   '/api/process-api/ctint-manual-queue/api/v1/email-rules/autoReplyTemplateList',
      updateEmailRule:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/patch',
      createEmailRule:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/patch',
      deleteEmailRule:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/delete',
      getAutoReplyTemplateAttachment:
        '/api/process-api/ctint-manual-queue/api/v1/file/get/access/url',
      // Auto Reply Rules
      getAutoReplyRuleDetail:
        '/api/process-api/ctint-manual-queue/api/v1/routing-rules/detail',
      getAutoReplyRuleList:
        '/api/process-api/ctint-manual-queue/api/v1/routing-rules/page',
      // Used for both create and update
      updateAutoReplyRule:
        '/api/process-api/ctint-manual-queue/api/v1/routing-rules/patch',
      deleteAutoReplyRule:
        '/api/process-api/ctint-manual-queue/api/v1/routing-rules/delete',
      createOrUpdateFullAddress:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/full-address/patch',
      getFullAddressList:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/full-address/list',
      getFullAddressGroupById:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/full-address/list',
      getEmailDefaultQueues:
        '/api/process-api/ctint-manual-queue/api/v1/email-rules/default/queues',
      getPlatformAccounts:
        '/api/process-api/ctint-campaign/api/v1/campaign/message/platformAccount',
    },
  },
};
