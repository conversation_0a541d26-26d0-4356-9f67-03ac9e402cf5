import {
  ResizablePanelGroup,
  ResizablePanel,
  ResizableHandle,
} from '@cdss-modules/design-system/components/_ui/Resizable';

import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useState } from 'react';
import { Button, Panel } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import React from 'react';
import useWebSocket from 'react-use-websocket';
import { v4 as uuidv4 } from 'uuid';

const CallPanel = ({
  show,
  area,
  bgColor,
  onClick,
}: {
  show: boolean;
  area: string;
  bgColor: string;
  onClick: () => void;
}) => {
  const [active, setActive] = useState(false);

  const handleActive = () => {
    setActive(!active);
  };

  if (!show)
    return (
      <div
        className={`size-full padding-32 text-t3 font-bold flex justify-center items-center ${bgColor}`}
      >
        <div className="flex flex-col gap-4 items-center justify-center">
          <div>Other Area {area}</div>
          <Button onClick={() => onClick()}>Put the panel here</Button>
        </div>
      </div>
    );

  return (
    <div className="call-panel">
      <Panel containerClassName="h-full call-panel__info">
        <div className="call-info">
          <div className="call-info__head">
            <div className="call-info__name">
              <h2 className="font-bold text-t5">Chan Tai Man</h2>
            </div>
            <span className="call-info__phone font-bold">(Tel: 54325432)</span>
          </div>
          <div className="call-info__all-infos">
            <div className="w-full h-[1px] border border-grey-200 my-2" />
            <div className="call-info__infos">
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">HKDI:</h3>
                <p>A12345678</p>
              </div>
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">Mobile no:</h3>
                <p>54325432</p>
              </div>
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">Gender:</h3>
                <p>M</p>
              </div>
              <div className="call-info__info">
                <h3 className="font-bold mb-[4px]">Email:</h3>
                <p><EMAIL></p>
              </div>
            </div>
            <div className="call-info__other-infos">
              <div className="w-full h-[1px] border border-grey-200 my-2" />
              <div className="call-info__infos ">
                {new Array(10).fill(0).map((_, index) => (
                  <div
                    key={index}
                    className="call-info__info"
                  >
                    <h3 className="font-bold mb-[4px]">Info:</h3>
                    <p>Info</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </Panel>
      <Panel containerClassName="h-full">
        <div className="call-buttons">
          <div className="call-buttons__info overflow-auto">
            <h2 className="font-bold text-t5">Some more info</h2>
            <p className="mt-4">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do
              eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
              enim ad minim veniam, quis nostrud exercitation ullamco laboris
              nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in
              reprehenderit in voluptate velit esse cillum dolore eu fugiat
              nulla pariatur. Excepteur sint occaecat cupidatat non proident,
              sunt in culpa qui officia deserunt mollit anim id est laborum.
            </p>
          </div>
          <div className="call-buttons__body">
            <div className="call-buttons__buttons">
              <CallControlButton
                icon={
                  <Icon
                    name="pad"
                    size={'2em'}
                  />
                }
                label={'Pad'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="mute"
                    size={'2em'}
                  />
                }
                label={'Mute'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="hold"
                    size={'1em'}
                  />
                }
                label={'Hold'}
                active={active}
                handleOnChange={handleActive}
              />
              <div className="w-full h-[1px] border border-grey-200 call-buttons__line" />
              <CallControlButton
                icon={
                  <Icon
                    name="transfer"
                    size={'1em'}
                  />
                }
                label={'Transfer'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="consult"
                    size={'1em'}
                  />
                }
                label={'Consult'}
                active={active}
                handleOnChange={handleActive}
              />
              <CallControlButton
                icon={
                  <Icon
                    name="group"
                    size={'1em'}
                  />
                }
                label={'Group'}
                active={active}
                handleOnChange={handleActive}
              />
            </div>
          </div>
        </div>
      </Panel>
    </div>
  );
};

const CallPanelDemo = () => {
  const [active, setActive] = useState('A');

  return (
    <div className="flex-1 flex w-full h-0 gap-x-3 overflow-auto">
      <ResizablePanelGroup
        autoSaveId="example"
        direction="vertical"
        className="h-full flex flex-col gap-y-3"
      >
        <ResizablePanel className="h-full">
          <ResizablePanelGroup
            autoSaveId="example"
            direction="horizontal"
            className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
          >
            <ResizablePanel>
              <CallPanel
                show={active === 'A'}
                area="A"
                bgColor="bg-red-400"
                onClick={() => {
                  setActive('A');
                }}
              />
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel className="h-full">
              <CallPanel
                show={active === 'B'}
                area="B"
                bgColor="bg-green-400"
                onClick={() => {
                  setActive('B');
                }}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel className="h-full">
          <ResizablePanelGroup
            autoSaveId="example"
            direction="horizontal"
            className="flex-1 flex w-full h-0 gap-x-3 overflow-auto"
          >
            <ResizablePanel className="h-full">
              <CallPanel
                show={active === 'C'}
                area="C"
                bgColor="bg-yellow-400"
                onClick={() => {
                  setActive('C');
                }}
              />
            </ResizablePanel>
            <ResizableHandle />
            <ResizablePanel className="h-full">
              <CallPanel
                show={active === 'D'}
                area="D"
                bgColor="bg-blue-400"
                onClick={() => {
                  setActive('D');
                }}
              />
            </ResizablePanel>
          </ResizablePanelGroup>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
};

const ToolBarDemo = () => {
  return (
    <div className="flex flex-col h-full overflow-auto bg-common-bg gap-y-2 text-footnote">
      <div className="flex flex-col h-0 flex-1 bg-white rounded-lg">
        <div className="flex items-center justify-between w-full border-b border-b-grey-200">
          <div className="flex px-2 gap-x-6">
            <div className="py-1 px-2 text-grey-500">My info</div>
            <div className="py-1 px-2 border-b-[2px] border-b-primary">
              Outbound call
            </div>
          </div>
          <div className="flex px-2 gap-x-6">
            <div className="flex items-center gap-1">
              <div className="inline-flex size-2 bg-status-success rounded-full" />
              Available,
            </div>
            <div className="flex items-center gap-2 text-status-success">
              ACD
              <Switch
                size="s"
                activeColor="green"
              />
            </div>
          </div>
        </div>
        <div className="flex items-stretch p-2 h-0 flex-1 gap-x-4">
          <div className="flex flex-col h-full">
            <div className="font-bold">From</div>
            <div className="flex h-0 flex-1 gap-x-2 w-[320px]">
              <div className="flex items-center">
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  Me
                </div>
              </div>
              <div className="h-full w-px bg-grey-200 " />
              <div className="flex flex-wrap items-center gap-2">
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  AB
                </div>
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  CD
                </div>
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  EF
                </div>
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  12
                </div>
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  34
                </div>
                <div className="flex items-center py-1 px-4 rounded-full border border-grey-200">
                  56
                </div>
              </div>
            </div>
          </div>
          <div className="h-full w-px bg-grey-200" />
          <div className="flex flex-col h-full">
            <div className="font-bold mb-2">To</div>
            <div className="flex items-center h-0 flex-1 gap-x-2">
              <div className="inline-flex">
                <Button
                  asSquare
                  variant="secondary"
                >
                  <Icon
                    name="phone"
                    size="1em"
                  />
                </Button>
              </div>
              <div className="inline-flex min-w-[180px]">
                <Input
                  beforeIcon={<Icon name="pad" />}
                  placeholder="Placeholder"
                />
              </div>
              <button className="flex-none inline-flex items-center justify-center rounded-full size-8 bg-status-success text-white">
                <Icon
                  name="phone"
                  size="1em"
                />
              </button>
            </div>
          </div>
          <div className="h-full w-px bg-grey-200" />
          <div className="flex flex-wrap w-full h-full gap-y-2">
            <div className="flex flex-col w-1/3">
              <div className="font-bold">Name:</div>
              <div>HoYin (M)</div>
            </div>
            {new Array(5).fill(0).map((_, index) => (
              <div
                key={index}
                className="flex flex-col w-1/3"
              >
                <div className="font-bold">Title:</div>
                <div>Lorem ipsum</div>
              </div>
            ))}
          </div>
        </div>
      </div>
      <div className="flex items-center p-2 gap-x-6 bg-white rounded-t-lg">
        <div className="font-bold">WEBCHAT</div>
        <div className="text-status-success">KPI: 02:32/03:00</div>
        <div className="text-status-danger">SLA: 3s 40/3s 30</div>
        <div>Inbound: 112</div>
        <div>Outbound: 21</div>
        <button className="flex items-center text-tertiary">
          <Icon
            name="pin"
            size="1em"
          />
        </button>
      </div>
    </div>
  );
};

export const SocketDemo = () => {
  const [isConnected, setIsConnected] = useState(false);
  const deviceId = localStorage.getItem('deviceId') || '';
  const { sendMessage, lastMessage, readyState } = useWebSocket(
    `ws://ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com/ctint/cdss-ws/ws?deviceID=${deviceId}`,
    {
      share: true,
      retryOnError: true,
      shouldReconnect: (e) => true,
      reconnectInterval: 4,
      reconnectAttempts: 2,
      onOpen: () => {
        console.log('connected');
        sendMessage(
          JSON.stringify({
            traceId: uuidv4(),
            tenant: 'ccba',
            sourceId: 'ctint-mf-cdss',
            previousId: 'ctint-bff-cdss',
            deviceId,
            'cdss-authorization':
              'Bearer ' + localStorage.getItem('cdss-auth-token'),
          })
        );
        setIsConnected(true);
      },
      onClose: () => {
        console.log('disconnected');
        setIsConnected(false);
      },
      onError: (event) => console.error('WebSocket error:', event),
    }
  );

  return (
    <div className="p-4 flex gap-4 items-center padding-2 bg-white rounded-lg">
      <h2 className="font-bold">SOCKET:</h2>
      <p>isConnected: {`${isConnected}`}</p>
      <p>readyState: {readyState}</p>
      <p>lastMessage: {`${lastMessage?.data}`}</p>
    </div>
  );
};

export const ToolBar = () => {
  // State variables to store the entire message for each type
  const iframeRef = React.useRef<HTMLIFrameElement>(null);
  const [screenPopMessage, setScreenPopMessage] = useState<any>(undefined);
  const [processCallLogMessage, setProcessCallLogMessage] =
    useState<any>(undefined);
  const [openCallLogMessage, setOpenCallLogMessage] = useState<any>(undefined);
  const [interactionSubscriptionMessage, setInteractionSubscriptionMessage] =
    useState<any>(undefined);
  const [userActionSubscriptionMessage, setUserActionSubscriptionMessage] =
    useState<any>(undefined);
  const [notificationSubscriptionMessage, setNotificationSubscriptionMessage] =
    useState<any>(undefined);
  const [searchText, setSearchText] = useState<any>(undefined);

  // useEffect(() => {
  //   const handleMessage = (event: any) => {
  //     try {
  //       const message = JSON.parse(event?.data || '{}');
  //       if (message) {
  //         switch (message.type) {
  //           case 'screenPop':
  //             setScreenPopMessage(event.data);
  //             break;
  //           case 'processCallLog':
  //             setProcessCallLogMessage(event.data);
  //             break;
  //           case 'openCallLog':
  //             setOpenCallLogMessage(event.data);
  //             break;
  //           case 'interactionSubscription':
  //             console.log('interactionSubscription', event.data);
  //             setInteractionSubscriptionMessage(event.data);
  //             break;
  //           case 'userActionSubscription':
  //             setUserActionSubscriptionMessage(event.data);
  //             break;
  //           case 'notificationSubscription':
  //             setNotificationSubscriptionMessage(event.data);
  //             break;
  //           case 'contactSearch':
  //             setSearchText(message.data.searchString);
  //             // sendContactSearch();
  //             break;
  //           default:
  //             break;
  //         }
  //       }
  //     } catch (error) {
  //       console.error("Error parsing message data:", error);
  //     }
  //   };

  //   window.addEventListener('message', handleMessage);

  //   return () => {
  //     window.removeEventListener('message', handleMessage);
  //   };
  // }, [searchText]);

  const doInteraction = (action: string) => {
    console.log('process interaction state change');
    const lastInteractionPayload = interactionSubscriptionMessage
      ? JSON.parse(interactionSubscriptionMessage || {})
      : null;
    let interactionId;
    if (lastInteractionPayload?.data?.interaction?.old) {
      interactionId = lastInteractionPayload?.data?.interaction?.old?.id;
    } else {
      interactionId = lastInteractionPayload?.data?.interaction?.id;
    }
    const payload = {
      action: action,
      id: interactionId,
    };
    const softphoneElem = iframeRef?.current as HTMLIFrameElement;
    console.log('softphoneElem', softphoneElem, interactionSubscriptionMessage);
    softphoneElem?.contentWindow?.postMessage(
      JSON.stringify({
        type: 'updateInteractionState',
        data: payload,
      }),
      '*'
    );
  };
  return (
    <div className="flex flex-col h-full gap-y-4 overflow-auto">
      <div className="h-[178px]">
        <ToolBarDemo />
      </div>
      <SocketDemo />
      <div className="flex gap-4 items-center padding-2 bg-white rounded-lg">
        <Button onClick={() => doInteraction('pickup')}>pickup</Button>
        <Button onClick={() => doInteraction('disconnect')}>disconnect</Button>
        <Button onClick={() => doInteraction('hold')}>hold</Button>
        <Button onClick={() => doInteraction('mute')}>mute</Button>
        <Button onClick={() => doInteraction('securePause')}>
          securePause
        </Button>
        <div className="max-w-[200px] overflow-auto whitespace-nowrap">
          {JSON.stringify(interactionSubscriptionMessage ?? {})}
        </div>
      </div>
      <div className="h-0 flex-1 bg-red-200">
        <div className="h-full overflow-hidden">
          <div className="w-full h-full">
            {/* <iframe
              width="100%"
              height="100%"
              src="https://cdss01-jupiter.ctint.com/pages/login?redirect=%2F&accesstoken=35a451309da393b0ef1b276ddfa87ce821c5b1b4&server=https%3A%2F%2Fcdss01-jupiter.ctint.com"
            /> */}
            {/* <div className="softphone w-full h-full">
              <iframe
                ref={iframeRef}
                id="softphone"
                width="100%"
                height="100%"
                allow="camera *; microphone *; autoplay *; hid *" src="https://apps.mypurecloud.com.au/crm/embeddableFramework.html?enableFrameworkClientId=true"
              />
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolBar;
