import { useCallback, useEffect, useState, useMemo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { debounce } from 'lodash';
import dayjs from 'dayjs';

import { Button, useRole, useToast } from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { SortingButton } from '@cdss-modules/design-system';
import { EllipsisVertical } from 'lucide-react';
import StatusBadge from '@cdss-modules/design-system/components/_ui/StatusBadge';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import {
  Popup,
  PopupTrigger,
  PopupContent,
  PopupFooter,
  PopupClose,
} from '@cdss-modules/design-system/components/_ui/Popup';

import { basePath } from '@cdss/lib/appConfig';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import { fireGetEmailRules, fireDeleteEmailRule } from '@cdss/lib/api';

import { useEmailNavigationStore } from '../../../_store/emailNavigationStore';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import { useEmailQueueOptions } from '../../../_hooks/useEmailQueueOptions';

// Types
type TMailboxRoutingRule = {
  id: string;
  mailboxAddress: string;
  status: 'active' | 'inactive';
  defaultQueue: string;
  lastUpdated: string;
  isActive: number;
  defaultQueueId: string;
  defaultQueueName: string;
  defaultAutoReplyId?: string;
};

// Real API call to fetch mailbox routing rules
const fetchMailboxRoutingRules = async (
  page: number,
  pageSize: number,
  sortOrder?: any,
  filters?: {
    status?: string;
    queue?: string;
    search?: string;
  }
) => {
  // Construct query parameters
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());

  // Add filters
  if (filters) {
    if (filters.search) {
      params.append('mailBoxAddress', filters.search);
    }

    if (filters.status && filters.status !== 'all') {
      params.append('isActive', filters.status === 'active' ? '1' : '0');
    }

    if (filters.queue && filters.queue !== 'all') {
      params.append('defaultQueueId', filters.queue);
    }
  }

  // Add ordering parameters
  if (sortOrder) {
    const sortField = Object.keys(sortOrder)[0];
    const sortDirection = sortOrder[sortField];

    if (sortField && sortDirection) {
      // Map UI field names to API field names
      const fieldMapping: { [key: string]: string } = {
        mailboxAddress: 'mailBoxAddress',
        defaultQueue: 'defaultQueueName',
        status: 'isActive',
        lastUpdated: 'updateTime',
        // lastUpdatedBy: 'updateBy',
      };

      const orderBy = fieldMapping[sortField] || sortField;
      params.append('orderBy', orderBy);
      params.append('order', sortDirection);
    }
  }

  try {
    const response = await fireGetEmailRules(basePath, params.toString());
    // console.log('response:', response);

    // Extract data using the correct structure
    const { list, total, totalPages } = response.data.data || {
      list: [],
      total: 0,
      totalPages: 0,
    };

    // Transform response to the format expected by the component
    const transformedItems = list.map((item: any) => ({
      id: item.emailRuleId,
      mailboxAddress: item.mailBoxAddress,
      status: item.isActive === '1' ? 'active' : 'inactive',
      defaultQueue: item.defaultQueueName || 'N/A',
      defaultQueueId: item.defaultQueueId,
      isActive: Number(item.isActive),
      lastUpdated: item.updateTime,
      defaultAutoReplyId: item.defaultAutoReplyId,
      lastUpdatedBy: item.lastUpdatedUser,
    }));

    return {
      data: {
        rules: transformedItems,
        total: total,
      },
    };
  } catch (error) {
    console.error('Error fetching mailbox routing rules:', error);
    throw error;
  }
};

const MailboxRoutingRulesListing = () => {
  const { globalConfig } = useRole();
  const { permissions } = usePermission();

  const emailAdminPermission = new CommonPermission(globalConfig, permissions);

  const isEditablePermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'edit'
  );

  const isCreatePermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'create'
  );

  const { t } = useTranslation();
  const { toast } = useToast();
  const createNew = useEmailNavigationStore((state) => state.createNew);
  const viewDetail = useEmailNavigationStore((state) => state.viewDetail);
  const editDetail = useEmailNavigationStore((state) => state.editDetail);
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TMailboxRoutingRule>>();
  const [sortOrder, setSortOrder] = useState<any>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<{
    id: string;
    mailboxAddress: string;
  } | null>(null);

  // Filter states
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [queueFilter, setQueueFilter] = useState<string>('all');
  const [searchValue, setSearchValue] = useState<string>('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState<string>('');

  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(50);

  // Debounced search handler
  const debouncedSearch = debounce((value: string) => {
    setDebouncedSearchValue(value);
  }, 300);

  // Update debounced value when search input changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // Get queue options from the custom hook
  const { queueOptions, isLoading: isLoadingQueues } = useEmailQueueOptions();

  // React Query
  const queryClient = useQueryClient();
  const { data, isLoading, error } = useQuery({
    queryKey: [
      'mailboxRoutingRules',
      currentPage,
      perPage,
      sortOrder,
      statusFilter,
      queueFilter,
      debouncedSearchValue,
    ],
    queryFn: () =>
      fetchMailboxRoutingRules(currentPage, perPage, sortOrder, {
        status: statusFilter,
        queue: queueFilter,
        search: debouncedSearchValue,
      }),
  });

  const rules = data?.data?.rules || [];
  const totalCount = data?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / perPage);

  console.log('rules:', rules);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, queueFilter, debouncedSearchValue]);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  const handleDeleteRule = (ruleId: string, mailboxAddress: string) => {
    setRuleToDelete({ id: ruleId, mailboxAddress });
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = () => {
    if (ruleToDelete) {
      setIsDeleting(true);
      fireDeleteEmailRule(ruleToDelete.id, basePath)
        .then(() => {
          queryClient.invalidateQueries({
            queryKey: ['mailboxRoutingRules'],
          });
          setShowDeleteConfirmation(false);
          setRuleToDelete(null);
          toast({
            title: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.deleteSuccessMessage'
            ),
            description: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.deleteSuccessDescription'
            ),
            variant: 'success',
          });
        })
        .catch((err) => {
          console.error('Error deleting routing rule:', err);
        })
        .finally(() => {
          setIsDeleting(false);
        });
    }
  };

  // Define columns
  const columns = [
    {
      id: 'mailboxAddress',
      accessorKey: 'mailboxAddress',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.mailboxAddress
              ? sortOrder?.mailboxAddress === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.mailboxAddress === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              mailboxAddress: targetSortOrder,
            });
          }}
        >
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.mailboxAddress'
          )}
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('mailboxAddress')}</div>,
    },

    {
      id: 'defaultQueue',
      accessorKey: 'defaultQueue',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.defaultQueue
              ? sortOrder?.defaultQueue === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.defaultQueue === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              defaultQueue: targetSortOrder,
            });
          }}
        >
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.defaultQueue'
          )}
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('defaultQueue')}</div>,
    },
    {
      id: 'status',
      accessorKey: 'status',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.status
              ? sortOrder?.status === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.status === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              status: targetSortOrder,
            });
          }}
        >
          {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.status')}
        </SortingButton>
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return <StatusBadge status={status as 'active' | 'inactive'} />;
      },
    },
    {
      id: 'lastUpdated',
      accessorKey: 'lastUpdated',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.lastUpdated
              ? sortOrder?.lastUpdated === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.lastUpdated === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              lastUpdated: targetSortOrder,
            });
          }}
        >
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.columnLastUpdated'
          )}
        </SortingButton>
      ),
      cell: ({ row }) => {
        const val = row.getValue('lastUpdated') as string;
        return <div>{dayjs(val).format(GLOBAL_DATETIME_FORMAT)}</div>;
      },
    },
    {
      id: 'lastUpdatedBy',
      accessorKey: 'lastUpdatedBy',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.lastUpdatedBy
              ? sortOrder?.lastUpdatedBy === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.lastUpdatedBy === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              lastUpdatedBy: targetSortOrder,
            });
          }}
        >
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.columnLastUpdatedBy'
          )}
        </SortingButton>
      ),
      cell: ({ row }) => {
        const val = row.getValue('lastUpdatedBy') as string;
        return <div>{val}</div>;
      },
    },
    {
      id: 'action',
      header: () => (
        <div>
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.columnAction'
          )}
        </div>
      ),
      cell: ({ row }) => {
        const menuItems = [
          {
            label: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.view'
            ),
            onClick: () => viewDetail(row.original.id, 'mailbox-routing-rules'),
            className: 'w-full text-left px-4 py-2 hover:bg-primary-100',
          },
        ];

        if (isEditablePermission) {
          menuItems.push({
            label: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.edit'
            ),
            onClick: () => editDetail(row.original.id, 'mailbox-routing-rules'),
            className: 'w-full text-left px-4 py-2 hover:bg-primary-100',
          });
          menuItems.push({
            label: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.delete'
            ),
            onClick: () => {
              handleDeleteRule(row.original.id, row.original.mailboxAddress);
            },
            className:
              'w-full text-left px-4 py-2 text-red-600 hover:bg-primary-100',
          });
        }

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <PopoverMenu
              icon={
                <EllipsisVertical className="w-5 h-5 cursor-pointer hover:text-primary-500" />
              }
              side="left"
            >
              <div className="bg-white rounded shadow-md py-1 min-w-32">
                {menuItems.map((item, index) => (
                  <button
                    key={index}
                    className={item.className}
                    onClick={item.onClick}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </PopoverMenu>
          </div>
        );
      },
    },
  ] as ColumnDef<TMailboxRoutingRule>[];

  // Status options for filter dropdown
  const statusOptions = [
    {
      id: 'all',
      label: t(
        'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.allStatuses'
      ),
      value: 'all',
    },
    {
      id: 'active',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.active'),
      value: 'active',
    },
    {
      id: 'inactive',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.inactive'),
      value: 'inactive',
    },
  ];

  return (
    <div className="px-4 pt-1 pb-6 flex flex-col h-full gap-y-4">
      <div className="flex justify-between items-center overflow-auto gap-2 pt-2">
        <div className="flex items-center gap-2">
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={setStatusFilter}
            placeholder={t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.allStatuses'
            )}
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="text-sm w-[200px]"
          />
          <Select
            options={queueOptions}
            value={queueFilter}
            onChange={setQueueFilter}
            placeholder={t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.allQueues'
            )}
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="text-sm w-[200px]"
            disabled={isLoadingQueues}
          />
          <div className="flex-1">
            <Input
              className="w-[180px]"
              beforeIcon={<Icon name="search" />}
              placeholder={t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.searchPlaceholder'
              )}
              value={searchValue}
              onChange={(value) => setSearchValue(String(value))}
              allowClear
              size="s"
            />
          </div>
        </div>
        {isCreatePermission && (
          <Button
            size="s"
            onClick={() => createNew('mailbox-routing-rules')}
          >
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.createNewButton'
            )}
          </Button>
        )}
      </div>
      <div className="flex-1 h-0">
        <DataTable<TMailboxRoutingRule>
          data={rules}
          columns={columns}
          loading={isLoading}
          emptyMessage={t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.emptyMessage'
          )}
          error={error instanceof Error ? error.message : undefined}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            viewDetail(row.original.id, 'mailbox-routing-rules');
          }}
          onTableSetUp={(tableInstance) => setTable(tableInstance)}
          resize={true}
        />
      </div>
      {totalPages > 0 && (
        <section className="flex-row">
          <div>
            <Pagination
              current={currentPage}
              perPage={perPage}
              total={totalPages}
              totalCount={totalCount}
              onChange={(v) => setCurrentPage(v)}
              handleOnPrevious={() => handlePrevious()}
              handleOnNext={() => handleNext()}
              handlePerPageSetter={(p: number) => {
                const pageSize = Number(p);
                if (!isNaN(pageSize)) {
                  setPerPage(pageSize);
                }
                setCurrentPage(1);
              }}
            />
          </div>
        </section>
      )}

      {/* Delete Confirmation Modal */}
      <Popup
        open={showDeleteConfirmation}
        onOpenChange={setShowDeleteConfirmation}
      >
        <PopupContent
          title={t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.confirmDeleteTitle'
          )}
          className="sm:max-w-md"
        >
          <div className="p-4">
            <p>
              {t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.confirmDeleteMessage'
              )}
            </p>
            {ruleToDelete && (
              <p className="mt-2 font-medium text-gray-900">
                {ruleToDelete.mailboxAddress}
              </p>
            )}
          </div>
          <PopupFooter className="px-4 pb-4 pt-2">
            <PopupClose asChild>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowDeleteConfirmation(false);
                  setRuleToDelete(null);
                }}
              >
                {t(
                  'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.cancel'
                )}
              </Button>
            </PopupClose>
            <Button
              variant="primary"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting
                ? t(
                    'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.deleting'
                  )
                : t(
                    'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.delete'
                  )}
            </Button>
          </PopupFooter>
        </PopupContent>
      </Popup>
    </div>
  );
};

export default MailboxRoutingRulesListing;
