import {
  Button,
  <PERSON>ading<PERSON>lock,
  useRole,
  useToast,
} from '@cdss-modules/design-system';
import { useEmailNavigationStore } from '../../../_store/emailNavigationStore';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState, memo, useCallback } from 'react';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import {
  useMailboxRoutingRuleFormStore,
  TMailboxRoutingRuleFormData,
} from '../../../_store/mailboxRoutingRulesFormStore';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';

import RoutingRuleEditor, {
  IRoutingRule,
  TSubjectType,
} from '../../../../../_ui/RoutingRuleEditor';
import TemplateConfigurationSelector, {
  ITemplateDetail,
  IAttachment,
} from '../../../../../_ui/RoutingRuleEditor/TemplateConfigurationSelector';
import {
  fireGetQueueOptions,
  fireGetEmailRuleDetail,
  fireGetAutoReplyTemplateList,
  fireGetAutoReplyTemplateDetail,
} from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import {
  Popup,
  PopupContent,
  PopupFooter,
} from '@cdss-modules/design-system/components/_ui/Popup';

export type TQueueOption = {
  id: string;
  value: string;
  label: string;
};

// Template Item Type
export interface ITemplateItem {
  id: string;
  title: string;
  language: string;
  contentType: string;
}

// Directory Item Type
export interface IDirectoryItem {
  id: string;
  title: string;
  items: ITemplateItem[] | null;
  contentType: string;
}

// Space Item Type
export interface ISpaceItem {
  id: string;
  name: string;
  spaceType: string;
  description: string;
  items: IDirectoryItem[] | null;
}

// Template response type - full API response structure
export interface ITemplateResponse {
  data: ISpaceItem[];
  error: string;
  isSuccess: boolean;
}

// Custom hook to fetch template options
const useGetTemplateOptions = () => {
  return useQuery<ITemplateResponse, Error>({
    queryKey: ['templateOptions'],
    queryFn: async () => {
      try {
        const response = await fireGetAutoReplyTemplateList(basePath);

        // console.log('Full API response:', response);
        console.log('Response data:', response.data);

        return response.data;
      } catch (error) {
        console.error('Error fetching template options:', error);
        throw error;
      }
    },
  });
};

// Queue data query
const useQueueOptions = () => {
  const { data, isLoading } = useQuery({
    queryKey: ['queueOptions'],
    queryFn: async () => {
      try {
        const response = await fireGetQueueOptions(basePath);

        return response.data.data || [];
      } catch (error) {
        console.error('Error fetching queue options:', error);
        return [];
      }
    },
  });

  // Define the queue options with the correct type
  const queueOptions: TQueueOption[] = [];

  if (data && Array.isArray(data)) {
    data.forEach((queue: any) => {
      queueOptions.push({
        id: queue.id,
        label: queue.name,
        value: queue.id,
      });
    });
  }

  return { queueOptions, isLoading };
};

// Types for API response
interface IEmailRuleDetailResponse {
  data: {
    emailRuleId: string;
    mailBoxAddress: string;
    isActive: number;
    rules: {
      defaultReturn: {
        queueId: string;
        autoReplyId?: string | null;
      };
      rules: IRoutingRule[];
    };
    createBy: string;
    createTime: string;
    tenant: string;
    updateBy: string;
    updateTime: string;
  };
  error: string;
  isSuccess: boolean;
}

// Mock data function - in real implementation, this would be an API call
const fetchMailboxRoutingRuleById = async (id: string | null) => {
  if (!id) return null;

  try {
    const response = await fireGetEmailRuleDetail(id, basePath);

    // Check for success before returning
    if (response.data.isSuccess) {
      return response.data as IEmailRuleDetailResponse;
    } else {
      throw new Error(response.data.error || 'Failed to fetch email rule');
    }
  } catch (error) {
    console.error('Error fetching email rule detail:', error);
    throw error;
  }
};

// Basic Information Component
interface IBasicInformationProps {
  isEditMode: boolean;
}

// Use memo to prevent unnecessary re-renders
const BasicInformation = memo(({ isEditMode }: IBasicInformationProps) => {
  // Status options for the select component
  const { t } = useTranslation();
  const statusOptions = [
    {
      id: '1',
      value: '1',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.active'),
    },
    {
      id: '0',
      value: '0',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.inactive'),
    },
  ];

  const formData = useMailboxRoutingRuleFormStore((state) => state.formData);
  const updateField = useMailboxRoutingRuleFormStore(
    (state) => state.updateField
  );
  const validationErrors = useMailboxRoutingRuleFormStore(
    (state) => state.validationErrors
  );

  // Local state for the input field to prevent re-renders during typing
  const [localMailboxAddress, setLocalMailboxAddress] = useState(
    formData.mailBoxAddress
  );

  // Update local state when formData changes from outside
  useEffect(() => {
    setLocalMailboxAddress(formData.mailBoxAddress);
  }, [formData.mailBoxAddress]);

  // Debounce function to update the store only after typing has paused
  const handleInputChange = useCallback((value: string | number) => {
    setLocalMailboxAddress(String(value));
  }, []);

  // Update the store when input loses focus
  const handleInputBlur = useCallback(() => {
    if (localMailboxAddress !== formData.mailBoxAddress) {
      updateField('mailBoxAddress', localMailboxAddress);
    }
  }, [localMailboxAddress, formData.mailBoxAddress, updateField]);

  return (
    <div className="mb-6 px-4">
      <h2 className="font-bold text-sm mb-3 text-primary-500">
        {t(
          'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.basicInformationSection'
        )}
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-bold mb-1">
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.mailboxAddress'
            )}
          </label>
          <Input
            disabled={!isEditMode}
            size="s"
            placeholder={t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.mailboxAddressPlaceholder'
            )}
            value={localMailboxAddress}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            status={validationErrors.mailBoxAddress ? 'danger' : undefined}
            message={validationErrors.mailBoxAddress}
            className="disabled:text-black"
          />
        </div>

        <div>
          <label className="block text-sm font-bold mb-1">
            {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.status')}
          </label>
          <Select
            disabled={!isEditMode}
            value={String(formData.isActive)}
            onChange={(value) => {
              updateField('isActive', Number(value));
              console.log('hello:', value);
            }}
            options={statusOptions}
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
          />
        </div>
      </div>
    </div>
  );
});

// Add display name for debugging purposes
BasicInformation.displayName = 'BasicInformation';

// Default Return Configuration Component
interface IDefaultReturnConfigurationProps {
  isEditMode: boolean;
}

// Also memoize the other components to prevent unnecessary re-renders
const DefaultReturnConfiguration = memo(
  ({ isEditMode }: IDefaultReturnConfigurationProps) => {
    // Fetch queue options using React Query
    const { t } = useTranslation();

    // Fetch queue options using React Query
    const { queueOptions, isLoading: loadingQueues } = useQueueOptions();
    // Fetch email templates using the new custom hook
    const { data: templateResponse, isLoading: loadingTemplates } =
      useGetTemplateOptions();

    console.log('templateResponse', templateResponse);

    const formData = useMailboxRoutingRuleFormStore((state) => state.formData);
    const updateDefaultReturn = useMailboxRoutingRuleFormStore(
      (state) => state.updateDefaultReturn
    );
    const validationErrors = useMailboxRoutingRuleFormStore(
      (state) => state.validationErrors
    );

    // Fetch template detail for DefaultReturnConfiguration
    const autoReplyId = formData.rules.defaultReturn.autoReplyId;
    const { data: templateDetail, isLoading: isLoadingTemplateDetail } =
      useQuery<ITemplateDetail | null, Error>({
        queryKey: ['templateDetail', autoReplyId],
        queryFn: async () => {
          if (!autoReplyId) return null;
          try {
            const response = await fireGetAutoReplyTemplateDetail(
              autoReplyId,
              basePath
            );
            return response.data.data; // Assuming this is ITemplateDetail
          } catch (error) {
            console.error(
              'Error fetching template detail in DefaultReturnConfiguration:',
              error
            );
            return null;
          }
        },
        enabled: !!autoReplyId,
      });

    // console.log('Template data from the hook', templateResponse);

    // Find the template object based on autoReplyId
    const findTemplateById = (
      templateId: string | null
    ): ITemplateItem | null => {
      if (!templateId || !templateResponse || !templateResponse.data)
        return null;

      // Loop through the 3-layer structure to find the template
      for (const space of templateResponse.data) {
        if (space.items) {
          // Check if space.items is an array
          for (const directory of space.items) {
            if (directory.items) {
              // Check if directory.items is an array
              for (const template of directory.items) {
                if (template.id === templateId) {
                  return template;
                }
              }
            }
          }
        }
      }

      return null;
    };

    // Transform autoReplyId to the format expected by TemplateConfigurationSelector
    const getTemplateValue = () => {
      const autoReplyId = formData.rules.defaultReturn.autoReplyId;
      if (!autoReplyId) return null;

      const template = findTemplateById(autoReplyId);
      if (!template) return null;

      // console.log('template', template.language);

      const language = template.language.toLowerCase();

      // console.log('language', language);

      return {
        language: language, // Default to English if not specified
        templateId: template.id,
      };
    };

    // Handle template update
    const handleUpdateTemplate = (
      template: { language: string; templateId: string } | null
    ) => {
      updateDefaultReturn('autoReplyId', template?.templateId || null);
    };

    // Convert template response to array of space items
    const templateOptions = templateResponse ? [templateResponse] : [];

    console.log('templateOptions', templateOptions);

    return (
      <div className="mb-6 px-4">
        <h2 className="font-bold text-sm text-primary-500">
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.defaultReturnConfigurationSection'
          )}
        </h2>
        <span className="text-sm text-gray-500">
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.defaultReturnDescription'
          )}
        </span>

        <TemplateConfigurationSelector
          templateValue={getTemplateValue()}
          queueValue={formData.rules.defaultReturn.queueId}
          queueOptions={queueOptions || []}
          templateOptions={templateOptions}
          isEditMode={isEditMode}
          onUpdateTemplate={handleUpdateTemplate}
          onUpdateQueue={(queue) => updateDefaultReturn('queueId', queue)}
          title=""
          queueLabel={t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.defaultQueue'
          )}
          templateLabel={t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.defaultAutoReplyTemplateLabel'
          )}
          isLoadingQueues={loadingQueues}
          isLoadingTemplates={loadingTemplates}
          queueValidationError={validationErrors['defaultReturn.queueId']}
          isQueueRequired={true}
          templateDetailData={templateDetail}
          isLoadingTemplateDetailData={isLoadingTemplateDetail}
        />
      </div>
    );
  }
);

// Add display name for debugging purposes
DefaultReturnConfiguration.displayName = 'DefaultReturnConfiguration';

// Routing Rules Component
interface IRoutingRulesProps {
  isEditMode: boolean;
}

// Also memoize the other components to prevent unnecessary re-renders
const RoutingRules = memo(({ isEditMode }: IRoutingRulesProps) => {
  const formData = useMailboxRoutingRuleFormStore((state) => state.formData);
  const { t } = useTranslation();
  const updateRoutingRules = useMailboxRoutingRuleFormStore(
    (state) => state.updateRoutingRules
  );

  const validationErrors = useMailboxRoutingRuleFormStore(
    (state) => state.validationErrors
  );

  // Get routing rules from the form data
  const routingRules = formData.rules.rules || [];

  // In a real app, this would be fetched from API
  const { queueOptions } = useQueueOptions();

  // Use the new template options hook
  const { data: templateResponse } = useGetTemplateOptions();
  // Convert template response to array of space items for the component
  const templateOptions = templateResponse ? [templateResponse] : [];

  // Define which subject fields are enabled for this instance of the editor
  const enabledFieldsForEditor: TSubjectType[] = [
    'subject' as TSubjectType,
    'domain' as TSubjectType,
    'emailAddressGroup' as TSubjectType,
    // Add 'time' or other TSubjectType values here if you want to enable them
    // e.g., 'time' as TSubjectType,
  ];

  // console.log('templateOptions', templateOptions);

  return (
    <div className="h-full">
      <h2 className="font-bold text-sm mb-3 text-primary-500 px-4">
        {t(
          'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.routingRulesSection'
        )}
      </h2>
      <RoutingRuleEditor
        rules={routingRules}
        onUpdateRules={updateRoutingRules}
        queueOptions={queueOptions || []}
        templateOptions={templateOptions || []}
        isEditMode={isEditMode}
        validationErrors={validationErrors}
        enabledSubjectFields={enabledFieldsForEditor}
        allowedLogicTypes={['OR', 'AND']}
      />
    </div>
  );
});

// Add display name for debugging purposes
RoutingRules.displayName = 'RoutingRules';

const MailboxRoutingRulesDetail = () => {
  const backToList = useEmailNavigationStore((state) => state.backToList);
  const selectedItemId = useEmailNavigationStore(
    (state) => state.mailboxRoutingRules.selectedItemId
  );
  const { t } = useTranslation();
  const mode = useEmailNavigationStore(
    (state) => state.mailboxRoutingRules.mode
  );
  const viewDetail = useEmailNavigationStore((state) => state.viewDetail);
  const editDetail = useEmailNavigationStore((state) => state.editDetail);

  const { loadExistingData, saveForm, resetForm, validationErrors, isDirty } =
    useMailboxRoutingRuleFormStore();

  // Permission hooks
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const emailAdminPermission = new CommonPermission(globalConfig, permissions);
  const { toast } = useToast();

  const hasEditPermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'edit'
  );
  const hasCreatePermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'create'
  );

  const [isSaving, setIsSaving] = useState(false);
  const [showCreateDeniedPopup, setShowCreateDeniedPopup] = useState(false);
  const [showEditDeniedPopup, setShowEditDeniedPopup] = useState(false);

  const isEditMode = mode === 'edit';
  const isNewMode = !selectedItemId && isEditMode;

  // Effect for permission checking and mode adjustment
  useEffect(() => {
    if (isNewMode) {
      // Trying to create
      if (!hasCreatePermission) {
        setShowCreateDeniedPopup(true);
      }
    } else if (selectedItemId) {
      // Item ID exists, so it's an attempt to view or edit an existing item
      if (mode === 'edit' && !hasEditPermission) {
        // Can view, but trying to edit without permission
        setShowEditDeniedPopup(true);
      }
    }
  }, [
    mode,
    selectedItemId,
    isNewMode,
    hasCreatePermission,
    hasEditPermission,
    t,
  ]);

  // Use React Query for data fetching
  const { isLoading, error, data } = useQuery<
    IEmailRuleDetailResponse | null,
    Error
  >({
    queryKey: ['mailboxRoutingRule', selectedItemId],
    queryFn: () => fetchMailboxRoutingRuleById(selectedItemId),
    enabled: isNewMode ? hasCreatePermission : !!selectedItemId, // Only run query if we have an ID and appropriate permissions
  });

  // Initialize the form
  useEffect(() => {
    if (isNewMode) {
      if (!hasCreatePermission) return; // Don't load if no create permission
    } else if (data && data.isSuccess && data.data) {
      // Add lastUpdated to the data object to be stored in form state
      const formData = {
        ...data.data,
        lastUpdated: data.data.updateTime,
      };
      loadExistingData(formData);
    }

    // Cleanup function not needed as we're manually managing form state
  }, [data, isNewMode, loadExistingData]);

  // Handle form submission
  const handleSave = async () => {
    setIsSaving(true);
    try {
      const result = await saveForm();
      if (result.success) {
        // Show success message
        toast({
          title: t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveSuccess'
          ),
          description: t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveSuccessDescription'
          ),
          variant: 'success',
        });
        // Redirect back to list
        backToList('mailbox-routing-rules');
      } else {
        // Check if we have a specific error message from the API
        if (result.error) {
          // Show the specific error message from the API response
          toast({
            title: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveError'
            ),
            description: result.error,
            variant: 'error',
          });
        } else {
          // Show error message for validation failures
          const errors = Object.keys(validationErrors);
          if (errors.length > 0) {
            // Check for specific types of validation errors to provide better guidance
            const priorityErrors = errors.filter((key) =>
              key.includes('.priority')
            );
            const conditionErrors = errors.filter((key) =>
              key.includes('.condition')
            );
            const mailboxErrors = errors.filter((key) =>
              key.includes('mailBoxAddress')
            );
            const queueErrors = errors.filter((key) => key.includes('queueId'));

            let errorDescription = t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.validationErrorDescription'
            );

            // Provide specific guidance based on error types
            if (priorityErrors.length > 0) {
              errorDescription +=
                ' ' +
                t(
                  'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.priorityErrorGuidance'
                );
            }
            if (conditionErrors.length > 0) {
              errorDescription +=
                ' ' +
                t(
                  'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.conditionErrorGuidance'
                );
            }
            if (mailboxErrors.length > 0) {
              errorDescription +=
                ' ' +
                t(
                  'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.mailboxErrorGuidance'
                );
            }
            if (queueErrors.length > 0) {
              errorDescription +=
                ' ' +
                t(
                  'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.queueErrorGuidance'
                );
            }

            // Validation errors
            toast({
              title: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.validationError'
              ),
              description: errorDescription,
              variant: 'error',
            });
          } else {
            // General save error fallback
            toast({
              title: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveError'
              ),
              description: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveErrorDescription'
              ),
              variant: 'error',
            });
          }
        }
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    resetForm();
    backToList('mailbox-routing-rules');
  };

  // Handle cancel from edit mode
  const handleCancelEdit = () => {
    resetForm();
    if (selectedItemId) {
      viewDetail(selectedItemId, 'mailbox-routing-rules');
    } else {
      backToList('mailbox-routing-rules');
    }
  };

  const handleCreateDeniedPopupClose = () => {
    setShowCreateDeniedPopup(false);
    backToList('mailbox-routing-rules');
  };

  const handleEditDeniedPopupClose = () => {
    setShowEditDeniedPopup(false);
    if (selectedItemId) {
      viewDetail(selectedItemId, 'mailbox-routing-rules'); // Go back to view mode
    }
  };

  // Early returns for permission denied scenarios before loading state
  if (isNewMode && !hasCreatePermission && !showCreateDeniedPopup) {
    return null; // Render nothing or a minimal component if popup is not yet shown
  }
  if (
    mode === 'edit' &&
    selectedItemId &&
    !hasEditPermission &&
    !showEditDeniedPopup
  ) {
    return null; // Render nothing if trying to edit without permission and popup not shown
  }

  if (
    isLoading &&
    ((isNewMode && hasCreatePermission) || selectedItemId) // Adjusted loading condition
  ) {
    return (
      <div className="p-4 h-full flex justify-center items-center">
        <LoadingBlock />
      </div>
    );
  }

  if (
    error &&
    ((isNewMode && hasCreatePermission) || selectedItemId) // Adjusted error condition
  ) {
    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.errorLoadingTitle'
            )}
          </h2>
          <Button
            variant="back"
            onClick={() => backToList('mailbox-routing-rules')}
          >
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.backToListButton'
            )}
          </Button>
        </div>
        <div className="text-red-500 mt-4">
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.errorLoadingMessage'
          )}
        </div>
      </div>
    );
  }

  const title = isNewMode
    ? t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.createTitle')
    : isEditMode
      ? t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.editTitle')
      : t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.viewTitle');

  if (!isNewMode && selectedItemId && (!data?.isSuccess || !data?.data)) {
    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.notFoundTitle'
            )}
          </h2>
          <Button
            variant="back"
            onClick={() => backToList('mailbox-routing-rules')}
          >
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.backToListButton'
            )}
          </Button>
        </div>
        <div className="mt-4">
          <p>
            {data?.error ||
              t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.notFoundMessage'
              )}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className=" bg-white rounded-xl">
      <div className="flex items-center mb-2 gap-x-2 justify-between px-4 pt-4">
        <h1 className="text-base font-bold">{title}</h1>
        <div className="flex justify-end gap-2">
          {/* Secondary button - Return or Cancel */}
          <Button
            size="s"
            variant="secondary"
            onClick={
              mode === 'edit' && selectedItemId
                ? handleCancelEdit
                : handleCancel
            }
          >
            {mode === 'edit' && selectedItemId
              ? t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.cancel')
              : t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.return')}
          </Button>

          {/* Primary button - Save or Edit */}
          {mode === 'view' && selectedItemId && hasEditPermission && (
            <Button
              size="s"
              variant="primary"
              onClick={() =>
                editDetail(selectedItemId, 'mailbox-routing-rules')
              }
            >
              {t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.editRule'
              )}
            </Button>
          )}
          {mode === 'edit' &&
            ((isNewMode && hasCreatePermission) ||
              (!isNewMode && hasEditPermission)) && (
              <Button
                size="s"
                variant="primary"
                onClick={handleSave}
                disabled={!isDirty || isSaving}
              >
                {isSaving
                  ? t(
                      'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saving'
                    )
                  : t(
                      'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveRule'
                    )}
              </Button>
            )}
        </div>
      </div>

      {/* Display general validation errors */}

      <BasicInformation
        isEditMode={
          isEditMode &&
          ((isNewMode && hasCreatePermission) ||
            (!isNewMode && hasEditPermission))
        }
      />

      <DefaultReturnConfiguration
        isEditMode={
          isEditMode &&
          ((isNewMode && hasCreatePermission) ||
            (!isNewMode && hasEditPermission))
        }
      />

      <RoutingRules
        isEditMode={
          isEditMode &&
          ((isNewMode && hasCreatePermission) ||
            (!isNewMode && hasEditPermission))
        }
      />

      {showCreateDeniedPopup && (
        <Popup
          open={showCreateDeniedPopup}
          onOpenChange={(open) => {
            if (!open) handleCreateDeniedPopupClose();
          }}
        >
          <PopupContent
            title={t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.permissionDeniedTitle'
            )}
          >
            <div className="p-4">
              {t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.permissionDeniedCreate'
              )}
            </div>
            <PopupFooter>
              <Button onClick={handleCreateDeniedPopupClose}>
                {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.ok')}
              </Button>
            </PopupFooter>
          </PopupContent>
        </Popup>
      )}

      {showEditDeniedPopup && (
        <Popup
          open={showEditDeniedPopup}
          onOpenChange={(open) => {
            if (!open) handleEditDeniedPopupClose();
          }}
        >
          <PopupContent
            title={t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.permissionDeniedTitle'
            )}
          >
            <div className="p-4">
              {t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.********************************'
              )}
            </div>
            <PopupFooter>
              <Button onClick={handleEditDeniedPopupClose}>
                {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.ok')}
              </Button>
            </PopupFooter>
          </PopupContent>
        </Popup>
      )}
    </div>
  );
};

export default MailboxRoutingRulesDetail;
