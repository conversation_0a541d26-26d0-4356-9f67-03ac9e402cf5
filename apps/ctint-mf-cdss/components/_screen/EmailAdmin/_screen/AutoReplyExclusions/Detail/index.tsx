import {
  But<PERSON>,
  Loading<PERSON>lock,
  useRole,
  useToast,
} from '@cdss-modules/design-system';
import { useEmailNavigationStore } from '../../../_store/emailNavigationStore';
import { useQuery } from '@tanstack/react-query';
import { useEffect, useState } from 'react';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import {
  useAutoReplyExclusionFormStore,
  TAutoReplyExclusionFormData,
} from '../../../_store/autoReplyExclusionsFormStore';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { fireGetAutoReplyFilterRuleDetail } from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';
import {
  Popup,
  PopupContent,
  PopupFooter,
} from '@cdss-modules/design-system/components/_ui/Popup';
import { Mail, Globe, AtSign } from 'lucide-react';

// API data function to fetch the auto reply exclusion detail
const fetchAutoReplyExclusionById = async (id: string | null) => {
  if (!id) return null;

  try {
    const response = await fireGetAutoReplyFilterRuleDetail(id, basePath);

    // Log the response for debugging
    console.log('Auto reply exclusion API response:', response);

    // Check for success before returning
    if (response.data.isSuccess) {
      return response.data;
    } else {
      throw new Error(response.data.error || 'Failed to fetch exclusion rule');
    }
  } catch (error) {
    console.error('Error fetching exclusion rule detail:', error);
    throw error;
  }
};

// Quick select options for different rule types
const SUBJECT_QUICK_OPTIONS = (t: any) => [
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.outOfOffice'
    ),
    value: 'Out of Office',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.autoReply'
    ),
    value: 'Auto-Reply',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.vacation'
    ),
    value: 'Vacation',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.automaticReply'
    ),
    value: 'Automatic Reply',
  },
];

const DOMAIN_QUICK_OPTIONS = (t: any) => [
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.gmailCom'
    ),
    value: 'gmail.com',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.outlookCom'
    ),
    value: 'outlook.com',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.yahooCom'
    ),
    value: 'yahoo.com',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.hotmailCom'
    ),
    value: 'hotmail.com',
  },
];

const EMAIL_QUICK_OPTIONS = (t: any) => [
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.noreplyExample'
    ),
    value: '<EMAIL>',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.adminExample'
    ),
    value: '<EMAIL>',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.supportExample'
    ),
    value: '<EMAIL>',
  },
  {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelect.infoExample'
    ),
    value: '<EMAIL>',
  },
];

// Define pattern input configurations
const PATTERN_INPUT_CONFIG = (t: any) => ({
  subject: {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.patternLabelSubject'
    ),
    placeholder: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.patternPlaceholderSubject'
    ),
  },
  domain: {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.patternLabelDomain'
    ),
    placeholder: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.patternPlaceholderDomain'
    ),
  },
  email: {
    label: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.patternLabelEmail'
    ),
    placeholder: t(
      'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.patternPlaceholderEmail'
    ),
  },
});

const AutoReplyExclusionsDetail = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const backToList = useEmailNavigationStore((state) => state.backToList);
  const selectedItemId = useEmailNavigationStore(
    (state) => state.autoReplyExclusions.selectedItemId
  );
  const mode = useEmailNavigationStore(
    (state) => state.autoReplyExclusions.mode
  );
  const viewDetail = useEmailNavigationStore((state) => state.viewDetail);
  const editDetail = useEmailNavigationStore((state) => state.editDetail);

  // Permission hooks
  const { globalConfig } = useRole();
  const { permissions } = usePermission();
  const emailAdminPermission = new CommonPermission(globalConfig, permissions);

  const hasEditPermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'edit'
  );
  const hasCreatePermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'create'
  );

  // Form store hooks
  const formData = useAutoReplyExclusionFormStore((state) => state.formData);
  const updateField = useAutoReplyExclusionFormStore(
    (state) => state.updateField
  );
  const loadExistingData = useAutoReplyExclusionFormStore(
    (state) => state.loadExistingData
  );
  const saveForm = useAutoReplyExclusionFormStore((state) => state.saveForm);
  const validationErrors = useAutoReplyExclusionFormStore(
    (state) => state.validationErrors
  );
  const resetForm = useAutoReplyExclusionFormStore((state) => state.resetForm);
  const isDirty = useAutoReplyExclusionFormStore((state) => state.isDirty);

  const [isSaving, setIsSaving] = useState(false);
  const [showCreateDeniedPopup, setShowCreateDeniedPopup] = useState(false);
  const [showEditDeniedPopup, setShowEditDeniedPopup] = useState(false);
  const isNewMode = !selectedItemId && mode === 'edit';

  // Effect for permission checking and mode adjustment
  useEffect(() => {
    if (isNewMode) {
      // Trying to create
      if (!hasCreatePermission) {
        setShowCreateDeniedPopup(true);
      }
    } else if (selectedItemId) {
      // Item ID exists, so it's an attempt to view or edit an existing item
      if (mode === 'edit' && !hasEditPermission) {
        // Can view, but trying to edit without permission
        setShowEditDeniedPopup(true);
      }
    }
  }, [
    mode,
    selectedItemId,
    isNewMode,
    hasCreatePermission,
    hasEditPermission,
    t,
  ]);

  // Use React Query for data fetching
  const { isLoading, error, data } = useQuery({
    queryKey: ['autoReplyExclusion', selectedItemId],
    queryFn: () => fetchAutoReplyExclusionById(selectedItemId),
    enabled: isNewMode ? hasCreatePermission : !!selectedItemId,
  });

  // Initialize the form
  useEffect(() => {
    if (isNewMode) {
      if (!hasCreatePermission) return;
    } else if (data?.data) {
      const apiData = data.data;
      const mappedData = {
        id: apiData.id,
        ruleType: apiData.type as 'subject' | 'domain' | 'email',
        pattern: apiData.value,
        status:
          apiData.status === 1 ? ('active' as const) : ('inactive' as const),
        reason: apiData.reason || '',
        expirationDate: apiData.expirationDate || null,
        lastUpdated: apiData.updateTime,
      };
      loadExistingData(mappedData);

      return () => {
        resetForm();
      };
    }
  }, [
    data,
    isNewMode,
    loadExistingData,
    resetForm,
    hasCreatePermission,
    hasEditPermission,
    selectedItemId,
  ]);

  // Handle form submission
  const handleSave = async () => {
    setIsSaving(true);
    try {
      const result = await saveForm();
      if (result.success) {
        // Show success message
        toast({
          title: t(
            'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.saveSuccess'
          ),
          description: t(
            'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.saveSuccessDescription'
          ),
          variant: 'success',
        });
        // Redirect back to list
        backToList('auto-reply-exclusions');
      } else {
        // Check if we have a specific error message from the API
        if (result.error) {
          // Show the specific error message from the API response
          toast({
            title: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveError'
            ),
            description: result.error,
            variant: 'error',
          });
        } else {
          // Show error message for validation failures
          const errors = Object.keys(validationErrors);
          if (errors.length > 0) {
            // Validation errors
            toast({
              title: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.validationError'
              ),
              description: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.validationErrorDescription'
              ),
              variant: 'error',
            });
          } else {
            // General save error fallback
            toast({
              title: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveError'
              ),
              description: t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveErrorDescription'
              ),
              variant: 'error',
            });
          }
        }
      }
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    resetForm();
    backToList('auto-reply-exclusions');
  };

  // Handle cancel from edit mode
  const handleCancelEdit = () => {
    resetForm();
    if (selectedItemId) {
      viewDetail(selectedItemId, 'auto-reply-exclusions');
    } else {
      backToList('auto-reply-exclusions');
    }
  };

  const handleCreateDeniedPopupClose = () => {
    setShowCreateDeniedPopup(false);
    backToList('auto-reply-exclusions');
  };

  const handleEditDeniedPopupClose = () => {
    setShowEditDeniedPopup(false);
    if (selectedItemId) {
      viewDetail(selectedItemId, 'auto-reply-exclusions');
    }
  };

  // Handle quick select for patterns
  const handleQuickSelect = (value: string) => {
    updateField('pattern', value);
  };

  // Get quick select options based on the current rule type
  const getQuickSelectOptions = () => {
    switch (formData.ruleType) {
      case 'subject':
        return SUBJECT_QUICK_OPTIONS(t);
      case 'domain':
        return DOMAIN_QUICK_OPTIONS(t);
      case 'email':
        return EMAIL_QUICK_OPTIONS(t);
      default:
        return [];
    }
  };

  if (isNewMode && !hasCreatePermission && !showCreateDeniedPopup) {
    return null;
  }
  if (
    mode === 'edit' &&
    selectedItemId &&
    !hasEditPermission &&
    !showEditDeniedPopup
  ) {
    return null;
  }
  if (isLoading && ((isNewMode && hasCreatePermission) || selectedItemId)) {
    return (
      <div className="p-4 h-full flex justify-center items-center">
        <LoadingBlock />
      </div>
    );
  }

  if (error && ((isNewMode && hasCreatePermission) || selectedItemId)) {
    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.errorLoadingTitle'
            )}
          </h2>
          <Button
            variant="back"
            onClick={() => backToList('auto-reply-exclusions')}
          >
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.backToListButton'
            )}
          </Button>
        </div>
        <div className="text-red-500 mt-4">
          {t(
            'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.errorLoadingMessage'
          )}
        </div>
      </div>
    );
  }

  const title = isNewMode
    ? t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.createTitle')
    : mode === 'edit'
      ? t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.editTitle')
      : t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.viewTitle');

  if (!isNewMode && selectedItemId && (!data?.isSuccess || !data?.data)) {
    return (
      <div className="p-4">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">
            {t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.notFoundTitle'
            )}
          </h2>
          <Button
            variant="back"
            onClick={() => backToList('auto-reply-exclusions')}
          >
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.detail.backToListButton'
            )}
          </Button>
        </div>
        <div className="mt-4">
          <p>
            {data?.error ||
              t(
                'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.notFoundMessage'
              )}
          </p>
        </div>
      </div>
    );
  }

  const quickSelectOptions = getQuickSelectOptions();
  const currentPatternConfig = PATTERN_INPUT_CONFIG(t)[formData.ruleType];

  // Rule type option component
  const RuleTypeOption = ({
    type,
    title,
    description,
    icon,
  }: {
    type: 'subject' | 'domain' | 'email';
    title: string;
    description: string;
    icon: React.ReactNode;
  }) => (
    <div
      className={cn(
        'border rounded-md p-4 flex flex-col items-center group',
        formData.ruleType === type
          ? 'bg-primary-100 border-primary-400'
          : mode === 'edit'
            ? 'border-gray-200 hover:border-primary-400 bg-gray-100'
            : 'border-gray-200 bg-gray-100'
      )}
    >
      <button
        className="flex flex-col items-center w-full h-full"
        onClick={() => mode === 'edit' && updateField('ruleType', type)}
        disabled={mode !== 'edit'}
      >
        <div
          className={cn(
            'text-gray-400 mb-2',
            mode === 'edit' && 'group-hover:text-primary-500',
            formData.ruleType === type && 'text-primary-500'
          )}
        >
          {icon}
        </div>
        <div className="flex flex-col items-center">
          <div className="font-bold">{title}</div>
          <div className="text-sm text-gray-500">{description}</div>
        </div>
      </button>
    </div>
  );

  // Rule type options data
  const ruleTypeOptions = [
    {
      type: 'subject' as const,
      title: t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.subject'),
      description: t(
        'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.ruleTypeSubjectDescription'
      ),
      icon: <Mail className="h-8 w-8" />,
    },
    {
      type: 'domain' as const,
      title: t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.domain'),
      description: t(
        'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.ruleTypeDomainDescription'
      ),
      icon: <Globe className="h-8 w-8" />,
    },
    {
      type: 'email' as const,
      title: t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.address'),
      description: t(
        'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.ruleTypeEmailDescription'
      ),
      icon: <AtSign className="h-8 w-8" />,
    },
  ];

  return (
    <div className="p-4">
      <div className="flex items-center mb-4 gap-x-2 justify-between">
        <h1 className="text-base font-bold">{title}</h1>
        <div className="flex justify-end gap-2">
          {/* Cancel button */}
          <Button
            size="s"
            variant="secondary"
            onClick={
              mode === 'edit' && selectedItemId
                ? handleCancelEdit
                : handleCancel
            }
          >
            {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.cancel')}
          </Button>

          {mode === 'view' && selectedItemId && hasEditPermission && (
            <Button
              size="s"
              variant="primary"
              onClick={() =>
                editDetail(selectedItemId, 'auto-reply-exclusions')
              }
              disabled={isSaving}
            >
              {t(
                'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.editRule'
              )}
            </Button>
          )}
          {mode === 'edit' && (
            <Button
              size="s"
              variant="primary"
              onClick={handleSave}
              disabled={!isDirty || isSaving}
            >
              {isSaving
                ? t(
                    'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saving'
                  )
                : t(
                    'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.saveRule'
                  )}
            </Button>
          )}
        </div>
      </div>

      {/* Display general validation errors */}
      {/* {validationErrors.general && (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4"
          role="alert"
        >
          <p>{validationErrors.general}</p>
        </div>
      )} */}

      <div className="bg-white rounded-lg">
        {/* Status */}
        <div className="mb-3 ">
          <label className="block text-sm font-medium mb-2">
            {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.status')}
          </label>
          <div className="w-1/3">
            <Select
              disabled={mode !== 'edit'}
              value={formData.status}
              onChange={(value) =>
                updateField('status', value as 'active' | 'inactive')
              }
              options={[
                {
                  id: 'active',
                  value: 'active',
                  label: t(
                    'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.active'
                  ),
                },
                {
                  id: 'inactive',
                  value: 'inactive',
                  label: t(
                    'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.inactive'
                  ),
                },
              ]}
              isPagination={false}
              labelClassName="h-full"
              triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
            />
          </div>
        </div>

        {/* Exclusion Criteria */}
        <div className="mb-6">
          <h2 className="text-primary-500 font-medium">
            {t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.exclusionCriteriaSection'
            )}
          </h2>
          <p className="text-gray-500 mb-2">
            {t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.exclusionCriteriaDescription'
            )}
          </p>

          {/* Rule Type */}
          <div className="mb-4">
            <label className="block text-sm font-medium mb-2">
              {t(
                'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.ruleType'
              )}
            </label>
            <div className="grid grid-cols-3 gap-2">
              {ruleTypeOptions.map((option) => (
                <RuleTypeOption
                  key={option.type}
                  type={option.type}
                  title={option.title}
                  description={option.description}
                  icon={option.icon}
                />
              ))}
            </div>
          </div>

          {/* Pattern Input - Reusable Component */}
          {formData.ruleType && currentPatternConfig && (
            <div>
              <label className="block text-sm font-medium mb-2">
                {currentPatternConfig.label}
              </label>
              <div className="w-1/3">
                <Input
                  disabled={mode !== 'edit'}
                  size="s"
                  placeholder={currentPatternConfig.placeholder}
                  value={formData.pattern}
                  onChange={(value) => updateField('pattern', String(value))}
                  status={validationErrors.pattern ? 'danger' : undefined}
                  message={validationErrors.pattern}
                  className="disabled:text-black"
                />
              </div>
            </div>
          )}

          {/* Quick Select - for all rule types */}
          {quickSelectOptions.length > 0 && (
            <div className="mt-4">
              <label className="block text-sm font-medium mb-2">
                {t(
                  'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.quickSelectLabel'
                )}
              </label>
              <div className="flex flex-wrap gap-2">
                {quickSelectOptions.map((option) => (
                  <button
                    key={option.value}
                    className="px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-md text-sm"
                    onClick={() =>
                      mode === 'edit' && handleQuickSelect(option.value)
                    }
                    disabled={mode !== 'edit'}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {showCreateDeniedPopup && (
        <Popup
          open={showCreateDeniedPopup}
          onOpenChange={(open) => {
            if (!open) handleCreateDeniedPopupClose();
          }}
        >
          <PopupContent
            title={t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.permissionDeniedTitle'
            )}
          >
            <div className="p-4">
              {t(
                'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.permissionDeniedCreate'
              )}
            </div>
            <PopupFooter>
              <Button onClick={handleCreateDeniedPopupClose}>
                {t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.ok')}
              </Button>
            </PopupFooter>
          </PopupContent>
        </Popup>
      )}

      {showEditDeniedPopup && (
        <Popup
          open={showEditDeniedPopup}
          onOpenChange={(open) => {
            if (!open) handleEditDeniedPopupClose();
          }}
        >
          <PopupContent
            title={t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.permissionDeniedTitle'
            )}
          >
            <div className="p-4">
              {t(
                'ctint-mf-cdss.emailAdmin.autoReplyExclusions.detail.permissionDeniedEditSwitchToView'
              )}
            </div>
            <PopupFooter>
              <Button onClick={handleEditDeniedPopupClose}>
                {t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.ok')}
              </Button>
            </PopupFooter>
          </PopupContent>
        </Popup>
      )}
    </div>
  );
};

export default AutoReplyExclusionsDetail;
