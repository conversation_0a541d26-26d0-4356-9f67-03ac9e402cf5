import { Button, useRole, useToast } from '@cdss-modules/design-system';
import { DataTable } from '@cdss-modules/design-system/components/_ui/DataTable';
import { SortingButton } from '@cdss-modules/design-system';
import { useEmailNavigationStore } from '../../../_store/emailNavigationStore';
import { useCallback, useEffect, useState } from 'react';
import { ColumnDef, Table as TableType } from '@tanstack/react-table';
import { cn } from '@cdss-modules/design-system/lib/utils';
import Pagination from '@cdss-modules/design-system/components/_ui/Pagination';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { GLOBAL_DATETIME_FORMAT } from '@cdss-modules/design-system/lib/constants';
import dayjs from 'dayjs';
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { EllipsisVertical } from 'lucide-react';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  Popup,
  PopupTrigger,
  PopupContent,
  PopupFooter,
  PopupClose,
} from '@cdss-modules/design-system/components/_ui/Popup';
import {
  fireGetAutoReplyFilterRules,
  fireDeleteAutoReplyFilterRule,
} from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';
import { debounce } from 'lodash';
import StatusBadge from '@cdss-modules/design-system/components/_ui/StatusBadge';
import { usePermission } from '@cdss-modules/design-system/context/PremissionContext';
import { CommonPermission } from '@cdss-modules/design-system/@types/CommonPermission';

// Types
type TAutoReplyExclusion = {
  id: string;
  ruleType: 'email' | 'domain' | 'wildcard';
  pattern: string;
  status: 'active' | 'inactive';
  lastUpdated: string;
};

// Real API call to fetch auto reply exclusions
const fetchAutoReplyExclusions = async (
  page: number,
  pageSize: number,
  sortOrder?: any,
  filters?: {
    status?: string;
    ruleType?: string;
    search?: string;
  }
) => {
  // Construct query parameters
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());

  // Add filters
  if (filters) {
    if (filters.search) {
      params.append('value', filters.search);
    }

    if (filters.status && filters.status !== 'all') {
      params.append('status', filters.status === 'active' ? '1' : '0');
    }

    if (filters.ruleType && filters.ruleType !== 'all') {
      params.append('type', filters.ruleType);
    }
  }

  // Add ordering parameters
  if (sortOrder) {
    const sortField = Object.keys(sortOrder)[0];
    const sortDirection = sortOrder[sortField];

    if (sortField && sortDirection) {
      // Map UI field names to API field names
      const fieldMapping: { [key: string]: string } = {
        ruleType: 'type',
        pattern: 'value',
        status: 'status',
        lastUpdated: 'updateTime',
      };

      const orderBy = fieldMapping[sortField] || sortField;
      params.append('orderBy', orderBy);
      params.append('order', sortDirection);
    }
  }

  try {
    const response = await fireGetAutoReplyFilterRules(
      basePath,
      params.toString()
    );

    // Extract data using the correct structure
    const { list, total, totalPages } = response.data.data || {
      list: [],
      total: 0,
      totalPages: 0,
    };

    // Transform response to the format expected by the component
    const transformedItems = list.map((item: any) => ({
      id: item.id,
      ruleType: item.type,
      pattern: item.value,
      status: item.status === '1' ? 'active' : 'inactive',
      lastUpdated: item.updateTime,
    }));

    return {
      data: {
        exclusions: transformedItems,
        total: total,
      },
    };
  } catch (error) {
    console.error('Error fetching auto reply exclusions:', error);
    throw error;
  }
};

const AutoReplyExclusionsListing = () => {
  const { globalConfig } = useRole();
  const { permissions } = usePermission();

  const emailAdminPermission = new CommonPermission(globalConfig, permissions);

  const isEditablePermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'edit'
  );

  const isCreatePermission = emailAdminPermission.isPermissionEnabled(
    'ctint-mf-admin',
    'email',
    'create'
  );

  const { t } = useTranslation();
  const { toast } = useToast();
  const createNew = useEmailNavigationStore((state) => state.createNew);
  const viewDetail = useEmailNavigationStore((state) => state.viewDetail);
  const editDetail = useEmailNavigationStore((state) => state.editDetail);
  const [rowSelection, setRowSelection] = useState({});
  const [table, setTable] = useState<TableType<TAutoReplyExclusion>>();
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [exclusionToDelete, setExclusionToDelete] = useState<{
    id: string;
    pattern: string;
    ruleType: string;
  } | null>(null);
  const [sortOrder, setSortOrder] = useState<any>();

  // Filter states
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [ruleTypeFilter, setRuleTypeFilter] = useState<string>('all');
  const [searchValue, setSearchValue] = useState<string>('');
  const [debouncedSearchValue, setDebouncedSearchValue] = useState<string>('');

  // Pagination
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [perPage, setPerPage] = useState<number>(50);

  // Debounced search handler
  const debouncedSearch = useCallback(
    debounce((value: string) => {
      setDebouncedSearchValue(value);
    }, 300),
    []
  );

  // Update debounced value when search input changes
  useEffect(() => {
    debouncedSearch(searchValue);
  }, [searchValue, debouncedSearch]);

  // React Query
  const queryClient = useQueryClient();
  const { data, isLoading, error } = useQuery({
    queryKey: [
      'autoReplyExclusions',
      currentPage,
      perPage,
      sortOrder,
      statusFilter,
      ruleTypeFilter,
      debouncedSearchValue,
    ],
    queryFn: () =>
      fetchAutoReplyExclusions(currentPage, perPage, sortOrder, {
        status: statusFilter,
        ruleType: ruleTypeFilter,
        search: debouncedSearchValue,
      }),
  });

  const exclusions = data?.data?.exclusions || [];
  const totalCount = data?.data?.total || 0;
  const totalPages = Math.ceil(totalCount / perPage);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [statusFilter, ruleTypeFilter, debouncedSearchValue]);

  const handleNext = () => {
    let tar = currentPage + 1;
    if (tar > totalPages) tar = totalPages;
    setCurrentPage(tar);
  };

  const handlePrevious = () => {
    let tar = currentPage - 1;
    if (tar < 1) tar = 1;
    setCurrentPage(tar);
  };

  const handleDeleteExclusion = (
    exclusionId: string,
    pattern: string,
    ruleType: string
  ) => {
    setExclusionToDelete({ id: exclusionId, pattern, ruleType });
    setShowDeleteConfirmation(true);
  };

  const confirmDelete = () => {
    if (exclusionToDelete) {
      setIsDeleting(true);
      fireDeleteAutoReplyFilterRule(exclusionToDelete.id, basePath)
        .then(() => {
          queryClient.invalidateQueries({
            queryKey: ['autoReplyExclusions'],
          });
          setShowDeleteConfirmation(false);
          setExclusionToDelete(null);
          toast({
            title: t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.listing.deleteSuccessMessage'
            ),
            description: t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.listing.deleteSuccessDescription'
            ),
            variant: 'success',
          });
        })
        .catch((err) => {
          console.error('Error deleting exclusion rule:', err);
        })
        .finally(() => {
          setIsDeleting(false);
        });
    }
  };

  // Define columns
  const columns = [
    {
      id: 'ruleType',
      accessorKey: 'ruleType',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.ruleType
              ? sortOrder?.ruleType === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.ruleType === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              ruleType: targetSortOrder,
            });
          }}
        >
          {t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.ruleType')}
        </SortingButton>
      ),
      cell: ({ row }) => (
        <div className="capitalize">{row.getValue('ruleType')}</div>
      ),
    },
    {
      id: 'pattern',
      accessorKey: 'pattern',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.pattern
              ? sortOrder?.pattern === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.pattern === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              pattern: targetSortOrder,
            });
          }}
        >
          {t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.pattern')}
        </SortingButton>
      ),
      cell: ({ row }) => <div>{row.getValue('pattern')}</div>,
    },
    {
      id: 'status',
      accessorKey: 'status',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.status
              ? sortOrder?.status === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.status === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              status: targetSortOrder,
            });
          }}
        >
          {t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.status')}
        </SortingButton>
      ),
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return <StatusBadge status={status as 'active' | 'inactive'} />;
      },
    },
    {
      id: 'lastUpdated',
      accessorKey: 'lastUpdated',
      header: () => (
        <SortingButton
          sorting={
            sortOrder?.lastUpdated
              ? sortOrder?.lastUpdated === 'ASC'
                ? 'asc'
                : 'desc'
              : false
          }
          onClick={() => {
            const targetSortOrder =
              sortOrder?.lastUpdated === 'ASC' ? 'DESC' : 'ASC';
            setSortOrder({
              lastUpdated: targetSortOrder,
            });
          }}
        >
          {t(
            'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.columnLastUpdated'
          )}
        </SortingButton>
      ),
      cell: ({ row }) => {
        const val = row.getValue('lastUpdated') as string;
        return <div>{dayjs(val).format(GLOBAL_DATETIME_FORMAT)}</div>;
      },
    },
    {
      id: 'actions',
      header: t(
        'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.columnAction'
      ),
      cell: ({ row }) => {
        const menuItems = [
          {
            label: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.view'
            ),
            onClick: () => viewDetail(row.original.id, 'auto-reply-exclusions'),
            className: 'w-full text-left px-4 py-2 hover:bg-primary-100',
          },
        ];

        if (isEditablePermission) {
          menuItems.push({
            label: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.edit'
            ),
            onClick: () => editDetail(row.original.id, 'auto-reply-exclusions'),
            className: 'w-full text-left px-4 py-2 hover:bg-primary-100',
          });
          menuItems.push({
            label: t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.delete'
            ),
            onClick: () => {
              handleDeleteExclusion(
                row.original.id,
                row.original.pattern,
                row.original.ruleType
              );
            },
            className:
              'w-full text-left px-4 py-2 text-red-600 hover:bg-primary-100',
          });
        }

        return (
          <div onClick={(e) => e.stopPropagation()}>
            <PopoverMenu
              icon={
                <EllipsisVertical className="w-5 h-5 cursor-pointer hover:text-primary-500" />
              }
              side="left"
            >
              <div className="bg-white rounded shadow-md py-1 min-w-32">
                {menuItems.map((item, index) => (
                  <button
                    key={index}
                    className={item.className}
                    onClick={item.onClick}
                  >
                    {item.label}
                  </button>
                ))}
              </div>
            </PopoverMenu>
          </div>
        );
      },
    },
  ] as ColumnDef<TAutoReplyExclusion>[];

  // Status options for filter dropdown
  const statusOptions = [
    {
      id: 'all',
      label: t(
        'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.allStatuses'
      ),
      value: 'all',
    },
    {
      id: 'active',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.active'),
      value: 'active',
    },
    {
      id: 'inactive',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.inactive'),
      value: 'inactive',
    },
  ];

  // Rule type options for filter dropdown
  const ruleTypeOptions = [
    {
      id: 'all',
      label: t(
        'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.allRuleTypes'
      ),
      value: 'all',
    },
    {
      id: 'subject',
      label: t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.subject'),
      value: 'subject',
    },
    {
      id: 'domain',
      label: t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.domain'),
      value: 'domain',
    },
    {
      id: 'email',
      label: t('ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.email'),
      value: 'email',
    },
  ];

  return (
    <div className="px-4 pt-1 pb-6 flex flex-col h-full gap-y-4">
      <div className="flex justify-between items-center overflow-auto gap-2 pt-2">
        <div className="flex items-center gap-2">
          <Select
            options={statusOptions}
            value={statusFilter}
            onChange={setStatusFilter}
            placeholder={t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.allStatuses'
            )}
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="text-sm w-[200px]"
          />
          <Select
            options={ruleTypeOptions}
            value={ruleTypeFilter}
            onChange={setRuleTypeFilter}
            placeholder={t(
              'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.allRuleTypes'
            )}
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="text-sm w-[200px]"
          />
          <div className="flex-1">
            <Input
              className=""
              beforeIcon={<Icon name="search" />}
              placeholder={t(
                'ctint-mf-cdss.emailAdmin.autoReplyExclusions.listing.searchPlaceholder'
              )}
              value={searchValue}
              onChange={(value) => setSearchValue(String(value))}
              allowClear
              size="s"
            />
          </div>
        </div>
        {isCreatePermission && (
          <Button
            size="s"
            onClick={() => createNew('auto-reply-exclusions')}
          >
            {t(
              'ctint-mf-cdss.emailAdmin.mailboxRoutingRules.listing.createNewButton'
            )}
          </Button>
        )}
      </div>
      <div className="flex-1 h-0">
        <DataTable<TAutoReplyExclusion>
          data={exclusions}
          columns={columns}
          loading={isLoading}
          emptyMessage={t(
            'ctint-mf-cdss.emailAdmin.autoReplyExclusions.listing.emptyMessage'
          )}
          error={error instanceof Error ? error.message : undefined}
          rowSelection={rowSelection}
          setRowSelection={setRowSelection}
          onClickRow={(row) => {
            viewDetail(row.original.id, 'auto-reply-exclusions');
          }}
          onTableSetUp={(tableInstance) => setTable(tableInstance)}
          resize={true}
        />
      </div>
      {totalPages > 0 && (
        <section className="flex-row">
          <div>
            <Pagination
              current={currentPage}
              perPage={perPage}
              total={totalPages}
              totalCount={totalCount}
              onChange={(v) => setCurrentPage(v)}
              handleOnPrevious={() => handlePrevious()}
              handleOnNext={() => handleNext()}
              handlePerPageSetter={(p: number) => {
                const pageSize = Number(p);
                if (!isNaN(pageSize)) {
                  setPerPage(pageSize);
                }
                setCurrentPage(1);
              }}
            />
          </div>
        </section>
      )}

      {/* Delete Confirmation Modal */}
      <Popup
        open={showDeleteConfirmation}
        onOpenChange={setShowDeleteConfirmation}
      >
        <PopupContent
          title={t(
            'ctint-mf-cdss.emailAdmin.autoReplyExclusions.listing.confirmDeleteTitle'
          )}
          className="sm:max-w-md"
        >
          <div className="p-4">
            <p>
              {t(
                'ctint-mf-cdss.emailAdmin.autoReplyExclusions.listing.confirmDeleteMessage'
              )}
            </p>
            {exclusionToDelete && (
              <div className="mt-2">
                <p className="font-medium text-gray-900">
                  <span className="text-gray-600">
                    {t(
                      `ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.${exclusionToDelete.ruleType}`
                    )}
                    :
                  </span>{' '}
                  {exclusionToDelete.pattern}
                </p>
              </div>
            )}
          </div>
          <PopupFooter className="px-4 pb-4 pt-2">
            <PopupClose asChild>
              <Button
                variant="secondary"
                onClick={() => {
                  setShowDeleteConfirmation(false);
                  setExclusionToDelete(null);
                }}
              >
                {t(
                  'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.cancel'
                )}
              </Button>
            </PopupClose>
            <Button
              variant="primary"
              onClick={confirmDelete}
              disabled={isDeleting}
            >
              {isDeleting
                ? t(
                    'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.deleting'
                  )
                : t(
                    'ctint-mf-cdss.emailAdmin.autoReplyExclusions.common.delete'
                  )}
            </Button>
          </PopupFooter>
        </PopupContent>
      </Popup>
    </div>
  );
};

export default AutoReplyExclusionsListing;
