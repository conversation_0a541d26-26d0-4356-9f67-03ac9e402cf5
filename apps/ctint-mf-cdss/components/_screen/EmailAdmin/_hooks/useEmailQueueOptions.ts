import { useQuery } from '@tanstack/react-query';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { basePath } from '@cdss/lib/appConfig';
import { fireGetEmailQueueOptions } from '@cdss/lib/api';

type QueueOption = {
  id: string;
  label: string;
  value: string;
};

export const useEmailQueueOptions = () => {
  const { t } = useTranslation();

  const { data, isLoading, error } = useQuery({
    queryKey: ['emailQueueOptions'],
    queryFn: async () => {
      try {
        const response = await fireGetEmailQueueOptions(basePath);
        return response.data?.data?.entities || [];
      } catch (error) {
        console.error('Error fetching email queue options:', error);
        return [];
      }
    },
  });

  // Transform the response data into options format
  const queueOptions: QueueOption[] = [
    {
      id: 'all',
      label: t('ctint-mf-cdss.emailAdmin.mailboxRoutingRules.common.allQueues'),
      value: 'all',
    },
  ];

  if (data && Array.isArray(data)) {
    data.forEach((queue: any) => {
      queueOptions.push({
        id: queue.id,
        label: queue.name,
        value: queue.id,
      });
    });
  }

  return { queueOptions, isLoading, error };
};
