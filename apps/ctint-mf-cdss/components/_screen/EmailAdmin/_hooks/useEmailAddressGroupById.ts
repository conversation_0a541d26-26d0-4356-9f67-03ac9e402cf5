import { fireGetFullAddressGroupById } from '@cdss/lib/api';
import { useQuery, useQueryClient } from '@tanstack/react-query';

export const useEmailAddressGroupById = (
  groupId: string | undefined,
  basePath?: string
) => {
  const queryClient = useQueryClient();

  return useQuery({
    queryKey: ['emailAddressGroup', groupId],
    queryFn: async () => {
      if (!groupId) return null;

      // First, try to get the group name from the existing list cache
      const listCacheData = queryClient.getQueryData(['emailAddressGroups']);
      if (
        listCacheData &&
        typeof listCacheData === 'object' &&
        'data' in listCacheData
      ) {
        const listData = listCacheData.data as any;
        if (listData?.list) {
          const foundGroup = listData.list.find(
            (group: any) => group.id === groupId
          );
          if (foundGroup?.groupName) {
            return foundGroup.groupName;
          }
        }
      }

      // If not found in cache, fetch from API
      const response = await fireGetFullAddressGroupById(groupId, basePath);

      if (response?.data?.data?.list?.[0]?.groupName) {
        return response.data.data.list[0].groupName;
      }

      return null;
    },
    enabled: !!groupId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
