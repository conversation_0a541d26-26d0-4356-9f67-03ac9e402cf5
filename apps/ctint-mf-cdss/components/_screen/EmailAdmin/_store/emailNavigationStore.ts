import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { useMailboxRoutingRuleFormStore } from './mailboxRoutingRulesFormStore';
import { useAutoReplyExclusionFormStore } from './autoReplyExclusionsFormStore';
import { useEmailAddressGroupsFormStore } from './emailAddressGroupsFormStore';

export type TabName =
  | 'mailbox-routing-rules'
  | 'auto-reply-exclusions'
  | 'email-address-groups';
type View = 'list' | 'detail';
type Mode = 'view' | 'edit';

interface TabState {
  view: View;
  selectedItemId: string | null;
  mode: Mode;
}

interface EmailNavigationState {
  currentTab: TabName;
  mailboxRoutingRules: TabState;
  autoReplyExclusions: TabState;
  emailAddressGroups: TabState;

  // Navigation actions
  setCurrentTab: (tab: TabName) => void;
  viewDetail: (id: string, tab: TabName) => void;
  editDetail: (id: string, tab: TabName) => void;
  backToList: (tab?: TabName) => void;
  createNew: (tab: TabName) => void;
}

export const useEmailNavigationStore = create<EmailNavigationState>()(
  immer((set) => ({
    currentTab: 'mailbox-routing-rules',
    mailboxRoutingRules: {
      view: 'list',
      selectedItemId: null,
      mode: 'view',
    },
    autoReplyExclusions: {
      view: 'list',
      selectedItemId: null,
      mode: 'view',
    },
    emailAddressGroups: {
      view: 'list',
      selectedItemId: null,
      mode: 'view',
    },

    setCurrentTab: (tab) =>
      set((state) => {
        state.currentTab = tab;
      }),

    viewDetail: (id, tab) =>
      set((state) => {
        if (tab === 'mailbox-routing-rules') {
          state.mailboxRoutingRules.selectedItemId = id;
          state.mailboxRoutingRules.view = 'detail';
          state.mailboxRoutingRules.mode = 'view';
        } else if (tab === 'auto-reply-exclusions') {
          state.autoReplyExclusions.selectedItemId = id;
          state.autoReplyExclusions.view = 'detail';
          state.autoReplyExclusions.mode = 'view';
        } else {
          state.emailAddressGroups.selectedItemId = id;
          state.emailAddressGroups.view = 'detail';
          state.emailAddressGroups.mode = 'view';
        }
        state.currentTab = tab;
      }),

    editDetail: (id, tab) =>
      set((state) => {
        if (tab === 'mailbox-routing-rules') {
          state.mailboxRoutingRules.selectedItemId = id;
          state.mailboxRoutingRules.view = 'detail';
          state.mailboxRoutingRules.mode = 'edit';
        } else if (tab === 'auto-reply-exclusions') {
          state.autoReplyExclusions.selectedItemId = id;
          state.autoReplyExclusions.view = 'detail';
          state.autoReplyExclusions.mode = 'edit';
        } else {
          state.emailAddressGroups.selectedItemId = id;
          state.emailAddressGroups.view = 'detail';
          state.emailAddressGroups.mode = 'edit';
        }
        state.currentTab = tab;
      }),

    createNew: (tab) =>
      set((state) => {
        if (tab === 'mailbox-routing-rules') {
          state.mailboxRoutingRules.selectedItemId = null;
          state.mailboxRoutingRules.view = 'detail';
          state.mailboxRoutingRules.mode = 'edit';

          // Initialize the form data immediately when creating a new mailbox routing rule
          const initNewForm =
            useMailboxRoutingRuleFormStore.getState().initNewForm;
          initNewForm();
        } else if (tab === 'auto-reply-exclusions') {
          state.autoReplyExclusions.selectedItemId = null;
          state.autoReplyExclusions.view = 'detail';
          state.autoReplyExclusions.mode = 'edit';

          // Initialize the form data immediately when creating a new auto reply exclusion
          const initNewFormARE =
            useAutoReplyExclusionFormStore.getState().initNewForm;
          initNewFormARE();
        } else if (tab === 'email-address-groups') {
          state.emailAddressGroups.selectedItemId = null;
          state.emailAddressGroups.view = 'detail';
          state.emailAddressGroups.mode = 'edit';

          // Initialize the form data for email address groups
          const initNewFormEAG =
            useEmailAddressGroupsFormStore.getState().initNewForm;
          initNewFormEAG();
        }
        state.currentTab = tab;
      }),

    backToList: (tab) =>
      set((state) => {
        // If no tab specified, use current tab
        const targetTab = tab || state.currentTab;
        if (
          targetTab === 'mailbox-routing-rules' &&
          state.mailboxRoutingRules.view === 'detail'
        ) {
          state.mailboxRoutingRules.view = 'list';
          state.mailboxRoutingRules.selectedItemId = null;
        } else if (
          targetTab === 'auto-reply-exclusions' &&
          state.autoReplyExclusions.view === 'detail'
        ) {
          state.autoReplyExclusions.view = 'list';
          state.autoReplyExclusions.selectedItemId = null;
        } else if (
          targetTab === 'email-address-groups' &&
          state.emailAddressGroups.view === 'detail'
        ) {
          state.emailAddressGroups.view = 'list';
          state.emailAddressGroups.selectedItemId = null;
        }
      }),
  }))
);
