import { create, StateCreator } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { WritableDraft } from 'immer';
import { fireCreateOrUpdateFullAddress } from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';

// Define the form data structure
export type TEmailAddressGroupsFormData = {
  id: string;
  name: string;
  // Add other fields relevant to email address groups here
  emails: string[]; // Added to store list of emails
  lastUpdated: string;
};

// Initial state for empty form
const initialFormState: TEmailAddressGroupsFormData = {
  id: '',
  name: '',
  emails: [], // Initialize emails as an empty array
  lastUpdated: new Date().toISOString(),
};

// Helper to create a deep copy of the form data
const deepCopyFormData = (
  data: TEmailAddressGroupsFormData
): TEmailAddressGroupsFormData => {
  return {
    ...data,
    emails: [...data.emails], // Ensure emails array is deep copied
  };
};

interface IEmailAddressGroupsFormState {
  // Form data
  formData: TEmailAddressGroupsFormData;
  originalFormData: TEmailAddressGroupsFormData | null; // Store the original data for reverting
  isNew: boolean;
  isDirty: boolean;
  validationErrors: Record<string, string>;
  isEditingEmails: boolean; // New state for email edit mode
  emailsInEdit: string[]; // New state for emails being edited

  // State for CSV Import staging and validation
  stagedForImportGroup: string[];
  stagedForImportValidation: {
    isValid: boolean;
    errors: {
      index: number;
      message: string;
      value: string; // Store the problematic value for display
    }[];
  } | null;

  // Form Actions
  initNewForm: () => void;
  loadExistingData: (data: Partial<TEmailAddressGroupsFormData>) => void;
  updateField: <K extends keyof TEmailAddressGroupsFormData>(
    field: K,
    value: TEmailAddressGroupsFormData[K]
  ) => void;
  addEmail: (email: string) => void; // Action to add an email
  removeEmail: (emailToRemove: string) => void; // Action to remove an email
  validateForm: () => boolean;
  resetForm: () => void;
  saveForm: () => Promise<{
    success: boolean;
    id?: string;
    name?: string;
    error?: string;
  }>;

  // Actions for bulk email editing
  startEditEmails: () => void;
  updateEmailInEdit: (index: number, emailValue: string) => void;
  removeEmailFromEdit: (index: number) => void; // New action to remove email during edit mode
  saveEditedEmails: () => boolean; // Returns true if successful, false otherwise (e.g. validation failed)
  cancelEditEmails: () => void;

  // Actions for CSV Import staging and validation
  setStagedEmailsForImport: (emails: string[]) => void;
  updateStagedEmailForImport: (index: number, emailValue: string) => void;
  removeStagedEmailForImport: (index: number) => void;
  confirmAndAddImportedEmails: () => boolean;
  cancelImportProcess: () => void;

  // Internal helper for validation calculation, not meant for direct call from component
  _calculateStagedEmailsValidation: (
    stagedEmailsToValidate: string[],
    currentGroupEmails: string[]
  ) => IEmailAddressGroupsFormState['stagedForImportValidation'];
}

export const useEmailAddressGroupsFormStore =
  create<IEmailAddressGroupsFormState>()(
    immer(
      (set, get): IEmailAddressGroupsFormState => ({
        // State
        formData: { ...initialFormState },
        originalFormData: null,
        isNew: true,
        isDirty: false,
        validationErrors: {},
        isEditingEmails: false, // Initialize new state
        emailsInEdit: [], // Initialize new state
        // Initialize CSV import state
        stagedForImportGroup: [],
        stagedForImportValidation: null,

        // Form Actions
        initNewForm: () =>
          set((state) => {
            const freshFormData: TEmailAddressGroupsFormData = {
              id: '',
              name: '',
              emails: [], // Initialize emails in new form
              lastUpdated: new Date().toISOString(),
            };

            state.formData = freshFormData;
            state.originalFormData = null;
            state.isNew = true;
            state.isDirty = false;
            state.validationErrors = {};
            // Clear staged import data when initializing new form
            state.stagedForImportGroup = [];
            state.stagedForImportValidation = null;
          }),

        loadExistingData: (data) =>
          set((state) => {
            const formData = {
              ...initialFormState,
              ...data,
              emails: data.emails ? [...data.emails] : [], // Handle emails in existing data
            };

            const originalData = deepCopyFormData(formData);

            state.formData = formData;
            state.originalFormData = originalData;
            state.isNew = false;
            state.isDirty = false;
            state.validationErrors = {};
          }),

        updateField: (field, value) =>
          set((state) => {
            state.formData[field] = value;
            state.isDirty = true;
            if (state.validationErrors[field]) {
              delete state.validationErrors[field];
            }
          }),

        addEmail: (email) =>
          set((state) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
              state.validationErrors.email = 'Invalid email format';
              return;
            }
            if (state.formData.emails.includes(email)) {
              state.validationErrors.email = 'Email already added';
              return;
            }
            delete state.validationErrors.email;
            state.formData.emails.push(email);
            state.isDirty = true;
          }),

        removeEmail: (emailToRemove) =>
          set((state) => {
            state.formData.emails = state.formData.emails.filter(
              (email) => email !== emailToRemove
            );
            // Also remove from emailsInEdit if in edit mode and the email exists there
            if (state.isEditingEmails) {
              state.emailsInEdit = state.emailsInEdit.filter(
                (e) => e !== emailToRemove
              );
            }
            state.isDirty = true;
          }),

        validateForm: () => {
          const { formData } = get();
          const errors: Record<string, string> = {};

          if (!formData.name.trim()) {
            errors.name = 'Name is required';
          }
          if (formData.emails.length === 0) {
            errors.emails = 'At least one email is required';
          }

          set((state) => {
            state.validationErrors = errors;
          });

          return Object.keys(errors).length === 0;
        },

        resetForm: () =>
          set((state) => {
            if (state.originalFormData) {
              state.formData = deepCopyFormData(state.originalFormData);
            } else if (state.isNew) {
              const freshFormData: TEmailAddressGroupsFormData = {
                id: '',
                name: '',
                emails: [], // Reset emails
                lastUpdated: new Date().toISOString(),
              };
              state.formData = freshFormData;
            }
            state.isDirty = false;
            state.validationErrors = {};
            // Clear staged import data when resetting form
            state.stagedForImportGroup = [];
            state.stagedForImportValidation = null;
          }),

        saveForm: async () => {
          const isValid = get().validateForm();
          if (!isValid) {
            return { success: false };
          }

          const { formData, isNew } = get();
          const apiPayload = {
            groupName: formData.name,
            fullAddressList: formData.emails,
            id: isNew ? undefined : formData.id,
          };

          try {
            const response = await fireCreateOrUpdateFullAddress(
              apiPayload,
              basePath
            );

            if (response && response.data && response.data.isSuccess) {
              let newId = formData.id;
              let groupName = formData.name; // Default to form data name

              // Extract the authoritative ID and name from the API response
              if (response.data.data) {
                if (isNew && response.data.data.id) {
                  newId = response.data.data.id;
                }

                // Use the API response title as the authoritative name
                if (response.data.data.title) {
                  groupName = response.data.data.title;
                }
              }

              set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
                state.isDirty = false;
                if (isNew && newId) {
                  state.formData.id = newId;
                }

                // Update the form data name to match the API response title
                if (response.data.data?.title) {
                  state.formData.name = response.data.data.title;
                }

                state.formData.lastUpdated =
                  response.data.data?.updateTime || new Date().toISOString();
                state.originalFormData = deepCopyFormData(state.formData);

                if (isNew) state.isNew = false;
              });

              return { success: true, id: newId, name: groupName };
            } else {
              const errorMessage =
                response?.data?.error || 'Failed to save group.';
              set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
                state.validationErrors.general = errorMessage;
              });
              return { success: false, error: errorMessage };
            }
          } catch (error) {
            console.error('Error saving email address group:', error);

            // Extract error message from axios error response
            let errorMessage = 'An unexpected error occurred.';

            if (error && typeof error === 'object' && 'response' in error) {
              // This is an axios error with response
              const axiosError = error as any;
              console.log(
                'Axios error response data:',
                axiosError.response?.data
              );

              if (axiosError.response?.data?.error) {
                // Use the specific error message from the API
                errorMessage = axiosError.response.data.error;
                console.log('Using API error message:', errorMessage);
              } else if (axiosError.response?.data?.message) {
                // Some APIs use 'message' instead of 'error'
                errorMessage = axiosError.response.data.message;
                console.log('Using API message:', errorMessage);
              } else if (axiosError.message) {
                // Fallback to axios error message
                errorMessage = axiosError.message;
                console.log('Using axios error message:', errorMessage);
              }
            } else if (error instanceof Error) {
              errorMessage = error.message;
              console.log('Using generic error message:', errorMessage);
            }

            set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
              state.validationErrors.general = errorMessage;
            });
            return { success: false, error: errorMessage };
          }
        },

        // --- Bulk Email Editing Actions ---
        startEditEmails: () =>
          set((state) => {
            if (state.formData.emails.length > 0) {
              state.emailsInEdit = [...state.formData.emails]; // Copy current emails to edit buffer
              state.isEditingEmails = true;
              state.validationErrors.emailsInEdit = ''; // Clear previous validation errors for this specific mode
            }
          }),

        updateEmailInEdit: (index, emailValue) =>
          set((state) => {
            if (
              state.isEditingEmails &&
              state.emailsInEdit[index] !== undefined
            ) {
              state.emailsInEdit[index] = emailValue;
              state.isDirty = true; // Mark dirty as form data will change if saved
              // Clear validation error for this specific email as user is typing
              if (state.validationErrors.emailsInEdit) {
                const errors = state.validationErrors.emailsInEdit.split('; ');
                const newErrors = errors.filter(
                  (err) => !err.startsWith(`Email #${index + 1}`)
                );
                state.validationErrors.emailsInEdit = newErrors.join('; ');
                if (!state.validationErrors.emailsInEdit)
                  delete state.validationErrors.emailsInEdit;
              }
            }
          }),

        removeEmailFromEdit: (indexToRemove) =>
          set((state) => {
            if (
              state.isEditingEmails &&
              state.emailsInEdit[indexToRemove] !== undefined
            ) {
              state.emailsInEdit.splice(indexToRemove, 1); // Remove email at index
              state.isDirty = true;
              // Optionally, re-validate or clear validation errors if the list changes significantly
              // For now, just marking dirty. Save action will re-validate.
              if (state.validationErrors.emailsInEdit) {
                // Attempt to clear validation errors related to index, though it's complex
                // as indices shift. Simpler to let save re-validate.
                delete state.validationErrors.emailsInEdit;
              }
            }
          }),

        saveEditedEmails: () => {
          const { emailsInEdit } = get();
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          let isValid = true;
          const validationMessages: string[] = [];

          for (let i = 0; i < emailsInEdit.length; i++) {
            if (!emailRegex.test(emailsInEdit[i])) {
              isValid = false;
              validationMessages.push(
                `Email #${i + 1} ('${emailsInEdit[i]}') is invalid`
              );
            }
            // Check for duplicates within the edited list
            for (let j = i + 1; j < emailsInEdit.length; j++) {
              if (emailsInEdit[i] === emailsInEdit[j]) {
                isValid = false;
                validationMessages.push(
                  `Email #${i + 1} and #${j + 1} are duplicates ('${emailsInEdit[i]}')`
                );
              }
            }
          }

          if (!isValid) {
            set((state) => {
              state.validationErrors.emailsInEdit =
                validationMessages.join('; ');
            });
            return false;
          }

          set((state) => {
            state.formData.emails = [...state.emailsInEdit];
            state.isEditingEmails = false;
            // state.emailsInEdit = []; // Clear the edit buffer
            state.isDirty = true; // formData.emails has been updated
            delete state.validationErrors.emailsInEdit; // Clear validation errors on success
          });
          return true;
        },

        cancelEditEmails: () =>
          set((state) => {
            state.isEditingEmails = false;
            // state.emailsInEdit = []; // Clear the edit buffer
            delete state.validationErrors.emailsInEdit; // Clear any validation errors from this mode
          }),

        // --- CSV Import Staging and Validation Actions ---
        _calculateStagedEmailsValidation: (
          stagedEmailsToValidate: string[],
          currentGroupEmails: string[]
        ): IEmailAddressGroupsFormState['stagedForImportValidation'] => {
          const errors: { index: number; message: string; value: string }[] =
            [];
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          let isValidOverall = true;

          if (stagedEmailsToValidate.length === 0) {
            return null; // No validation state if no emails are staged
          }

          const seenInStaged: Record<string, number> = {};

          stagedEmailsToValidate.forEach((email: string, index: number) => {
            let currentEmailIsValid = true;
            if (!emailRegex.test(email)) {
              errors.push({
                index,
                message: 'Invalid email format.',
                value: email,
              });
              currentEmailIsValid = false;
              isValidOverall = false;
            }

            if (currentEmailIsValid) {
              if (seenInStaged[email] !== undefined) {
                errors.push({
                  index,
                  message: `Duplicate of an earlier entry in this import list (entry #${seenInStaged[email] + 1}).`,
                  value: email,
                });
                const firstIndex = seenInStaged[email];
                if (
                  !errors.some(
                    (err) =>
                      err.index === firstIndex &&
                      err.message.startsWith('Duplicate')
                  )
                ) {
                  errors.push({
                    index: firstIndex,
                    message: `This email is duplicated later in this import list (entry #${index + 1}).`,
                    value: email,
                  });
                }
                isValidOverall = false;
              } else {
                seenInStaged[email] = index;
              }

              if (currentGroupEmails.includes(email)) {
                errors.push({
                  index,
                  message: 'Already exists in the current group.',
                  value: email,
                });
                isValidOverall = false;
              }
            }
          });

          return {
            isValid: isValidOverall,
            errors: errors.sort(
              (a: { index: number }, b: { index: number }) => a.index - b.index
            ),
          };
        },

        setStagedEmailsForImport: (emails: string[]) =>
          set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
            state.stagedForImportGroup = emails;
            state.stagedForImportValidation =
              get()._calculateStagedEmailsValidation(
                emails,
                state.formData.emails
              );
          }),

        updateStagedEmailForImport: (index: number, emailValue: string) =>
          set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
            if (state.stagedForImportGroup[index] !== undefined) {
              state.stagedForImportGroup[index] = emailValue;
              state.stagedForImportValidation =
                get()._calculateStagedEmailsValidation(
                  state.stagedForImportGroup,
                  state.formData.emails
                );
            }
          }),

        removeStagedEmailForImport: (indexToRemove: number) =>
          set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
            if (state.stagedForImportGroup[indexToRemove] !== undefined) {
              state.stagedForImportGroup.splice(indexToRemove, 1);
              state.stagedForImportValidation =
                get()._calculateStagedEmailsValidation(
                  state.stagedForImportGroup,
                  state.formData.emails
                );
            }
          }),

        confirmAndAddImportedEmails: () => {
          let currentValidationState = get().stagedForImportValidation;
          const currentStagedGroup = get().stagedForImportGroup; // Get the definitive current staged list
          const currentFormDataEmails = get().formData.emails; // Get the definitive current form emails

          // Recalculate validation if it seems stale or if there are items to validate
          if (!currentValidationState || currentStagedGroup.length > 0) {
            currentValidationState = get()._calculateStagedEmailsValidation(
              currentStagedGroup,
              currentFormDataEmails
            );
            // If after calculation, it's still null (e.g., list became empty and _calculateStagedEmailsValidation returned null)
            // or if it is explicitly not valid, then update the store state and return false.
            if (!currentValidationState || !currentValidationState.isValid) {
              set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
                state.stagedForImportValidation = currentValidationState; // Ensure store reflects this validation state
              });
              return false;
            }
          }

          // Final check on the (potentially re-calculated) validation state
          if (!currentValidationState || !currentValidationState.isValid) {
            set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
              // This block might be redundant if the one above correctly sets and returns,
              // but it's a safeguard to ensure the store reflects the latest validation if we are about to return false.
              if (state.stagedForImportValidation !== currentValidationState) {
                state.stagedForImportValidation = currentValidationState;
              }
            });
            return false;
          }

          // const { stagedForImportGroup } = get(); // Use currentStagedGroup obtained earlier
          if (currentStagedGroup.length === 0) {
            set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
              state.stagedForImportGroup = [];
              state.stagedForImportValidation = null;
            });
            return true;
          }

          set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
            const uniqueNewEmails = state.stagedForImportGroup.filter(
              // state.stagedForImportGroup should be same as currentStagedGroup if no async issues
              (email: string, index: number, self: string[]) =>
                email.trim() !== '' &&
                self.indexOf(email) === index &&
                !state.formData.emails.includes(email)
            );

            state.formData.emails.push(...uniqueNewEmails);
            state.stagedForImportGroup = [];
            state.stagedForImportValidation = null;
            state.isDirty = true;
          });
          return true;
        },

        cancelImportProcess: () =>
          set((state: WritableDraft<IEmailAddressGroupsFormState>) => {
            state.stagedForImportGroup = [];
            state.stagedForImportValidation = null;
          }),
      })
    )
  );
