/* eslint-disable @nx/enforce-module-boundaries */
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import MiniWallboard from '../_ui/MiniWallboard';
import {
  ResizablePanel,
  ResizablePanelGroup,
} from '@cdss-modules/design-system';
import ToolbarModal from '../_ui/ToolbarModal';
import ToolbarHead from '../_ui/ToolbarHead';
import ToolbarCallPanel from '../_ui/ToolbarCallPanel';
import ToolbarFlexiblePanel from '../_ui/ToolbarFlexiblePanel';
import ToolbarDialPad from '../_ui/ToolbarDialPad';
import { cn } from '@cdss-modules/design-system/lib/utils';
import ToolbarInteractions from '../_ui/ToolbarInteractions';
import ToolbarAgentStatus from '../_ui/ToolbarAgentStatus';
import { BarChart3 } from 'lucide-react';
import ToolbarIVR from '../_ui/ToolbarIVR';
import ToolbarOutbound from '../_ui/ToolbarOutbound';
import ToolbarTransfer from '../_ui/ToolbarTransfer';
import ToolbarCallSettings from '../_ui/ToolbarCallSettings';
import ToolbarStations from '../_ui/ToolbarStations';
import ToolbarActiveCallInfo from '../_ui/ToolbarActiveCallInfo';
import ToolbarAgentInfo from '../_ui/ToolbarAgentInfo';
import ToolbarCustomerInfo from '../_ui/ToolbarCustomerInfo';
import WebRtcBlock from '@cdss-modules/design-system/components/_other/WebRtcBlock';
const Toolbar = () => {
  const {
    interactions,
    stationContext: { station },
  } = useTbarContext();
  const hasConnectedInteraction = interactions?.some(
    (item: any) => item?.status === 'connected'
  );

  return (
    <div
      className={cn(
        'relative w-full h-full overflow-hidden rounded-lg',
        hasConnectedInteraction && 'border-[2px] border-status-success'
      )}
    >
      {station?.type == 'inin_webrtc_softphone' && (
        <WebRtcBlock className="hidden" />
      )}
      <ToolbarModal
        id="transfer-panel"
        coverArea="right"
      >
        <ToolbarTransfer />
      </ToolbarModal>
      <ToolbarModal
        id="outbound-panel"
        coverArea="right"
      >
        <ToolbarOutbound />
      </ToolbarModal>
      <ToolbarModal
        id="dialpad-panel"
        coverArea="right"
      >
        <div className="size-full flex items-center">
          <ToolbarDialPad />
        </div>
      </ToolbarModal>
      <ToolbarModal
        id="toolbar-ivr-panel"
        coverArea="right"
      >
        <ToolbarIVR />
      </ToolbarModal>
      <ToolbarModal
        id="customer-info-panel"
        coverArea="right"
      >
        <ToolbarCustomerInfo />
      </ToolbarModal>
      <ToolbarModal
        id="agent-status-panel"
        coverArea="right"
      >
        <ToolbarAgentStatus />
      </ToolbarModal>
      <ToolbarModal
        id="agent-info-panel"
        coverArea="full"
      >
        <ToolbarAgentInfo />
      </ToolbarModal>
      <ToolbarModal
        id="stations-panel"
        coverArea="right"
      >
        <ToolbarStations />
      </ToolbarModal>
      <ToolbarModal
        id="call-control-menu"
        coverArea="right"
      >
        <ToolbarCallSettings />
      </ToolbarModal>
      <div className="z-10 flex flex-col w-full h-full overflow-hidden bg-common-bg text-footnote 4xl:text-remark">
        <ToolbarHead />
        <div className="w-full h-0 flex-1 flex items-center justify-between bg-white p-1">
          <ResizablePanelGroup
            direction="horizontal"
            className="flex gap-x-1 w-full h-full"
          >
            <ResizablePanel className="bg-white z-20">
              <div
                className={cn(
                  'w-full h-full bg-white rounded-lg',
                  'flex flex-row-reverse gap-x-2'
                )}
              >
                <div
                  className={cn(
                    'bg-white border-common-bg w-full z-10',
                    'border-l-[1px] flex flex-col max-w-[400px] bg-primary-100 border border-primary-1000 rounded'
                  )}
                >
                  <div className="z-20 w-full flex justify-between">
                    <div
                      className={cn(
                        'z-20 w-full flex items-center px-2 overflow-auto',
                        'min-h-14'
                      )}
                    >
                      <ToolbarCallPanel />
                    </div>
                  </div>
                  <ToolbarActiveCallInfo />
                </div>
                <div
                  className={cn(
                    'z-10 flex flex-wrap gap-4 bg-white rounded-l-lg pl-2',
                    'w-0 flex-1 overflow-x-auto overflow-y-hidden',
                    'h-full'
                  )}
                >
                  <ToolbarInteractions />
                </div>
              </div>
            </ResizablePanel>
            <ToolbarFlexiblePanel
              tooltip="Mini Wallboard"
              icon={<BarChart3 className="size-5" />}
              maxSize={50}
            >
              <MiniWallboard />
            </ToolbarFlexiblePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    </div>
  );
};

export default Toolbar;
