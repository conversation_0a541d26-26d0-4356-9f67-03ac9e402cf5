'use client';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { Tooltip } from '@cdss-modules/design-system/components/_ui/Tooltip';
import ToolbarParticipantBlock from '../ToolbarParticipantBlock';
import ToolbarHistoryBlock from '../ToolbarHistoryBlock';
import { List, AppWindowMac, Eye, Undo2 } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import {
  TSubmitWrapup,
  TInteractionFilter,
  IInteractionItemResp,
} from '@cdss-modules/design-system/@types';

import _ from 'lodash';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import ToolbarWrapup from '../ToolbarWrapup';
import { useInView } from 'react-intersection-observer';

const ToolbarInteractions = () => {
  const {
    wrapupContext: {
      selectedWrapupList,
      updateSelectedWrapupList,
      updateSelectedWrapupId,
      showWrapup,
      selectedWrapupId,
    },
    interactions,
    conversationHistoryListHandle,
    interactionFilter,
    updateInteractionFilter,
  } = useTbarContext();
  const [filterExpanded, setFilterExpanded] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [showWrapupResult, setShowWrapupResult] = useState<boolean>(false);
  const [showTextarea, setShowTextarea] = useState<{
    type: 'CATEGORY' | 'GROUP' | 'CODE' | null;
    isOpen: boolean;
  }>({
    type: null,
    isOpen: false,
  });
  const handleSelectCategory = (cateCode: string) => {
    setSelectedGroup('');
    if (selectedCategory === cateCode) {
      setSelectedCategory('');
    } else {
      setShowTextarea({
        type: null,
        isOpen: false,
      });
      setSelectedCategory(cateCode);
    }
  };

  const handleSelectGroup = (
    groupCode: string,
    groupId?: number,
    groupParentId?: number,
    groupName?: string
  ) => {
    setSelectedGroup(groupCode);
    updateSelectedWrapupList((prev: any) => {
      let result = [...prev];
      const matchedIndex = result?.findIndex(
        (item) => item.id === groupParentId
      );

      if (matchedIndex > -1) {
        result[matchedIndex] = {
          ...result[matchedIndex],
          groupId,
          groupName,
        };
      } else {
        result = [...result];
      }
      return result;
    });
  };

  const handleCodeItem = (codeItem: any) => {
    if (
      selectedWrapupList.find(
        (wrapup: TSubmitWrapup) => wrapup.wrapUpCode === codeItem.code
      )
    ) {
      updateSelectedWrapupList(
        selectedWrapupList.filter(
          (wrap: TSubmitWrapup) => wrap.wrapUpCode !== codeItem.code
        )
      );
    } else {
      updateSelectedWrapupList((prev: any) => [
        ...prev,
        {
          wrapUpCode: codeItem.code,
          wrapUpName: codeItem.name,
        },
      ]);
    }
  };

  const updateSelectedCategory = (value: string | null) => {
    setSelectedCategory(value);
  };

  const updateSelectedGroup = (value: string | null) => {
    setSelectedGroup(value);
  };

  const updateShowTextarea = (data: any) => {
    setShowTextarea(data);
  };
  const interactionHistory = useMemo(() => {
    if (conversationHistoryListHandle.data) {
      const newArr = conversationHistoryListHandle.data.pages.flatMap(
        (page: any) => page.data
      );
      return newArr;
    }
    return [];
  }, [conversationHistoryListHandle.data]);
  const fullInteractionList = [
    ...(interactionFilter !== 'history' ? interactions : []),
    ...(interactionFilter !== 'current' ? interactionHistory : []),
  ];
  const hasInteraction = fullInteractionList && fullInteractionList?.length > 0;
  const allInteractionObject = {
    ...(interactionFilter !== 'history'
      ? {
          current: [...interactions]?.map((i) => ({
            ...i,
          })),
        }
      : {}),
    ...(interactionFilter !== 'current'
      ? {
          history: [...interactionHistory]?.map((i) => ({
            ...i,
            isHistory: true,
          })),
        }
      : {}),
  };
  const { ref, inView } = useInView();
  useEffect(() => {
    if (inView) {
      conversationHistoryListHandle.fetchNextPage();
    }
  }, [inView]);
  // Convert to entries, sort based on custom logic, and then map
  const sortedResults = Object.entries(allInteractionObject);
  const filterOptions: {
    label: string;
    filterValue: TInteractionFilter;
    tooltipContent: string;
  }[] = [
    { label: 'All', filterValue: null, tooltipContent: 'See all Interactions' },
    {
      label: 'Current',
      filterValue: 'current',
      tooltipContent: 'See only current interactions',
    },
    {
      label: 'History',
      filterValue: 'history',
      tooltipContent: 'See only history',
    },
  ];
  const recordFilter = () => {
    return (
      <div className="absolute right-0 bottom-0 flex gap-2 justify-center backdrop-blur-md rounded-tl-lg bg-[rgba(255,255,255,0.7)] p-1 transition-opacity opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto">
        {filterExpanded ? (
          <div className="flex gap-2">
            {filterOptions.map((option, index) => (
              <Tooltip
                key={index}
                trigger={
                  <button
                    className={cn(
                      'flex items-center h-6 p-1 rounded-md hover:bg-primary-100',
                      interactionFilter === option.filterValue &&
                        'bg-primary-200 hover:bg-primary-200'
                    )}
                    onClick={() => updateInteractionFilter(option.filterValue)}
                  >
                    <div className="font-bold">{option.label}</div>
                  </button>
                }
                content={option.tooltipContent}
              />
            ))}
            {showWrapup && (
              <Tooltip
                trigger={
                  <button
                    className={cn(
                      'relative flex items-center h-6 p-1 rounded-md hover:bg-primary-100',
                      interactionFilter === 'wrapup' &&
                        'bg-primary-200 hover:bg-primary-200'
                    )}
                    onClick={() => updateInteractionFilter('wrapup')}
                  >
                    <div className="font-bold">Wrap up</div>
                    <span className="absolute -top-1 -left-1 size-2.5 rounded-full bg-status-danger "></span>
                  </button>
                }
                content={'See Wrap up'}
              />
            )}
            <Tooltip
              trigger={
                <button
                  className={cn(
                    'flex items-center size-6 p-1 rounded-md hover:bg-primary-100'
                  )}
                  onClick={() => setFilterExpanded(false)}
                >
                  <Undo2 className="size-full" />
                </button>
              }
              content={`Back`}
            />
          </div>
        ) : (
          <div className="relative flex gap-2">
            <Tooltip
              side="left"
              trigger={
                <button
                  className={cn(
                    'flex items-center gap-x-[3px] h-6 px-1 hover:px-2 rounded-md hover:bg-primary-100 group/inner'
                  )}
                  onClick={() => setFilterExpanded(true)}
                >
                  <Eye className="size-4" />
                  <span className="hidden font-bold group-hover/inner:inline-flex">
                    {interactionFilter === null && 'All'}
                    {interactionFilter === 'current' && 'Current'}
                    {interactionFilter === 'history' && 'History'}
                  </span>
                </button>
              }
              content={`Click to change`}
            />
            {showWrapup && (
              <span className="absolute -top-1 -left-1 size-2.5 rounded-full bg-status-danger "></span>
            )}
            {selectedWrapupList?.length > 0 && !showWrapupResult && (
              <Tooltip
                side="left"
                trigger={
                  <button
                    onClick={() => {
                      setShowWrapupResult(true);
                    }}
                    className={cn(
                      'flex items-center gap-x-[3px] h-6 px-1 hover:px-2 rounded-md hover:bg-primary-100 group/inner'
                    )}
                  >
                    <Icon
                      name="check"
                      size={14}
                    />
                  </button>
                }
                content={`Show wrap up preview`}
              />
            )}
          </div>
        )}
      </div>
    );
  };

  const body = () => {
    if (!hasInteraction) {
      let wording = 'at the moment or in the history';
      if (interactionFilter === 'current') {
        wording = 'at the moment';
      }
      if (interactionFilter === 'history') {
        wording = 'in the history';
      }

      return (
        <div className="text-footnote p-2 bg-primary-100 rounded">
          {`No interaction(s) ${wording}.`}
        </div>
      );
    }

    return (
      <div className="w-full h-full overflow-y-auto">
        {interactionFilter !== 'wrapup' && (
          <table className="w-full border-separate border-spacing-x-0 overflow-hidden">
            {sortedResults.map(([key, items]) => {
              return (
                <Fragment key={key}>
                  {items?.map((item: IInteractionItemResp, index) => {
                    return item?.isHistory ? (
                      <Fragment key={`${item.conversationId}-${index}`}>
                        <ToolbarHistoryBlock
                          interaction={item}
                          key={`${item.conversationId}-${index}`}
                        />
                        <tbody
                          ref={ref}
                          className="h-1"
                        ></tbody>
                      </Fragment>
                    ) : (
                      <ToolbarParticipantBlock
                        interaction={item}
                        key={`${item.conversationId}-${index}`}
                      />
                    );
                  })}
                </Fragment>
              );
            })}
          </table>
        )}
        {showWrapup && interactionFilter === 'wrapup' && (
          <div
            className={cn(
              'w-full h-full flex border border-primary-300 rounded'
            )}
          >
            <ToolbarWrapup
              showTextarea={showTextarea}
              updateShowTextarea={updateShowTextarea}
              selectedCategory={selectedCategory}
              selectedGroup={selectedGroup}
              selectedWrapupList={selectedWrapupList}
              handleSelectCategory={handleSelectCategory}
              handleCodeItem={handleCodeItem}
              updateSelectedWrapupId={updateSelectedWrapupId}
              handleSelectGroup={handleSelectGroup}
              updateSelectedWrapupList={updateSelectedWrapupList}
              selectedWrapupId={selectedWrapupId}
              updateSelectedCategory={updateSelectedCategory}
              updateSelectedGroup={updateSelectedGroup}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="w-full h-full flex gap-2 justify-between min-w-[600px]">
      <div className={`w-full h-full relative group`}>
        {body()}
        {recordFilter()}
      </div>
    </div>
  );
};

export default ToolbarInteractions;
