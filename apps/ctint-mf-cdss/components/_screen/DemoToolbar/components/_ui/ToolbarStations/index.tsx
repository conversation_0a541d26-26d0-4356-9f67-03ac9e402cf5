/* eslint-disable @nx/enforce-module-boundaries */
import { useMemo } from 'react';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { TStation } from '@cdss-modules/design-system/@types';
import { cn } from '@cdss-modules/design-system/lib/utils';

export const ToolbarStations = () => {
  const {
    closeToolbarModal,
    stationContext: {
      station,
      deleteCurrentStation,
      setStationSearchKeyword,
      stationHandler,
      stationSearchHandler,
      stationSearchKeyword,
      updateCurrentStation,
    },
  } = useTbarContext();

  const selectGroup = (stationId: string) => {
    updateCurrentStation(stationId, station?.id);
    closeToolbarModal();
  };

  const handleInputChange = (e: any) => {
    const value = e;
    setStationSearchKeyword(value);
  };

  const filteredList = useMemo(() => {
    const stations = stationSearchKeyword
      ? stationSearchHandler?.data?.data
      : stationHandler?.data?.data;
    return stations || [];
  }, [
    stationSearchHandler?.data?.data,
    stationHandler?.data?.data,
    stationSearchKeyword,
  ]);

  return (
    <div className="flex flex-col gap-2 size-full">
      <div className="w-full">
        <Input
          type="text"
          size="s"
          beforeIcon={<Icon name="search" />}
          placeholder="Search Stations"
          value={stationSearchKeyword}
          onChange={handleInputChange}
          allowClear
        />
      </div>
      <div className="flex flex-1 h-0 gap-y-2 w-full">
        <div className="flex flex-wrap gap-2 h-full items-start overflow-y-auto">
          <Pill
            variant="person"
            onClick={() => {
              deleteCurrentStation(station?.id || '');
              closeToolbarModal();
            }}
            className={cn(stationSearchKeyword !== '' && '!hidden')}
            disabled={station?.name === undefined}
          >
            <div className="flex gap-1 items-center">
              <div className={cn(`size-[10px] rounded-full bg-grey-300`)} />
              <div className="text-remark">Deselect Phone</div>
            </div>
          </Pill>
          {filteredList?.map((item: TStation) => {
            return (
              <Pill
                variant="person"
                key={item.id}
                onClick={() => {
                  if (item.stationStatus === 'ASSOCIATED') {
                    return;
                  } else {
                    selectGroup(item.id);
                  }
                }}
                active={station?.name === item?.name}
                className={cn(
                  item.stationStatus === 'ASSOCIATED' &&
                    'bg-grey-100 text-grey-300 hover:!bg-grey-100 hover:!text-grey-300 hover:!border-grey-200'
                )}
              >
                <div className="flex flex-col items-start">
                  <div className="flex gap-1 items-center">
                    <div
                      className={cn(
                        `size-[10px] rounded-full bg-status-success`,
                        item.stationStatus === 'ASSOCIATED' &&
                          'bg-status-danger'
                      )}
                    />
                    <div className="text-remark">{item?.name}</div>
                  </div>
                </div>
              </Pill>
            );
          })}
          {filteredList.length === 0 && (
            <div className="text-body">No specific result(s).</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ToolbarStations;
