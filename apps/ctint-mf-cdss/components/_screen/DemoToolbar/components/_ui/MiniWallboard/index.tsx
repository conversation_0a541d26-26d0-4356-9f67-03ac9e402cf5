/* eslint-disable react-hooks/rules-of-hooks */
import { Tooltip, useObserveElementHeight } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { cn, secondsToFormat } from '@cdss-modules/design-system/lib/utils';
import { useEffect, useMemo, useState } from 'react';
import { Eye, Undo2 } from 'lucide-react';
import { TMiniWallboardState } from '@cdss-modules/design-system/@types';
import dayjs from 'dayjs';
const MiniWallboard = () => {
  const {
    userMiniWallboardList,
    queueMiniWallboardList,
    statusContext: { isOnQueue },
  } = useTbarContext();
  const [miniWallboardView, setMiniWallboardView] =
    useState<TMiniWallboardState>('workgroup');

  const [pinned, setPinned] = useState<string[]>([]);
  const [headPinned, setHeadPinned] = useState(false);
  const [switcherExpanded, setSwitcherExpanded] = useState(false);

  const { ref, height } = useObserveElementHeight<HTMLDivElement>();

  const handlePin = (id: string) => {
    if (pinned.includes(id)) {
      setPinned(pinned.filter((item) => item !== id));
    } else {
      setPinned([...pinned, id]);
    }
  };
  const [onQueueSum, setOnQueueSum] = useState(
    userMiniWallboardList?.[0]?.presence?.onQueue || 0
  );
  const [offQueueSum, setOffQueueSum] = useState(
    userMiniWallboardList?.[0]?.presence?.offQueue || 0
  );
  useEffect(() => {
    setOnQueueSum(userMiniWallboardList?.[0]?.presence?.onQueue);
    setOffQueueSum(userMiniWallboardList?.[0]?.presence?.offQueue);
    const interval = setInterval(() => {
      if (isOnQueue) {
        setOnQueueSum((prevTime) => prevTime + 1000);
      } else {
        setOffQueueSum((prevTime) => prevTime + 1000);
      }
    }, 1000);
    return () => clearInterval(interval);
  }, [isOnQueue, userMiniWallboardList]);

  const sortedQueueMiniWallboardList = useMemo(() => {
    return [...queueMiniWallboardList].sort((a, b): number => {
      const isAPinned = pinned.some((item) => item === a.queueId);
      const isBPinned = pinned.find((item) => item === b.queueId);
      if (isAPinned && !isBPinned) {
        return -1;
      } else if (!isAPinned && isBPinned) {
        return 1;
      } else {
        return 0;
      }
    });
  }, [pinned, queueMiniWallboardList]);
  // const onQueueSum = useKeepCountingTime(
  //   userMiniWallboardList?.[0]?.presence?.onQueue || 0,
  //   isOnQueue
  // );

  // const offQueueSum = useKeepCountingTime(
  //   userMiniWallboardList?.[0]?.presence?.offQueue || 0,
  //   !isOnQueue
  // );
  const stateSwitcher = () => {
    return (
      <div className="absolute right-0 bottom-0 flex gap-2 justify-center backdrop-blur-md rounded-tl-lg bg-[rgba(255,255,255,0.7)] p-1 transition-opacity opacity-0 pointer-events-none group-hover:opacity-100 group-hover:pointer-events-auto">
        {switcherExpanded ? (
          <div className="flex gap-2">
            <Tooltip
              trigger={
                <button
                  className={cn(
                    'flex items-center h-6 p-1 rounded-md hover:bg-primary-100',
                    miniWallboardView === 'workgroup' &&
                      'bg-primary-200 hover:bg-primary-200'
                  )}
                  onClick={() => setMiniWallboardView('workgroup')}
                >
                  <div className="font-bold">Workgroup</div>
                </button>
              }
              content={'Workgroup Stat'}
            />
            <Tooltip
              side="left"
              trigger={
                <button
                  className={cn(
                    'flex items-center h-6 p-1 rounded-md hover:bg-primary-100',
                    miniWallboardView === 'agent' &&
                      'bg-primary-200 hover:bg-primary-200'
                  )}
                  onClick={() => setMiniWallboardView('agent')}
                >
                  <div className="font-bold">Agent</div>
                </button>
              }
              content={'Agent Stat'}
            />
            <Tooltip
              trigger={
                <button
                  className={cn(
                    'flex items-center size-6 p-1 rounded-md hover:bg-primary-100'
                  )}
                  onClick={() => setSwitcherExpanded(false)}
                >
                  <Undo2 className="size-full" />
                </button>
              }
              content={`Back`}
            />
          </div>
        ) : (
          <div>
            <Tooltip
              side="left"
              trigger={
                <button
                  className={cn(
                    'flex items-center gap-x-[3px] h-6 px-1 hover:px-2 rounded-md hover:bg-primary-100 group/inner'
                  )}
                  onClick={() => setSwitcherExpanded(true)}
                >
                  <Eye className="size-4" />
                  <span className="hidden font-bold group-hover/inner:inline-flex">
                    {miniWallboardView === 'workgroup' && 'Workgroup'}
                    {miniWallboardView === 'agent' && 'Agent'}
                  </span>
                </button>
              }
              content={`Click to change`}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="relative w-full h-full rounded-br-lg bg-white overflow-hidden group">
      {miniWallboardView === 'workgroup' ? (
        <div className="relative w-full h-full overflow-auto">
          <table className={cn('w-full bg-white')}>
            <thead
              ref={ref as any}
              className="group h-[30px]"
            >
              <tr
                className={cn(
                  'py-1 text-footnote group-hover:bg-primary-200',
                  headPinned && 'bg-primary-200'
                )}
              >
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition rounded-l',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  Queue
                </th>
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  OnQ
                </th>
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  OffQ
                </th>
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  WT
                </th>
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  Aban(%)
                </th>
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  SVL(%)
                </th>
                <th
                  className={cn(
                    'z-20 italic pl-2 text-left sticky top-0 transition rounded-r',
                    !headPinned && ' opacity-0 group-hover:opacity-100'
                  )}
                >
                  <button
                    className={cn(
                      'size-full flex items-center px-1',
                      headPinned
                        ? 'text-tertiary-400'
                        : 'text-grey-400 hover:text-tertiary-400'
                    )}
                    onClick={() => setHeadPinned(!headPinned)}
                  >
                    <Icon
                      name="pin"
                      className={cn(
                        'transition',
                        headPinned && 'rotate-[35deg]'
                      )}
                    />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody
              className="relative"
              style={{ top: headPinned ? 0 : -height }}
            >
              {sortedQueueMiniWallboardList?.map((item, i: number) => (
                <tr
                  className={cn(
                    'w-full',
                    i === 0 && !headPinned && 'group-hover:opacity-0'
                  )}
                  key={`mini-wallboard-${item.queueId}`}
                >
                  <td className="pl-2 py-1">{item?.name ?? '-'}</td>
                  <td className="pl-2 py-1">
                    {item?.queue?.oOnQueueUsers ?? '-'}
                  </td>
                  <td className="pl-2 py-1">
                    {item?.queue?.oOffQueueUsers ?? '-'}
                  </td>
                  <td className="pl-2 py-1">{item?.call?.nWait ?? 0}</td>
                  <td className="pl-2 py-1">
                    {item?.call?.abandonRate ?? '-'}
                  </td>
                  <td className="pl-2 py-1">
                    {item?.call?.oServiceLevel ?? '-'}
                  </td>
                  <td className="pl-2 py-1">
                    <button
                      className={cn(
                        'flex items-center',
                        pinned.includes(item?.queueId)
                          ? 'text-tertiary-400'
                          : 'text-grey-400 hover:text-tertiary-400'
                      )}
                      onClick={() => handlePin(item?.queueId)}
                    >
                      <Icon
                        name="pin"
                        className={cn(
                          'transition',
                          pinned.includes(item?.queueId) && 'rotate-[35deg]'
                        )}
                      />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="relative w-full">
          <div className="w-full grid grid-rows-5 grid-flow-col gap-x-4 gap-y-1 overflow-y-auto justify-items-start place-items-center">
            <table className={cn('w-full bg-white')}>
              <thead className="bg-primary-200 h-[30px] rounded">
                <tr>
                  <th className="text-left pl-2 rounded-l">OnQ</th>
                  <th className="text-left">OffQ</th>
                  <th className="text-left">TTT</th>
                  <th className="text-left pl-2">Alert</th>
                  <th className="text-left rounded-r">Ans</th>
                </tr>
              </thead>
              <tbody>
                {userMiniWallboardList?.map((item, i: number) => {
                  return (
                    <tr>
                      <td className="pl-2 py-2">
                        {dayjs(onQueueSum).utc().format('HH:mm:ss')}
                      </td>
                      <td className="py-2">
                        {dayjs(offQueueSum).utc().format('HH:mm:ss')}
                      </td>
                      <td className="py-2">
                        {secondsToFormat(
                          (item?.activity?.totalTalkTime as number) / 1000 || 0,
                          'HH:mm:ss'
                        )}
                      </td>
                      <td className="pl-2 pt-1">
                        {item?.activity?.tAlert || 0}
                      </td>
                      <td className="pt-1">
                        {item?.activity?.inboundCalls +
                          item?.activity?.outboundCalls || 0}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {stateSwitcher()}
    </div>
  );
};

export default MiniWallboard;
