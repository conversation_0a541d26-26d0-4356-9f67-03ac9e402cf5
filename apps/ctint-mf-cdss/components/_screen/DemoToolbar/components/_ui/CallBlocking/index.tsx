import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>up, PopupContent } from '@cdss-modules/design-system';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';
import Field from '@cdss-modules/design-system/components/_ui/Field';
import {
  CallBlockingResponse,
  DisplayRuleItem,
} from '@cdss-modules/design-system/@types';
import { geVerifyUser } from '@cdss/lib/api';
type BlockingProps = {
  blockingData: CallBlockingResponse | null;
};
const CallBlocking = ({ blockingData }: BlockingProps) => {
  const [isOpen, setIsOpen] = useState(blockingData?.isBlocking);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [contractRules, setContractRules] = useState<DisplayRuleItem[]>();

  useEffect(() => {
    const hitCallBlockingRuleIDList =
      blockingData?.blockingRules?.hitCallBlockingRuleIDList?.split(',');
    const newContractRules =
      blockingData?.blockingRules?.displayRuleList?.map((v) => {
        hitCallBlockingRuleIDList?.map((vs) => {
          if (v.ruleId == vs) {
            v.checked = true;
          }
        });
        return v;
      }) || [];
    setContractRules(newContractRules);
  }, []);
  const verifyUser = async () => {
    const passwrodBase64Encoded = btoa(password);
    await geVerifyUser(username, passwrodBase64Encoded);
  };
  return (
    <div>
      <Popup
        open={isOpen}
        onOpenChange={setIsOpen}
      >
        <PopupContent
          title="Call Blocking and Contact Rules Summary"
          className="h-[80vh]"
        >
          <div className="p-4  overflow-y-auto">
            <Field title="You're dialing to:">
              <Field
                title="Number:"
                direction="horizontal"
              >
                123
              </Field>
              <Field
                title="PPS:"
                direction="horizontal"
              >
                123
              </Field>
              <Field
                title="is hit PPS on call block list:"
                direction="horizontal"
              >
                123
              </Field>
            </Field>
            <Field
              title="Out Call log in current month:"
              className="py-2"
            >
              <Field
                title="=0 seconds:"
                direction="horizontal"
              >
                {blockingData?.blockingRules?.callSummary?.equalToZero}
              </Field>
              <Field
                title=">15 seconds:"
                direction="horizontal"
              >
                {blockingData?.blockingRules?.callSummary?.greaterThan15Sec}
              </Field>
              <Field
                title="<15 seconds:"
                direction="horizontal"
              >
                {blockingData?.blockingRules?.callSummary?.lessThan15Sec}
              </Field>
            </Field>
            <Field
              title="You're hitting below contract rules or call block:"
              className="py-2"
            >
              {contractRules?.map((v: DisplayRuleItem, i) => (
                <Checkbox
                  disabled
                  key={i}
                  label={v?.userDisplay}
                  checked={v?.checked}
                />
              ))}
            </Field>
            <Field title="Supervsor Release Call Block:">
              <Field
                title="Approver Login ID:"
                className="py-2"
              >
                <Input
                  className="w-full"
                  value={username}
                  type="text"
                  size="s"
                  placeholder={'Username'}
                  onChange={(e: any) => {
                    setUsername(e);
                  }}
                />
              </Field>
              <Field
                title="Approver Password:"
                className="py-2"
              >
                <Input
                  className="w-full"
                  value={password}
                  type="text"
                  size="s"
                  placeholder={'Password'}
                  onChange={(e: any) => {
                    setPassword(e);
                  }}
                />
              </Field>
            </Field>
            <Button
              className="py-4"
              onClick={verifyUser}
            >
              OK
            </Button>
          </div>
        </PopupContent>
      </Popup>
    </div>
  );
};

export default CallBlocking;
