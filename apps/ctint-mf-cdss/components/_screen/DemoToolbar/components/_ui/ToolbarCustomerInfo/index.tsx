/* eslint-disable @nx/enforce-module-boundaries */
import {
  Tabs,
  TabsContent,
} from '@cdss-modules/design-system/components/_ui/Tabs';

import * as yup from 'yup';
import ToolbarForm from '../ToolbarForm';
import { useForm } from 'react-hook-form';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import ToolbarWrapup from '../ToolbarWrapup';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useEffect, useState } from 'react';
import { useTabsContext } from '@cdss-modules/design-system';
import { useAttributes } from '@cdss-modules/design-system/lib/hooks/useAttributes';
import {
  TAttributesData,
  TSubmitWrapup,
} from '@cdss-modules/design-system/@types';
const triggers = [
  {
    value: 'customerInformation',
    label: 'Customer Information',
  },
  {
    value: 'wrapup',
    label: 'Wrap up',
  },
];

export const ToolbarCustomerInfo = () => {
  const { submitAttributes, getTenantAttributesHandle } = useAttributes();
  const {
    wrapupContext: {
      selectedWrapupList,
      updateSelectedWrapupList,
      updateSelectedWrapupId,
      selectedWrapupId,
    },
    conversationHistoryListHandle,
    activeParticipantData,
    activeModal,
    closeToolbarModal,
  } = useTbarContext();
  const { onChangeTab } = useTabsContext();

  const { setValue } = useForm();

  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);
  const [showTextarea, setShowTextarea] = useState<{
    type: 'CATEGORY' | 'GROUP' | 'CODE' | null;
    isOpen: boolean;
  }>({
    type: null,
    isOpen: false,
  });
  const customerData =
    activeParticipantData?.conversationDetail?.attributes &&
    JSON.stringify(activeParticipantData?.conversationDetail?.attributes) !==
      '{}'
      ? activeParticipantData?.conversationDetail?.attributes
      : null;

  const updateSelectedCategory = (value: string | null) => {
    setSelectedCategory(value);
  };

  const updateSelectedGroup = (value: string | null) => {
    setSelectedGroup(value);
  };

  const updateShowTextarea = (data: any) => {
    setShowTextarea(data);
  };

  const handleSelectCategory = (cateCode: string) => {
    setSelectedGroup('');
    if (selectedCategory === cateCode) {
      setSelectedCategory('');
    } else {
      setShowTextarea({
        type: null,
        isOpen: false,
      });
      setSelectedCategory(cateCode);
    }
  };

  const handleCodeItem = (codeItem: any) => {
    if (
      selectedWrapupList.find(
        (wrapup: TSubmitWrapup) => wrapup.wrapUpCode === codeItem.code
      )
    ) {
      updateSelectedWrapupList(
        selectedWrapupList.filter(
          (wrap: TSubmitWrapup) => wrap.wrapUpCode !== codeItem.code
        )
      );
    } else {
      updateSelectedWrapupList((prev: any) => [
        ...prev,
        {
          wrapUpCode: codeItem.code,
          wrapUpName: codeItem.name,
        },
      ]);
    }
  };

  const handleSelectGroup = (
    groupCode: string,
    groupId?: number,
    groupParentId?: number,
    groupName?: string
  ) => {
    setSelectedGroup(groupCode);
    updateSelectedWrapupList((prev: any) => {
      let result = [...prev];
      const matchedIndex = result?.findIndex(
        (item) => item.id === groupParentId
      );

      if (matchedIndex > -1) {
        result[matchedIndex] = {
          ...result[matchedIndex],
          groupId,
          groupName,
        };
      } else {
        result = [...result];
      }
      return result;
    });
  };

  const customerInfoYupObj = getTenantAttributesHandle.data?.reduce(
    (acc: Record<string, any>, cur: TAttributesData) => {
      if (cur?.isRequired) {
        acc[cur.key] = yup.string().required(`${[cur.name]} is required`);
      }
      return acc;
    },
    {}
  );

  const customerInfoSchema = yup.object(customerInfoYupObj).required();

  const handleCustomerInfoInputChange = (key: string, value: any) => {
    setValue(key, value);
  };

  const handleSubmit = async (data: any) => {
    let result = {};
    for (const [key, value] of Object.entries(data)) {
      const isPII = getTenantAttributesHandle.data?.find(
        (item: TAttributesData) => item.key === key
      )?.isPII;

      const isEncrypted = getTenantAttributesHandle.data?.find(
        (item: TAttributesData) => item.key === key
      )?.isEncrypted;

      result = {
        ...result,
        [key]: {
          value: value,
          isPII,
          isEncrypted,
        },
      };
    }
    submitAttributes(
      activeParticipantData.conversationId,
      activeParticipantData?.participantId,
      result
    ).then(() => {
      conversationHistoryListHandle.refetch();
    });

    closeToolbarModal();
  };
  useEffect(() => {
    if (activeModal === 'customer-info-panel')
      onChangeTab(['customerInformation']);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeModal]);

  return (
    <div className="flex size-full">
      <Tabs
        defaultTab="customerInformation"
        triggers={triggers}
        triggerClassName="p-0 h-full"
      >
        <TabsContent
          value={'customerInformation'}
          className="overflow-y-auto"
        >
          <ToolbarForm
            schema={customerInfoSchema}
            values={customerData ? JSON.parse(customerData) : {}}
            formItems={getTenantAttributesHandle.data}
            onSubmit={handleSubmit}
            handleOnChange={handleCustomerInfoInputChange}
            haveSubmitButton
          />
        </TabsContent>
        <TabsContent
          value={'wrapup'}
          className={cn('w-full h-full overflow-y-auto')}
        >
          <div className="flex flex-col w-full h-full gap-2">
            <div
              className={cn(
                'w-full h-full min-h-[114px] flex flex-col border border-primary-300 rounded'
              )}
            >
              <ToolbarWrapup
                showTextarea={showTextarea}
                updateShowTextarea={updateShowTextarea}
                selectedCategory={selectedCategory}
                selectedGroup={selectedGroup}
                selectedWrapupList={selectedWrapupList}
                handleSelectCategory={handleSelectCategory}
                handleCodeItem={handleCodeItem}
                updateSelectedWrapupId={updateSelectedWrapupId}
                handleSelectGroup={handleSelectGroup}
                updateSelectedWrapupList={updateSelectedWrapupList}
                selectedWrapupId={selectedWrapupId}
                updateSelectedCategory={updateSelectedCategory}
                updateSelectedGroup={updateSelectedGroup}
              />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ToolbarCustomerInfo;
