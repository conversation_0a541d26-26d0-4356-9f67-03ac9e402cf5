/* eslint-disable @nx/enforce-module-boundaries */
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import Switch from '@cdss-modules/design-system/components/_ui/Switch';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import {
  Button,
  toast,
  Tooltip,
  useRole,
  useToast,
} from '@cdss-modules/design-system';
import { RotateCcw } from 'lucide-react';
export const ToolbarHead = () => {
  const {
    openToolbarModal,
    refreshing,
    refreshInteractions,
    statusContext: { isOnQueue, agentStatus, onChangeIsOnQueue, agentColor },
    stationContext: {
      station,
      stationHandler,
      stationSearchHandler,
      stationSearchKeyword,
    },
    primaryContactInfo,
  } = useTbarContext();

  const { userConfig } = useRole();
  const { dismiss } = useToast();
  const handleToastClose = () => {
    dismiss();
  };

  const currentUserDirectNumber = primaryContactInfo?.find(
    (info) => info.mediaType === 'PHONE'
  )?.address;
  const selectStationTip = () => {
    return toast({
      variant: 'error',
      title: 'Warning',
      description: (
        <div className="flex flex-col w-full gap-2">
          <div>You have no phone selected and will not receive calls.</div>
          <div className="flex w-full gap-2">
            <Button
              variant={'primary'}
              size={'s'}
              onClick={() => {
                handleToastClose();
                const refetchPromise = stationSearchKeyword
                  ? stationSearchHandler.refetch()
                  : stationHandler.refetch();
                refetchPromise.finally(() => {
                  openToolbarModal('stations-panel');
                });
              }}
            >
              Select Phone
            </Button>
            <Button
              variant={'blank'}
              size={'s'}
              onClick={() => {
                onChangeIsOnQueue();
                handleToastClose();
              }}
            >
              Continue without phone
            </Button>
          </div>
        </div>
      ),
    });
  };
  const handleStationChange = () => {
    if (isOnQueue) {
      onChangeIsOnQueue();
    } else {
      if (!station?.name) {
        selectStationTip();
      } else {
        onChangeIsOnQueue();
      }
    }
  };
  const handleStatusClick = () => {
    openToolbarModal('agent-status-panel');

    // if (!station?.name) {
    //   selectStationTip();
    // } else {
    //   openToolbarModal('agent-status-panel');
    // }
  };
  return (
    <div className="flex items-center gap-x-2 justify-between px-2 py-1 bg-white rounded-t-lg z-10">
      <div className="flex items-center gap-x-2 whitespace-nowrap overflow-auto">
        <button
          onClick={() => openToolbarModal('agent-info-panel')}
          className="flex items-center gap-x-1 group"
        >
          <Icon
            name="arrow-left"
            className="-rotate-90 hidden group-hover:block"
          />
          <Icon
            name="user"
            className="text-primary-500 group-hover:hidden"
          />
          <div>{userConfig?.userName}</div>
        </button>
        <div className="w-px h-5 bg-grey-200" />
        <div className="flex items-center gap-x-1 group">
          <div>{`Direct Line - ${currentUserDirectNumber}`}</div>
        </div>

        <div className="w-px h-5 bg-grey-200" />

        <button
          id="stations-panel"
          onClick={() => {
            const refetchPromise = stationSearchKeyword
              ? stationSearchHandler.refetch()
              : stationHandler.refetch();
            refetchPromise.finally(() => {
              openToolbarModal('stations-panel');
            });
          }}
          className="flex items-center gap-x-1 group"
        >
          <Icon
            name="arrow-left"
            className="-rotate-90 hidden group-hover:block"
          />
          <Icon
            name="outbound"
            className="text-primary-500 group-hover:hidden"
          />
          <div>{station?.name || 'No station'}</div>
        </button>
      </div>
      <div className="flex gap-x-6">
        <button
          onClick={handleStatusClick}
          className="flex items-center gap-1 cursor-pointer"
        >
          <div
            style={{
              backgroundColor: agentColor,
            }}
            className={`inline-flex size-2 rounded-full`}
          />
          {agentStatus}
        </button>
        <div className="flex items-center gap-2">
          <label
            className={`${isOnQueue ? 'text-status-success' : 'text-grey-500'}`}
          >
            {isOnQueue ? 'On Queue' : 'Off Queue'}
          </label>
          <Switch
            id="queue"
            size="s"
            activeColor="green"
            checked={isOnQueue}
            onChange={handleStationChange}
          />
        </div>
        <div className="-ml-2 inline-flex items-center h-8">
          <Tooltip
            trigger={
              <button
                className="hover:text-primary disabled:opacity-60"
                onClick={() => refreshInteractions()}
                disabled={refreshing}
              >
                <RotateCcw size={20} />
              </button>
            }
            side="bottom"
            content={'Refresh'}
          />
        </div>
        <div className="-ml-4 inline-flex items-center h-8">
          <button
            className="hover:text-primary"
            onClick={() => openToolbarModal('call-control-menu')}
          >
            <Icon
              name="verticalDots"
              size={23}
            />
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToolbarHead;
