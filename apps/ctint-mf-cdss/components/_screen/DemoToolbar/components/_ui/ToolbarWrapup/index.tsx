import Checkbox from '@cdss-modules/design-system/components/_ui/Checkbox';

import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { Tooltip } from '@cdss-modules/design-system/components/_ui/Tooltip';
import { toast } from '@cdss-modules/design-system';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import Button from '@cdss-modules/design-system/components/_ui/Button';
import { TSubmitWrapup, TWrapupItem } from '@cdss-modules/design-system/@types';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useEffect, useMemo, useState } from 'react';
import ToolbarWrapupPreview from '../ToolbarWrapupPreview';

type TShowTextarea = {
  type: string | null;
  isOpen: boolean;
};

type TToolbarWrapupProps = {
  showTextarea: TShowTextarea;
  updateShowTextarea: (obj: TShowTextarea) => void;
  selectedCategory: string | null;
  selectedGroup: string | null;
  selectedWrapupList: any[];
  handleSelectCategory: (cateCode: string) => void;
  handleCodeItem: (codeItem: any) => void;
  updateSelectedWrapupId: (id: string | null) => void;
  handleSelectGroup: (
    groupCode: string,
    groupId?: number,
    groupParentId?: number,
    groupName?: string
  ) => void;
  updateSelectedWrapupList: (data: any) => void;
  updateSelectedCategory: (value: string | null) => void;
  updateSelectedGroup: (value: string | null) => void;
  selectedWrapupId: string | null;
};

const ToolbarWrapup = ({
  showTextarea,
  updateShowTextarea,
  selectedCategory,
  selectedGroup,
  selectedWrapupList,
  handleSelectCategory,
  handleCodeItem,
  updateSelectedWrapupId,
  handleSelectGroup,
  updateSelectedWrapupList,
  selectedWrapupId,
  updateSelectedCategory,
  updateSelectedGroup,
}: TToolbarWrapupProps) => {
  const [showWrapupResult, setShowWrapupResult] = useState<boolean>(false);
  const [selectedAllWrapupList, setSelectedAllWrapupList] = useState<
    TSubmitWrapup[]
  >([]);
  const {
    selectedInteraction,
    wrapupContext: { wrapupCategoryFullListHandle },
  } = useTbarContext();
  const { agentData } = useHandleInteractionData(selectedInteraction);
  const onChangeWrapupPreview = () => {
    setShowWrapupResult(!showWrapupResult);
  };
  const findAllLevelWrapup = (
    data: TWrapupItem[],
    selectedValue: TSubmitWrapup
  ): TSubmitWrapup | null => {
    for (const category of data) {
      if (
        !category.items ||
        (category?.items?.length === 0 &&
          selectedValue.wrapUpCode === category.code)
      ) {
        return {
          categoryName: category.name,
          categoryCode: category.code,
          wrapUpName: selectedValue?.wrapUpName,
          wrapUpCode: selectedValue?.wrapUpCode,
          remark: selectedValue?.remark,
        };
      }

      if (category?.type === 'CATEGORY' && category.items?.length > 0) {
        for (const group of category.items) {
          // this is for group has no options
          if (!group.items || group.items?.length === 0) {
            return {
              categoryName: category.name,
              categoryCode: category.code,
              groupName: group.name,
              groupCode: group.code,
              wrapUpName: selectedValue?.wrapUpName,
              wrapUpCode: selectedValue?.wrapUpCode,
              remark: selectedValue?.remark,
            };
          }

          if (group.type === 'GROUP' && group.items?.length > 0) {
            for (const code of group.items) {
              if (code.code === selectedValue?.wrapUpCode) {
                return {
                  categoryName: category.name,
                  categoryCode: category.code,
                  groupName: group.name,
                  groupCode: group.code,
                  codeName: code.name,
                  wrapUpName: selectedValue?.wrapUpName,
                  wrapUpCode: selectedValue?.wrapUpCode,
                  remark: selectedValue?.remark,
                };
              }
            }
          }
        }
      }
    }
    return null; // Return null if not found
  };
  const wrapupCategory = useMemo(() => {
    if (agentData?.queue?.id) {
      return (
        wrapupCategoryFullListHandle?.filter((item: any) => {
          return item?.queueId === agentData?.queue?.id;
        })?.[0]?.items || []
      );
    }
    return wrapupCategoryFullListHandle?.flatMap(
      (queueItem: any) => queueItem?.items || []
    );
  }, [wrapupCategoryFullListHandle, agentData]);
  useEffect(() => {
    if (agentData?.wrapUps?.wrapUpList?.length > 0) {
      updateSelectedWrapupList(agentData?.wrapUps?.wrapUpList);
    }
  }, [agentData?.wrapUps?.wrapUpList]);

  const defaultWrapups = agentData?.wrapUps?.wrapUpList
    ?.map((wrapup: TSubmitWrapup) =>
      findAllLevelWrapup(wrapupCategory || [], wrapup)
    )
    .filter((item: any) => item !== null);

  const wrapupItem = selectedWrapupList?.find(
    (wrapup: any) => wrapup?.wrapUpCode === selectedWrapupId
  );

  const handleInputChange = (value: string) => {
    if (
      selectedWrapupList?.every(
        (wrapup: any) => wrapup?.wrapUpCode !== selectedWrapupId
      )
    ) {
      toast({
        variant: 'error',
        title: 'Error',
        description: 'Please select wrap up item',
      });
    } else {
      updateSelectedWrapupList((prev: any) =>
        prev.map((wrapup: any) =>
          wrapup.wrapUpCode === selectedWrapupId
            ? { ...wrapup, remark: value }
            : wrapup
        )
      );
    }
  };

  return !showWrapupResult ? (
    <div className={cn('w-full h-full flex flex-col justify-between')}>
      <div className={cn('flex  h-full')}>
        <Tooltip
          side="left"
          trigger={
            <button
              onClick={(e) => {
                e.stopPropagation();
                updateShowTextarea({
                  type: null,
                  isOpen: false,
                });
              }}
              className={cn(
                'h-full bg-primary-300 hidden p-1',
                showTextarea.type === 'GROUP' && 'flex items-center',
                showTextarea.type === 'CODE' && 'flex min-h-full items-center'
              )}
            >
              <Icon
                name="dropdown-arrow"
                size={6}
                className="rotate-90"
              />
            </button>
          }
          content={`Close the remark`}
        />
        <div
          id="level-category"
          className={cn(
            'flex flex-col flex-1 gap-1 p-1 overflow-auto',
            showTextarea.type === 'CODE' && 'hidden'
          )}
        >
          {wrapupCategory?.map((category: TWrapupItem) => {
            const selectedCategoryCode = selectedWrapupList?.find(
              (wrapup: any) => wrapup?.wrapUpCode === category?.code
            )?.wrapUpCode;

            return (
              <div
                key={category.id}
                className={cn(
                  'flex justify-between items-center px-2 py-1 rounded hover:bg-primary-100 group',
                  selectedCategory === category.code && 'bg-primary-100',
                  selectedWrapupList?.find(
                    (wrapup: TSubmitWrapup) =>
                      wrapup.wrapUpCode === category.code
                  ) && 'bg-primary-300'
                )}
              >
                <Checkbox
                  id={category.id.toString()}
                  label={category.name}
                  value={category.code}
                  className={cn(
                    selectedCategory === category.code && 'font-bold',
                    selectedCategoryCode === category.code && 'font-bold'
                  )}
                  checked={
                    selectedCategory === category.code ||
                    selectedWrapupList?.find(
                      (wrapup: TSubmitWrapup) =>
                        wrapup.wrapUpCode === category.code
                    ) ||
                    selectedWrapupList?.find(
                      (wrapup: TSubmitWrapup) =>
                        wrapup.wrapUpCode ===
                        category?.items
                          ?.find(
                            (item: TWrapupItem) =>
                              item?.parentId === category.id
                          )
                          ?.items?.find(
                            (code: TWrapupItem) =>
                              code.code === wrapup.wrapUpCode
                          )?.code
                    ) ||
                    defaultWrapups?.find(
                      (wrapup: any) => wrapup.categoryCode === category.code
                    )
                  }
                  onChange={(e) => {
                    if (category.items && category.items?.length > 0) {
                      handleSelectCategory(e.target.value);
                    } else {
                      handleCodeItem(category);
                    }
                  }}
                />
                {category.items && category?.items.length > 0 && (
                  <Icon
                    name="dropdown-arrow"
                    size={6}
                    className="-rotate-90"
                  />
                )}
                {category?.items?.length === 0 && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      updateShowTextarea({
                        type: category.type,
                        isOpen: !showTextarea.isOpen,
                      });
                      updateSelectedWrapupId(category.code);
                    }}
                    className={cn(
                      `size-5 flex-none items-center justify-center hidden group-hover:flex`
                    )}
                  >
                    <Icon
                      name="remark"
                      size={16}
                      className="text-black"
                    />
                  </button>
                )}
              </div>
            );
          })}
        </div>
        <div
          id="level-group"
          className={cn(
            'flex flex-col flex-1 gap-1 p-1 border-l border-primary-300 overflow-auto',
            showTextarea.type === 'CATEGORY' && 'hidden',
            showTextarea.type === 'CODE' && 'hidden'
          )}
        >
          {wrapupCategory
            ?.find((cate: TWrapupItem) => cate.code === selectedCategory)
            ?.items?.map((group: TWrapupItem) => {
              const selectedGroupCode = selectedWrapupList?.find(
                (wrapup: any) => wrapup?.groupCode === group?.code
              )?.groupCode;

              return (
                <div
                  key={group.id}
                  className={cn(
                    'flex justify-between items-center px-2 py-1 rounded hover:bg-primary-100',
                    selectedGroup === group.code && 'bg-primary-100'
                  )}
                >
                  <Checkbox
                    id={group.id.toString()}
                    label={group.name}
                    value={group.code}
                    className={cn(
                      selectedGroup === group.code && 'font-bold',
                      selectedGroupCode === group.code && 'font-bold'
                    )}
                    checked={
                      selectedGroup === group.code ||
                      selectedWrapupList?.find(
                        (wrapup: TSubmitWrapup) =>
                          wrapup.wrapUpCode ===
                          group.items?.find(
                            (item) => item.name === wrapup.wrapUpName
                          )?.code
                      )
                    }
                    onChange={(e) => {
                      handleSelectGroup(
                        e.target.value,
                        group.id,
                        group.parentId,
                        group.name
                      );
                    }}
                  />
                  {group.items && group?.items.length > 0 && (
                    <Icon
                      name="dropdown-arrow"
                      size={6}
                      className="-rotate-90"
                    />
                  )}
                  {group?.items?.length === 0 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        updateShowTextarea({
                          type: group.type,
                          isOpen: !showTextarea.isOpen,
                        });

                        updateSelectedWrapupId(group.code);
                      }}
                      className={cn(
                        `size-5 flex-none items-center justify-center hidden group-hover:flex`
                      )}
                    >
                      <Icon
                        name="remark"
                        size={16}
                        className="text-black"
                      />
                    </button>
                  )}
                </div>
              );
            })}
        </div>
        <div
          id="level-code"
          className={cn(
            'p-1 flex flex-col flex-1 h-full gap-1 border-l border-primary-300 overflow-auto',
            showTextarea.type === 'CATEGORY' && 'hidden'
          )}
        >
          {wrapupCategory
            ?.find((cate: TWrapupItem) => cate.code === selectedCategory)
            ?.items?.find((group: TWrapupItem) => group.code === selectedGroup)
            ?.items?.map((code: TWrapupItem) => {
              const selectedCode = selectedWrapupList?.find(
                (wrapup: any) => wrapup?.wrapUpCode === code?.code
              )?.wrapUpCode;

              return (
                <div
                  key={code.id}
                  className={cn(
                    'flex justify-between items-center px-2 py-1 rounded hover:bg-primary-100 group',
                    selectedWrapupList?.find(
                      (wrapup: TSubmitWrapup) => wrapup.wrapUpCode === code.code
                    ) && 'bg-primary-300'
                  )}
                >
                  <Checkbox
                    id={code.id.toString()}
                    label={code.name}
                    value={code.code}
                    className={cn(
                      '',
                      selectedWrapupList?.find(
                        (wrapup: TSubmitWrapup) =>
                          wrapup.wrapUpCode === code.code
                      ) && 'font-bold',
                      selectedCode === code.code && 'font-bold'
                    )}
                    checked={
                      selectedWrapupList?.find(
                        (wrapup: TSubmitWrapup) =>
                          wrapup.wrapUpCode === code.code
                      ) ||
                      selectedWrapupList?.find(
                        (wrapup: TSubmitWrapup) =>
                          wrapup.wrapUpCode === code.code
                      )
                    }
                    onChange={(e) => {
                      if (
                        selectedWrapupList?.find(
                          (wrapup: TSubmitWrapup) =>
                            wrapup.wrapUpCode === e.target.value
                        )
                      ) {
                        updateSelectedWrapupList(
                          selectedWrapupList?.filter(
                            (wrap: TSubmitWrapup) =>
                              wrap.wrapUpCode !== code.code
                          )
                        );
                      } else {
                        updateSelectedWrapupList((prev: any) => [
                          ...prev,
                          {
                            wrapUpCode: code.code,
                            wrapUpName: code.name,
                          },
                        ]);
                      }
                    }}
                  />
                  {
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        updateShowTextarea({
                          type: code.type,
                          isOpen: true,
                        });

                        updateSelectedWrapupId(code.code);
                      }}
                      className={cn(
                        `size-5 flex-none items-center justify-center hidden group-hover:flex`
                      )}
                    >
                      <Icon
                        name="remark"
                        size={16}
                        className="text-black"
                      />
                    </button>
                  }
                </div>
              );
            })}
        </div>
        {selectedWrapupId !== '' && (
          <div
            className={cn(
              'hidden w-2/3 border-l border-primary-300',
              showTextarea.isOpen && 'flex flex-col'
            )}
          >
            <textarea
              className="w-full h-full p-2 rounded outline-0"
              value={wrapupItem?.remark || ''}
              onChange={(e: any) => handleInputChange(e.target.value)}
              placeholder="Type your notes here (no personal data)..."
            />
          </div>
        )}
      </div>
      <div className={cn('text-right')}>
        <Button
          onClick={onChangeWrapupPreview}
          variant={'primary'}
          size={'mini'}
        >
          <Icon
            name="check"
            size={12}
            className="text-white"
          />
        </Button>
      </div>
    </div>
  ) : (
    <ToolbarWrapupPreview
      onChangeWrapupPreview={onChangeWrapupPreview}
      updateSelectedCategory={updateSelectedCategory}
      updateSelectedGroup={updateSelectedGroup}
      updateShowTextarea={updateShowTextarea}
    />
  );
};

export default ToolbarWrapup;
