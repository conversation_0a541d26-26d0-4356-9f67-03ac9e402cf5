/* eslint-disable @nx/enforce-module-boundaries */
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import { Undo2 } from 'lucide-react';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { useState } from 'react';
import {
  IInteractionItemProps,
  MakeCallRequestBody,
} from '@cdss-modules/design-system/@types';

export const ToolbarHistoryBlock: React.FC<IInteractionItemProps> = ({
  interaction,
}) => {
  const { call } = useCallControl();
  const {
    selectInteraction,
    openToolbarModal,
    updateActiveParticipantData,
    updateInteractionFilter,
    handleAfterCallFunc,
  } = useTbarContext();
  const {
    conversationId,
    isActive,
    acdData,
    customerDatas,
    consultDatas,
    isHaveCustomerOrConsult,
    talkTime,
  } = useHandleInteractionData(interaction);
  const [isOpen, setIsOpen] = useState(false);
  const generateRow = (rowData: any) => {
    if (!rowData) return null;
    const { direction, role, participantName, phoneNumber } = rowData;
    const onCallback = () => {
      let data: MakeCallRequestBody;
      switch (role) {
        case 'acd':
          data = { callQueueId: rowData?.queue?.id };
          break;
        default:
          data = { phoneNumber };
          break;
      }
      // call(data).then((res) => handleAfterCallFunc(res?.conversationId));
      updateInteractionFilter('current');
    };
    const attributesData = {
      conversationId,
      participantId: rowData?.id,
      conversationDetail: rowData,
    };
    return (
      <tr key={rowData.id}>
        <td className={cn('relative p-2 w-[20px]')}>
          <div className={cn('relative z-30')}>
            <Icon
              name="history"
              size={22}
              className="text-grey-600"
            />
          </div>
        </td>
        <td className={cn('relative p-2')}>{direction}</td>
        <td className="p-2">
          <div
            className="flex gap-2 items-center"
            onClick={() => setIsOpen(!isOpen)}
          >
            {participantName}
            {participantName === 'Call Flow' && (
              <Icon
                name="dropdown-arrow"
                size={10}
                className={` transition ${isOpen ? '' : '-rotate-90'}`}
              />
            )}
          </div>
        </td>
        {participantName === 'Call Flow' ? (
          <td className="p-2"></td>
        ) : (
          <td className="p-2">
            <span
              dangerouslySetInnerHTML={{
                __html: phoneNumber,
              }}
            />
          </td>
        )}
        <td className="px-2 py-2">{talkTime}</td>
        <td className="pl-2 py-2 gap-4 pr-4">
          <div className="flex items-center gap-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCallback();
              }}
              className="relative flex-none size-5 rounded-full text-white bg-status-success flex items-center justify-center hover:opacity-80 disabled:bg-grey-400 disabled:cursor-not-allowed"
            >
              <Icon
                name="phone"
                size={10}
                className="absolute bottom-[3px] left-[3px]"
              />
              <Undo2
                name="back"
                size={10}
                strokeWidth={4}
                className="absolute top-[2px] right-[2px]"
              />
            </button>
            <button
              onClick={() => {
                openToolbarModal('customer-info-panel');
                updateActiveParticipantData(attributesData);
              }}
              className="flex-none size-5 rounded-full bg-grey-400 flex items-center justify-center hover:opacity-80"
            >
              <div className="relative">
                <Icon
                  name="pencil"
                  size={12}
                  className="text-white"
                />
              </div>
            </button>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <tbody
      className={cn('hover:bg-primary-100', isActive && 'bg-primary-200')}
      onClick={() => selectInteraction(interaction)}
    >
      {customerDatas?.map((v: any) => generateRow(v))}
      {consultDatas?.map((v: any) => generateRow(v))}
      {!isHaveCustomerOrConsult && generateRow(acdData)}
    </tbody>
  );
};

export default ToolbarHistoryBlock;
