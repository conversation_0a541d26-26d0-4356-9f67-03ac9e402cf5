/* eslint-disable @nx/enforce-module-boundaries */
import { toast } from '@cdss-modules/design-system';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { Timer } from '@cdss/components/_ui/Timer';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { IInteractionItemProps } from '@cdss-modules/design-system/@types';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
type TIconMapping = {
  [key: string]: React.ReactNode; // Define the mapping type
};
export const ToolbarParticipantBlock: React.FC<IInteractionItemProps> = ({
  interaction,
}) => {
  const {
    selectInteraction,
    openToolbarModal,
    updateActiveParticipantData,
    stationContext: { station },
  } = useTbarContext();
  const {
    consultToTransfer,
    callConverSationControl,
    consultCompleted,
    conference,
  } = useCallControl();
  const {
    conversationId,
    customerData,
    customerDatas,
    consultDatas,
    agentData,
    tBarStatus,
    isConference,
    fullConference,
    isActive,
    consultInitiator,
    isHaveCustomerOrConsult,
    acdData,
  } = useHandleInteractionData(interaction);

  const iconMapping: TIconMapping = {
    connecting: (
      <Icon
        name="connecting"
        size={20}
        className="text-status-connected"
      />
    ),
    alerting: (
      <Icon
        name="alerting"
        size={20}
        className="text-status-connected"
      />
    ),
    connected: (
      <Icon
        name="connected"
        size={20}
        className="text-status-connected"
      />
    ),
    hold: (
      <Icon
        name="onhold"
        size={20}
        className="text-primary-500"
      />
    ),
    conference: (
      <Icon
        name="conference"
        size={20}
        className="text-status-connected"
      />
    ),
    wrapup: (
      <Icon
        name="wrapup"
        size={20}
        className="text-[#636363]"
      />
    ),
  };

  const handleCallControl = (type: string, rowData: any) => {
    const { role } = rowData;
    if (type == 'transfer') {
      consultToTransfer('disconnected', conversationId, agentData?.id);
      return;
    }
    if (type == 'disconnected') {
      if (isConference && consultInitiator === 'agent' && role === 'consult') {
        consultCompleted(conversationId, customerData?.id);
      } else {
        callConverSationControl({
          type: 'disconnect',
          conversationId,
          agentParticipantId: agentData?.id,
        });
      }
      return;
    }
    if (type == 'pickup') {
      if (!station?.name) {
        toast({
          variant: 'error',
          title: 'CALL ERROR',
          description:
            'You do not have a station selected. A station is required to make and receive calls.',
        });
      } else {
        callConverSationControl({
          type: 'pickup',
          conversationId,
          agentParticipantId: agentData.id,
        });
      }
      return;
    }
    if (type == 'hold') {
      if (fullConference && consultInitiator === 'agent') {
        const parmarType = role == 'consult' ? 'OBJECT' : 'DESTINATION';
        conference(parmarType, conversationId, customerData?.id);
        return;
      }
      callConverSationControl({
        type: 'hold',
        conversationId,
        agentParticipantId: agentData.id,
      });
    }
    if (type == 'unhold') {
      if (!isConference || (isConference && consultInitiator === 'consult')) {
        callConverSationControl({
          type: 'unhold',
          conversationId,
          agentParticipantId: agentData.id,
        });
        return;
      } else {
        const parmarType = role == 'consult' ? 'DESTINATION' : 'OBJECT';
        conference(parmarType, conversationId, customerData?.id);
      }
    }
  };

  const generateRow = (rowData: any) => {
    if (!rowData) return null;
    const { role, held, phoneNumber, participantName, state, confined } =
      rowData;
    const attributesData = {
      conversationId,
      participantId: rowData?.id,
      conversationDetail: rowData,
    };
    let iconShowStatus;
    let holdShow = false;
    let unholdShow = false;
    let pickUpShow = false;
    let disconnectShow = false;

    if (
      !isConference &&
      (agentData?.state === 'alerting' || agentData?.state === 'dialing') &&
      agentData?.direction === 'inbound'
    ) {
      pickUpShow = true;
    }
    if (
      agentData?.state !== 'alerting' &&
      agentData?.state !== 'dialing' &&
      tBarStatus !== 'disconnected' &&
      !['alerting', 'dialing', 'terminated', 'disconnected'].includes(state)
    ) {
      if (isConference) {
        if (consultInitiator === 'agent') {
          if (confined || held) {
            holdShow = true;
          }
          if (fullConference) {
            unholdShow = true;
          }
        }
      } else {
        if (confined || held) {
          holdShow = true;
        } else {
          unholdShow = true;
        }
      }
    }
    if (tBarStatus !== 'disconnected') {
      if (isConference) {
        if (consultInitiator === 'agent' && role == 'consult') {
          disconnectShow = true;
        }
      } else {
        if (agentData?.state !== 'disconnected') {
          disconnectShow = true;
        }
      }
    }

    if (state == 'connected') {
      iconShowStatus = 'connected';
    }
    if (state === 'alerting' || state === 'dialing') {
      iconShowStatus = 'alerting';
    }
    if (confined || held || agentData?.confined) {
      iconShowStatus = 'hold';
    }
    if (fullConference) {
      iconShowStatus = 'conference';
    }
    return (
      <tr
        key={rowData.id}
        className={cn(
          isActive && 'bg-primary-300 font-bold',
          'h-[30px]',
          isConference &&
            (held || confined || agentData?.confined) &&
            'bg-grey-100 border border-green-300'
        )}
      >
        <td
          className={cn(
            'relative w-[5%] pl-4 pr-2 !hover:rounded-l align-middle',
            isActive &&
              !isConference &&
              'rounded-l border border-r-0 border-l-primary-400 border-y-primary-400'
          )}
        >
          {isConference && (
            <span className="absolute h-[calc(100%_+_2px)] w-2 bg-[#57B62D] -top-px -left-px"></span>
          )}
          {iconMapping[iconShowStatus ?? '']}
        </td>
        <td
          className={cn(
            'align-middle',
            isActive &&
              !isConference &&
              'border border-x-0  border-y-primary-400'
          )}
        >
          {participantName}
        </td>
        <td
          className={cn(
            'align-middle',
            isActive &&
              !isConference &&
              'border border-x-0 border-y-primary-400'
          )}
        >
          {phoneNumber}
        </td>
        <td
          className={cn(
            'align-middle',
            isActive &&
              !isConference &&
              'border border-x-0 border-y-primary-400'
          )}
        >
          {tBarStatus !== 'disconnected' && (
            <Timer
              initialTime={
                consultInitiator === 'agent'
                  ? rowData.connectedTime
                  : agentData?.connectedTime
              }
              show={!!rowData.connectedTime}
            />
          )}
        </td>
        <td
          className={cn(
            'align-middle gap-4 pr-4',
            isActive &&
              !isConference &&
              'rounded-r border border-l-0 border-r-primary-400 border-y-primary-400'
          )}
        >
          <div className="flex items-center gap-x-2">
            {pickUpShow && (
              <button
                onClick={() => handleCallControl('pickup', rowData)}
                className="flex-none size-5 rounded-full bg-status-connected flex items-center justify-center hover:opacity-80 disabled:bg-grey-400 disabled:cursor-not-allowed"
              >
                <Icon
                  name="phone"
                  size={10}
                  className="text-white"
                />
              </button>
            )}
            {holdShow && (
              <button
                onClick={() => handleCallControl('unhold', rowData)}
                className="flex-none size-5 rounded-full bg-status-connected flex items-center justify-center hover:opacity-80 disabled:bg-grey-400 disabled:cursor-not-allowed"
              >
                <Icon
                  name="phone"
                  size={10}
                  className="text-white"
                />
              </button>
            )}
            {unholdShow && (
              <button
                onClick={() => handleCallControl('hold', rowData)}
                className="flex-none size-5 rounded-full bg-status-connected flex items-center justify-center hover:opacity-80"
              >
                <Icon
                  name="hold"
                  size={10}
                  className="text-white"
                />
              </button>
            )}
            {disconnectShow && (
              <button
                onClick={() => handleCallControl('disconnected', rowData)}
                className="flex-none size-5 rounded-full bg-status-danger flex items-center justify-center hover:opacity-80"
              >
                <div className="relative">
                  <Icon
                    name="phone-end"
                    size={12}
                    className="text-white"
                  />
                </div>
              </button>
            )}
            {tBarStatus !== 'disconnected' &&
              isConference &&
              consultInitiator !== 'consult' &&
              role === 'consult' && (
                <button
                  onClick={() => handleCallControl('transfer', rowData)}
                  className="flex-none size-5 rounded-full bg-status-info flex items-center justify-center hover:opacity-80"
                >
                  <div className="relative">
                    <Icon
                      name="transfer"
                      size={12}
                      className="text-white"
                    />
                  </div>
                </button>
              )}
            {/* wrapup btn */}
            {tBarStatus === 'disconnected' && (
              <button
                onClick={(e) => {
                  openToolbarModal('customer-info-panel');
                  updateActiveParticipantData(attributesData);
                }}
                className="flex-none size-5 rounded-full bg-grey-400 flex items-center justify-center hover:opacity-80"
              >
                <div className="relative">
                  <Icon
                    name="pencil"
                    size={12}
                    className="text-white"
                  />
                </div>
              </button>
            )}
          </div>
        </td>
      </tr>
    );
  };
  return (
    <tbody
      key={`interaction-tr-${conversationId}`}
      className={cn(
        '!hover:rounded hover:bg-primary-100 hover:border-primary-100',
        isActive && 'bg-primary-300 font-bold'
      )}
      onClick={() => selectInteraction(interaction)}
    >
      {customerDatas?.map((v: any) => generateRow(v))}
      {consultDatas?.map((v: any) => generateRow(v))}
      {!isHaveCustomerOrConsult && generateRow(acdData)}
    </tbody>
  );
};

export default ToolbarParticipantBlock;
