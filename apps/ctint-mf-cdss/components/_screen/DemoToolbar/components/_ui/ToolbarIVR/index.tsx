/* eslint-disable @nx/enforce-module-boundaries */
import { useEffect, useMemo, useState } from 'react';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import Pill from '@cdss-modules/design-system/components/_ui/Pill';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { TWorkgroup, TIVRItem } from '@cdss-modules/design-system/@types';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
import ToolbarForm from '../ToolbarForm';
import * as yup from 'yup';
import { toast } from '@cdss-modules/design-system';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { useAttributes } from '@cdss-modules/design-system/lib/hooks/useAttributes';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';
import { useDebounce } from '@cdss-modules/design-system/lib/hooks/useDebounce';
import { TAttributesData } from '@cdss-modules/design-system/@types';

export const ToolbarIVR = () => {
  const [searchValue, setSearchValue] = useState('');
  const [selectedIVR, setSelectedIVR] = useState<TIVRItem | null>(null);
  const [isInputFieldOpen, setIsInputFieldOpen] = useState(false);
  const [searchIvrWorkgroup, setSearchIvrWorkgroup] = useState<TIVRItem[]>([]);

  const {
    selectedInteraction,
    closeToolbarModal,
    activeParticipantData,
    updateActiveParticipantData,
    conversationHistoryListHandle,
  } = useTbarContext();
  const { submitAttributes } = useAttributes();
  const { getWorkgroupByUserHandle } = useGetworkGroupOrUser();
  const { transfer, IVRConference } = useCallControl();
  const { agentData, conversationId, customerData, consultData } =
    useHandleInteractionData(selectedInteraction);
  const allIvrWorkgroup = useMemo(() => {
    let allIvrWorkgroup: TIVRItem[] = [];
    getWorkgroupByUserHandle.data?.forEach((v: TWorkgroup) => {
      if (v.externalPhoneNums) {
        allIvrWorkgroup.push(...v.externalPhoneNums);
      }
    });
    const uniqueAllIvrWorkgroup = Array.from(
      new Set(allIvrWorkgroup.map((item) => item.number))
    )
      .map((number) => allIvrWorkgroup.find((item) => item.number === number))
      .filter((item): item is TIVRItem => item !== undefined);
    setSearchIvrWorkgroup(uniqueAllIvrWorkgroup);
    return uniqueAllIvrWorkgroup;
  }, [getWorkgroupByUserHandle?.data]);

  const ivrFormData = selectedIVR?.formItems;

  const ivrYupObj = ivrFormData?.reduce(
    (acc: Record<string, any>, cur: TAttributesData) => {
      if (cur?.isRequired) {
        acc[cur.key] = yup.string().required(`${[cur.name]} is required`);
      }
      return acc;
    },
    {}
  );
  const ivrFormSchema = yup.object(ivrYupObj).required();
  const debounceSearchKeyword = useDebounce(searchValue);
  useEffect(() => {
    if (debounceSearchKeyword === '') {
      setSearchIvrWorkgroup(allIvrWorkgroup);
      return;
    }
    const searchIvrWorkGroup = allIvrWorkgroup.filter(
      (record) =>
        record.name.toLowerCase().includes(debounceSearchKeyword) ||
        record.number.includes(debounceSearchKeyword)
    );
    setSearchIvrWorkgroup(searchIvrWorkGroup);
  }, [debounceSearchKeyword]);
  const handleInputChange = (e: any) => {
    const value = e?.toLowerCase();
    setSearchValue(value);
  };

  const handleIVRFormOnChange = (key: string, value: string | number) => {
    const isPII = ivrFormData?.find((item) => item.key === key)?.isPII;
    const isEncrypted = ivrFormData?.find(
      (item) => item.key === key
    )?.isEncrypted;
    updateActiveParticipantData({
      ...activeParticipantData,
      [key]: {
        value: value,
        isPII,
        isEncrypted,
      },
    });
  };

  const validateIVRFormData = () => {
    return ivrFormData?.every((item: TAttributesData) => {
      if (item?.isRequired) {
        return activeParticipantData?.[item?.key]?.value !== '';
      }
      return true;
    });
  };

  const handleTrasferAndConference = (type: 'transfer' | 'conference') => {
    if (searchValue !== '' && !selectedIVR) {
      if (type === 'transfer') {
        transfer(conversationId, agentData?.id, {
          destinationAddress: searchValue,
        });
      } else {
        IVRConference(conversationId, searchValue);
      }
    } else {
      if (activeParticipantData !== null && validateIVRFormData()) {
        if (type === 'transfer') {
          transfer(conversationId, agentData?.id, {
            destinationAddress: selectedIVR?.number,
          });
        } else {
          IVRConference(conversationId, selectedIVR?.number || '');
        }
        submitAttributes(
          conversationId,
          customerData?.id || consultData?.id,
          activeParticipantData
        ).then(() => conversationHistoryListHandle.refetch());
        setSelectedIVR(null);
        setIsInputFieldOpen(false);
        closeToolbarModal();
      } else {
        toast({
          variant: 'error',
          title: 'Error in Customer Info',
          description: 'Please fill in all required fields.',
        });
      }
    }
  };
  return (
    <div className="flex size-full">
      <div className="flex flex-col gap-2 w-full">
        <div className="flex gap-x-2 w-full ">
          <button
            className={`flex px-2 items-center text-xs cursor-pointer gap-x-1 whitespace-nowrap rounded-[4px] border border-grey-200 ${!selectedIVR && 'hidden'}`}
            onClick={() => setIsInputFieldOpen(!isInputFieldOpen)}
          >
            {selectedIVR?.name}
            <Icon
              name="dropdown-arrow"
              size={10}
              className=""
            />
          </button>
          <Input
            type="text"
            size="s"
            placeholder="Input IVR ID or Name to search IVR"
            value={searchValue}
            onChange={handleInputChange}
            allowClear
          />

          <CallControlButton
            tooltip={'Transfer'}
            icon={
              <Icon
                name="outbound"
                size={16}
                className="text-white"
              />
            }
            className={cn(
              'flex-none size-8 bg-status-success disabled:bg-grey-400 hover:border-none'
            )}
            onClick={() => handleTrasferAndConference('transfer')}
          />

          <CallControlButton
            tooltipPosition="left"
            tooltip={'Conference'}
            icon={
              <Icon
                name="conference"
                size={20}
                className="text-white"
              />
            }
            className={cn(
              'flex-none size-8 bg-status-success disabled:bg-grey-400 hover:border-none'
            )}
            onClick={() => handleTrasferAndConference('conference')}
          />
        </div>
        {!isInputFieldOpen && (
          <div className="flex flex-wrap gap-x-2 gap-y-0 h-full items-start overflow-y-auto">
            {searchIvrWorkgroup.map((ivr: TIVRItem) => {
              return (
                <Pill
                  variant="person"
                  key={ivr?.name}
                  onClick={() => {
                    setSelectedIVR(ivr);
                    const formItems: Record<string, any> = {};
                    ivr.formItems.forEach((v: TAttributesData) => {
                      formItems[v.key] = {
                        value: v?.value || '',
                        isPII: v?.isPII,
                        isEncrypted: v?.isEncrypted,
                      };
                    });
                    updateActiveParticipantData({
                      ...activeParticipantData,
                      ...formItems,
                    });
                    setIsInputFieldOpen(true);
                  }}
                  active={selectedIVR?.name === ivr?.name}
                >
                  <div className="flex flex-col items-start justify-center min-h-12">
                    <div className="flex gap-1 items-center">
                      <div className="size-[10px] rounded-full bg-status-success" />
                      <div className="text-remark">{ivr?.name}</div>
                    </div>
                    <div className="italic text-footnote text-grey-500">
                      {ivr?.number}
                    </div>
                  </div>
                </Pill>
              );
            })}
            {searchIvrWorkgroup?.length === 0 && (
              <div className="text-body">
                No search result, continue to make the call.
              </div>
            )}
          </div>
        )}
        {isInputFieldOpen && (
          <div className="flex flex-col gap-2 w-full h-full overflow-auto">
            <ToolbarForm
              schema={ivrFormSchema}
              formItems={ivrFormData || []}
              handleOnChange={handleIVRFormOnChange}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolbarIVR;
