import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import CallControlButton from '@cdss-modules/design-system/components/_ui/CallControlButton';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { FilePenLine, Undo2 } from 'lucide-react';
import Timer from '../../../../../_ui/Timer';
import { useCountDown } from '@cdss-modules/design-system/lib/hooks/useCountDown';
import { Tooltip } from '@cdss-modules/design-system';
import { toast } from '@cdss-modules/design-system';
import { useHandleInteractionData } from '@cdss-modules/design-system/lib/hooks/useHandleInteractionData';
import { useCallControl } from '@cdss-modules/design-system/lib/hooks/useCallControl';
export const ToolbarActiveCallInfo = () => {
  const { call, callConverSationControl } = useCallControl();
  const {
    selectedInteraction,
    wrapupContext: { showWrapup },
    handleAfterCallFunc,
    stationContext: { station },
  } = useTbarContext();
  const {
    conversationId,
    agentData,
    consultData,
    customerData,
    name,
    phoneNumber,
    tBarStatus,
    isHistory,
    consultInitiator,
  } = useHandleInteractionData(selectedInteraction);
  const wrapupPrompt = agentData?.wrapupPrompt;
  const wrapupRequired = agentData?.wrapupRequired;
  const timeout = agentData?.wrapupTimeoutMs;
  const remainTime = useCountDown(timeout, wrapupRequired);
  let pickUpShow = false;
  if (tBarStatus === 'alerting') {
    pickUpShow = true;
  } else if (
    consultInitiator === 'consult' &&
    (agentData?.state === 'alerting' || agentData?.state === 'dialing')
  ) {
    pickUpShow = true;
  }
  return (
    <div className="w-full h-0 flex-1 max-h-[100px] p-2 flex items-center gap-x-4">
      {conversationId ? (
        <>
          {!isHistory && tBarStatus !== 'disconnected' && (
            <div>
              <Timer
                initialTime={agentData?.connectedTime}
                show={agentData?.connectedTime}
                showAsProgressRing
                countDuration={300000}
              />
            </div>
          )}
          <div className="w-full text-remark font-bold">
            <div className="flex justify-between">
              {consultInitiator === 'consult'
                ? consultData?.participantName
                : name}
              {showWrapup && wrapupPrompt === 'timeout' && (
                <span className="text-status-danger">{remainTime}</span>
              )}
            </div>
            {
              <span
                dangerouslySetInnerHTML={{
                  __html:
                    consultInitiator === 'consult'
                      ? consultData?.phoneNumber
                      : phoneNumber,
                }}
              />
            }
          </div>
          {isHistory && (
            <CallControlButton
              tooltip={'Pick up'}
              tooltipPosition="right"
              icon={
                <div className="relative flex-none size-full rounded-full text-white bg-status-success flex items-center justify-center hover:opacity-80 disabled:bg-grey-400 disabled:cursor-not-allowed">
                  <Icon
                    name="phone"
                    size={20}
                    className="absolute bottom-[6px] left-[6px]"
                  />
                  <Undo2
                    name="back"
                    size={20}
                    strokeWidth={3}
                    className="absolute top-[5px] right-[5px]"
                  />
                </div>
              }
              className={cn(
                'flex-none size-10 bg-status-success hover:border-status-success hover:opacity-80 text-white disabled:bg-grey-100 disabled:text-grey-300'
              )}
              onClick={() =>
                call({
                  phoneNumber:
                    customerData?.phoneNumber || consultData?.phoneNumber,
                }).then((res) => {
                  // handleAfterCallFunc(res?.conversationId);
                })
              }
              disabled={!conversationId}
            />
          )}
          {pickUpShow && (
            <CallControlButton
              tooltip={'Pick up'}
              tooltipPosition="right"
              icon={
                <div className="relative">
                  <Icon
                    name="phone"
                    size={20}
                  />
                </div>
              }
              className={cn(
                'flex-none size-10 bg-status-success hover:border-status-success hover:opacity-80 text-white disabled:bg-grey-100 disabled:text-grey-300'
              )}
              handleOnChange={() => {
                if (station?.name) {
                  callConverSationControl({
                    type: 'pickup',
                    conversationId,
                    agentParticipantId: agentData.id,
                  });
                } else {
                  toast({
                    variant: 'error',
                    title: 'CALL ERROR',
                    description:
                      'You do not have a station selected. A station is required to make and receive calls.',
                  });
                }
              }}
              disabled={!conversationId}
            />
          )}
          {!isHistory && tBarStatus !== 'disconnected' && (
            <CallControlButton
              tooltip={'Disconnect'}
              tooltipPosition="right"
              onClick={() => {
                callConverSationControl({
                  type: 'disconnect',
                  conversationId,
                  agentParticipantId: agentData.id,
                });
              }}
              icon={
                <Icon
                  name="phone-end"
                  size={25}
                />
              }
              className={cn(
                'flex-none size-10 bg-status-danger hover:border-status-danger hover:opacity-80 text-white disabled:bg-grey-100 disabled:text-grey-300',
                tBarStatus === 'wrapup' && 'hidden'
              )}
              handleOnChange={() => null}
              disabled={!conversationId}
            />
          )}
          {(isHistory || tBarStatus === 'disconnected') && (
            <Tooltip
              trigger={
                <div className="flex-none flex items-center justify-center size-12 rounded-full bg-primary-200">
                  <FilePenLine />
                </div>
              }
              content="Wrap up"
            />
          )}
        </>
      ) : (
        <div className="w-full text-footnote font-bold text-center">
          No selected interaction.
        </div>
      )}
    </div>
  );
};

export default ToolbarActiveCallInfo;
