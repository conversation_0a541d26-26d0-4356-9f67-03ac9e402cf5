import React, { ChangeEvent, useEffect, useState } from 'react';
import {
  TAutoReplyFormData,
  TChannelType,
  // IWhatsAppConfig, // No longer directly used here for channelConfig
  // ISMSConfig, // No longer directly used here for channelConfig
  // IEmailConfig, // No longer directly used here for channelConfig
  // TChannelConfig, // No longer a separate object in formData
} from '../../_store/autoReplyFormStore';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import TextArea from '@cdss-modules/design-system/components/_ui/TextArea';
// RadioGroup removed as status is now a Select
import PopoverMenu from '@cdss-modules/design-system/components/_ui/PopoverMenu';
import { HelpCircle } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { fireGetPlatformAccounts } from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';

interface IBasicInfoProps {
  formData: TAutoReplyFormData;
  mode: string;
  updateField: <K extends keyof TAutoReplyFormData>(
    field: K,
    value: TAutoReplyFormData[K]
  ) => void;
  validationErrors: Record<string, string>;
  allowedChannels?: TChannelType[];
  allowedStatuses?: (0 | 1)[]; // Updated to use 0 | 1
}

// ChannelConfigProps, ChannelInputConfig, CombinedChannelInputProps, and CombinedChannelInput component are removed
// as channel configuration (phoneNumber) is now directly part of formData.

export default function BasicInfo({
  formData,
  mode,
  updateField,
  validationErrors,
  allowedChannels = ['WhatsApp'], // Defaulting to WhatsApp
  allowedStatuses = [1, 0], // Defaulting to Active (1), Inactive (0)
}: IBasicInfoProps) {
  // Local state for address to avoid timing issues
  const [localAddress, setLocalAddress] = useState(formData.address || '');

  // Sync local address state with formData.address changes
  useEffect(() => {
    if (formData.address !== localAddress) {
      setLocalAddress(formData.address || '');
    }
  }, [formData.address, localAddress]);

  // Handle address change - update both local state and form data
  const handleAddressChange = (value: string) => {
    setLocalAddress(value);
    updateField('address', value);
  };

  // Fetch platform accounts for phone number dropdown
  const { data: platformAccountsData, isLoading: isLoadingPlatformAccounts } =
    useQuery({
      queryKey: ['platformAccounts', '852'], // Using 852 as default country code
      queryFn: async () => {
        try {
          const response = await fireGetPlatformAccounts('852', basePath);
          return response.data;
        } catch (error) {
          console.error('Error fetching platform accounts:', error);
          throw error;
        }
      },
      enabled: true, // Always enabled since we need phone numbers for WhatsApp
    });

  // Transform platform accounts data for Select component
  const phoneNumberOptions = React.useMemo(() => {
    // Start with current address if it exists - this ensures it's always available
    const optionsWithCurrent: Array<{
      id: string;
      value: string;
      label: string;
    }> = [];

    if (localAddress) {
      optionsWithCurrent.push({
        id: localAddress,
        value: localAddress,
        label: localAddress, // Don't add "(Current)" here as it might be in the API list too
      });
    }

    // Add API options, but avoid duplicates
    if (platformAccountsData?.isSuccess && platformAccountsData.data) {
      const apiOptions = platformAccountsData.data
        .map((account: any) => ({
          id: account.value,
          value: account.value,
          label: account.label,
        }))
        .filter((option: { value: string }) => option.value !== localAddress); // Avoid duplicates

      optionsWithCurrent.push(...apiOptions);
    }

    return optionsWithCurrent;
  }, [platformAccountsData, localAddress]);

  // useEffect(() => {
  //   // If only one channel type is allowed and it's not the current one, update it.
  //   if (
  //     allowedChannels.length === 1 &&
  //     formData.channelType !== allowedChannels[0]
  //   ) {
  //     updateField('channelType', allowedChannels[0]);
  //     // If switching to a channel type that doesn't use phoneNumber, clear it.
  //     // For now, assuming WhatsApp is the primary, so phoneNumber is expected.
  //     // If other channel types are introduced that don't use phoneNumber,
  //     // this logic might need adjustment.
  //     if (
  //       formData.channelType !== 'WhatsApp' &&
  //       formData.channelType !== 'SMS'
  //     ) {
  //       updateField('address', '');
  //     }
  //   }
  // }, [allowedChannels, formData.channelType, updateField, formData.address]);

  // Convert allowed statuses to options format for Select
  const statusOptions = allowedStatuses.map((status) => ({
    id: String(status), // Select expects string ID
    value: String(status), // Select expects string value
    label: status === 1 ? 'Active' : 'Inactive', // Map 1 to Active, 0 to Inactive
  }));

  // Handle channel type change
  const handleChannelTypeChange = (newChannelType: string) => {
    updateField('channelType', newChannelType as TChannelType);
    // If the new channel type doesn't use a phone number (e.g., Email, if added later),
    // you might want to clear the phoneNumber field.
    if (newChannelType !== 'WhatsApp' && newChannelType !== 'SMS') {
      updateField('address', '');
    }
  };

  const isViewMode = mode === 'view';

  return (
    <div className="px-4">
      <h2 className="font-bold text-sm mb-3 text-primary-500">
        Basic Information
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Rule Name field */}
        <div>
          <label className="block text-sm font-bold mb-1">Rule Name</label>
          <Input
            disabled={isViewMode}
            size="s"
            placeholder="Enter auto reply rule name"
            value={formData.ruleName} // Changed from formData.name
            onChange={(value) => updateField('ruleName', String(value))} // Changed from 'name'
            status={validationErrors.ruleName ? 'danger' : undefined} // Changed from validationErrors.name
            message={validationErrors.ruleName} // Changed from validationErrors.name
            className="disabled:text-black"
          />
        </div>

        {/* Priority field */}
        <div>
          <label className="block text-sm font-bold mb-1">Priority</label>
          <Input
            disabled={isViewMode}
            size="s"
            type="number"
            placeholder="Enter priority (e.g., 1, 2, 3...)"
            value={String(formData.priority)}
            onChange={(value) => {
              const sanitizedValue = String(value).replace(/^-/, '');
              const numericValue = parseInt(sanitizedValue, 10) || 0;
              const newValue = Math.max(numericValue, 0);
              updateField('priority', newValue || 0);
            }}
            status={validationErrors.priority ? 'danger' : undefined}
            message={validationErrors.priority}
            className="disabled:text-black"
          />
        </div>

        {/* Status field */}
        <div>
          <label className="block text-sm font-bold mb-1">Status</label>
          <Select
            disabled={isViewMode}
            value={String(formData.status)} // formData.status is 0 | 1, Select expects string
            onChange={
              (value) =>
                !isViewMode &&
                updateField(
                  'status',
                  Number(value) as TAutoReplyFormData['status']
                ) // Convert back to number
            }
            options={statusOptions}
            isPagination={false}
            labelClassName="h-full"
            triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
          />
        </div>

        {/* Description field - spans full width */}
        <div className="md:col-span-2">
          <TextArea
            disabled={isViewMode}
            label="Description"
            placeholder="Enter description for this auto reply rule"
            value={formData.description} // Changed from formData.message
            onChange={(e) => updateField('description', e.target.value)} // Changed from 'message'
            error={validationErrors.description} // Changed from validationErrors.message
            rows={3}
            className="disabled:opacity-100 disabled:bg-grey-100 disabled:text-black text-sm"
          />
        </div>

        {/* Channel and Phone Number field */}
        {allowedChannels.includes('WhatsApp') && (
          <div className="md:col-span-2">
            <label className="text-sm font-bold mb-1 flex items-center gap-2">
              {formData.channelType === 'SMS'
                ? 'SMS Platform Account'
                : 'WhatsApp Platform Account'}
              <PopoverMenu
                icon={<HelpCircle className="h-4 w-4 text-gray-500" />}
                side="right"
              >
                <div className="w-full bg-white rounded-md p-4 shadow-md border border-gray-200 ml-1 text-sm text-gray-600 max-w-xs">
                  Select the platform account to use for {formData.channelType}{' '}
                  auto replies
                </div>
              </PopoverMenu>
            </label>
            <div className="flex gap-1">
              <Select
                disabled={isViewMode || allowedChannels.length === 1}
                value={formData.channelType}
                onChange={(value) => handleChannelTypeChange(String(value))}
                isPagination={false}
                labelClassName="h-full"
                triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
                options={allowedChannels.map((option) => ({
                  id: option,
                  value: option,
                  label: option,
                }))}
              />
              <Select
                disabled={isViewMode || isLoadingPlatformAccounts}
                value={localAddress}
                onChange={(value) => handleAddressChange(String(value))}
                options={phoneNumberOptions}
                isPagination={false}
                labelClassName="h-full"
                triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm flex-1"
                placeholder={
                  isLoadingPlatformAccounts
                    ? 'Loading platform accounts...'
                    : 'Select platform account'
                }
              />
            </div>
            {validationErrors.address && (
              <div className="text-red-500 text-xs mt-1">
                {validationErrors.address}
              </div>
            )}
          </div>
        )}
        {/* If other channel types like 'Email' are supported without a phone number, 
            conditional rendering for their specific inputs would go here. */}
      </div>
    </div>
  );
}
