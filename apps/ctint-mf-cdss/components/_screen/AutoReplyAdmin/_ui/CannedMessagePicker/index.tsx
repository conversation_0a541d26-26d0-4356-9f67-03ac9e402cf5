// import { <PERSON><PERSON>, Select } from '@cdss-modules/design-system';
// import Input from '@cdss-modules/design-system/components/_ui/Input';
// import React, { useEffect, useState } from 'react';
// import { useQuery } from '@tanstack/react-query';
// import {
//   useAutoReplyFormStore,
//   ICannedMessage,
// } from '../../_store/autoReplyFormStore';
// import { ChevronDown, Icon } from 'lucide-react';
// import { cn } from '@cdss-modules/design-system/lib/utils';
// import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';

// // Define the structure for our canned message categories
// interface IMessageCategory {
//   id: string;
//   name: string;
//   messages: ICannedMessage[];
// }

// type TLanguageOption = {
//   id: string;
//   label: string;
//   value: string;
// };

// const languageOptions: TLanguageOption[] = [
//   {
//     id: '1',
//     label: 'English',
//     value: 'en',
//   },
//   {
//     id: '2',
//     label: 'Chinese (Simplified)',
//     value: 'zh_cn',
//   },
//   {
//     id: '3',
//     label: 'Chinese (Traditional)',
//     value: 'zh_tw',
//   },
// ];

// // Fake API function to simulate getting canned messages
// const fetchCannedMessages = async (): Promise<IMessageCategory[]> => {
//   // Simulate API delay
//   await new Promise((resolve) => setTimeout(resolve, 500));

//   // Return fake data
//   return [
//     {
//       id: '1',
//       name: 'Greetings',
//       messages: [
//         {
//           id: '101',
//           name: 'Hello',
//           message: '您好，欢迎联系我们的客服中心。',
//           language: 'zh_cn',
//         },
//         {
//           id: '102',
//           name: 'Welcome',
//           message: '欢迎使用我们的服务。',
//           language: 'zh_cn',
//         },
//         {
//           id: '103',
//           name: 'Hello',
//           message: 'Hello, welcome to our customer service center.',
//           language: 'en',
//         },
//         {
//           id: '104',
//           name: 'Welcome',
//           message: 'Welcome to our services.',
//           language: 'en',
//         },
//         {
//           id: '105',
//           name: 'Hello',
//           message: '您好，歡迎聯繫我們的客服中心。',
//           language: 'zh_tw',
//         },
//         {
//           id: '106',
//           name: 'Welcome',
//           message: '歡迎使用我們的服務。',
//           language: 'zh_tw',
//         },
//       ],
//     },
//     {
//       id: '2',
//       name: 'Support',
//       messages: [
//         {
//           id: '201',
//           name: 'Help',
//           message: '需要帮助吗？请告诉我们您遇到的问题。',
//           language: 'zh_cn',
//         },
//         {
//           id: '202',
//           name: 'Ticket',
//           message: '您的支持请求已经被记录，我们会尽快回复您。',
//           language: 'zh_cn',
//         },
//         {
//           id: '203',
//           name: 'Help',
//           message: 'Need help? Please tell us about your issue.',
//           language: 'en',
//         },
//         {
//           id: '204',
//           name: 'Ticket',
//           message:
//             'Your support request has been logged, we will respond soon.',
//           language: 'en',
//         },
//         {
//           id: '205',
//           name: 'Help',
//           message: '需要幫助嗎？請告訴我們您遇到的問題。',
//           language: 'zh_tw',
//         },
//         {
//           id: '206',
//           name: 'Ticket',
//           message: '您的支持請求已經被記錄，我們會儘快回复您。',
//           language: 'zh_tw',
//         },
//       ],
//     },
//     {
//       id: '3',
//       name: 'Closing',
//       messages: [
//         {
//           id: '301',
//           name: 'Thanks',
//           message: '感谢您的耐心等待。',
//           language: 'zh_cn',
//         },
//         {
//           id: '302',
//           name: 'Goodbye',
//           message: '再见，祝您有美好的一天！',
//           language: 'zh_cn',
//         },
//         {
//           id: '303',
//           name: 'Thanks',
//           message: 'Thank you for your patience.',
//           language: 'en',
//         },
//         {
//           id: '304',
//           name: 'Goodbye',
//           message: 'Goodbye, have a great day!',
//           language: 'en',
//         },
//         {
//           id: '305',
//           name: 'Thanks',
//           message: '感謝您的耐心等待。',
//           language: 'zh_tw',
//         },
//         {
//           id: '306',
//           name: 'Goodbye',
//           message: '再見，祝您有美好的一天！',
//           language: 'zh_tw',
//         },
//       ],
//     },
//     {
//       id: '4',
//       name: 'Troubleshooting',
//       messages: [
//         {
//           id: '401',
//           name: 'Technical Issue',
//           message: '看起来您遇到了技术问题。请尝试重新启动应用程序。',
//           language: 'zh_cn',
//         },
//         {
//           id: '402',
//           name: 'Connection Error',
//           message: '连接错误可能是由于网络问题。请检查您的网络连接。',
//           language: 'zh_cn',
//         },
//         {
//           id: '403',
//           name: 'Technical Issue',
//           message:
//             'It looks like you are experiencing a technical issue. Please try restarting the application.',
//           language: 'en',
//         },
//         {
//           id: '404',
//           name: 'Connection Error',
//           message:
//             'Connection errors may be due to network issues. Please check your network connection.',
//           language: 'en',
//         },
//         {
//           id: '405',
//           name: 'Technical Issue',
//           message: '看起來您遇到了技術問題。請嘗試重新啟動應用程序。',
//           language: 'zh_tw',
//         },
//         {
//           id: '406',
//           name: 'Connection Error',
//           message: '連接錯誤可能是由於網絡問題。請檢查您的網絡連接。',
//           language: 'zh_tw',
//         },
//       ],
//     },
//     {
//       id: '5',
//       name: 'Billing',
//       messages: [
//         {
//           id: '501',
//           name: 'Payment Confirmation',
//           message: '我们已收到您的付款。感谢您的支持！',
//           language: 'zh_cn',
//         },
//         {
//           id: '502',
//           name: 'Invoice Request',
//           message: '您的发票将在3个工作日内发送到您的邮箱。',
//           language: 'zh_cn',
//         },
//         {
//           id: '503',
//           name: 'Payment Confirmation',
//           message: 'We have received your payment. Thank you for your support!',
//           language: 'en',
//         },
//         {
//           id: '504',
//           name: 'Invoice Request',
//           message:
//             'Your invoice will be sent to your email within 3 business days.',
//           language: 'en',
//         },
//         {
//           id: '505',
//           name: 'Payment Confirmation',
//           message: '我們已收到您的付款。感謝您的支持！',
//           language: 'zh_tw',
//         },
//         {
//           id: '506',
//           name: 'Invoice Request',
//           message: '您的發票將在3個工作日內發送到您的郵箱。',
//           language: 'zh_tw',
//         },
//       ],
//     },
//     {
//       id: '6',
//       name: 'Product Information',
//       messages: [
//         {
//           id: '601',
//           name: 'Features',
//           message: '我们的产品包括以下功能：自动回复、多语言支持和实时分析。',
//           language: 'zh_cn',
//         },
//         {
//           id: '602',
//           name: 'Pricing',
//           message: '关于价格信息，请访问我们的官方网站或联系销售团队。',
//           language: 'zh_cn',
//         },
//         {
//           id: '603',
//           name: 'Features',
//           message:
//             'Our product includes the following features: auto-reply, multi-language support, and real-time analytics.',
//           language: 'en',
//         },
//         {
//           id: '604',
//           name: 'Pricing',
//           message:
//             'For pricing information, please visit our official website or contact our sales team.',
//           language: 'en',
//         },
//         {
//           id: '605',
//           name: 'Features',
//           message: '我們的產品包括以下功能：自動回复、多語言支持和實時分析。',
//           language: 'zh_tw',
//         },
//         {
//           id: '606',
//           name: 'Pricing',
//           message: '關於價格信息，請訪問我們的官方網站或聯繫銷售團隊。',
//           language: 'zh_tw',
//         },
//       ],
//     },
//     {
//       id: '7',
//       name: 'Promotions',
//       messages: [],
//     },
//     {
//       id: '8',
//       name: 'Feedback',
//       messages: [],
//     },
//     {
//       id: '9',
//       name: 'Returns',
//       messages: [],
//     },
//     {
//       id: '10',
//       name: 'Shipping',
//       messages: [],
//     },
//   ];
// };

// // Language selector component
// const LanguageSelector = ({
//   isDisabled,
//   cannedMessages,
//   handleLanguageChange,
// }: {
//   isDisabled: boolean;
//   cannedMessages: any[];
//   handleLanguageChange: (values: any) => void;
// }) => {
//   const { removeAllCannedMessageLanguages, removeCannedMessageLanguage } =
//     useAutoReplyFormStore();

//   const removeLanguage = (language: string) => {
//     const index = cannedMessages.findIndex((msg) => msg.language === language);
//     if (index !== -1) {
//       removeCannedMessageLanguage(index);
//     }
//   };

//   return (
//     <div className="flex flex-col gap-2 w-full max-w-md">
//       <label
//         htmlFor="language-select"
//         className="text-sm font-semibold whitespace-nowrap"
//       >
//         Languages
//       </label>
//       <Select
//         disabled={isDisabled}
//         options={languageOptions}
//         isPagination={false}
//         placeholder="Select languages"
//         value={cannedMessages.map((message) => message.language)}
//         onChange={handleLanguageChange}
//         mode={'multiple'}
//         noSearchResultMessage="No languages found"
//         displaySelectedItems={true}
//         labelClassName="h-full"
//         triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
//         removeAllSelection={() => {
//           // Clear all canned message languages
//           removeAllCannedMessageLanguages();
//         }}
//         removeSelection={removeLanguage}
//       />
//     </div>
//   );
// };

// // Empty state component when no language is selected
// const EmptyState = () => {
//   return (
//     <div className="text-center py-8 text-gray-500">
//       Please select at least one language to view canned messages
//     </div>
//   );
// };

// // Search component with create button
// const MessageSearch = ({
//   searchTerm,
//   setSearchTerm,
// }: {
//   searchTerm: string;
//   setSearchTerm: (term: string) => void;
// }) => {
//   return (
//     <div className="flex items-center gap-2 mb-2">
//       <Input
//         // isSearch
//         placeholder="Search"
//         size="s"
//         value={searchTerm}
//         onChange={(value) => setSearchTerm(value as string)}
//         allowClear
//       />

//       <Button
//         size="s"
//         className="whitespace-nowrap"
//       >
//         Create New
//       </Button>
//     </div>
//   );
// };

// // Category list component
// const CategoryList = ({
//   isLoading,
//   filteredCategories,
//   selectedCategories,
//   handleSelectCategory,
//   selectedMessages,
//   handleSelectMessage,
//   language,
//   showAllLanguages = true,
// }: {
//   isLoading: boolean;
//   filteredCategories: IMessageCategory[];
//   selectedCategories: { [key: string]: string[] };
//   handleSelectCategory: (language: string, categoryId: string) => void;
//   selectedMessages: { [key: string]: ICannedMessage | null };
//   handleSelectMessage: (language: string, message: ICannedMessage) => void;
//   language: string;
//   showAllLanguages: boolean;
// }) => {
//   const updateCannedMessage = useAutoReplyFormStore(
//     (state) => state.updateCannedMessage
//   );

//   // Function to check if category contains the selected message
//   const isCategoryWithSelectedMessage = (category: IMessageCategory) => {
//     if (!selectedMessages[language]) return false;
//     return category.messages.some(
//       (msg) => msg.id === selectedMessages[language]?.id
//     );
//   };

//   if (isLoading) {
//     return <div className="text-center py-4">Loading...</div>;
//   }

//   return (
//     <div className="h-full overflow-y-auto">
//       {filteredCategories.map((category) => (
//         <div key={category.id}>
//           <div
//             className={cn(
//               'px-3 py-1 font-semibold cursor-pointer hover:bg-primary-100 flex items-center justify-between active:bg-primary-200',
//               selectedCategories[language]?.includes(category.id) &&
//                 'bg-primary-200 hover:bg-primary-200',
//               isCategoryWithSelectedMessage(category) &&
//                 !selectedCategories[language]?.includes(category.id) &&
//                 'text-primary-500 bg-primary-50'
//             )}
//             onClick={() => handleSelectCategory(language, category.id)}
//           >
//             {category.name}
//             <ChevronDown
//               size={16}
//               className={cn(
//                 'transition-all mt-[2px]',
//                 selectedCategories[language]?.includes(category.id)
//                   ? `rotate-180`
//                   : `rotate-0`,
//                 (selectedCategories[language]?.includes(category.id) ||
//                   isCategoryWithSelectedMessage(category)) &&
//                   `text-primary-500`
//               )}
//             />
//           </div>
//           {selectedCategories[language]?.includes(category.id) && (
//             <div className="">
//               {category.messages
//                 .filter((msg) => showAllLanguages || msg.language === language)
//                 .map((message) => (
//                   <div
//                     key={message.id}
//                     className={`py-1 pl-6 pr-3 cursor-pointer flex items-center justify-between text-sm ${
//                       selectedMessages[language]?.id === message.id
//                         ? 'bg-primary-100 text-primary-500'
//                         : 'hover:bg-primary-100'
//                     }`}
//                     onClick={() => {
//                       handleSelectMessage(language, message);
//                       updateCannedMessage(language, message);
//                     }}
//                   >
//                     <span className="">{message.name}</span>
//                     <span
//                       className={cn(
//                         'border rounded-sm px-1',
//                         selectedMessages[language]?.id === message.id &&
//                           'border-primary-500'
//                       )}
//                     >
//                       {message.language}
//                     </span>
//                   </div>
//                 ))}
//             </div>
//           )}
//         </div>
//       ))}
//     </div>
//   );
// };

// // Message preview component
// const MessagePreview = ({
//   selectedMessage,
// }: {
//   selectedMessage: ICannedMessage | null;
// }) => {
//   if (!selectedMessage) {
//     return (
//       <div className="h-full flex flex-col justify-between">
//         <span className="text-sm font-semibold mb-3">Preview</span>
//         <div className="text-gray-500 text-center py-4 flex flex-col items-center gap-4 h-full justify-center">
//           <IconEmptyRecords size="78" />
//           <span className="text-sm">No message selected</span>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <div className="h-full flex flex-col justify-between">
//       <span className="text-sm font-semibold mb-3">Preview</span>
//       <div className="bg-primary-200 p-2 flex flex-col justify-between">
//         <div className="flex justify-between items-center">
//           <span className="font-semibold">{selectedMessage.name}</span>
//           <div className="bg-primary-100 text-primary-600 px-2 py-0.5 text-xs rounded">
//             {selectedMessage.language === 'en'
//               ? 'EN'
//               : selectedMessage.language}
//           </div>
//         </div>
//       </div>
//       <div className="p-2 bg-primary-100 h-full">{selectedMessage.message}</div>
//     </div>
//   );
// };

// // Language filter toggle component
// const LanguageFilterToggle = ({
//   showAllLanguages,
//   setShowAllLanguages,
// }: {
//   showAllLanguages: boolean;
//   setShowAllLanguages: (show: boolean) => void;
// }) => {
//   return (
//     <div className="flex items-center gap-2 mb-4">
//       <input
//         type="checkbox"
//         id="language-filter-toggle"
//         checked={showAllLanguages}
//         onChange={(e) => setShowAllLanguages(e.target.checked)}
//       />
//       <label
//         htmlFor="language-filter-toggle"
//         className="text-sm cursor-pointer"
//       >
//         Show all languages
//       </label>
//     </div>
//   );
// };

// // Language section component
// const LanguageSection = ({
//   isDisabled,
//   cannedMessage,
//   filteredCategories,
//   selectedCategories,
//   handleSelectCategory,
//   selectedMessages,
//   handleSelectMessage,
//   isLoading,
//   showAllLanguages,
// }: {
//   isDisabled: boolean;
//   cannedMessage: any;
//   filteredCategories: (searchTerm: string) => IMessageCategory[];
//   selectedCategories: { [key: string]: string[] };
//   handleSelectCategory: (language: string, categoryId: string) => void;
//   selectedMessages: { [key: string]: ICannedMessage | null };
//   handleSelectMessage: (language: string, message: ICannedMessage) => void;
//   isLoading: boolean;
//   showAllLanguages: boolean;
// }) => {
//   const [searchTerm, setSearchTerm] = useState('');
//   const languageName =
//     languageOptions.find((l) => l.value === cannedMessage.language)?.label ||
//     cannedMessage.language;

//   return (
//     <div
//       key={cannedMessage.language}
//       className="border-t pt-4"
//     >
//       <h3 className="text-lg font-semibold mb-2">{languageName}</h3>
//       <div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-fit ">
//         {!isDisabled && (
//           <div className="flex flex-col h-full">
//             <MessageSearch
//               searchTerm={searchTerm}
//               setSearchTerm={setSearchTerm}
//             />

//             <div className="border border-gray-200 rounded-md h-64 py-1">
//               <CategoryList
//                 isLoading={isLoading}
//                 filteredCategories={filteredCategories(searchTerm)}
//                 selectedCategories={selectedCategories}
//                 handleSelectCategory={handleSelectCategory}
//                 selectedMessages={selectedMessages}
//                 handleSelectMessage={handleSelectMessage}
//                 language={cannedMessage.language}
//                 showAllLanguages={showAllLanguages}
//               />
//             </div>
//           </div>
//         )}
//         <div className="border border-gray-200 rounded-md p-2 px-3 h-full">
//           <MessagePreview
//             selectedMessage={selectedMessages[cannedMessage.language]}
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default function CannedMessagePicker({
//   mode,
// }: {
//   mode: 'view' | 'edit';
// }) {
//   const { formData, updateCannedMessageLanguage } = useAutoReplyFormStore();
//   const { cannedMessages } = formData;

//   const [selectedCategories, setSelectedCategories] = useState<{
//     [key: string]: string[];
//   }>({});
//   const [selectedMessages, setSelectedMessages] = useState<{
//     [key: string]: ICannedMessage | null;
//   }>({});
//   const [showAllLanguages, setShowAllLanguages] = useState(true);

//   // Fetch canned messages
//   const { data: categories, isLoading } = useQuery({
//     queryKey: ['cannedMessages'],
//     queryFn: fetchCannedMessages,
//   });

//   const isDisabled = mode === 'view';

//   const handleLanguageChange = (values: any) => {
//     if (values.target.value) {
//       values.stopPropagation?.();
//       console.log('valuess', values.target.value);
//       updateCannedMessageLanguage(values.target.value);
//     }
//   };

//   // Filter categories and messages based on search term and language
//   const getFilteredCategories = (language: string) => (searchTerm: string) => {
//     if (!categories) return [];

//     return categories
//       .map((category) => ({
//         ...category,
//         messages: category.messages.filter(
//           (msg) =>
//             !searchTerm ||
//             msg.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
//             msg.message?.toLowerCase().includes(searchTerm.toLowerCase())
//         ),
//       }))
//       .filter((category) => category.messages.length > 0);
//   };

//   const handleSelectCategory = (language: string, categoryId: string) => {
//     setSelectedCategories((prev) => {
//       const currentSelected = prev[language] || [];

//       // If category is already selected, remove it
//       if (currentSelected.includes(categoryId)) {
//         return {
//           ...prev,
//           [language]: currentSelected.filter((id) => id !== categoryId),
//         };
//       }
//       // Otherwise add it to the selected categories
//       else {
//         return {
//           ...prev,
//           [language]: [...currentSelected, categoryId],
//         };
//       }
//     });
//   };

//   const handleSelectMessage = (language: string, message: ICannedMessage) => {
//     setSelectedMessages({
//       ...selectedMessages,
//       [language]: message,
//     });
//   };

//   return (
//     <div className="flex flex-col gap-4">
//       <LanguageSelector
//         isDisabled={isDisabled}
//         cannedMessages={cannedMessages}
//         handleLanguageChange={handleLanguageChange}
//       />

//       {cannedMessages.length > 0 ? (
//         <div className="flex flex-col gap-6">
//           {/* <LanguageFilterToggle
//             showAllLanguages={showAllLanguages}
//             setShowAllLanguages={setShowAllLanguages}
//           /> */}

//           {cannedMessages.map((cannedMessage) => {
//             return (
//               <LanguageSection
//                 isDisabled={isDisabled}
//                 key={cannedMessage.language}
//                 cannedMessage={cannedMessage}
//                 filteredCategories={getFilteredCategories(
//                   cannedMessage.language
//                 )}
//                 selectedCategories={selectedCategories}
//                 handleSelectCategory={handleSelectCategory}
//                 selectedMessages={selectedMessages}
//                 handleSelectMessage={handleSelectMessage}
//                 isLoading={isLoading}
//                 showAllLanguages={showAllLanguages}
//               />
//             );
//           })}
//         </div>
//       ) : (
//         <EmptyState />
//       )}
//     </div>
//   );
// }
