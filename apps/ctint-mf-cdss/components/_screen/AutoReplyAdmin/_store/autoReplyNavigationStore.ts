import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { useAutoReplyFormStore } from './autoReplyFormStore';

type TMode = 'view' | 'edit';

type TView = 'list' | 'detail';

interface AutoReplyState {
  view: TView;
  selectedItemId: string | null;
  mode: TMode;
  isCreate?: boolean;
  setView: (view: TView) => void;
  setMode: (mode: TMode) => void;
  // setSelectedItemId: (id: string | null) => void;
  viewDetail: (id: string) => void;
  editDetail: (id: string) => void;
  backToList: () => void;
  createNew: () => void;
}

export const useAutoReplyNavigationStore = create<AutoReplyState>()(
  immer((set) => ({
    view: 'list',
    mode: 'view',
    selectedItemId: null,
    isCreate: false,

    setView: (view) =>
      set((state) => {
        state.view = view;
      }),

    setMode: (mode) =>
      set((state) => {
        state.mode = mode;
      }),

    // setSelectedItemId: (id) =>
    //   set((state) => {
    //     state.selectedItemId = id;
    //   }),

    viewDetail: (id) =>
      set((state) => {
        state.selectedItemId = id;
        state.view = 'detail';
        state.mode = 'view';
        state.isCreate = false
      }),

    editDetail: (id) =>
      set((state) => {
        console.log('editDetail', id);
        state.selectedItemId = id;
        state.view = 'detail';
        state.mode = 'edit';
        state.isCreate = false
      }),

    createNew: () =>
      set((state) => {
        state.selectedItemId = null;
        state.view = 'detail';
        state.mode = 'edit';
        state.isCreate=true;
        const initNewForm = useAutoReplyFormStore.getState().initNewForm;
        initNewForm();
      }),

    backToList: () =>
      set((state) => {
        state.view = 'list';
        state.isCreate = false
      }),
  }))
);
