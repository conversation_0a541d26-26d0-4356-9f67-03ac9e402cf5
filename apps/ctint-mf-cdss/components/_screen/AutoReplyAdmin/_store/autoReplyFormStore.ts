import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import { IRoutingRule } from '../../../_ui/RoutingRuleEditor';
import { MultiLanguageTemplateInfo } from '../../../_ui/RoutingRuleEditor/MultiLanguageTemplateConfigurationSelector';

// Condition type literal
export type TConditionType = 'time' | 'boolean' | 'text';

// Boolean condition types
export type TBooleanConditionType =
  | 'isHoliday'
  | 'isWeekend'
  | 'isBusinessHour';

// Channel types - currently only WhatsApp but easily extensible
export type TChannelType = 'WhatsApp' | 'SMS' | 'Email';

// Channel configuration interfaces
export interface IWhatsAppConfig {
  phoneNumber: string;
}

export interface ISMSConfig {
  phoneNumber: string;
}

export interface IEmailConfig {
  emailAddress: string;
}

export type TChannelConfig = IWhatsAppConfig | ISMSConfig | IEmailConfig;

// Logic type for relationships between conditions
export type TLogicType = 'and' | 'or';

// Base condition interface with a discriminator
interface IBaseCondition {
  type: TConditionType;
}

// Specific condition types
export interface ITimeCondition extends IBaseCondition {
  type: 'time';
  operator: 'within' | 'outside' | 'before' | 'after';
  times: string[];
}

export interface IBooleanCondition extends IBaseCondition {
  type: 'boolean';
  conditionType: TBooleanConditionType;
  value: boolean;
}

export interface ITextCondition extends IBaseCondition {
  type: 'text';
  text: string;
}

// Union type for all conditions
export type TCondition = ITimeCondition | IBooleanCondition | ITextCondition;

// Condition with logic relationship
export interface IConditionWithLogic {
  condition: TCondition;
  logic?: TLogicType; // Logic relationship to the next condition, optional for last condition
}

// Group interface
export interface IGroup {
  conditions: IConditionWithLogic[];
}

// Unified condition type that can be either a simple condition or a group
export type TUnifiedCondition =
  | {
      type: 'simple';
      condition: TCondition;
      logic?: TLogicType;
    }
  | {
      type: 'group';
      group: IGroup;
      logic?: TLogicType;
    };

export interface ICannedMessage {
  language: string;
  id: string | null;
  name: string | null;
  message: string | null;
}

// Define the form data structure
export type TAutoReplyFormData = {
  ruleId: string;
  ruleName: string;
  description: string;
  status: 0 | 1;
  priority: number;
  channelType: TChannelType;
  address: string;
  rules: IRoutingRule[];
  createDate?: string;
  lastUpdate?: string;
};

// Initial state for empty form
const initialFormState: TAutoReplyFormData = {
  ruleId: '',
  ruleName: '',
  description: '',
  status: 1,
  priority: 0,
  channelType: 'WhatsApp',
  address: '',
  rules: [],
  createDate: undefined,
  lastUpdate: undefined,
};

// Type for API response data structure
type TAutoReplyApiData = {
  id?: string;
  name?: string;
  description?: string;
  isActive?: 0 | 1;
  priority?: number;
  channelType?: string;
  address?: string;
  rules?: any[];
  createTime?: string;
  updateTime?: string;
  createBy?: string;
  updateBy?: string;
  // Also support legacy field names for backward compatibility
  ruleId?: string;
  ruleName?: string;
  status?: 0 | 1;
  createDate?: string;
  lastUpdate?: string;
};

interface IAutoReplyFormState {
  // Form data
  formData: TAutoReplyFormData;
  originalFormData: TAutoReplyFormData | null; // Store original data for reset operations
  isNew: boolean;
  isDirty: boolean;
  validationErrors: Record<string, string>;

  // Form Actions
  initNewForm: () => void;
  loadExistingData: (data: TAutoReplyApiData) => void;
  updateField: <K extends keyof TAutoReplyFormData>(
    field: K,
    value: TAutoReplyFormData[K]
  ) => void;
  validateForm: () => boolean;
  resetForm: () => void;

  updateRules: (rules: IRoutingRule[]) => void;
}

export const useAutoReplyFormStore = create<IAutoReplyFormState>()(
  immer((set, get) => ({
    // State
    formData: { ...initialFormState },
    originalFormData: null,
    isNew: true,
    isDirty: false,
    validationErrors: {},

    // Form Actions
    initNewForm: () =>
      set((state) => {
        state.formData = { ...initialFormState };
        state.originalFormData = null;
        state.isNew = true;
        state.isDirty = false;
        state.validationErrors = {};
      }),

    loadExistingData: (data) =>
      set((state) => {
        // Map backend data to form data structure
        const mappedFormData = {
          ...initialFormState, // Start with initial state
          ruleId: data.id || data.ruleId || '',
          ruleName: data.name || data.ruleName || '',
          description: data.description || '',
          status:
            (data.isActive !== undefined ? data.isActive : data.status) === 1
              ? (1 as const)
              : (0 as const), // Convert to 0 or 1
          priority: data.priority || 0, // Add priority mapping
          channelType: (data.channelType?.toLowerCase() === 'whatsapp'
            ? 'WhatsApp'
            : data.channelType) as TChannelType, // Normalize to store type
          address: data.address || '', // Simplified to only use address from API data
          rules:
            data.rules?.map((rule: any) => ({
              ...rule,
              id: rule.id || `rule-${Date.now()}-${Math.random()}`, // Ensure ID exists for UI keys
              conditions: rule.conditions.map((condition: any) => ({
                ...condition,
                // Handle time values - convert from API format to UI format
                value:
                  condition.field === 'time' && Array.isArray(condition.value)
                    ? (() => {
                        console.log(
                          'Converting time values from API:',
                          condition.value
                        );
                        const convertedTimes = condition.value.map(
                          (timeStr: string) => {
                            // Convert from ISO time format (e.g., "10:24:47.430708Z") to HH:mm format
                            if (
                              timeStr.includes('T') ||
                              timeStr.includes('Z')
                            ) {
                              // Full ISO string - extract time part
                              const timeMatch =
                                timeStr.match(/(\d{2}):(\d{2})/);
                              return timeMatch
                                ? `${timeMatch[1]}:${timeMatch[2]}`
                                : timeStr;
                            } else if (timeStr.includes(':')) {
                              // Already in time format, just extract HH:mm
                              const timeMatch =
                                timeStr.match(/(\d{2}):(\d{2})/);
                              return timeMatch
                                ? `${timeMatch[1]}:${timeMatch[2]}`
                                : timeStr;
                            }
                            return timeStr;
                          }
                        );
                        const result = JSON.stringify(convertedTimes);
                        console.log(
                          'Converted time values to UI format:',
                          result
                        );
                        return result;
                      })()
                    : condition.value,
              })),
              // Ensure return object and its properties exist
              return: {
                queueId: rule.return?.queueId || '', // Assuming queueId might not always be present in auto-reply rules
                autoReplyId: rule.return?.autoReplyId || null,
                language: rule.return?.language || undefined,
                // For AutoReply rules, we should populate multiLanguageTemplates from autoReplyId
                // This allows proper template display and validation
                multiLanguageTemplates: rule.return?.autoReplyId
                  ? // Convert comma-separated autoReplyId to basic multiLanguageTemplates format
                    // The RoutingRuleEditor will handle the proper conversion with template data
                    rule.return.autoReplyId.includes(',')
                    ? // For comma-separated, create a basic structure
                      rule.return.autoReplyId.split(',').reduce(
                        (
                          acc: { [key: string]: string },
                          templateId: string,
                          index: number
                        ) => {
                          // Use default language keys for now, RoutingRuleEditor will correct this with actual template data
                          const defaultLanguages = ['en', 'zh_cn', 'zh_hk'];
                          const lang =
                            defaultLanguages[index] || `lang_${index}`;
                          acc[lang] = templateId.trim();
                          return acc;
                        },
                        {} as { [key: string]: string }
                      )
                    : // For single template, assume it's English by default
                      { en: rule.return.autoReplyId }
                  : null,
              },
            })) || [],
          createDate: data.createTime || data.createDate,
          lastUpdate: data.updateTime || data.lastUpdate,
        };

        state.formData = mappedFormData;
        // Store a deep copy of the original data for reset functionality
        state.originalFormData = JSON.parse(JSON.stringify(mappedFormData));
        state.isNew = false;
        state.isDirty = false;
        state.validationErrors = {};
      }),

    updateField: (field, value) =>
      set((state) => {
        state.formData[field] = value;
        state.isDirty = true;
        // Clear validation error for this field if exists
        if (state.validationErrors[field]) {
          delete state.validationErrors[field];
        }
      }),

    validateForm: () => {
      const { formData } = get();
      const errors: Record<string, string> = {};

      // Basic validation rules
      if (!formData.ruleName.trim()) {
        errors.ruleName = 'Rule Name is required';
      }

      if (!formData.description.trim()) {
        errors.description = 'Description is required';
      }

      // Priority validation
      if (formData.priority < 0) {
        errors.priority = 'Priority must be 0 or greater';
      }

      // Channel validation
      if (
        formData.channelType === 'WhatsApp' ||
        formData.channelType === 'SMS'
      ) {
        if (!formData.address.trim()) {
          errors.address = `${formData.channelType} Platform Account is required`;
        }
      }

      // Validate rules if they exist
      if (formData.rules && formData.rules.length > 0) {
        // Check for duplicate priorities (same logic as mailbox routing rules)
        const priorities = formData.rules.map((rule) => rule.priority);
        const duplicatePriorities = priorities.filter(
          (priority, index) => priorities.indexOf(priority) !== index
        );

        // Add specific errors for duplicate priorities
        if (duplicatePriorities.length > 0) {
          const uniqueDuplicates = [...new Set(duplicatePriorities)];
          uniqueDuplicates.forEach((duplicatePriority) => {
            const rulesWithSamePriority = formData.rules
              .map((rule, index) => ({ rule, index }))
              .filter(({ rule }) => rule.priority === duplicatePriority);

            rulesWithSamePriority.forEach(({ index }) => {
              errors[`rule${index}.priority`] =
                `Priority ${duplicatePriority} is used by multiple rules`;
            });
          });
        }

        // Validate individual rules
        formData.rules.forEach((rule, index) => {
          // Validate that each rule has at least one condition
          if (!rule.conditions || rule.conditions.length === 0) {
            errors[`rule${index}.conditions`] =
              `Rule #${index + 1} must have at least one condition`;
          }

          // Validate template selection - either single template or comma-separated template IDs
          const hasTemplateSelection =
            rule.return.autoReplyId &&
            rule.return.autoReplyId.trim().length > 0;

          if (!hasTemplateSelection) {
            errors[`rule${index}.template`] =
              'Each rule must have at least one template selected.';
          }

          // For AutoReply rules, check if multiLanguageTemplates is properly populated
          // This indicates that languages have been selected for the templates
          const hasLanguageSelection =
            rule.return.multiLanguageTemplates &&
            Object.keys(rule.return.multiLanguageTemplates).length > 0;

          if (!hasLanguageSelection) {
            errors[`rule${index}.language`] = 'Language is required.';
          }

          // Validate individual conditions within a rule
          rule.conditions.forEach((condition, cIndex) => {
            // Validate that each condition has a value
            if (!condition.value.trim()) {
              errors[`rule${index}.condition${cIndex}.value`] =
                `Value is required for condition ${cIndex + 1} in rule #${index + 1}`;
            }

            // Validate time format for time conditions
            if (condition.field === 'time') {
              try {
                const times = JSON.parse(condition.value);
                if (
                  !Array.isArray(times) ||
                  times.some(
                    (t) =>
                      typeof t !== 'string' ||
                      !t.match(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
                  )
                ) {
                  errors[`rule${index}.condition${cIndex}.value`] =
                    'Invalid time format. Please use HH:mm format.';
                }
                if (times.length >= 2 && condition.operator === 'within') {
                  const [time1, time2] = times;
                  const toMinutes = (time: string) => {
                    const [h, m] = time.split(':').map(Number);
                    return h * 60 + m;
                  };

                  if (toMinutes(time1) >= toMinutes(time2)) {
                    errors[`rule${index}.condition${cIndex}.value`] =
                      'End time must be later than start time';
                  }
                }
              } catch (e) {
                errors[`rule${index}.condition${cIndex}.value`] =
                  'Invalid time value format.';
              }
            }
          });
        });
      }

      set((state) => {
        state.validationErrors = errors;
      });

      return Object.keys(errors).length === 0;
    },

    resetForm: () =>
      set((state) => {
        if (state.isNew) {
          // Reset to initial state for new items
          state.formData = { ...initialFormState };
          state.originalFormData = null;
        } else if (state.originalFormData) {
          // Restore to original data for existing items
          state.formData = JSON.parse(JSON.stringify(state.originalFormData));
        }
        state.isDirty = false;
        state.validationErrors = {};
      }),

    updateRules: (rules) =>
      set((state) => {
        state.formData.rules = rules;
        state.isDirty = true;
        // Trigger validation after updating rules to catch priority conflicts
        // setTimeout(() => get().validateForm(), 0);
      }),
  }))
);
