import { <PERSON><PERSON>, toast, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useTabsContext } from "@cdss-modules/design-system"
import Icon from "@cdss-modules/design-system/components/_ui/Icon"
import Input from "@cdss-modules/design-system/components/_ui/Input"
import SearchInput from "@cdss-modules/design-system/components/_ui/SearchInput"
import { cn, extractErrorMessage } from "@cdss-modules/design-system/lib/utils"
import { useRef, useState } from "react"
import { DncCondition, TDnc } from "../DncList/DncList"
import { PopupModel } from "../DncPopup"
import { fireExportDnc, fireGetTemplate, fireImportDnc } from "@cdss/lib/api"
import { Upload } from "lucide-react"
import * as XLSX from 'xlsx';
export type TDncFilterProps = {
    onApplyFilter: (filterValue: any) => void;
    onClearFilter: () => void;
    onSave: () => Promise<any>;
    isEditMode: boolean;
    saveLoading: boolean;
    onCancel: () => void;
    editList: TDnc[];
    setUploadSuccess: (success: boolean) => void;
};
declare global {
    interface Window {
        showSaveFilePicker: (options?: {
            suggestedName?: string;
            types?: Array<{
                description?: string;
                accept: Record<string, string[]>;
            }>;
        }) => Promise<FileSystemFileHandle>;
    }
}
export const DncFilter = ({
    onApplyFilter,
    onClearFilter,
    onCancel,
    isEditMode,
    onSave,
    saveLoading,
    editList,
    setUploadSuccess,
}: TDncFilterProps) => {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const uploadRef = useRef<any>(null);
    const buttonRef = useRef<string>("");
    const [templateLoading, setTemplateLoading] = useState(false)
    const [importLoading, setImportLoading] = useState(false)
    const [exportLoading, setExportLoading] = useState(false)
    const [open, setOpen] = useState(false)
    const [isFooter, setIsFooter] = useState(true)
    const [popupChildren, setPopupChildren] = useState(<></>)

    const { basePath } = useRouteHandler();
    const {
        selected,
    } = useTabsContext();
    const handleImportTemplate = async () => {
        try {
            setTemplateLoading(true)
            const result = await fireGetTemplate(
                basePath,
                selected?.includes("dnc") ? "dnc" : "blacklist"
            );
            const blob = new Blob([result?.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const suggestedFileName = `${selected?.includes("dnc") ? "DNC" : "Blacklist"}_template.xlsx`;

            // 使用现代文件系统API
            if ('showSaveFilePicker' in window) {
                const handle = await window?.showSaveFilePicker({
                    suggestedName: suggestedFileName,
                    types: [{
                        description: 'Excel Files',
                        accept: { 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'] }
                    }]
                });
                const writable = await handle.createWritable();
                await writable.write(blob);
                await writable.close();
            } else {
                // 旧浏览器回退方案
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = suggestedFileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }
        catch (error) {
            if (error instanceof DOMException) {  // 修改错误类型判断
                if (error?.name === 'AbortError') {  // 检查特定错误类型
                    // 用户取消操作不视为错误
                    return;
                }
            }
            if (error instanceof Error) {
                if (error?.message === 'Request timeout') {
                    toast({
                        variant: 'error',
                        title: 'Error refreshing the web',
                        description: `Request timed out. Please try again.`,
                    });
                } else {
                    toast({
                        variant: 'error',
                        title: 'Error refreshing the web',
                        description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
                    });
                }
            }
        }
        finally {
            setTemplateLoading(false)
        }

    }
    const handleImport = async () => {
        try {
            const result = await fireImportDnc(
                uploadRef.current,
                selected?.includes("dnc") ? "dnc" : "blacklist",
                basePath,
            );
            if (result?.data?.isSuccess) {
                if (result?.data?.data?.failedCount == 0) {
                    setPopupChildren(<>
                        <p>Import data successfully</p>
                        <p>total：{result?.data?.data?.totalCount}</p>
                        {result?.data?.data?.successCount > 0 && <p>successCount：{result?.data?.data?.successCount}</p>}
                    </>)
                } else {
                    setPopupChildren(<>
                        <p>Import data error</p>
                        <p>total：{result?.data?.data?.totalCount}</p>
                        {result?.data?.data?.failedCount > 0 && <p>failedCount：{result?.data?.data?.failedCount}</p>}
                        <p>error detail:</p>
                        {result?.data?.data?.errors?.map((item: any, index: number) => {
                            return <p key={index}>{index + 1}. {item?.error}</p>
                        })}
                    </>)
                }
                setIsFooter(false)
                setOpen(true)
                setUploadSuccess(true)
            }

        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        } finally {
            buttonRef.current = ""
            setImportLoading(false)
        }
    }
    const handleEditMode = () => {
        onCancel()
    }
    const handleExport = async () => {
        try {
            setExportLoading(true)
            const result = await fireExportDnc(
                {
                    type: selected?.includes("dnc") ? "dnc" : "blacklist",
                },
                basePath,
            );
            const blob = new Blob([result?.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const suggestedFileName = `${selected?.includes("dnc") ? "DNC" : "Blacklist"}_data.xlsx`;

            // 使用现代文件系统API
            if ('showSaveFilePicker' in window) {
                const handle = await window?.showSaveFilePicker({
                    suggestedName: suggestedFileName,
                    types: [{
                        description: 'Excel Files',
                        accept: { 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'] }
                    }]
                });
                const writable = await handle.createWritable();
                await writable.write(blob);
                await writable.close();
            } else {
                // 旧浏览器回退方案
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = suggestedFileName;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                URL.revokeObjectURL(url);
            }
        }
        catch (error) {
            if (error instanceof DOMException) {  // 修改错误类型判断
                if (error?.name === 'AbortError') {  // 检查特定错误类型
                    // 用户取消操作不视为错误
                    return;
                }
            }
            if (error instanceof Error) {
                if (error?.message === 'Request timeout') {
                    toast({
                        variant: 'error',
                        title: 'Error refreshing the web',
                        description: `Request timed out. Please try again.`,
                    });
                } else {
                    toast({
                        variant: 'error',
                        title: 'Error refreshing the web',
                        description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
                    });
                }
            }
        }
        finally {
            setExportLoading(false)
        }
    }
    const popupFooter = isFooter ? <>
        <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => {
                setOpen(false);
                buttonRef.current = ""
            }}
            variant="blank"
            size="s"
        >
            No
        </Button>
        <Button
            className="self-center ml-2"
            bodyClassName="border border-black py-[0.375rem]"
            onClick={() => {

                if (buttonRef.current === "Discard") {
                    onCancel()
                }
                if (buttonRef.current === "Save") {
                    onSave().then((result) => {
                        if (result?.data?.data?.failedCount > 0) {
                            setPopupChildren(<>
                                <p>Import data error</p>
                                {<p>failedCount：{result?.data?.data?.failedCount}</p>}
                                <p>error detail:</p>
                                {result?.data?.data?.errors?.map((item: any, index: number) => {
                                    return <p key={index}>{index + 1}. {item?.error}</p>
                                })}
                            </>)
                            setIsFooter(false)
                            setOpen(true)
                        }
                    }).finally(() => {
                        buttonRef.current = ""
                    })
                }
                if (buttonRef.current === "Import") {
                    handleImport()
                }
                setOpen(false);
            }}
            size="s"
        >
            Yes
        </Button>
    </> : <></>

    return <>
        <div
            className={cn(
                `w-full py-2 px-3 bg-white rounded-xl inline-flex flex-row overflow-x-auto`
            )}
        >
            <div className="w-full grid grid-cols-3 gap-6">
                <div className="flex justify-center items-center">
                    {isEditMode && <Button
                        asSquare
                        variant="back"
                        onClick={() => { onCancel() }}
                        beforeIcon={<Icon name="back" />}
                    />}
                    <Input
                        ref={inputRef}
                        // value={inputRef?.current?.value}
                        isSearch={true}
                        afterIcon={<Icon name="search" />}
                        afterIconFn={() => { onApplyFilter(inputRef.current?.value) }}
                        onChange={(value) => { }}
                        allowClear
                        size='s'
                        placeholder='Search Contact No. or Customer Name'
                    />
                </div>

                {editList?.length > 0 && isEditMode && <div className="col-span-2 flex justify-end">
                    <Button
                        className="self-center ml-2"
                        bodyClassName="border border-black py-[0.375rem]"
                        onClick={() => {
                            buttonRef.current = "Discard"
                            setOpen(true)
                            setPopupChildren(<p>Are you sure to discard?</p>)
                            // onCancel()
                        }}
                        variant="blank"
                        size="s"
                        disabled={false}
                    >
                        Discard
                    </Button>
                    <Button
                        className="self-center ml-2"
                        bodyClassName="border border-black py-[0.375rem]"
                        onClick={() => {
                            buttonRef.current = "Save"
                            setOpen(true)
                            setPopupChildren(<p>Are you sure to save?</p>)
                            // onSave()
                        }}
                        size="s"
                        disabled={saveLoading}
                    >
                        Save
                    </Button>
                </div >}
                {!isEditMode && <div className="col-span-2 flex justify-end">
                    <Button
                        className="self-center ml-2"
                        bodyClassName="border border-black py-[0.375rem]"
                        onClick={() => {
                            handleImportTemplate()
                        }}
                        variant="blank"
                        size="s"
                        disabled={templateLoading}
                    >
                        Import Template
                    </Button>
                    <Button
                        type="button"
                        size="s"
                        className="self-center ml-2"
                        bodyClassName="border border-black py-[0.375rem]"
                        beforeIcon={
                            <Upload
                                size={16}
                                className="mr-2"
                            />
                        }
                        disabled={importLoading}
                        onClick={() => {
                            buttonRef.current = "Import"
                        }}
                    >
                        Import
                        <input
                            type="file"
                            className="absolute opacity-0 size-full left-0 top-0 z-20 cursor-pointer"
                            accept=".xlsx,.xls,.csv, text/csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"
                            onChange={(e) => {
                                buttonRef.current = "Import"
                                const files = e.target?.files;
                                if (files && files?.length > 0) {
                                    const selectedFile = files[0];
                                    const validTypes = [
                                        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
                                        'application/vnd.ms-excel', // xls
                                        'text/csv',
                                        'text/plain' // 部分CSV文件可能识别为此类型
                                    ];

                                    if (!validTypes?.includes(selectedFile.type)) {
                                        setPopupChildren(
                                            <>
                                                <p>Import data error:</p>
                                                <p>{selectedFile?.name?.split(".")?.pop()} format unspported</p>
                                            </>

                                        );
                                        buttonRef.current = ""
                                        setOpen(true);
                                        e.target.value = '';  // 清空选择
                                        return;
                                    }

                                    try {
                                        const reader = new FileReader();
                                        let jsonData: any[] = []
                                        let workbook: XLSX.WorkBook;
                                        reader.onload = (event) => {
                                            const data = new Uint8Array(event.target?.result as ArrayBuffer);

                                            workbook = XLSX.read(data, { type: 'array' });

                                            const sheetName = workbook.SheetNames[0];
                                            const worksheet = workbook.Sheets[sheetName];

                                            jsonData = XLSX.utils.sheet_to_json(worksheet);
                                            const formData = new FormData();
                                            formData.append('file', selectedFile);
                                            // 显示实际记录数
                                            setPopupChildren(
                                                <p>Are you sure to add {jsonData?.length} records to the DNC list from {selectedFile?.name}?</p>
                                            );
                                            setOpen(true);
                                            uploadRef.current = formData;
                                        };
                                        reader.readAsArrayBuffer(selectedFile);
                                    } catch (error) {
                                        uploadRef.current = null;
                                        toast({
                                            variant: 'error',
                                            title: 'File parse error',
                                            description: `Failed to read Excel file: ${extractErrorMessage(error)}`,
                                        });
                                    } finally {
                                        uploadRef.current = null;
                                        e.target.value = '';
                                    }
                                }
                            }}
                        />
                    </Button>
                    <Button
                        className="self-center ml-2"
                        bodyClassName="border border-black py-[0.375rem]"
                        onClick={() => {
                            handleEditMode()
                        }}
                        size="s"
                    >
                        Edit Mode
                    </Button>
                    <Button
                        className="self-center ml-2"
                        bodyClassName="border border-black py-[0.375rem]"
                        onClick={() => {
                            handleExport()
                        }}
                        size="s"
                        disabled={exportLoading}
                    >
                        Export
                    </Button>
                </div>}

            </div>
        </div>
        <PopupModel openPopup={open} children={popupChildren} onChang={(v) => { setIsFooter(true); setOpen(v) }} popupFooter={popupFooter} popupFooterClassName="pr-4 pb-4" />
    </>
}