import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, toast, <PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useTabsContext } from "@cdss-modules/design-system";
import { DataTable } from "@cdss-modules/design-system/components/_ui/DataTable";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from "@cdss-modules/design-system/components/_ui/DropdownMenu";
import { Condition } from "@cdss-modules/design-system/components/_ui/FilterComponent";
import Icon from "@cdss-modules/design-system/components/_ui/Icon";
import Pagination from "@cdss-modules/design-system/components/_ui/Pagination";
import { Select } from "@cdss-modules/design-system/components/_ui/Select";
import { GLOBAL_DATETIME_FORMAT } from "@cdss-modules/design-system/lib/constants";
import { cn, extractErrorMessage } from "@cdss-modules/design-system/lib/utils";
import { fireDeleteDnc, fireGetDnc } from "@cdss/lib/api";
import { ColumnDef, Table as TableType } from "@tanstack/react-table";
import dayjs from "dayjs";
import React from "react";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from 'react-i18next';

export type TDnc = {
    id?: string;
    contactNo?: string;
    customerName?: string;
    channelType?: string;
    customerId?: string;
    reason?: string;
    createAt?: string;
    updateAt?: string;
    isNew?: boolean;
};
export interface DncCondition extends Condition {
    sort?: boolean;
    edit?: boolean;
}
export type DncListProps = {
    cols: Record<string, DncCondition>;
    currentFilter?: any
    isEditMode: boolean;
    isUploadSuccess: boolean;
    isClickSearch: boolean;
    setEditList: (input: any) => void,
    setUploadSuccess: (v: boolean) => void,
    setIsClickSearch: (v: boolean) => void,
    editList: TDnc[],
    channelList: any[],
};
const generateColumns = (
    columnKeys: string[],
    columnLabels: string[],
    cols: Record<string, DncCondition>,
    sortOrder: any,
    setSortOrder: (input: any) => void,
    setEditRow: (input: any) => void,
    editRow: string[],
    setEditList: (input: any) => void,
    editList: TDnc[],
    isEditMode: boolean,
    onAdd: () => void,
    onDelete: (v: string[]) => void,
    setDncList: (v: any) => void,
    setOpen: (v: boolean) => void,
    open: boolean,
    deleteLoading: boolean,
    channelList: any[],
) => {
    const formattedColumns = columnKeys.map(
        (customColumn: string, index: number) => {
            var isDate = false
            if (customColumn === 'updateAt' || customColumn === 'createAt') {
                isDate = true;
            } else {
                isDate = false;
            }

            return {
                id: customColumn,
                accessorKey: customColumn,
                header: () => {
                    return (
                        // 如果 cols 包含 customColumn
                        cols?.[customColumn]?.sort ? (
                            <SortingButton
                                sorting={
                                    sortOrder?.[customColumn]
                                        ? sortOrder?.[customColumn] === 'ASC'
                                            ? 'asc'
                                            : 'desc'
                                        : false
                                }
                                onClick={async () => {
                                    const targetSortOrder =
                                        sortOrder?.[customColumn] === 'ASC' ? 'DESC' : 'ASC';
                                    setSortOrder({
                                        [customColumn]: targetSortOrder,
                                    });
                                }}
                            >
                                {columnLabels?.[index]}
                            </SortingButton>
                        ) : (
                            <div className="inline-flex items-center gap-x-2">
                                <div className="inline-flex">{columnLabels?.[index]}</div>
                            </div>
                        )
                    );
                },
                cell: ({ row }) => {                    
                    let val = row?.getValue(customColumn) as any;
                    if ((cols?.[customColumn]?.edit || row.original?.isNew) && editRow?.includes(row.original?.id as string) && customColumn !== 'createAt' && customColumn !== 'updateAt') {
                        const editItem = editList?.find(item => item?.id === row.original?.id);
                        return (<>
                            {customColumn == "channelType" ? <Select
                                placeholder="Please select"
                                mode="single"
                                options={channelList}
                                showSearch={false}
                                onChange={(value) => {
                                    setEditList((prev: any[]) => prev?.map(item =>
                                        item.id === row.original?.id
                                            ? { ...item, [customColumn]:value }
                                            : item
                                    ));
                                }}
                                isPagination={false}
                                value={editItem?.[customColumn as keyof TDnc] ?? val}
                                key={row.original?.id}
                            />:
                            <input
                                defaultValue={editItem?.[customColumn as keyof TDnc] ?? val}
                                className="border px-2 py-1 rounded"
                                onBlur={(e) => {  // 仅在失去焦点时更新状态
                                    setEditList((prev: any[]) => prev?.map(item =>
                                        item?.id === row.original?.id
                                            ? { ...item, [customColumn]: e.target.value }
                                            : item
                                    ));
                                }}
                                key={row.original?.id}
                            />}
                        </>

                        );
                    }
                    if (isDate && val) val = dayjs(val)?.format(GLOBAL_DATETIME_FORMAT);
                    return <div>{val != '' && val != undefined ? val : '——'}</div>;
                },
            } as ColumnDef<TDnc>;
        }
    );

    if (!isEditMode) {
        return [...formattedColumns]
    }
    return [...formattedColumns, {
        id: 'actions',
        header: () => {
            return (
                <>
                    <Tooltip
                        content={"add"}
                        trigger={
                            <button
                                className={cn(
                                    'flex items-center hover:text-primary',
                                    // showWrapup ? 'text-primary' : ''
                                )}
                                onClick={() => {
                                    onAdd()
                                }}
                            >
                                <Icon
                                    name="add"
                                    size={18}
                                    className="hover:text-primary fill-current"
                                />
                            </button>
                        }
                    />

                </>
            )
        },
        cell: ({ row }: { row: any }) => (
            <div className="flex gap-2">
                {editRow?.includes(row.original?.id as string) ? (
                    <>
                        <Tooltip
                            content={"cancel"}
                            trigger={
                                <button
                                    className={cn(
                                        'flex items-center hover:text-primary',
                                        // showWrapup ? 'text-primary' : ''
                                    )}
                                    onClick={() => {
                                        if (row.original?.isNew) {
                                            setDncList((prev: any[]) => prev?.filter(item => item?.id !== row.original?.id));
                                        }
                                        setEditRow(editRow?.filter(item => item !== row.original?.id))
                                        setEditList(editList?.filter(item => item?.id !== row.original?.id))
                                    }}
                                >
                                    <Icon
                                        name="add"
                                        size={18}
                                        className="hover:text-primary fill-current -rotate-45"
                                    />
                                </button>
                            }
                        />
                    </>
                ) : (
                    <Tooltip
                        content={"edit"}
                        trigger={
                            <button
                                className={cn(
                                    'flex items-center hover:text-primary',
                                    // showWrapup ? 'text-primary' : ''
                                )}
                                onClick={() => {
                                    setEditRow([...editRow, row.original?.id])
                                    setEditList([...editList, { ...row.original }]);
                                }}
                            >
                                <Icon
                                    name="edit"
                                    size={18}
                                    className="text-primary fill-current"
                                />
                            </button>
                        }
                    />

                )}
                {!row.original?.isNew && <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <button
                            className={cn(
                                'flex items-center hover:text-primary',
                                // showWrapup ? 'text-primary' : ''
                            )}
                            onClick={() => {
                            }}
                            disabled={deleteLoading}
                        >
                            <Icon
                                name="delete"
                                size={18}
                                className="hover:text-primary fill-current"
                            />
                        </button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuLabel><p className="text-sm">Are you sure to delete?</p></DropdownMenuLabel>
                        <DropdownMenuLabel
                        >
                            <div className="flex justify-end">
                                <Button
                                    className="self-center ml-2"
                                    bodyClassName="border border-black py-[0.375rem]"
                                    onClick={() => {
                                        setOpen(!open)
                                    }}
                                    variant="blank"
                                    size="m"
                                >
                                    No
                                </Button>
                                <Button
                                    className="self-center ml-2"
                                    bodyClassName="border border-black py-[0.375rem]"
                                    onClick={() => {
                                        onDelete([row.original?.id])
                                    }}
                                    size="m"
                                >
                                    Yes
                                </Button>
                            </div>
                        </DropdownMenuLabel>

                    </DropdownMenuContent>
                </DropdownMenu>}
            </div>
        )
    }];
};
export const DncList: React.FC<DncListProps> = React.memo(({ cols, currentFilter, isEditMode, setEditList, editList, isUploadSuccess, setUploadSuccess, channelList,isClickSearch,setIsClickSearch }) => {
    const {
        selected,
    } = useTabsContext();
    const [tableCols, setTableCols] =
        useState<Record<string, DncCondition>>(cols);
    const [sortOrder, setSortOrder] = useState<any>();
    const [rowSelection, setRowSelection] = useState({});
    // const [table, setTable] = useState<TableType<TDnc>>();
    const [editRow, setEditRow] = useState<any[]>([]);
    const [open, setOpen] = useState<boolean>(false);
    const { basePath } = useRouteHandler();
    const { t, i18n } = useTranslation();
    const showColumnLabels: string[] = [];
    const showColumnKeys: string[] = [];
    Object.entries(tableCols).forEach(([key, item]) => {
        showColumnKeys.push(key);
        const label = i18n.language == 'en' ? item.labelEn : item.labelCh;
        showColumnLabels.push(label);
    });

    const [currentPage, setCurrentPage] = useState<number>(1);
    const [perPage, setPerPage] = useState<number>(50);
    const [totalCount, setTotalCount] = useState<number>(0);
    const [loading, setLoading] = useState<boolean>(false);
    const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
    const [dncList, setDncList] = useState<TDnc[]>([]);
    const totalPages = Math.ceil(totalCount / perPage);
    const handleNext = () => {
        let tar = currentPage + 1;
        if (tar > totalPages) tar = totalPages;
        setCurrentPage(tar);
    };

    const handlePrevious = () => {
        let tar = currentPage - 1;
        if (tar < 1) tar = 1;
        setCurrentPage(tar);
    };
    const getDncListFunc = async () => {
        try {
            setLoading(true);
            const payload = Object.keys(tableCols).reduce<Record<string, any>>(
                (acc, key) => {
                    return acc;
                },
                {}
            );
            if (sortOrder) {
                payload.sort = sortOrder;
            }
            // 处理page的参数
            payload.page = currentPage;
            // 处理perPage的参数
            payload.pageSize = perPage;

            payload.keyword = currentFilter
            if (selected?.includes("dnc")) {
                payload.type = "dnc"
            } else if (selected?.includes("blacklist")) {
                payload.type = "blacklist"
            }
            const result = await fireGetDnc(
                payload,
                basePath,
            );
            if (!result?.data?.isSuccess && result?.data?.error) {
                toast({
                    variant: 'error',
                    title: 'Error refreshing the web',
                    description: `get ${selected?.includes("dnc") ? "dnc" : "blacklist"} list failed: ${result?.data?.error}. If problem persists, please re-login.`,
                });
            } else {
                setIsClickSearch(false)
                setDncList(result?.data?.data?.data);
                setTotalCount(result?.data?.data?.total);
            }
        }
        catch (error) {
            if (error instanceof Error) {
                if (error.message === 'Request timeout') {
                    toast({
                        variant: 'error',
                        title: 'Error refreshing the web',
                        description: `Request timed out. Please try again.`,
                    });
                } else {
                    toast({
                        variant: 'error',
                        title: 'Error refreshing the web',
                        description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
                    });
                }
            }
        }
        finally {
            setIsClickSearch(false)
            setLoading(false);
            setUploadSuccess(false)
        }
    }

    useEffect(() => {
        const fetchData = async () => {
            if (!isEditMode || isUploadSuccess) {
                await getDncListFunc();
            }
        };
        fetchData();
    }, [currentPage, perPage, sortOrder, currentFilter, isEditMode, isUploadSuccess]);
    useEffect(() => {
        setTableCols(cols);
        setCurrentPage(1);
    }, [cols]);
    useEffect(() => {
        if(isClickSearch){
            setCurrentPage(1);
        }
    }, [isClickSearch]);
    useEffect(() => {
        setDncList((prev: any[]) => prev?.filter(item => !item?.isNew));
        setEditRow([])
        setEditList([])
    }, [isEditMode]);
    const onAdd = () => {
        // 生成唯一ID（这里用时间戳简单实现）
        const newId = Date.now().toString();

        // 创建新行数据
        const newRow: TDnc = {
            id: newId,
            contactNo: '',
            customerName: '',
            channelType: '',
            reason: '',
            isNew: true,
            // 其他字段根据需求初始化
        };

        setDncList(prev => [newRow, ...prev]);
        setEditList((prev: any) => [newRow, ...prev]);
        setEditRow((prev: any) => [...prev, newId]);
    }
    const onDelete = async (v: string[]) => {
        try {
            setDeleteLoading(true)
            const result = await fireDeleteDnc(
                v,
                basePath,
            );
            if (result?.data?.isSuccess) {
                toast({
                    variant: 'success',
                    title: `delete ${selected?.includes("dnc") ? "dnc" : "blacklist"} record`,
                    description: `delete ${selected?.includes("dnc") ? "dnc" : "blacklist"}(${v?.join(",")}) record successfully`,
                });
                getDncListFunc()
            } else {
                toast({
                    variant: 'error',
                    title: `delete ${selected?.includes("dnc") ? "dnc" : "blacklist"} record`,
                    description: `These is an error: ${extractErrorMessage(result?.data?.error)}.please try again.`,
                });
                alert('Delete failed:' + result?.data?.error);
            }
        }
        catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        }
        finally {
            setDeleteLoading(false);
        }
    }
    return <div
        className={cn(
            'px-4 pt-1 pb-6 flex flex-col h-full gap-y-4 overflow-auto'
        )}
    >
        <div className="flex-1 h-0">
            <DataTable<TDnc>
                data={dncList}
                columns={
                    generateColumns(
                        showColumnKeys,
                        showColumnLabels,
                        tableCols,
                        sortOrder,
                        (input) => {
                            setSortOrder(input);
                            setCurrentPage(1);
                        },
                        setEditRow,
                        editRow,
                        setEditList,
                        editList,
                        isEditMode,
                        onAdd,
                        onDelete,
                        setDncList,
                        setOpen,
                        open,
                        deleteLoading,
                        channelList
                    ) as any
                }
                loading={loading}
                emptyMessage="No data found"
                // error={error?.message}
                rowSelection={rowSelection}
                setRowSelection={setRowSelection}
                onClickRow={(row) => {
                    // console.log(row);
                }}
                onTableSetUp={(table) => { }}
                resize={true}
            />
        </div>
        {totalPages > 0 && (
            <section className="flex-row">
                <div>
                    <Pagination
                        current={currentPage}
                        perPage={perPage}
                        total={totalPages}
                        totalCount={totalCount}
                        onChange={(v) => setCurrentPage(v)}
                        handleOnPrevious={() => handlePrevious()}
                        handleOnNext={() => handleNext()}
                        handlePerPageSetter={(p: number) => {
                            const pageSize = Number(p);
                            if (!isNaN(pageSize)) {
                                setPerPage(pageSize);
                            }
                            setCurrentPage(1);
                        }}
                    />
                </div>
            </section>
        )}
    </div>
})
DncList.displayName = 'DncList';