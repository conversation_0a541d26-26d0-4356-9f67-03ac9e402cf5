import React, { useEffect, useState } from 'react';
import ListSelect from './listSelect';
import {
  Layout,
  Settings,
  Users,
  Maximize2,
  Minimize2,
  Save,
} from 'lucide-react';
import MemberManagement, { Member } from './agentManagement';
import { mockMembers } from './userListData';
import {
  GetQueues,
  GetTenantConfig,
  GetUserList,
  UpdateTenantConfig,
} from '@cdss/lib/api';
import { Loader, toast, useRouteHandler } from '@cdss-modules/design-system';
import { TAdminUserDataResp } from '@cdss/types/microfrontendsConfig';
export const AdminSuperDashboardComponent = () => {
  const { basePath } = useRouteHandler();
  const initConfig = {
    session: 'adminSuperDashboad',
    data: {
      members: [],
      queueOrder: [],
      dashboardOrder: [],
    },
  };
  const [tenantConfig, setTenantConfig] = useState<any>(initConfig);
  const [queues, setQueues] = useState<any>([]);
  const [isExpanded, setIsExpanded] = useState(false);
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(false);
  useEffect(() => {
    fetchTenantConfig();
    fetchQueues();
    fetchUserList();
  }, []);

  const fetchTenantConfig = async () => {
    const result = await GetTenantConfig(basePath)
      .catch(async (e) => {
        // console.error(e);
        console.error(e.response.status); // handle 404 issue
        if (e.response.status == 404) {
          console.log('NO ANY Configuration, Please Go To Setup');
          await UpdateTenantConfig(basePath, initConfig);
        }
        // console.log('config init');
      })
      .finally(() => {
        setLoading(false);
      });

    if (result) {
      console.log('fetch tenant config:', result.data.data);
      const config = result.data.data;
      setTenantConfig({ ...initConfig, data: config });
    }
  };

  const fetchQueues = async () => {
    const result = await GetQueues(basePath);
    if (result) {
      // console.log('fetch queues:', result.data.data);
      setQueues(result.data.data);
    }
  };

  const fetchUserList = async () => {
    const result = await GetUserList(basePath);
    const respData: TAdminUserDataResp = result.data;
    //console.log(respData.data)

    const memberList = respData.data.map((item, index) => ({
      id: item.id,
      name: item.name,
      isActive: true,
      avatar: '',
      position: index,
    }));
    setMembers(memberList as Member[]);
    //console.log(memberList);
  };

  const updateTenantConfig = async (requestData: any) => {
    setLoading(true);
    const response = await UpdateTenantConfig(basePath, requestData)
      .catch((e) => {
        toast({
          variant: 'error',
          title: 'Tenant Config Saved fail',
          description: `Update tenant configuration was failed!`,
        });
      })
      .finally(() => {
        setLoading(false);
      });
    if (response && response.data.isSuccess) {
      toast({
        variant: 'success',
        title: 'Tenant Config Saved',
        description: `Update tenant configuration was saved successfully!`,
      });
    }
  };

  const handleMembersSave = async (members: any[]) => {
    console.log('Saved members:', members);
    const requestData = {
      ...tenantConfig,
      data: {
        ...tenantConfig.data,
        members: members,
      },
    };
    setTenantConfig(requestData);
    updateTenantConfig(requestData);
  };

  const handleDashboardOrdering = (data: any) => {
    console.log('Dashboard ordering:', data);
    const requestData = {
      ...tenantConfig,
      data: {
        ...tenantConfig.data,
        dashboardOrder: data,
      },
    };
    setTenantConfig(requestData);
    updateTenantConfig(requestData);
  };

  const handleQueueOrdering = (data: any) => {
    console.log('Dashboard ordering:', data);
    const requestData = {
      ...tenantConfig,
      data: {
        ...tenantConfig.data,
        queueOrder: data,
      },
    };
    setTenantConfig(requestData);
    updateTenantConfig(requestData);
  };

  const handleGlobalSetting = (data: any) => {
    console.log('Global setting:', data);
    const requestData = {
      ...tenantConfig,
      data: {
        ...tenantConfig.data,
        globalSetting: data,
      },
    };
    setTenantConfig(requestData);
    updateTenantConfig(requestData);
  };

  useEffect(() => {
    console.log('config changed');
    //console.log(tenantConfig);
  }, [tenantConfig]);

  return (
    // 移除 min-h-screen，添加 min-h-0 确保内容可以超出视窗
    <div className="flex flex-col bg-gradient-to-br from-gray-50 to-gray-100 overflow-auto">
      {loading && (
        <div
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 999,
          }}
        >
          <Loader size={100} />
        </div>
      )}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* 头部区域 - 固定高度 */}
      <div className="bg-white shadow-sm flex-none">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center gap-3">
              <Settings className="h-8 w-8 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">
                Admin Super Dashboard
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 - 允许滚动 */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="space-y-8">
            <div
              className={`bg-white rounded-2xl shadow-lg overflow-hidden transition-all duration-200 ${
                isExpanded ? 'fixed inset-4 z-50' : ''
              }`}
            >
              <div className="px-6 py-4 border-b border-gray-100 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-blue-600" />
                  <h2 className="text-lg font-semibold text-gray-900">
                    Team Management
                  </h2>
                </div>
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="p-2 hover:bg-gray-100 rounded-full transition-colors duration-200"
                >
                  {isExpanded ? (
                    <Minimize2 className="h-5 w-5 text-gray-600" />
                  ) : (
                    <Maximize2 className="h-5 w-5 text-gray-600" />
                  )}
                </button>
              </div>
              <div
                className={'overflow-auto'}
                style={{
                  maxHeight: isExpanded ? 'calc(100% - 70px)' : '30rem',
                }}
              >
                <div className="p-6">
                  {/* {console.log(tenantConfig.data.members.length === 0)} */}
                  <MemberManagement
                    initialData={
                      tenantConfig.data.members.length === 0
                        ? members
                        : tenantConfig.data.members
                    }
                    onSave={handleMembersSave}
                  />
                </div>
              </div>
            </div>

            {!isExpanded && (
              <>
                <form
                  onSubmit={(e) => {
                    e.preventDefault(); // 阻止表单默认提交行为
                    const formData = new FormData(e.target as HTMLFormElement);
                    const data = {
                      swiperSpeed: formData.get('swiperSpeed'),
                      queueNum: formData.get('queueNum'),
                      agentsNum: formData.get('agentsNum'),
                      agentsRowNum: formData.get('agentsRowNum'),
                    };
                    // 验证所有字段
                    for (const [key, value] of Object.entries(data)) {
                      // 检查是否为空
                      if (!value || value.toString().trim() === '') {
                        alert(`请输入${key}的值`);
                        return;
                      }

                      // 检查是否为数字
                      if (isNaN(Number(value))) {
                        alert(`${key}必须是数字`);
                        return;
                      }
                    }
                    console.log(data);
                    handleGlobalSetting(data);
                  }}
                >
                  <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div className="px-6 py-4 border-b border-gray-100 flex items-center gap-3">
                      <Layout className="h-5 w-5 text-blue-600" />
                      <h2 className="text-lg font-semibold text-gray-900">
                        Global Setting
                      </h2>
                      <div className="flex flex-1 justify-end">
                        <button
                          type="submit"
                          className="inline-flex items-center gap-2 px-4 py-2 bg-orange-500 text-white text-sm font-medium rounded-lg hover:shadow-lg transition-colors duration-200"
                        >
                          <Save size={16} />
                          Save Changes
                        </button>
                      </div>
                    </div>
                    <div className="p-6 flex justify-items-center">
                      <div className="mr-5">
                        Slider Speed:
                        <input
                          id="swiperSpeed"
                          name="swiperSpeed"
                          type="number"
                          min="1"
                          max="100"
                          step="1"
                          defaultValue={
                            tenantConfig.data.globalSetting?.swiperSpeed
                          }
                          // value={tenantConfig.data.globalSetting?.swiperSpeed}
                          onChange={(e) => {
                            console.log(e);
                          }}
                          className="ml-2 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:shadow"
                        />
                      </div>
                      <div className="flex-1">
                        Number of queues / page:
                        <input
                          id="queueNum"
                          name="queueNum"
                          type="number"
                          min="1"
                          max="8"
                          step="1"
                          defaultValue={
                            tenantConfig.data.globalSetting?.queueNum
                          }
                          // value={tenantConfig.data.globalSetting?.queueNum}
                          onChange={(e) => {
                            console.log(e);
                          }}
                          className="ml-2 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:shadow"
                        />
                      </div>
                      <div className="flex-1">
                        Number of agents / row:
                        <input
                          id="agentsNum"
                          name="agentsNum"
                          type="number"
                          min="1"
                          max="9"
                          step="1"
                          defaultValue={
                            tenantConfig.data.globalSetting?.agentsNum
                          }
                          // value={tenantConfig.data.globalSetting?.agentsNum}
                          onChange={(e) => {
                            console.log(e);
                          }}
                          className="ml-2 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:shadow"
                        />
                      </div>
                      <div className="flex-1">
                        Number of agent rows / page:
                        <input
                          id="agentsRowNum"
                          name="agentsRowNum"
                          type="number"
                          min="1"
                          max="5"
                          step="1"
                          defaultValue={
                            tenantConfig.data.globalSetting?.agentsRowNum
                          }
                          // value={tenantConfig.data.globalSetting?.agentsRowNum}
                          onChange={(e) => {
                            console.log(e);
                          }}
                          className="ml-2 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent hover:shadow"
                        />
                      </div>
                    </div>
                  </div>
                </form>
                {/* Dashboard Ordering Section */}
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-100 flex items-center gap-3">
                    <Layout className="h-5 w-5 text-blue-600" />
                    <h2 className="text-lg font-semibold text-gray-900">
                      Dashboard Ordering
                    </h2>
                  </div>
                  <div className="p-6">
                    <ListSelect
                      onChange={handleDashboardOrdering}
                      data={[
                        { id: 1, name: 'QueuesDashboard' },
                        { id: 2, name: 'AgentsDashboard' },
                      ]}
                      restoreSelectedItems={tenantConfig.data.dashboardOrder}
                    />
                  </div>
                </div>

                {/* Queues Ordering Section */}
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-100 flex items-center gap-3">
                    <Layout className="h-5 w-5 text-blue-600" />
                    <h2 className="text-lg font-semibold text-gray-900">
                      Queue Ordering
                    </h2>
                  </div>
                  <div className="p-6">
                    <ListSelect
                      onChange={handleQueueOrdering}
                      data={queues}
                      restoreSelectedItems={tenantConfig.data.queueOrder}
                    />
                  </div>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSuperDashboardComponent;
