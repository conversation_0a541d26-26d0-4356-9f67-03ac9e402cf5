import React, { useEffect, useState } from 'react';
import { <PERSON>ripVertical, <PERSON>Up, ArrowDown, Save, Search } from 'lucide-react';

interface ListSelectProps {
  onChange: (result: any) => void;
  data: Array<{ id: number; name: string }>;
  restoreSelectedItems: [];
}

const ListSelect = ({
  onChange,
  data,
  restoreSelectedItems,
}: ListSelectProps) => {
  const [sourceItems, setSourceItems] = useState<any>(data);
  const [selectedItems, setSelectedItems] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  // useEffect(() => {
  //   //setSourceItems(data);
  // }, [data]);

  useEffect(() => {
    if (restoreSelectedItems || (restoreSelectedItems as any[])?.length > 0) {
      setSelectedItems(restoreSelectedItems);
      const selectedIds = new Set(
        restoreSelectedItems.map((item: any) => item.id)
      );
      const filteredSourceItems = data.filter(
        (item) => !selectedIds.has(item.id)
      );
      setSourceItems(filteredSourceItems);
    }
  }, [restoreSelectedItems, data]);

  const filteredSourceItems = sourceItems.filter((item: any) =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (item: any) => {
    setSourceItems(sourceItems.filter((i: any) => i.id !== item.id));
    setSelectedItems([...selectedItems, item]);
  };

  const moveUp = (index: number) => {
    if (index === 0) return;
    const newItems = [...selectedItems];
    [newItems[index], newItems[index - 1]] = [
      newItems[index - 1],
      newItems[index],
    ];
    setSelectedItems(newItems);
  };

  const moveDown = (index: number) => {
    if (index === selectedItems.length - 1) return;
    const newItems = [...selectedItems];
    [newItems[index], newItems[index + 1]] = [
      newItems[index + 1],
      newItems[index],
    ];
    setSelectedItems(newItems);
  };

  const handleRemove = (item: any) => {
    setSelectedItems(selectedItems.filter((i) => i.id !== item.id));
    setSourceItems([...sourceItems, item]);
  };

  return (
    <div className="bg-gray-50 rounded-xl p-6 overflow-auto">
      {/* 保存按钮 */}
      <div className="flex justify-end">
        <button
          onClick={() => onChange(selectedItems)}
          className="inline-flex items-center gap-2 px-4 py-2 bg-orange-500 text-white text-sm font-medium rounded-lg hover:shadow-lg transition-colors duration-200"
        >
          <Save size={16} />
          Save Changes
        </button>
      </div>
      <div className="flex flex-col md:flex-row md:gap-8">
        {/* 左侧列表 */}
        <div className="md:w-1/2">
          <h2 className="text-sm font-medium text-gray-500 mb-3">可选项目</h2>
          {/* 搜索框 */}
          <div className="relative mb-4">
            <input
              type="text"
              placeholder="搜索项目..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 pl-9 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <Search
              size={16}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            />
          </div>
          <div className="space-y-2">
            {filteredSourceItems.map((item: any) => (
              <div
                key={item.id}
                onClick={() => handleSelect(item)}
                className="group px-4 py-3 bg-white rounded-lg cursor-pointer border border-gray-200 transition-all duration-200 hover:border-blue-400 hover:shadow-sm"
              >
                <div className="flex items-center justify-between">
                  <span className="text-gray-700 group-hover:text-blue-600">
                    {item.name}
                  </span>
                  <span className="text-blue-500 opacity-0 group-hover:opacity-100 transition-all duration-200">
                    选择 →
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 右侧列表 */}
        <div className="md:w-1/2 mt-6 md:mt-0">
          <h2 className="text-sm font-medium text-gray-500 mb-3">已选项目</h2>
          <div className="space-y-2">
            {selectedItems.map((item, index) => (
              <div
                key={item.id}
                className="group px-4 py-3 bg-white rounded-lg border border-gray-200 transition-all duration-200 hover:border-blue-400 hover:shadow-sm"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <GripVertical
                      className="text-gray-400"
                      size={16}
                    />
                    <span className="text-gray-700">{item.name}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <button
                      onClick={() => moveUp(index)}
                      disabled={index === 0}
                      className="p-1.5 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ArrowUp
                        size={14}
                        className="text-gray-600"
                      />
                    </button>
                    <button
                      onClick={() => moveDown(index)}
                      disabled={index === selectedItems.length - 1}
                      className="p-1.5 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <ArrowDown
                        size={14}
                        className="text-gray-600"
                      />
                    </button>
                    <button
                      onClick={() => handleRemove(item)}
                      className="p-1.5 rounded hover:bg-red-50 text-gray-400 hover:text-red-500"
                    >
                      ×
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListSelect;
