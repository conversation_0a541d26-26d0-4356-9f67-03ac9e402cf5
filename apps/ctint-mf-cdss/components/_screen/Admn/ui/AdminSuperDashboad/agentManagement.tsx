import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Pencil,
  Trash2,
  X,
  Check,
  Image as ImageIcon,
  MoveUp,
  MoveDown,
} from 'lucide-react';
import { GetImage, UploadAvatar } from '@cdss/lib/api';
import { useRouteHandler } from '@cdss-modules/design-system';
import { apiConfig } from '@cdss/lib/api/config';

export interface Member {
  id: string;
  isActive: boolean;
  name: string;
  avatar: string;
  position: number;
}

interface MemberManagementProps {
  initialData?: Member[];
  onSave: (data: Member[]) => void;
}

const MemberManagement: React.FC<MemberManagementProps> = ({
  initialData = [],
  onSave,
}) => {
  const { basePath } = useRouteHandler();

  const [members, setMembers] = useState<Member[]>(
    [...initialData].sort((a, b) => a.position - b.position)
  );
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newMember, setNewMember] = useState<Partial<Member> | null>(null);
  const [draggedId, setDraggedId] = useState<string | null>(null);

  useEffect(() => {
    setMembers(initialData);
  }, [initialData]);
  // 处理头像上传
  const handleAvatarUpload = async (
    e: React.ChangeEvent<HTMLInputElement>,
    member: Partial<Member>
  ) => {
    const file = e.target.files?.[0];
    if (file) {
      const fileName = file?.name;
      console.log(fileName);
      try {
        const formData = new FormData();
        formData.append('image', file);
        const res = await UploadAvatar(basePath, formData);
        if (res.status === 200 && res.data.isSuccess) {
          console.log('Upload image success:', res.data);
          if (member.id) {
            setMembers(
              members.map((m) =>
                m.id === member.id ? { ...m, avatar: fileName } : m
              )
            );
          }
        }
      } catch (error) {
        console.error('Upload error:', error);
      }
      //return;
      // const reader = new FileReader();
      // reader.onloadend = () => {
      //   const base64String = reader.result as string;
      //   console.log(base64String);
      //   if (member.id) {
      // setMembers(
      //   members.map((m) =>
      //     m.id === member.id ? { ...m, avatar: base64String } : m
      //   )
      // );
      //   } else {
      //     setNewMember({ ...member, avatar: base64String });
      //   }
      // };
      // reader.readAsDataURL(file);
    }

    // try {
    // const formData = new FormData();
    // formData.append('image', file);
    // formData.append('memberId', member.id || '');
    // const data = await GetImage(basePath).then((res) => {
    //   //console.log(res.data);
    //   const url = URL.createObjectURL(res.data);
    //   // console.log("url");
    //   console.log(url);
    //   setMembers(
    //     members.map((m) =>
    //       m.id === member.id ? { ...m, avatar: url } : m
    //     )
    //   );
    // });
    // console.log(basePath+apiConfig.paths.getImage+"test.png");
    //http://localhost:4400/ctint/mf-cdss/api/process-api/ctint-user/images/test.png
    //`${basePath}${apiConfig.paths.getImage
    //console.log(data);

    // const res = await UploadAvatar(basePath, formData);
    // if(res.status === 200 && res.data.isSuccess){
    //   console.log('Upload success:', res.data);
    // }

    // 这里可以更新用户界面或状态
    // } catch (error) {
    //   console.error('Upload error:', error);
    //   // 处理错误情况
    // }
  };

  // 移动成员位置
  const moveMember = (fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= members.length) return;

    const newMembers = [...members];
    const [movedMember] = newMembers.splice(fromIndex, 1);
    newMembers.splice(toIndex, 0, movedMember);

    // 更新所有成员的位置
    const updatedMembers = newMembers.map((member, index) => ({
      ...member,
      position: index,
    }));

    setMembers(updatedMembers);
  };

  // 处理拖拽开始
  const handleDragStart = (e: React.DragEvent<HTMLDivElement>, id: string) => {
    setDraggedId(id);
    e.currentTarget.classList.add('opacity-50');
  };

  // 处理拖拽结束
  const handleDragEnd = (e: React.DragEvent<HTMLDivElement>) => {
    setDraggedId(null);
    e.currentTarget.classList.remove('opacity-50');
  };

  // 处理拖拽悬停
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
  };

  // 处理拖拽放下
  const handleDrop = (e: React.DragEvent<HTMLDivElement>, targetId: string) => {
    e.preventDefault();
    if (!draggedId || draggedId === targetId) return;

    const draggedIndex = members.findIndex((m) => m.id === draggedId);
    const targetIndex = members.findIndex((m) => m.id === targetId);

    if (draggedIndex === -1 || targetIndex === -1) return;

    const newMembers = [...members];
    const [draggedMember] = newMembers.splice(draggedIndex, 1);
    newMembers.splice(targetIndex, 0, draggedMember);

    // 更新所有成员的位置
    const updatedMembers = newMembers.map((member, index) => ({
      ...member,
      position: index,
    }));

    setMembers(updatedMembers);
  };

  const addBlankWorkstation = () => {
    console.log(members);

    setMembers([
      {
        isActive: true,
        name: '',
        avatar: '',
        position: members.length,
        id: generateUUID(),
      },
      ...members,
    ]);
  };

  // 渲染头像
  const renderAvatar = (member: Partial<Member>, isEditing = false) => (
    <div className="relative group w-12 h-12">
      <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-200">
        {member.avatar ? (
          <img
            src={basePath + apiConfig.paths.getImage + member.avatar}
            alt={member.name || 'avatar'}
            className="w-full h-full object-cover"
          />
        ) : (
          <ImageIcon className="w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-gray-400" />
        )}
      </div>
      {isEditing && (
        <label className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 cursor-pointer rounded-full transition-opacity">
          <input
            type="file"
            accept="image/*"
            onChange={(e) => handleAvatarUpload(e, member)}
            className="hidden"
          />
          <ImageIcon className="w-5 h-5 text-white" />
        </label>
      )}
    </div>
  );

  // 渲染编辑表单
  const renderEditForm = (member: Member) => (
    <div className="flex items-center gap-4 p-4 bg-blue-50 rounded-lg">
      {renderAvatar(member, true)}
      <input
        type="text"
        value={member.name}
        disabled
        onChange={(e) =>
          setMembers(
            members.map((m) =>
              m.id === member.id ? { ...m, name: e.target.value } : m
            )
          )
        }
        className="flex-1 px-3 py-2 border rounded-md"
      />
      <label className="flex items-center gap-2">
        <input
          type="checkbox"
          checked={member.isActive}
          onChange={(e) =>
            setMembers(
              members.map((m) =>
                m.id === member.id ? { ...m, isActive: e.target.checked } : m
              )
            )
          }
          className="w-4 h-4 rounded text-blue-600"
        />
        <span className="text-sm text-gray-600">Active</span>
      </label>
      <div className="flex gap-2">
        <button
          onClick={() => setEditingId(null)}
          className="p-2 text-gray-600 hover:text-gray-800 rounded-full hover:bg-gray-100"
        >
          <X size={16} />
        </button>
      </div>
    </div>
  );

  const generateUUID = () => {
    const timestamp = Date.now();
    return (
      'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (timestamp + Math.random() * 16) % 16 | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }) + '-blank'
    );
  };

  // 渲染新成员表单
  const renderNewForm = () => {
    if (!newMember) return null;
    return (
      <div className="flex items-center gap-4 p-4 bg-green-50 rounded-lg">
        {renderAvatar(newMember, true)}
        <span>Agent Name:</span>
        <input
          type="text"
          value={newMember.name || ''}
          onChange={(e) => setNewMember({ ...newMember, name: e.target.value })}
          placeholder="Enter name"
          className="flex-1 px-3 py-2 border rounded-md"
        />

        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={newMember.isActive}
            onChange={(e) =>
              setNewMember({ ...newMember, isActive: e.target.checked })
            }
            className="w-4 h-4 rounded text-blue-600"
          />
          <span className="text-sm text-gray-600">Active</span>
        </label>
        <div className="flex gap-2">
          <button
            onClick={() => setNewMember(null)}
            className="p-2 text-gray-600 hover:text-gray-800 rounded-full hover:bg-gray-100"
          >
            <X size={16} />
          </button>
          <button
            onClick={() => {
              if (newMember.name) {
                const newMemberComplete = {
                  ...newMember,
                  id: Date.now().toString(),
                  position: members.length,
                } as Member;
                setMembers([...members, newMemberComplete]);
                setNewMember(null);
              }
            }}
            className="p-2 text-green-600 hover:text-green-800 rounded-full hover:bg-green-100"
          >
            <Check size={16} />
          </button>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {/* 添加新成员按钮 */}
      <div className="flex text-right justify-end">
        <div className="flex justify-end mr-3">
          <button
            onClick={
              () => addBlankWorkstation()
              // setNewMember({
              //   isActive: true,
              //   name: '',
              //   avatar: '',
              //   position: members.length,
              // })
            }
            disabled={!!newMember}
            className="inline-flex items-center gap-2 px-4 py-2 bg-teal-400 text-white text-sm font-medium rounded-lg hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
          >
            <PlusCircle size={16} />
            Add Blank Workstation
          </button>
        </div>
        {/* 保存按钮 */}
        {members.length >= 0 && (
          <div className="flex justify-end">
            <button
              onClick={() => onSave(members)}
              className="inline-flex items-center gap-2 px-4 py-2 bg-orange-500 text-white text-sm font-medium rounded-lg hover:shadow-lg transition-colors duration-200"
            >
              <Check size={16} />
              Save Changes
            </button>
          </div>
        )}
      </div>

      {/* 新成员表单 */}
      {newMember && renderNewForm()}

      {/* 成员列表 */}
      <div className="space-y-2">
        {members.map((member, index) => (
          <div key={member.id}>
            {editingId === member.id ? (
              renderEditForm(member)
            ) : (
              <div
                draggable
                onDragStart={(e) => handleDragStart(e, member.id)}
                onDragEnd={handleDragEnd}
                onDragOver={handleDragOver}
                onDrop={(e) => handleDrop(e, member.id)}
                className="flex items-center gap-4 p-4 bg-white hover:bg-gray-50 rounded-lg group border border-gray-200 cursor-move"
              >
                {renderAvatar(member)}
                <span className="flex-1 font-medium text-gray-900">
                  {member.name != '' ? member.name : 'Blank Workstation'}
                </span>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    member.isActive
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {member.isActive ? 'Active' : 'Inactive'}
                </span>
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => moveMember(index, index - 1)}
                    disabled={index === 0}
                    className="p-2 text-gray-600 hover:text-gray-800 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <MoveUp size={16} />
                  </button>
                  <button
                    onClick={() => moveMember(index, index + 1)}
                    disabled={index === members.length - 1}
                    className="p-2 text-gray-600 hover:text-gray-800 rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <MoveDown size={16} />
                  </button>
                  <button
                    onClick={() => setEditingId(member.id)}
                    className="p-2 text-blue-600 hover:text-blue-800 rounded-full hover:bg-blue-100"
                  >
                    <Pencil size={16} />
                  </button>

                  {member.id.includes('blank') && (
                    <button
                      onClick={() =>
                        setMembers(members.filter((m) => m.id !== member.id))
                      }
                      className="p-2 text-red-600 hover:text-red-800 rounded-full hover:bg-red-100"
                    >
                      <Trash2 size={16} />
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default MemberManagement;
