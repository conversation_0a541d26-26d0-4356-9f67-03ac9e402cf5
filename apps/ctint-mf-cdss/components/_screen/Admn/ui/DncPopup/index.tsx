import { Popup, PopupContent, <PERSON>upFooter, PopupTitle } from "@cdss-modules/design-system"
import React from "react"

type TPopup = {
    openPopup: boolean,
    onChang: (b: boolean) => void
    children?: React.ReactNode
    className?: string,
    contentClassName?: string
    popupFooterClassName?: string
    title?: any,
    popupFooter?: React.ReactNode
}
export const PopupModel = React.memo((props: TPopup) => {
    const { onChang, openPopup, children, title, className, popupFooter, contentClassName, popupFooterClassName } = props
    return (
        <Popup
            open={openPopup}
            onOpenChange={onChang}
        >
            <PopupContent
                className={contentClassName}
            >
                <PopupTitle >
                    {title}
                </PopupTitle>
                <div className="p-4  overflow-y-auto">
                    {children}
                </div>
                <PopupFooter className={popupFooterClassName}>
                    {popupFooter}
                </PopupFooter>
            </PopupContent>
        </Popup>
    )
})