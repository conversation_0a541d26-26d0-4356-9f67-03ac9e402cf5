import { <PERSON>, Tabs, TabsContent, toast, useRole, useRouteHandler, useTabsContext } from "@cdss-modules/design-system";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { DncFilter } from "../../ui/DncFilter";
import { DncCondition, DncList, DncListProps, TDnc } from "../../ui/DncList/DncList";
import { DncTabName, microfrontends } from "@cdss/types/microfrontendsConfig";
import { useEffect, useMemo, useState } from "react";
import { fireCreateDnc, fireGetChannelData } from "@cdss/lib/api";
import { extractErrorMessage } from "@cdss-modules/design-system/lib/utils";

export const DNCAdminBody = () => {
    const { globalConfig } = useRole();
    const [currentFilter, setCurrentFilter] =
        useState<any>(undefined);
    const [isEditMode, setIsEditMode] = useState(false)
    const [isUploadSuccess, setUploadSuccess] = useState(false)
    const [saveLoading, setSaveLoading] = useState(false)
    const [isClickSearch, setIsClickSearch] = useState(false)
    const [editList, setEditList] = useState<TDnc[]>([]);
    const [channelList, setChannelList] = useState<any[]>([]);
    const {
        selected,
    } = useTabsContext();
    const { basePath } = useRouteHandler();
    const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
    const dncCols: DncTabName[] =
        microfrontendsConfig?.['ctint-mf-cdss']?.['dnc-tab-names'] || [];
    const initTableColsData = useMemo(() => dncCols?.reduce(
        (acc, filter) => {
            acc[filter?.value] = {
                ...filter,
                checked: false,
                require: false, // Adding the missing required property
            }; // Initialize all filter values to empty
            return acc;
        },
        {} as Record<string, DncCondition>), [dncCols]
    );
    const onApplyFilter = (filterValue: any) => {
        setCurrentFilter(filterValue);
        setIsClickSearch(true)
    };
    const onClearFilter = () => {
        setCurrentFilter(undefined);
    }
    const onSave = async () => {
        try {
            const saveList = editList?.map((item) => {
                const { isNew, ...rest } = item;
                return {
                    ...rest,
                    ...(isNew && { id: "" })
                };
            })
            const result = await fireCreateDnc(
                saveList,
                selected?.includes("dnc") ? "dnc" : "blacklist",
                basePath
            );
            if (result?.data?.isSuccess) {
                setIsEditMode(!isEditMode)
                setSaveLoading(false)
                return result
            } else {
                return null
            }
        } catch (err) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(err)}. If problem persists, please re-login.`,
            });
        } finally {
            setSaveLoading(false)
        }

    }
    const onCancel = () => {
        setEditList([])
        setIsEditMode(!isEditMode)
    }

    const getChannelData = async () => {
        try {
            const result = await fireGetChannelData(
                basePath,selected?.includes("dnc") ? "dnc" : "blacklist"
            );
            if (result?.data?.isSuccess) {
                if (result?.data?.data && result?.data?.data?.length > 0) {
                    setChannelList(result?.data?.data)
                }

            }
        } catch (error) {
            toast({
                variant: 'error',
                title: 'Error refreshing the web',
                description: `These is an error: ${extractErrorMessage(error)}. If problem persists, please re-login.`,
            });
        }
    }
    useEffect(() => {
        getChannelData()
    }, [selected])

    return <div className="flex flex-col h-full gap-y-4 overflow-auto">
        <Panel containerClassName="h-full">
            <Tabs
                onChangeTabFunc={() => {
                    setIsEditMode(false)
                    setEditList([])
                    onClearFilter()
                }}
                defaultTab={'dnc'}
                triggers={[
                    {
                        value: 'dnc',
                        label: "DNC",
                    },
                    {
                        value: 'blacklist',
                        label: "Blacklist",
                    },
                ]}
                triggerClassName="py-2 px-2 text-body"
            >
                <TabsContent
                    value={'dnc'}
                    className="p-0 h-0 flex-1 flex flex-col"
                >
                    <DncFilter onApplyFilter={onApplyFilter}
                        onClearFilter={onClearFilter}
                        isEditMode={isEditMode}
                        onCancel={onCancel}
                        onSave={onSave}
                        saveLoading={saveLoading}
                        editList={editList}
                        setUploadSuccess={setUploadSuccess}
                    />
                    <DncList
                        cols={initTableColsData}
                        currentFilter={currentFilter}
                        isEditMode={isEditMode}
                        editList={editList}
                        setEditList={setEditList}
                        isUploadSuccess={isUploadSuccess}
                        setUploadSuccess={setUploadSuccess}
                        channelList={[...channelList]}
                        isClickSearch={isClickSearch}
                        setIsClickSearch={setIsClickSearch}
                    />
                </TabsContent>
                <TabsContent
                    value={'blacklist'}
                    className="p-0 h-0 flex-1 flex flex-col"
                >
                    <DncFilter onApplyFilter={onApplyFilter}
                        onClearFilter={onClearFilter}
                        isEditMode={isEditMode}
                        onCancel={onCancel}
                        onSave={onSave}
                        saveLoading={saveLoading}
                        editList={editList}
                        setUploadSuccess={setUploadSuccess}
                    />
                    <DncList
                        cols={initTableColsData}
                        currentFilter={currentFilter}
                        isEditMode={isEditMode}
                        editList={editList}
                        setEditList={setEditList}
                        isUploadSuccess={isUploadSuccess}
                        setUploadSuccess={setUploadSuccess}
                        channelList={[...channelList]}
                        isClickSearch={isClickSearch}
                        setIsClickSearch={setIsClickSearch}
                    />
                </TabsContent>
            </Tabs>
        </Panel>
    </div>
}


const queryClient = new QueryClient();

const DNCAdminScreen = () => {
    return (
        <QueryClientProvider client={queryClient}>
            <DNCAdminBody />
        </QueryClientProvider>
    );
};

export default DNCAdminScreen;