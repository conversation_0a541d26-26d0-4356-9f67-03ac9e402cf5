'use client';

import Button from '@cdss-modules/design-system/components/_ui/Button';
import Icon from '@cdss-modules/design-system/components/_ui/Icon';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import Field from '@cdss-modules/design-system/components/_ui/Field';

import { useEffect, memo } from 'react';
import { Controller, useForm } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { STRICT_PASSWORD_REGEX } from '@cdss-modules/design-system/lib/constants';

const loginSchema = yup
  .object({
    password: yup
      .string()
      .required('Password is required')
      .matches(
        STRICT_PASSWORD_REGEX,
        'Password must include: at least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character'
      ),
    confirm_password: yup
      .string()
      .required('Confirm Password is required')
      .oneOf([yup.ref('password'), ''], 'Passwords must match'),
  })
  .required();

export type TResetPasswordFormProps = {
  isLoading: boolean;
  setIsLoading: (isLoading: boolean) => void;
  onSubmit: (data: any) => void;
  defaultValues?: any;
  readonlyFields?: string[];
  onBack: () => void;
};

const ResetPasswordFormComponent = ({
  isLoading,
  onSubmit,
  defaultValues,
  onBack,
}: TResetPasswordFormProps) => {
  const {
    handleSubmit,
    control,
    formState: { errors },
    reset,
  } = useForm({
    resolver: yupResolver(loginSchema),
  });

  useEffect(() => {
    if (!defaultValues) return;
    reset(defaultValues);
  }, [defaultValues, reset]);

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="h-full overflow-y-auto px-2 md:px-5"
    >
      <div className="flex flex-col gap-6">
        <p className="text-body font-bold">
          Please create a new password for your first login since account
          creation or password reset.
        </p>
        <div>
          <Field
            title={'New Password'}
            icon={<Icon name="error" />}
            status={errors?.password?.message ? 'danger' : undefined}
            message={errors?.password?.message}
          >
            <Controller
              name="password"
              control={control}
              render={({ field }) => (
                <Input
                  type="password"
                  placeholder="New password"
                  {...field}
                />
              )}
            />
          </Field>
        </div>
        <div>
          <Field
            title={'Confirm password'}
            icon={<Icon name="error" />}
            status={errors?.confirm_password?.message ? 'danger' : undefined}
            message={errors?.confirm_password?.message}
          >
            <Controller
              name="confirm_password"
              control={control}
              render={({ field }) => (
                <Input
                  type="password"
                  placeholder="Confirm your password"
                  {...field}
                />
              )}
            />
          </Field>
        </div>
        <div className="flex flex-col gap-4">
          <Button
            type="submit"
            fullWidth
            disabled={isLoading}
            variant={'primary'}
          >
            Confirm
          </Button>
          <Button
            onClick={() => onBack()}
            fullWidth
            disabled={isLoading}
            variant="secondary"
          >
            Back
          </Button>
        </div>
      </div>
    </form>
  );
};

export const ResetPasswordForm = memo(ResetPasswordFormComponent);

export default ResetPasswordForm;
