export const moduleMap: Record<string, any> = {
  // 'interaction/module': {
  //   // @ts-expect-error: Can't type check dynamic imports
  //   entry: () => import('interaction/module'),
  //   entryName: 'menu.interactions',
  //   icon: 'ClipboardList',
  // },
  // 'superDashboard/main': {
  //   // @ts-expect-error: Can't type check dynamic imports
  //   entry: () => import('superDashboard/main'),
  //   entryName: 'menu.superdashboard',
  //   icon: 'SquareActivity',
  // },
};
