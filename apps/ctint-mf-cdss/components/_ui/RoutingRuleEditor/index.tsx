import React, {
  useState,
  useEffect,
  memo,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import { Button } from '@cdss-modules/design-system';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import { Trash, Plus, ChevronDown } from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import TemplateConfigurationSelector, {
  ITemplateDetail,
  IAttachment,
} from './TemplateConfigurationSelector';
import MultiLanguageTemplateConfigurationSelector, {
  MultiLanguageTemplateInfo,
} from './MultiLanguageTemplateConfigurationSelector';
import {
  ITemplateResponse,
  TQueueOption,
} from '../../_screen/EmailAdmin/_screen/MailboxRoutingRules/Detail';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import EmailAddressGroupPicker from '../../_screen/EmailAdmin/_screen/EmailAddressGroups/EmailAddressGroupPicker';
import { useQuery } from '@tanstack/react-query';
import { fireGetAutoReplyTemplateDetail } from '@cdss/lib/api';
import { basePath } from '@cdss/lib/appConfig';
import { useEmailAddressGroupById } from '../../_screen/EmailAdmin/_hooks/useEmailAddressGroupById';
import { debounce } from 'lodash';

// Rule types and interfaces
export type TLogicType = 'AND' | 'OR';
export type TConditionOperator =
  | 'contains'
  | 'equals'
  | 'startsWith'
  | 'endsWith'
  | 'notContains'
  | 'in'
  | 'within'
  | 'outside'
  | 'before'
  | 'after';
export type TSubjectType =
  | 'subject'
  | 'sender'
  | 'body'
  | 'recipient'
  | 'domain'
  | 'emailAddressGroup'
  | 'time';

export interface ICondition {
  field: TSubjectType;
  operator: TConditionOperator;
  value: string;
}

export interface IRoutingRule {
  id: string;
  logic: TLogicType;
  priority: number;
  conditions: ICondition[];
  return: {
    queueId?: string;
    autoReplyId: string | null;
    language?: string;
    multiLanguageTemplates?: MultiLanguageTemplateInfo | null;
  };
}

// Props for the RoutingRuleEditor component
interface IRoutingRuleEditorProps {
  rules: IRoutingRule[];
  onUpdateRules: (rules: IRoutingRule[]) => void;
  queueOptions: TQueueOption[];
  templateOptions: ITemplateResponse[];
  isEditMode: boolean;
  validationErrors: Record<string, string>;
  enabledSubjectFields?: TSubjectType[];
  showQueueInput?: boolean;
  useMultiLanguageTemplates?: boolean;
  allowedLogicTypes?: TLogicType[];
  isCreate?: boolean;
}

// Individual Condition component
const Condition = memo(
  ({
    condition,
    onUpdate,
    onRemove,
    isEditMode,
    validationError,
    subjectOptions,
  }: {
    condition: ICondition;
    onUpdate: (condition: ICondition) => void;
    onRemove: () => void;
    isEditMode: boolean;
    validationError?: string;
    subjectOptions: { id: string; label: string; value: TSubjectType }[];
  }) => {
    const { t } = useTranslation();

    // Local state for text input fields to prevent re-renders on every keystroke
    const [localInputValue, setLocalInputValue] = useState(condition.value);

    // Time-specific state
    const [time1, setTime1] = useState('');
    const [time2, setTime2] = useState('');

    // State for group name display
    const [selectedGroupName, setSelectedGroupName] = useState<string | null>(
      null
    );

    // Ref to maintain group name persistently across re-renders
    const groupNameRef = useRef<Record<string, string>>({});

    // Fetch group name if condition is for email address group
    const { data: fetchedGroupName, isLoading: isLoadingGroupName } =
      useEmailAddressGroupById(
        condition.field === 'emailAddressGroup' ? condition.value : undefined,
        basePath
      );

    // Update local input value when condition value changes externally
    useEffect(() => {
      setLocalInputValue(condition.value);
      if (condition.field === 'time') {
        try {
          const times = JSON.parse(condition.value || '[]');
          if (Array.isArray(times)) {
            setTime1(times[0] || '');
            setTime2(times[1] || '');
          }
        } catch (e) {
          setTime1('');
          setTime2('');
        }
      }
    }, [condition.value]);

    // Update group name when fetched or when field/value changes
    useEffect(() => {
      if (condition.field === 'emailAddressGroup') {
        if (fetchedGroupName) {
          groupNameRef.current[condition.value] = fetchedGroupName;
          setSelectedGroupName(fetchedGroupName);
        } else if (groupNameRef.current[condition.value]) {
          // Use cached name if available
          setSelectedGroupName(groupNameRef.current[condition.value]);
        } else if (!isLoadingGroupName && condition.value) {
          // Fallback to showing ID if no name found
          setSelectedGroupName(condition.value);
        }
      } else {
        setSelectedGroupName(null);
      }
    }, [
      condition.field,
      condition.value,
      fetchedGroupName,
      isLoadingGroupName,
    ]);

    const handleValueChange = useCallback((value: string | number) => {
      setLocalInputValue(String(value));
    }, []);

    const handleTimeChange = useCallback(
      (index: 0 | 1, timeValue: string) => {
        let newTimes: string[] = [];
        if (index === 0) {
          setTime1(timeValue);
          newTimes = [timeValue, time2];
        } else {
          setTime2(timeValue);
          newTimes = [time1, timeValue];
        }
        // For 'before' and 'after', only the first time is relevant
        if (condition.operator === 'before' || condition.operator === 'after') {
          newTimes = [newTimes[0]];
        }
        const newLocalValue = JSON.stringify(
          newTimes.filter((t) => t !== undefined && t !== null)
        );
        setLocalInputValue(newLocalValue);
        // Immediately update to reflect changes in time inputs
        onUpdate({ ...condition, value: newLocalValue });
      },
      [time1, time2, condition, onUpdate]
    );

    const handleValueBlur = useCallback(() => {
      // Only update the condition if the value has actually changed
      if (condition.field !== 'time' && localInputValue !== condition.value) {
        onUpdate({ ...condition, value: localInputValue });
      }
      // For time fields, update is handled by handleTimeChange directly or when operator changes
    }, [localInputValue, condition, onUpdate]);

    const handleGroupSelected = useCallback(
      (groupId: string, groupName: string) => {
        setSelectedGroupName(groupName);
        onUpdate({
          ...condition,
          field: 'emailAddressGroup',
          operator: 'in',
          value: groupId,
        });
      },
      [condition, onUpdate]
    );

    // Memoize operator options to prevent unnecessary recalculations
    const operatorOptions = useMemo(() => {
      const getOperatorOptionsForField = (field: TSubjectType) => {
        switch (field) {
          case 'domain':
            return [
              {
                id: 'equals',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorEquals'
                ),
                value: 'equals',
              },
            ];
          case 'subject':
            return [
              {
                id: 'contains',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorContains'
                ),
                value: 'contains',
              },
            ];
          case 'emailAddressGroup':
            return [
              {
                id: 'in',
                label: t('ctint-mf-cdss.common.routingRuleEditor.operatorIn'),
                value: 'in',
              },
            ];
          case 'time':
            return [
              {
                id: 'within',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorWithin'
                ),
                value: 'within',
              },
              {
                id: 'outside',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorOutside'
                ),
                value: 'outside',
              },
              {
                id: 'before',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorBefore'
                ),
                value: 'before',
              },
              {
                id: 'after',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorAfter'
                ),
                value: 'after',
              },
            ];
          default:
            return [
              {
                id: 'contains',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorContains'
                ),
                value: 'contains',
              },
              {
                id: 'equals',
                label: t(
                  'ctint-mf-cdss.common.routingRuleEditor.operatorEquals'
                ),
                value: 'equals',
              },
              {
                id: 'in',
                label: t('ctint-mf-cdss.common.routingRuleEditor.operatorIn'),
                value: 'in',
              },
            ];
        }
      };

      return getOperatorOptionsForField(condition.field);
    }, [condition.field, t]);

    const handleFieldOrOperatorChange = (
      newField?: TSubjectType,
      newOperator?: TConditionOperator
    ) => {
      const fieldToUpdate = newField || condition.field;
      let operatorToUpdate = newOperator || condition.operator;
      let valueToUpdate = condition.value;

      // If field changed, reset operator to the first valid one for the new field
      if (newField) {
        const getOperatorOptionsForField = (field: TSubjectType) => {
          switch (field) {
            case 'domain':
              return [
                {
                  id: 'equals',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorEquals'
                  ),
                  value: 'equals',
                },
              ];
            case 'subject':
              return [
                {
                  id: 'contains',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorContains'
                  ),
                  value: 'contains',
                },
              ];
            case 'emailAddressGroup':
              return [
                {
                  id: 'in',
                  label: t('ctint-mf-cdss.common.routingRuleEditor.operatorIn'),
                  value: 'in',
                },
              ];
            case 'time':
              return [
                {
                  id: 'within',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorWithin'
                  ),
                  value: 'within',
                },
                {
                  id: 'outside',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorOutside'
                  ),
                  value: 'outside',
                },
                {
                  id: 'before',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorBefore'
                  ),
                  value: 'before',
                },
                {
                  id: 'after',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorAfter'
                  ),
                  value: 'after',
                },
              ];
            default:
              return [
                {
                  id: 'contains',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorContains'
                  ),
                  value: 'contains',
                },
                {
                  id: 'equals',
                  label: t(
                    'ctint-mf-cdss.common.routingRuleEditor.operatorEquals'
                  ),
                  value: 'equals',
                },
                {
                  id: 'in',
                  label: t('ctint-mf-cdss.common.routingRuleEditor.operatorIn'),
                  value: 'in',
                },
              ];
          }
        };
        const validOperators = getOperatorOptionsForField(newField);
        operatorToUpdate = validOperators[0].value as TConditionOperator;
      }

      // If changing to or from 'time' field, or changing operator within 'time' field
      if (
        fieldToUpdate === 'time' ||
        (condition.field === 'time' && newField !== 'time')
      ) {
        if (operatorToUpdate === 'before' || operatorToUpdate === 'after') {
          valueToUpdate = JSON.stringify([time1 || '00:00']);
        } else {
          // 'within', 'outside', or if field changes away from time
          valueToUpdate = JSON.stringify([time1 || '00:00', time2 || '00:00']);
        }
        if (newField !== 'time' && condition.field === 'time') {
          // Reset if changing away from time
          valueToUpdate = '';
        }
        if (newField === 'time' && newField !== condition.field) {
          // Reset if changing to time
          const defaultTimes =
            operatorToUpdate === 'before' || operatorToUpdate === 'after'
              ? ['00:00']
              : ['00:00', '00:00'];
          setTime1(defaultTimes[0]);
          setTime2(defaultTimes[1] || '');
          valueToUpdate = JSON.stringify(defaultTimes);
        }
      } else if (newField && newField !== condition.field) {
        // If field changed and it's not 'time'
        valueToUpdate = ''; // Reset value for other field types
      }

      onUpdate({
        field: fieldToUpdate,
        operator: operatorToUpdate,
        value: valueToUpdate,
      });
    };

    return (
      <div
        className={cn(
          'bg-gray-100 p-2 rounded-md',
          !isEditMode && 'bg-white p-0'
        )}
      >
        <div className="flex items-center justify-between mb-2">
          <label className="block text-sm font-medium">
            {t('ctint-mf-cdss.common.routingRuleEditor.conditionLabel')}
          </label>
          {isEditMode && (
            <Button
              variant="secondary"
              size="xs"
              onClick={onRemove}
              className=""
            >
              <Trash className="h-3 w-3 mr-1" />
              {/* Remove Condition */}
            </Button>
          )}
        </div>
        <div className="flex items-center gap-2 mb-2">
          <div className="flex-1 grid grid-cols-3 gap-2">
            <Select
              disabled={!isEditMode}
              value={condition.field}
              onChange={(value) =>
                handleFieldOrOperatorChange(value as TSubjectType, undefined)
              }
              options={subjectOptions}
              isPagination={false}
              labelClassName="h-full"
              triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
            />
            <Select
              disabled={
                !isEditMode ||
                (condition.field === 'time' &&
                  (condition.operator === 'before' ||
                    condition.operator === 'after') &&
                  operatorOptions.length === 1 &&
                  operatorOptions[0].value === condition.operator)
              }
              value={condition.operator}
              onChange={(value) =>
                handleFieldOrOperatorChange(
                  undefined,
                  value as TConditionOperator
                )
              }
              options={operatorOptions}
              isPagination={false}
              labelClassName="h-full"
              triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
            />
            {condition.field === 'emailAddressGroup' ? (
              <EmailAddressGroupPicker
                selectedGroupName={selectedGroupName}
                onGroupSelected={handleGroupSelected}
                disabled={!isEditMode}
              />
            ) : condition.field === 'time' ? (
              <div className="col-span-1 grid grid-cols-2 gap-2 items-center">
                <Input
                  disabled={!isEditMode}
                  size="s"
                  type="time"
                  value={time1}
                  onChange={(val) => handleTimeChange(0, String(val))}
                  className="disabled:text-black"
                  status={validationError ? 'danger' : undefined}
                  message={validationError}
                />
                {(condition.operator === 'within' ||
                  condition.operator === 'outside') && (
                  <>
                    {/* <span className="text-center">-</span> */}
                    <Input
                      disabled={!isEditMode}
                      size="s"
                      type="time"
                      value={time2}
                      onChange={(val) => handleTimeChange(1, String(val))}
                      className="disabled:text-black"
                      status={validationError ? 'danger' : undefined}
                      message={validationError}
                    />
                  </>
                )}
              </div>
            ) : (
              <Input
                disabled={!isEditMode}
                size="s"
                placeholder={t(
                  'ctint-mf-cdss.common.routingRuleEditor.valuePlaceholder'
                )}
                value={localInputValue}
                onChange={handleValueChange}
                onBlur={handleValueBlur}
                className="disabled:text-black"
                status={validationError ? 'danger' : undefined}
              />
            )}
          </div>
        </div>
        {/* {validationError && (
          <p className="text-xs text-red-500 mt-1">{validationError}</p>
        )} */}
      </div>
    );
  }
);

Condition.displayName = 'Condition';

// Priority Input component - follows same pattern as Condition component
const PriorityInput = memo(
  ({
    value,
    onUpdate,
    isEditMode,
    validationError,
    placeholder,
  }: {
    value: number;
    onUpdate: (value: number) => void;
    isEditMode: boolean;
    validationError?: string;
    placeholder?: string;
  }) => {
    // Local state for input to prevent re-renders on every keystroke
    const [localInputValue, setLocalInputValue] = useState(value.toString());

    // Update local input value when external value changes
    useEffect(() => {
      setLocalInputValue(value.toString());
    }, [value]);

    // Create debounced function to update parent after user stops typing for 500ms
    const debouncedUpdate = useMemo(
      () =>
        debounce((inputValue: string) => {
          const priority = parseInt(inputValue, 10);
          if (!isNaN(priority) && priority !== value) {
            onUpdate(priority);
          }
        }, 500),
      [value, onUpdate]
    );

    // Clean up debounced function on unmount
    useEffect(() => {
      return () => {
        debouncedUpdate.cancel();
      };
    }, [debouncedUpdate]);

    const handleValueChange = useCallback(
      (inputValue: string | number) => {
        const sanitizedValue = String(inputValue).replace(/^-/, '');
        const numericValue = parseInt(sanitizedValue, 10) || 0;
        const stringValue = Math.max(numericValue, 0).toString();
        setLocalInputValue(stringValue);
        // Trigger debounced update
        debouncedUpdate(stringValue);
      },
      [debouncedUpdate]
    );

    const handleValueBlur = useCallback(() => {
      const priority = parseInt(localInputValue, 10);
      // Cancel any pending debounced updates and update immediately on blur
      debouncedUpdate.cancel();
      if (!isNaN(priority) && priority !== value) {
        onUpdate(priority);
      } else if (isNaN(priority)) {
        // Reset to original value if invalid
        setLocalInputValue(value.toString());
      }
    }, [localInputValue, value, onUpdate, debouncedUpdate]);

    return (
      <Input
        disabled={!isEditMode}
        size="s"
        type="number"
        min="1"
        placeholder={placeholder}
        value={localInputValue}
        onChange={handleValueChange}
        onBlur={handleValueBlur}
        className="disabled:text-black"
        status={validationError ? 'danger' : undefined}
        message={validationError}
      />
    );
  }
);

PriorityInput.displayName = 'PriorityInput';

// Logic Selector component
const LogicSelector = memo(
  ({
    logic,
    onChange,
    isEditMode,
    allowedLogicTypes,
  }: {
    logic: TLogicType;
    onChange: (logic: TLogicType) => void;
    isEditMode: boolean;
    allowedLogicTypes?: TLogicType[];
  }) => {
    const { t } = useTranslation();

    // All possible logic options
    const allLogicOptions = [
      {
        id: 'AND',
        label: t('ctint-mf-cdss.common.routingRuleEditor.logicMatchAll'),
        value: 'AND',
      },
      {
        id: 'OR',
        label: t('ctint-mf-cdss.common.routingRuleEditor.logicMatchAny'),
        value: 'OR',
      },
    ];

    // Filter options based on allowedLogicTypes prop
    const logicOptions = allowedLogicTypes
      ? allLogicOptions.filter((option) =>
          allowedLogicTypes.includes(option.value as TLogicType)
        )
      : allLogicOptions;

    return (
      <div className="w-full">
        <Select
          disabled={!isEditMode}
          isPagination={false}
          options={logicOptions}
          value={logic}
          onChange={(value) => onChange(value as TLogicType)}
          labelClassName="h-full"
          triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
        />
      </div>
    );
  }
);

LogicSelector.displayName = 'LogicSelector';

// Return Configuration component
const ReturnConfiguration: React.FC<{
  rule: IRoutingRule;
  onUpdate: (
    field: keyof IRoutingRule['return'] | '_batch_update',
    value: any
  ) => void;
  queueOptions: any[];
  templateOptions: any[];
  isEditMode: boolean;
  showQueueInput?: boolean;
  useMultiLanguageTemplates?: boolean;
  isWhatsAppTemplate?: boolean;
  languageValidationErrors: string;
  templateValidationErrors: string;
}> = ({
  rule,
  onUpdate,
  queueOptions,
  templateOptions,
  isEditMode,
  showQueueInput = true,
  useMultiLanguageTemplates = false,
  isWhatsAppTemplate = false,
  languageValidationErrors,
  templateValidationErrors,
}) => {
  const { t } = useTranslation();

  // Fetch template detail based on autoReplyId (single language)
  const { data: templateDetail, isLoading: isLoadingTemplateDetail } = useQuery<
    ITemplateDetail | null,
    Error
  >({
    queryKey: ['templateDetail', rule.return.autoReplyId],
    queryFn: async () => {
      if (!rule.return.autoReplyId) return null;
      try {
        // For comma-separated IDs, take the first one for single template mode
        const templateId = rule.return.autoReplyId.includes(',')
          ? rule.return.autoReplyId.split(',')[0].trim()
          : rule.return.autoReplyId;

        const response = await fireGetAutoReplyTemplateDetail(
          templateId,
          basePath
        );
        return response.data.data; // Assuming this is ITemplateDetail
      } catch (error) {
        console.error(
          'Error fetching template detail in RoutingRuleEditor:',
          error
        );
        return null;
      }
    },
    enabled: !!rule.return.autoReplyId && !useMultiLanguageTemplates,
  });

  // Fetch template details for multi-language templates
  const {
    data: multiLanguageTemplateDetails,
    isLoading: isLoadingMultiLanguageTemplateDetails,
  } = useQuery<{ [language: string]: ITemplateDetail | null }, Error>({
    queryKey: [
      'multiLanguageTemplateDetails',
      rule.return.multiLanguageTemplates,
      rule.return.autoReplyId,
      useMultiLanguageTemplates,
    ],
    queryFn: async () => {
      console.log(
        'multi-language-rule.return.multiLanguageTemplates',
        rule.return.multiLanguageTemplates
      );
      console.log(
        'multi-language-rule.return.autoReplyId',
        rule.return.autoReplyId
      );

      const templateDetails: { [language: string]: ITemplateDetail | null } =
        {};

      // First try to use multiLanguageTemplates object if available
      if (rule.return.multiLanguageTemplates) {
        await Promise.all(
          Object.entries(rule.return.multiLanguageTemplates).map(
            async ([language, templateId]) => {
              if (templateId) {
                try {
                  const response = await fireGetAutoReplyTemplateDetail(
                    templateId,
                    basePath
                  );
                  templateDetails[language] = response.data.data;
                } catch (error) {
                  console.error(
                    `Error fetching template detail for ${language}:`,
                    error
                  );
                  templateDetails[language] = null;
                }
              } else {
                templateDetails[language] = null;
              }
            }
          )
        );
      }
      // Fallback: use comma-separated autoReplyId
      else if (
        rule.return.autoReplyId &&
        rule.return.autoReplyId.includes(',')
      ) {
        const templateIds = rule.return.autoReplyId
          .split(',')
          .map((id) => id.trim())
          .filter((id) => id.length > 0);

        await Promise.all(
          templateIds.map(async (templateId) => {
            try {
              const response = await fireGetAutoReplyTemplateDetail(
                templateId,
                basePath
              );
              const templateData = response.data.data;
              if (templateData) {
                // Use template's language as key, or fall back to template ID
                const language =
                  templateData.language?.toLowerCase() || templateId;
                templateDetails[language] = templateData;
              }
            } catch (error) {
              console.error(
                `Error fetching template detail for ${templateId}:`,
                error
              );
            }
          })
        );
      }
      // Handle single template ID (not comma-separated)
      else if (rule.return.autoReplyId && rule.return.autoReplyId.trim()) {
        try {
          const response = await fireGetAutoReplyTemplateDetail(
            rule.return.autoReplyId.trim(),
            basePath
          );
          const templateData = response.data.data;
          if (templateData) {
            // Use template's language as key, or fall back to 'en' as default
            let language = templateData.language?.toLowerCase() || 'en';

            // Normalize language code to match our standard format
            if (language.includes('zh')) {
              if (
                language.includes('cn') ||
                language.includes('hans') ||
                language.includes('simplified')
              ) {
                language = 'zh_cn';
              } else if (
                language.includes('hk') ||
                language.includes('tw') ||
                language.includes('hant') ||
                language.includes('traditional')
              ) {
                language = 'zh_hk';
              } else {
                language = 'zh_cn'; // Default Chinese to simplified
              }
            } else if (language === 'en' || language.includes('english')) {
              language = 'en';
            }

            templateDetails[language] = templateData;
          }
        } catch (error) {
          console.error(
            `Error fetching template detail for ${rule.return.autoReplyId}:`,
            error
          );
        }
      }

      return templateDetails;
    },
    // Fetch when we have multi-language templates, comma-separated autoReplyId, or single autoReplyId
    enabled:
      useMultiLanguageTemplates &&
      ((!!rule.return.multiLanguageTemplates &&
        Object.keys(rule.return.multiLanguageTemplates).length > 0) ||
        (!!rule.return.autoReplyId &&
          rule.return.autoReplyId.trim().length > 0)),
  });

  // Transform the templateValue to the format expected by TemplateConfigurationSelector
  const getTemplateValue = () => {
    if (useMultiLanguageTemplates) {
      // First check if we have multiLanguageTemplates object (preferred for frontend)
      if (rule.return.multiLanguageTemplates) {
        return rule.return.multiLanguageTemplates;
      }

      // Fallback: Handle comma-separated autoReplyId for multi-language templates
      if (rule.return.autoReplyId && rule.return.autoReplyId.includes(',')) {
        // Convert comma-separated IDs to multiLanguageTemplates format
        const templateIds = rule.return.autoReplyId
          .split(',')
          .map((id) => id.trim());
        const multiLangTemplates: { [language: string]: string } = {};

        // Find template details to determine languages
        if (templateOptions && templateOptions.length > 0) {
          const spaceItem = templateOptions[0];
          if (spaceItem && spaceItem.data) {
            templateIds.forEach((templateId) => {
              // Search for template to get its language
              for (const space of spaceItem.data) {
                if (space.items) {
                  for (const directory of space.items) {
                    if (directory.items) {
                      for (const templateItem of directory.items) {
                        if (templateItem.id === templateId) {
                          // Normalize language code to match languageOptions format
                          const rawLanguage =
                            templateItem.language?.toLowerCase() || 'en';
                          let normalizedLanguage = rawLanguage;

                          // Map template language codes to our standard format
                          if (rawLanguage.includes('zh')) {
                            if (rawLanguage.includes('cn')) {
                              normalizedLanguage = 'zh_cn';
                            } else if (rawLanguage.includes('hk')) {
                              normalizedLanguage = 'zh_hk';
                            } else {
                              // Default Chinese to simplified
                              normalizedLanguage = 'zh_cn';
                            }
                          } else if (rawLanguage === 'en') {
                            normalizedLanguage = 'en';
                          }

                          multiLangTemplates[normalizedLanguage] = templateId;
                          break;
                        }
                      }
                    }
                  }
                }
              }
            });
          }
        }

        return Object.keys(multiLangTemplates).length > 0
          ? multiLangTemplates
          : null;
      }

      // Handle single template ID in multi-language mode
      if (rule.return.autoReplyId && rule.return.autoReplyId.trim()) {
        const templateId = rule.return.autoReplyId.trim();
        const multiLangTemplates: { [language: string]: string } = {};

        // Find template details to determine languages
        if (templateOptions && templateOptions.length > 0) {
          const spaceItem = templateOptions[0];
          if (spaceItem && spaceItem.data) {
            // Search for template to get its language
            for (const space of spaceItem.data) {
              if (space.items) {
                for (const directory of space.items) {
                  if (directory.items) {
                    for (const templateItem of directory.items) {
                      if (templateItem.id === templateId) {
                        // Normalize language code to match languageOptions format
                        const rawLanguage =
                          templateItem.language?.toLowerCase() || 'en';
                        let normalizedLanguage = rawLanguage;

                        // Map template language codes to our standard format
                        if (rawLanguage.includes('zh')) {
                          if (rawLanguage.includes('cn')) {
                            normalizedLanguage = 'zh_cn';
                          } else if (rawLanguage.includes('hk')) {
                            normalizedLanguage = 'zh_hk';
                          } else {
                            // Default Chinese to simplified
                            normalizedLanguage = 'zh_cn';
                          }
                        } else if (
                          rawLanguage === 'en' ||
                          rawLanguage.includes('english')
                        ) {
                          normalizedLanguage = 'en';
                        }

                        multiLangTemplates[normalizedLanguage] = templateId;
                        break;
                      }
                    }
                  }
                }
              }
            }
          }
        }

        return Object.keys(multiLangTemplates).length > 0
          ? multiLangTemplates
          : null;
      }

      // For no template selection in multi-language mode, return null to show no selection
      return null;
    }

    if (!rule.return.autoReplyId) return null;

    // For single template mode, handle comma-separated as single template (take first one)
    const templateId = rule.return.autoReplyId.includes(',')
      ? rule.return.autoReplyId.split(',')[0].trim()
      : rule.return.autoReplyId;

    if (!templateId || !templateOptions || templateOptions.length === 0) {
      return null;
    }

    // Access the first item which contains the ITemplateResponse
    const spaceItem = templateOptions[0];

    if (!spaceItem || !spaceItem.data) {
      return null;
    }

    // First level - Spaces
    for (const space of spaceItem.data) {
      // Second level - Directories
      if (space.items) {
        for (const directory of space.items) {
          // Third level - Templates
          if (directory.items) {
            for (const templateItem of directory.items) {
              if (templateItem.id === templateId) {
                return {
                  language: templateItem.language?.toLowerCase() || 'en',
                  templateId: templateItem.id,
                };
              }
            }
          }
        }
      }
    }
    return null;
  };

  const handleUpdateTemplate = (
    template:
      | { language: string; templateId: string }
      | MultiLanguageTemplateInfo
      | null
  ) => {
    if (useMultiLanguageTemplates) {
      // console.log('template', template);

      // Handle MultiLanguageTemplateInfo
      const multiLangTemplate = template as MultiLanguageTemplateInfo | null;
      if (multiLangTemplate) {
        const templateIds = Object.values(multiLangTemplate).filter(
          (id): id is string =>
            id !== null && id !== undefined && id.trim().length > 0
        );
        // console.log('templateIds', templateIds);

        // Update both fields in a single state update to avoid race condition
        const updatedReturn = {
          ...rule.return,
          multiLanguageTemplates: multiLangTemplate,
          autoReplyId: templateIds.length > 0 ? templateIds.join(',') : null,
        };

        // Use a custom update function that updates multiple fields at once
        onUpdate('_batch_update', updatedReturn);
      } else {
        // Clear both formats in a single update
        const updatedReturn = {
          ...rule.return,
          multiLanguageTemplates: null,
          autoReplyId: null,
        };
        onUpdate('_batch_update', updatedReturn);
      }
    } else {
      const singleTemplate = template as {
        language: string;
        templateId: string;
      } | null;
      onUpdate('autoReplyId', singleTemplate?.templateId || null);
    }
  };
  if (useMultiLanguageTemplates) {
    return (
      <MultiLanguageTemplateConfigurationSelector
        templateValue={getTemplateValue() as MultiLanguageTemplateInfo | null}
        queueValue={rule.return.queueId || ''}
        queueOptions={queueOptions}
        templateOptions={templateOptions}
        isEditMode={isEditMode}
        onUpdateTemplate={
          handleUpdateTemplate as (
            template: MultiLanguageTemplateInfo | null
          ) => void
        }
        onUpdateQueue={(queue) => onUpdate('queueId', queue)}
        title={t(
          'ctint-mf-cdss.common.routingRuleEditor.returnConfigurationTitle'
        )}
        isQueueRequired={false}
        templateDetailData={multiLanguageTemplateDetails || {}}
        isLoadingTemplateDetailData={isLoadingMultiLanguageTemplateDetails}
        showQueueInput={showQueueInput}
        isWhatsAppTemplate={isWhatsAppTemplate}
        languageValidationErrors={languageValidationErrors}
        templateValidationErrors={templateValidationErrors}
      />
    );
  }

  return (
    <TemplateConfigurationSelector
      templateValue={
        getTemplateValue() as { language: string; templateId: string } | null
      }
      queueValue={rule.return.queueId || ''}
      queueOptions={queueOptions}
      templateOptions={templateOptions}
      isEditMode={isEditMode}
      onUpdateTemplate={
        handleUpdateTemplate as (
          template: { language: string; templateId: string } | null
        ) => void
      }
      onUpdateQueue={(queue) => onUpdate('queueId', queue)}
      title={t(
        'ctint-mf-cdss.common.routingRuleEditor.returnConfigurationTitle'
      )}
      isQueueRequired={false}
      templateDetailData={templateDetail}
      isLoadingTemplateDetailData={isLoadingTemplateDetail}
      showQueueInput={showQueueInput}
      languageValidationErrors={languageValidationErrors}
      templateValidationErrors={templateValidationErrors}
    />
  );
};

// Individual Rule Item component
const RuleItem = memo(
  ({
    rule,
    ruleIndex,
    isEditMode,
    onUpdateRule,
    onUpdateReturnField,
    onRemoveRule,
    onAddCondition,
    onUpdateCondition,
    onRemoveCondition,
    queueOptions,
    templateOptions,
    validationErrors,
    subjectOptions,
    // allRules,
    showQueueInput,
    useMultiLanguageTemplates,
    allowedLogicTypes,
  }: {
    rule: IRoutingRule;
    ruleIndex: number;
    isEditMode: boolean;
    onUpdateRule: (
      index: number,
      field: keyof Omit<IRoutingRule, 'return'>,
      value: any
    ) => void;
    onUpdateReturnField: (
      index: number,
      field: keyof IRoutingRule['return'] | '_batch_update',
      value: any
    ) => void;
    onRemoveRule: (index: number) => void;
    onAddCondition: (ruleIndex: number) => void;
    onUpdateCondition: (
      ruleIndex: number,
      conditionIndex: number,
      condition: ICondition
    ) => void;
    onRemoveCondition: (ruleIndex: number, conditionIndex: number) => void;
    queueOptions: TQueueOption[];
    templateOptions: ITemplateResponse[];
    validationErrors: Record<string, string>;
    subjectOptions: { id: string; label: string; value: TSubjectType }[];
    // allRules: IRoutingRule[];
    showQueueInput?: boolean;
    useMultiLanguageTemplates?: boolean;
    allowedLogicTypes?: TLogicType[];
  }) => {
    const { t } = useTranslation();

    // Get priority validation error from form store
    const priorityError = validationErrors[`rule${ruleIndex}.priority`];

    return (
      <div className="mb-4 shadow-[0_0_10px_4px_rgba(0,0,0,0.1)] px-4 py-2 bg-white">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-semibold">
            {t('ctint-mf-cdss.common.routingRuleEditor.ruleNumberPrefix')}
            {ruleIndex + 1}
          </h3>
          {isEditMode && (
            <Button
              variant="secondary"
              size="s"
              onClick={() => onRemoveRule(ruleIndex)}
            >
              <Trash className="h-4 w-4 mr-1" />{' '}
              {t('ctint-mf-cdss.common.routingRuleEditor.removeRuleButton')}
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('ctint-mf-cdss.common.routingRuleEditor.logicTypeLabel')}
            </label>
            <LogicSelector
              logic={rule.logic}
              onChange={(value) => onUpdateRule(ruleIndex, 'logic', value)}
              isEditMode={isEditMode}
              allowedLogicTypes={allowedLogicTypes}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              {t('ctint-mf-cdss.common.routingRuleEditor.priorityLabel')}
            </label>
            <PriorityInput
              value={rule.priority}
              onUpdate={(value) => onUpdateRule(ruleIndex, 'priority', value)}
              isEditMode={isEditMode}
              validationError={priorityError}
              placeholder={t(
                'ctint-mf-cdss.common.routingRuleEditor.priorityPlaceholder'
              )}
            />
          </div>
        </div>

        <div>
          <div className="space-y-2">
            {rule.conditions.map((condition, conditionIndex) => (
              <Condition
                key={conditionIndex}
                condition={condition}
                onUpdate={(updatedCondition) =>
                  onUpdateCondition(ruleIndex, conditionIndex, updatedCondition)
                }
                onRemove={() => onRemoveCondition(ruleIndex, conditionIndex)}
                isEditMode={isEditMode}
                validationError={
                  validationErrors[
                    `rule${ruleIndex}.condition${conditionIndex}.value`
                  ]
                }
                subjectOptions={subjectOptions}
              />
            ))}
          </div>

          {/* Show error if rule has no conditions */}
          {validationErrors[`rule${ruleIndex}.conditions`] && (
            <p className="text-xs text-red-500 mt-1 mb-1">
              {validationErrors[`rule${ruleIndex}.conditions`]}
            </p>
          )}

          {isEditMode && (
            <Button
              size="s"
              variant="secondary"
              className="mt-2"
              onClick={() => onAddCondition(ruleIndex)}
              beforeIcon={<Plus className="h-3 w-3 mr-1" />}
            >
              {t('ctint-mf-cdss.common.routingRuleEditor.addConditionButton')}
            </Button>
          )}
        </div>

        <ReturnConfiguration
          rule={rule}
          onUpdate={(field, value) =>
            onUpdateReturnField(ruleIndex, field, value)
          }
          queueOptions={queueOptions}
          templateOptions={templateOptions}
          isEditMode={isEditMode}
          showQueueInput={showQueueInput}
          useMultiLanguageTemplates={useMultiLanguageTemplates}
          isWhatsAppTemplate={useMultiLanguageTemplates} // For auto-reply, multi-language means WhatsApp
          languageValidationErrors={
            useMultiLanguageTemplates
              ? validationErrors[`rule${ruleIndex}.language`] || ''
              : ''
          }
          templateValidationErrors={
            useMultiLanguageTemplates
              ? validationErrors[`rule${ruleIndex}.template`] || ''
              : ''
          }
        />
      </div>
    );
  }
);

RuleItem.displayName = 'RuleItem';

// Main RoutingRuleEditor component
const RoutingRuleEditor: React.FC<IRoutingRuleEditorProps> = ({
  rules,
  onUpdateRules,
  queueOptions,
  templateOptions,
  isEditMode,
  validationErrors,
  enabledSubjectFields,
  showQueueInput = true,
  useMultiLanguageTemplates = false,
  allowedLogicTypes,
  isCreate,
}) => {
  const { t } = useTranslation();

  // console.log('templateOptions', templateOptions);

  // Helper to get operator options for a field, similar to the one in Condition component
  const getOperators = useCallback(
    (
      field: TSubjectType
    ): { id: string; label: string; value: TConditionOperator }[] => {
      switch (field) {
        case 'domain':
          return [
            {
              id: 'equals',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorEquals'),
              value: 'equals',
            },
          ];
        case 'subject':
          return [
            {
              id: 'contains',
              label: t(
                'ctint-mf-cdss.common.routingRuleEditor.operatorContains'
              ),
              value: 'contains',
            },
          ];
        case 'emailAddressGroup':
          return [
            {
              id: 'in',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorIn'),
              value: 'in',
            },
          ];
        case 'time':
          return [
            {
              id: 'within',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorWithin'),
              value: 'within',
            },
            {
              id: 'outside',
              label: t(
                'ctint-mf-cdss.common.routingRuleEditor.operatorOutside'
              ),
              value: 'outside',
            },
            {
              id: 'before',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorBefore'),
              value: 'before',
            },
            {
              id: 'after',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorAfter'),
              value: 'after',
            },
          ];
        default: // for 'sender', 'body', 'recipient' or any other
          return [
            {
              id: 'contains',
              label: t(
                'ctint-mf-cdss.common.routingRuleEditor.operatorContains'
              ),
              value: 'contains',
            },
            {
              id: 'equals',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorEquals'),
              value: 'equals',
            },
            {
              id: 'in',
              label: t('ctint-mf-cdss.common.routingRuleEditor.operatorIn'),
              value: 'in',
            },
          ];
      }
    },
    [t]
  );

  // Define all possible subject options internally
  const allSubjectOptions: {
    id: string;
    label: string;
    value: TSubjectType;
  }[] = [
    {
      id: 'subject',
      label: t('ctint-mf-cdss.common.routingRuleEditor.subjectLabel'),
      value: 'subject',
    },
    {
      id: 'sender',
      label: t(
        'ctint-mf-cdss.common.routingRuleEditor.senderLabel' // Assume this translation key exists or will be added
      ),
      value: 'sender',
    },
    {
      id: 'body',
      label: t(
        'ctint-mf-cdss.common.routingRuleEditor.bodyLabel' // Assume this translation key exists or will be added
      ),
      value: 'body',
    },
    {
      id: 'recipient',
      label: t(
        'ctint-mf-cdss.common.routingRuleEditor.recipientLabel' // Assume this translation key exists or will be added
      ),
      value: 'recipient',
    },
    {
      id: 'domain',
      label: t('ctint-mf-cdss.common.routingRuleEditor.domainLabel'),
      value: 'domain',
    },
    {
      id: 'emailAddressGroup',
      label: t('ctint-mf-cdss.common.routingRuleEditor.emailAddressGroupLabel'),
      value: 'emailAddressGroup',
    },
    {
      id: 'time',
      label: t(
        'ctint-mf-cdss.common.routingRuleEditor.timeLabel' // Assume this translation key exists or will be added
      ),
      value: 'time',
    },
  ];

  // Filter subject options based on enabledSubjectFields prop
  const subjectOptionsToRender = enabledSubjectFields
    ? allSubjectOptions.filter((option) =>
        enabledSubjectFields.includes(option.value)
      )
    : allSubjectOptions; // Default to all if prop not provided or handle as needed

  // Handle adding a new rule
  const handleAddRule = useCallback(() => {
    // Find the next available priority
    const usedPriorities = rules.map((r) => r.priority);
    let nextPriority = 1;
    while (usedPriorities.includes(nextPriority)) {
      nextPriority++;
    }

    // Determine default field based on enabled fields
    const defaultSubjectField =
      enabledSubjectFields && enabledSubjectFields.length > 0
        ? enabledSubjectFields[0]
        : 'subject';

    // Get default operator based on the determined default subject field
    const defaultOperators = getOperators(defaultSubjectField);
    const defaultOperator =
      defaultOperators.length > 0
        ? defaultOperators[0].value
        : ('contains' as TConditionOperator); // Fallback, though getOperators should always return something

    let defaultValue = '';
    if (defaultSubjectField === 'time') {
      // Adjust default value based on operator for time field
      if (defaultOperator === 'before' || defaultOperator === 'after') {
        defaultValue = JSON.stringify(['00:00']);
      } else {
        // For 'within', 'outside', or other operators
        defaultValue = JSON.stringify(['00:00', '00:00']);
      }
    }

    const newRule: IRoutingRule = {
      id: `rule-${Date.now()}`,
      logic:
        allowedLogicTypes && allowedLogicTypes.length > 0
          ? allowedLogicTypes[0]
          : 'AND', // Default to first allowed logic type, or 'AND' if no restriction
      priority: nextPriority,
      conditions: [
        {
          field: defaultSubjectField,
          operator: defaultOperator,
          value: defaultValue,
        },
      ],
      return: {
        queueId: queueOptions.length > 0 ? queueOptions[0].value : '',
        autoReplyId: null,
        multiLanguageTemplates: useMultiLanguageTemplates ? null : undefined,
      },
    };

    onUpdateRules([...rules, newRule]);
  }, [
    rules,
    onUpdateRules,
    enabledSubjectFields,
    getOperators,
    queueOptions,
    useMultiLanguageTemplates,
  ]);

  // Handle updating a rule - use original array order, no sorting
  const handleUpdateRule = useCallback(
    (index: number, field: keyof Omit<IRoutingRule, 'return'>, value: any) => {
      const updatedRules = [...rules];
      updatedRules[index] = { ...updatedRules[index], [field]: value };
      onUpdateRules(updatedRules);
    },
    [rules, onUpdateRules]
  );

  // Handle updating a rule's return object - use original array order, no sorting
  const handleUpdateReturnField = useCallback(
    (
      index: number,
      field: keyof IRoutingRule['return'] | '_batch_update',
      value: any
    ) => {
      console.log('handleUpdateReturnField', index, field, value);
      const updatedRules = [...rules];

      if (field === '_batch_update') {
        // Handle batch update of multiple return fields
        updatedRules[index] = {
          ...updatedRules[index],
          return: value,
        };
      } else {
        // Handle single field update
        updatedRules[index] = {
          ...updatedRules[index],
          return: {
            ...updatedRules[index].return,
            [field]: value,
          },
        };
      }

      console.log('updatedRules', updatedRules);
      onUpdateRules(updatedRules);
    },
    [rules, onUpdateRules]
  );

  // Handle adding a condition to a rule
  const handleAddCondition = useCallback(
    (ruleIndex: number) => {
      const defaultField =
        subjectOptionsToRender.length > 0
          ? subjectOptionsToRender[0].value
          : 'subject';
      const defaultOperators = getOperators(defaultField);
      const defaultOperator =
        defaultOperators.length > 0
          ? defaultOperators[0].value
          : ('contains' as TConditionOperator);

      let defaultValue = '';
      if (defaultField === 'time') {
        if (defaultOperator === 'before' || defaultOperator === 'after') {
          defaultValue = JSON.stringify(['00:00']);
        } else {
          defaultValue = JSON.stringify(['00:00', '00:00']);
        }
      }

      const newCondition: ICondition = {
        field: defaultField,
        operator: defaultOperator,
        value: defaultValue,
      };

      const updatedRules = [...rules];
      updatedRules[ruleIndex] = {
        ...updatedRules[ruleIndex],
        conditions: [...updatedRules[ruleIndex].conditions, newCondition],
      };
      onUpdateRules(updatedRules);
    },
    [rules, onUpdateRules, subjectOptionsToRender, getOperators]
  );

  // Handle updating a condition - use original array order, no sorting
  const handleUpdateCondition = useCallback(
    (ruleIndex: number, conditionIndex: number, condition: ICondition) => {
      const updatedRules = [...rules];
      const updatedConditions = [...updatedRules[ruleIndex].conditions];
      updatedConditions[conditionIndex] = condition;
      updatedRules[ruleIndex] = {
        ...updatedRules[ruleIndex],
        conditions: updatedConditions,
      };
      onUpdateRules(updatedRules);
    },
    [rules, onUpdateRules]
  );

  // Handle removing a condition - use original array order, no sorting
  const handleRemoveCondition = useCallback(
    (ruleIndex: number, conditionIndex: number) => {
      const updatedRules = [...rules];
      const updatedConditions = [...updatedRules[ruleIndex].conditions];
      updatedConditions.splice(conditionIndex, 1);
      updatedRules[ruleIndex] = {
        ...updatedRules[ruleIndex],
        conditions: updatedConditions,
      };
      onUpdateRules(updatedRules);
    },
    [rules, onUpdateRules]
  );

  // Handle removing a rule - use original array order, no sorting
  const handleRemoveRule = useCallback(
    (index: number) => {
      const updatedRules = [...rules];
      updatedRules.splice(index, 1);
      onUpdateRules(updatedRules);
    },
    [rules, onUpdateRules]
  );

  useEffect(() => {
    if (isCreate) {
      handleAddRule();
    }
  }, [isCreate]);
  if (rules.length === 0) {
    return (
      <div className="px-6 -py-2">
        {isEditMode ? (
          <Button
            size="s"
            variant="secondary"
            onClick={handleAddRule}
          >
            <Plus className="h-3 w-3 mr-1" />{' '}
            {t('ctint-mf-cdss.common.routingRuleEditor.addFirstRuleButton')}
          </Button>
        ) : (
          <p className="text-gray-500 text-sm mb-4">
            {t('ctint-mf-cdss.common.routingRuleEditor.noRulesDefined')}
          </p>
        )}
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center">
        {/* <p className="text-gray-500 text-sm">
          Configure specific routing rules based on email attributes like
          keywords, sender address, or other criteria.
        </p> */}
      </div>

      {rules.map((rule, ruleIndex) => (
        <RuleItem
          key={rule.id}
          rule={rule}
          ruleIndex={ruleIndex}
          isEditMode={isEditMode}
          onUpdateRule={handleUpdateRule}
          onUpdateReturnField={handleUpdateReturnField}
          onRemoveRule={handleRemoveRule}
          onAddCondition={handleAddCondition}
          onUpdateCondition={handleUpdateCondition}
          onRemoveCondition={handleRemoveCondition}
          queueOptions={queueOptions}
          templateOptions={templateOptions}
          validationErrors={validationErrors}
          subjectOptions={subjectOptionsToRender}
          // allRules={rules}
          showQueueInput={showQueueInput}
          useMultiLanguageTemplates={useMultiLanguageTemplates}
          allowedLogicTypes={allowedLogicTypes}
        />
      ))}

      {isEditMode && (
        <div className="p-6 pt-0">
          <Button
            size="s"
            variant="secondary"
            onClick={handleAddRule}
          >
            <Plus className="h-3 w-3 mr-1" />{' '}
            {t('ctint-mf-cdss.common.routingRuleEditor.addRuleButton')}
          </Button>
        </div>
      )}
    </div>
  );
};

export default RoutingRuleEditor;
