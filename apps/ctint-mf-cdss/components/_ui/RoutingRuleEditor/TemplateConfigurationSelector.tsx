import React, {
  useState,
  useEffect,
  useMemo,
  useC<PERSON>back,
  memo,
  FC,
} from 'react';
import { Button } from '@cdss-modules/design-system';
import { Select } from '@cdss-modules/design-system/components/_ui/Select';
import Input from '@cdss-modules/design-system/components/_ui/Input';
import {
  Trash,
  ChevronDown,
  FileText,
  Paperclip,
  FileImage,
} from 'lucide-react';
import { cn } from '@cdss-modules/design-system/lib/utils';
import { ITemplateResponse } from '../../_screen/EmailAdmin/_screen/MailboxRoutingRules/Detail';
import { fireGetAutoReplyTemplateAttachment } from '@cdss/lib/api';
import { useQuery } from '@tanstack/react-query';
import { basePath } from '@cdss/lib/appConfig';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import Fuse from 'fuse.js';
import { LoadingBlock } from '@cdss-modules/design-system';

interface IAttachmentUrlData {
  id: string;
  emailId: string;
  attachmentName: string;
  contentId: string;
  fileType: string;
  fileSize: number;
  isInLine: boolean;
  storageFileDir: string;
  storageFileName: string;
  storageShareName: string;
  url: string;
}

export interface IAttachment {
  id: string;
  title: string;
  contentType: string;
  content: string;
  metadata?: string;
  urlData?: {
    id: string;
    attachmentName: string;
    fileType: string;
    fileSize: number;
    url: string;
    contentId?: string;
    isInLine?: boolean;
  };
}

export interface ITemplateDetail {
  id: string;
  content: string;
  language: string;
  title: string;
  attachments: IAttachment[];
}

// Template Preview Component
const _TemplatePreview = ({
  template,
  onRemove,
  isLoading,
}: {
  template: ITemplateDetail | null;
  onRemove?: () => void;
  isLoading?: boolean;
}) => {
  const { t } = useTranslation();
  // Fetch all attachments in a single API call
  const { data: attachmentsData, isLoading: isLoadingAttachments } = useQuery({
    queryKey: ['templateAttachments', template?.attachments?.map((a) => a.id)],
    queryFn: async () => {
      if (!template?.attachments || template.attachments.length === 0) {
        return { data: [], error: '', isSuccess: true };
      }
      try {
        // Get all attachment IDs
        const attachmentIds = template.attachments.map(
          (attachment) => attachment.id
        );

        // Make a single API call with all IDs
        const response = await fireGetAutoReplyTemplateAttachment(
          attachmentIds,
          basePath
        );

        // console.log('Attachment response:', response.data);
        return response.data;
      } catch (error) {
        console.error('Error fetching template attachments:', error);
        return {
          data: [],
          error: 'Failed to fetch attachments',
          isSuccess: false,
        };
      }
    },
    enabled: !!template?.attachments && template.attachments.length > 0,
  });

  // Combine the attachments from the template with the fetched URL data
  const attachmentsWithUrls = useMemo(() => {
    if (!template?.attachments) return [];

    return template.attachments.map((attachment) => {
      // console.log('ids', attachment.id);
      // console.log('attachmentsData', attachmentsData?.data);
      // Find matching attachment data from the API response
      const matchingAttachment = attachmentsData?.data?.find(
        (a: IAttachmentUrlData) => a.attachmentName === attachment.content
      );

      return {
        ...attachment,
        urlData: matchingAttachment,
      };
    });
  }, [template?.attachments, attachmentsData?.data]);

  // Process HTML to replace cid links with actual URLs
  const processedHtmlContent = useMemo(() => {
    if (!template?.content || !attachmentsData?.data) {
      return template?.content || '';
    }

    let html = template.content;
    const inlineAttachments = attachmentsData.data.filter(
      (att: IAttachmentUrlData) => att.isInLine && att.contentId
    );

    inlineAttachments.forEach((att: IAttachmentUrlData) => {
      const cid = att.contentId;
      const url = att.url;

      if (cid && url) {
        // Handle both src="cid:xxxx" and src='cid:xxxx'
        const regex = new RegExp(`src=(['"])cid:${cid}\\1`, 'g');
        html = html.replace(regex, `src="${url}"`);
      }
    });
    return html;
  }, [template?.content, attachmentsData?.data]);

  if (isLoading) {
    return (
      <div className="text-center border border-gray-200 rounded-md p-2 h-full flex flex-col items-center justify-center">
        <LoadingBlock />
      </div>
    );
  }

  if (!template) {
    return (
      <div className="text-center border border-gray-200 rounded-md p-2 h-full flex flex-col items-start justify-start">
        <span className="font-bold">
          {t('ctint-mf-cdss.common.templateConfigurationSelector.previewLabel')}
        </span>
        <span className="text-gray-500 h-full self-center flex flex-col items-center justify-center gap-4">
          <IconEmptyRecords size="78" />
          {t(
            'ctint-mf-cdss.common.templateConfigurationSelector.noTemplateSelected'
          )}
        </span>
      </div>
    );
  }

  // Parse metadata for attachments if available
  const parseAttachmentMetadata = (attachment: IAttachment) => {
    try {
      if (attachment.metadata) {
        return JSON.parse(attachment.metadata);
      }
      return null;
    } catch (e) {
      console.error('Error parsing attachment metadata:', e);
      return null;
    }
  };

  // Determine if attachment is an image
  const isImageAttachment = (attachment: IAttachment) => {
    const metadata = parseAttachmentMetadata(attachment);
    return (
      metadata?.fileType?.startsWith('image/') ||
      attachment.urlData?.fileType?.startsWith('image/') ||
      false
    );
  };

  return (
    <div className="h-full flex flex-col border border-gray-200 rounded-md pt-2 px-2">
      <div className="flex justify-between items-center mb-3">
        <span className="text-sm font-semibold">
          {t('ctint-mf-cdss.common.templateConfigurationSelector.previewLabel')}
        </span>

        {onRemove && (
          <div className="flex col-span-2 justify-end">
            <Trash
              className="h-4 w-4 mr-1 hover:cursor-pointer hover:text-gray-500"
              onClick={onRemove}
            />
          </div>
        )}
      </div>

      <div className="bg-primary-200 p-2">
        <div className="flex justify-between items-center">
          <span className="font-semibold">{template.title}</span>
          <div className="bg-primary-100 text-primary-600 px-2 py-0.5 text-xs rounded">
            {template.language === 'en'
              ? t(
                  'ctint-mf-cdss.common.templateConfigurationSelector.languageEN'
                )
              : template.language === 'zh_cn'
                ? t(
                    'ctint-mf-cdss.common.templateConfigurationSelector.languageZHCN'
                  )
                : template.language === 'zh_hk'
                  ? t(
                      'ctint-mf-cdss.common.templateConfigurationSelector.languageZHTW'
                    )
                  : template.language ||
                    t(
                      'ctint-mf-cdss.common.templateConfigurationSelector.languageEN'
                    )}
          </div>
        </div>
      </div>
      {/* {template.subject && (
        <div className="bg-primary-50 p-2 mb-2">
          <span className="font-medium">Subject: </span>
          {template.subject}
        </div>
      )} */}
      <div className="p-2 bg-primary-100 mb-2 h-60 overflow-y-auto">
        {/* Render attachments if any */}
        {isLoadingAttachments && ( // Skeleton loader for attachments
          <div className="flex gap-4 overflow-x-auto py-1 animate-pulse">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="flex items-center"
              >
                <div className="h-5 w-5 mr-2 bg-gray-300 rounded"></div>
                <div className="flex-grow">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-1"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        )}
        {!isLoadingAttachments && attachmentsWithUrls.length > 0 && (
          <div className="flex gap-2">
            <div className="flex gap-4 overflow-x-auto py-1">
              {attachmentsWithUrls
                .filter((attachment) => !attachment.urlData?.isInLine)
                .map((attachment) => {
                  const metadata = parseAttachmentMetadata(attachment);
                  const attachmentName =
                    attachment.urlData?.attachmentName ||
                    metadata?.attachmentName ||
                    attachment.content ||
                    attachment.title;
                  const fileType =
                    attachment.urlData?.fileType ||
                    metadata?.fileType ||
                    attachment.contentType;
                  const fileSize =
                    attachment.urlData?.fileSize || metadata?.fileSize;
                  const downloadUrl = attachment.urlData?.url;

                  return (
                    <div
                      key={attachment.id}
                      className="flex items-center"
                    >
                      {isImageAttachment(attachment) ? (
                        <FileImage className="h-5 w-5 mr-2 text-gray-500" />
                      ) : (
                        <FileText className="h-5 w-5 mr-2 text-gray-500" />
                      )}
                      <div className="overflow-hidden flex-grow">
                        <a
                          className="text-sm truncate underline"
                          href={downloadUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          {attachmentName}
                        </a>
                        <div className="text-xs text-gray-500">
                          {fileType}{' '}
                          {fileSize
                            ? `(${Math.round(fileSize / 1024)}${t('ctint-mf-cdss.common.templateConfigurationSelector.fileSizeKB')})`
                            : ''}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        <div
          className="flex-grow mt-3"
          dangerouslySetInnerHTML={{ __html: processedHtmlContent }}
        />
      </div>
    </div>
  );
};

export const TemplatePreview = memo(_TemplatePreview);

export const languageOptions = (t: (key: string) => string) => [
  {
    id: '1',
    label: t('ctint-mf-cdss.common.templateConfigurationSelector.langEnglish'),
    value: 'en',
  },
  {
    id: '2',
    label: t(
      'ctint-mf-cdss.common.templateConfigurationSelector.langChineseSimplified'
    ),
    value: 'zh_cn',
  },
  {
    id: '3',
    label: t(
      'ctint-mf-cdss.common.templateConfigurationSelector.langChineseTraditional'
    ),
    value: 'zh_hk',
  },
];

export interface TemplateInfo {
  language: string;
  templateId: string;
}

export interface TemplateConfigurationSelectorProps {
  templateValue: TemplateInfo | null;
  queueValue: string;
  queueOptions: any[];
  templateOptions: ITemplateResponse[];
  isEditMode: boolean;
  onUpdateTemplate: (template: TemplateInfo | null) => void;
  onUpdateQueue: (queue: string) => void;
  title?: string;
  queueLabel?: string;
  templateLabel?: string;
  isLoadingQueues?: boolean;
  isLoadingTemplates?: boolean;
  queueValidationError?: string;
  alwaysShowTemplateSelector?: boolean;
  isQueueRequired?: boolean;
  showQueueInput?: boolean;
  isTemplateRequired?: boolean;
  templateDetailData?: ITemplateDetail | null;
  isLoadingTemplateDetailData?: boolean;
  languageValidationErrors?: string;
  templateValidationErrors?: string;
}

interface ITemplateItem {
  id: string;
  title: string;
  language: string;
  contentType: string;
}

interface IDirectoryItem {
  id: string;
  title: string;
  items: ITemplateItem[] | null;
  contentType: string;
}

interface ISpaceItem {
  id: string;
  name: string;
  spaceType: string;
  description: string;
  items: IDirectoryItem[] | null;
}

type TNormalizedTemplateOptions = ISpaceItem[];

const TemplateConfigurationSelector: FC<TemplateConfigurationSelectorProps> = ({
  templateValue,
  queueValue,
  queueOptions,
  templateOptions,
  isEditMode,
  onUpdateTemplate,
  onUpdateQueue,
  title: propTitle,
  queueLabel: propQueueLabel,
  templateLabel: propTemplateLabel,
  isLoadingQueues = false,
  isLoadingTemplates = false,
  queueValidationError,
  alwaysShowTemplateSelector = false,
  isQueueRequired = false,
  showQueueInput = true,
  isTemplateRequired = false,
  templateDetailData = null,
  isLoadingTemplateDetailData = false,
  languageValidationErrors,
  templateValidationErrors,
}) => {
  const { t } = useTranslation();

  const resolvedTitle =
    propTitle ||
    t('ctint-mf-cdss.common.templateConfigurationSelector.defaultTitle');
  const resolvedQueueLabel =
    propQueueLabel ||
    t('ctint-mf-cdss.common.templateConfigurationSelector.defaultQueueLabel');
  const resolvedTemplateLabel =
    propTemplateLabel ||
    t(
      'ctint-mf-cdss.common.templateConfigurationSelector.defaultTemplateLabel'
    );

  const [selectedLanguage, setSelectedLanguage] = useState<string | null>(
    templateValue?.language || null
  );

  useEffect(() => {
    if (templateValue) {
      setSelectedLanguage(templateValue.language);
    }
  }, [templateValue]);

  const [expandedSpaceId, setExpandedSpaceId] = useState<string | null>(null);
  const [expandedDirectoryId, setExpandedDirectoryId] = useState<string | null>(
    null
  );
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTemplate, setSelectedTemplate] =
    useState<ITemplateItem | null>(null);

  const NO_QUEUE_VALUE = '__NO_QUEUE_SELECTED__'; // Define a unique value for "None" option

  // Extract the actual template data from the response
  const getNormalizedTemplateOptions = (): TNormalizedTemplateOptions => {
    // Defensive checks to handle different response structures
    if (!templateOptions || templateOptions.length === 0) return [];

    // Access the first item which contains the ITemplateResponse
    const templateResponse = templateOptions[0];

    // Check if we have data property and it's an array
    if (
      templateResponse &&
      templateResponse.data &&
      Array.isArray(templateResponse.data)
    ) {
      return templateResponse.data as ISpaceItem[];
    }

    return [];
  };

  const normalizedTemplateOptions = getNormalizedTemplateOptions();

  // DEBUG: Log the normalized data structure
  // console.log('Normalized template options:', normalizedTemplateOptions);

  const allTemplatesForFuse = useMemo(() => {
    if (!normalizedTemplateOptions || normalizedTemplateOptions.length === 0) {
      return [];
    }
    const flatTemplates: ITemplateItem[] = [];
    normalizedTemplateOptions.forEach((space) => {
      if (space.items) {
        space.items.forEach((directory) => {
          if (directory.items) {
            directory.items.forEach((template) => {
              flatTemplates.push(template);
            });
          }
        });
      }
    });
    return flatTemplates;
  }, [normalizedTemplateOptions]);

  const fuse = useMemo(() => {
    return new Fuse(allTemplatesForFuse, {
      keys: ['title'],
      threshold: 0.4, // Adjust threshold for sensitivity
    });
  }, [allTemplatesForFuse]);

  const finalQueueOptions = useMemo(() => {
    const options = [...queueOptions]; // Make a mutable copy
    if (!isQueueRequired) {
      options.unshift({
        id: '---no-queue---', // A distinct ID for the "None" option
        label: t(
          'ctint-mf-cdss.common.templateConfigurationSelector.noQueueOptionLabel',
          '(None)' // Fallback text
        ),
        value: NO_QUEUE_VALUE, // Use the unique non-empty value
      });
    }
    return options;
  }, [queueOptions, isQueueRequired, t]);

  // Filter templates based on search term and language
  const getFilteredTemplates = useCallback(() => {
    const langMatches = (template: ITemplateItem) => {
      const templateLang = template.language?.toLowerCase() || '';
      const currentSelectedLang = selectedLanguage?.toLowerCase() || '';
      if (!currentSelectedLang) return true; // If no language selected, all languages match
      return (
        templateLang.includes(currentSelectedLang) ||
        currentSelectedLang.includes(templateLang)
      );
    };

    if (!normalizedTemplateOptions || normalizedTemplateOptions.length === 0) {
      return [] as TNormalizedTemplateOptions;
    }

    let templatesToFilterStructure = normalizedTemplateOptions;

    if (searchTerm) {
      const fuseResults = fuse.search(searchTerm);
      const matchedTemplateIdsFromFuse = new Set(
        fuseResults.map((result) => result.item.id)
      );

      templatesToFilterStructure = normalizedTemplateOptions
        .map((space) => {
          const filteredDirectories = space.items
            ?.map((directory) => {
              const filteredTemplates = directory.items?.filter((template) =>
                matchedTemplateIdsFromFuse.has(template.id)
              );
              return {
                ...directory,
                items:
                  filteredTemplates && filteredTemplates.length > 0
                    ? filteredTemplates
                    : null,
              };
            })
            .filter(
              (directory) =>
                directory && directory.items && directory.items.length > 0
            );
          return {
            ...space,
            items:
              filteredDirectories && filteredDirectories.length > 0
                ? filteredDirectories
                : null,
          };
        })
        .filter((space) => space.items && space.items.length > 0);
    }

    // Apply language filter to the (potentially search-filtered) list
    return templatesToFilterStructure
      .map((space) => {
        const filteredDirectories = space.items
          ?.map((directory) => {
            const filteredTemplates = directory.items?.filter(langMatches);
            return {
              ...directory,
              items:
                filteredTemplates && filteredTemplates.length > 0
                  ? filteredTemplates
                  : null,
            };
          })
          .filter(
            (directory) =>
              directory && directory.items && directory.items.length > 0
          );
        return {
          ...space,
          items:
            filteredDirectories && filteredDirectories.length > 0
              ? filteredDirectories
              : null,
        };
      })
      .filter(
        (space) => space.items && space.items.length > 0
      ) as TNormalizedTemplateOptions;
  }, [searchTerm, normalizedTemplateOptions, selectedLanguage, fuse]);

  const filteredTemplates = getFilteredTemplates();

  // useEffect for auto-expansion on search
  useEffect(() => {
    if (searchTerm && filteredTemplates.length > 0) {
      const firstSpace = filteredTemplates[0];
      if (firstSpace && firstSpace.items && firstSpace.items.length > 0) {
        const firstDirectory = firstSpace.items[0];
        if (firstDirectory) {
          setExpandedSpaceId(firstSpace.id);
          setExpandedDirectoryId(firstDirectory.id);
        }
      }
    }
    // If searchTerm is cleared or no results, expansion state is not changed by this effect,
    // preserving manual expansion or expansion from template selection.
  }, [searchTerm, filteredTemplates]);

  // Find the template by ID across the entire structure
  const findTemplateById = (
    templateId: string | null
  ): ITemplateItem | null => {
    if (!templateId || !normalizedTemplateOptions) return null;

    for (const space of normalizedTemplateOptions) {
      if (space.items) {
        for (const directory of space.items) {
          if (directory.items) {
            for (const template of directory.items) {
              if (template.id === templateId) {
                return template;
              }
            }
          }
        }
      }
    }
    return null;
  };

  // Find the selected template
  useEffect(() => {
    if (!normalizedTemplateOptions || !templateValue) {
      setSelectedTemplate(null);
      return;
    }

    const template = findTemplateById(templateValue.templateId);
    if (template) {
      setSelectedTemplate(template);

      // Make sure we set the correct language when initially loading a template
      if (templateValue.language) {
        const normalizedLanguage = templateValue.language.toLowerCase();
        setSelectedLanguage(normalizedLanguage);
      }

      // Find parent space and directory to expand them
      for (const space of normalizedTemplateOptions) {
        if (space.items) {
          for (const directory of space.items) {
            if (directory.items) {
              const foundTemplate = directory.items.find(
                (t) => t.id === templateValue.templateId
              );
              if (foundTemplate) {
                setExpandedSpaceId(space.id);
                setExpandedDirectoryId(directory.id);
                return;
              }
            }
          }
        }
      }
    } else {
      setSelectedTemplate(null);
    }
  }, [normalizedTemplateOptions, templateValue]);

  // Handle language change
  const handleLanguageChange = (value: string) => {
    console.log('value', value);
    setSelectedLanguage(value);
    setSelectedTemplate(null);
    // setExpandedDirectoryId(null); // Removed to allow auto-expansion logic to take precedence
  };

  // Handle space expansion toggle
  const handleToggleSpace = (spaceId: string) => {
    setExpandedSpaceId(expandedSpaceId === spaceId ? null : spaceId);
    setExpandedDirectoryId(null);
  };

  // Handle directory expansion toggle
  const handleToggleDirectory = (directoryId: string) => {
    setExpandedDirectoryId(
      expandedDirectoryId === directoryId ? null : directoryId
    );
  };

  // Handle template selection
  const handleSelectTemplate = (template: ITemplateItem) => {
    setSelectedTemplate(template);

    // Normalize the language and ensure it's never null
    let normalizedLanguage = 'en'; // Default to English

    if (template.language) {
      // Normalize template language
      const templateLang = template.language.toLowerCase();
      // Find matching language option
      const foundLanguage = languageOptions(t).find(
        (option) =>
          templateLang.includes(option.value) ||
          option.value.includes(templateLang)
      );

      if (foundLanguage) {
        normalizedLanguage = foundLanguage.value;
      } else if (templateLang.startsWith('zh')) {
        // Special handling for Chinese variants
        if (templateLang.includes('cn') || templateLang.includes('hans')) {
          normalizedLanguage = 'zh_cn';
        } else if (templateLang.includes('hk')) {
          normalizedLanguage = 'zh_hk';
        }
      }
    } else if (selectedLanguage) {
      // Use already selected language if template doesn't specify one
      normalizedLanguage = selectedLanguage;
    }

    // Update the selected language in the component
    setSelectedLanguage(normalizedLanguage);

    onUpdateTemplate({
      language: normalizedLanguage,
      templateId: template.id,
    });
  };

  const handleRemoveTemplate = useCallback(() => {
    setSelectedTemplate(null);
    onUpdateTemplate(null);
  }, [onUpdateTemplate]);

  // Render queue selection with loading state
  const renderQueueSelect = () => {
    if (isLoadingQueues) {
      return (
        <div className="animate-pulse mt-1 h-9 bg-gray-200 rounded w-full"></div>
      );
    }

    return (
      <Select
        showSearch
        disabled={!isEditMode}
        value={queueValue}
        onChange={(selectedValue) => {
          if (selectedValue === NO_QUEUE_VALUE) {
            onUpdateQueue('');
          } else {
            onUpdateQueue(String(selectedValue));
          }
        }}
        options={finalQueueOptions}
        isPagination={false}
        placeholder={t(
          'ctint-mf-cdss.common.templateConfigurationSelector.placeholder'
        )}
        status={queueValidationError ? 'danger' : undefined}
        labelClassName="h-full"
        triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
      />
    );
  };

  // Render the template selection UI with 3-layer structure
  const renderTemplateSelectionUI = () => {
    if (isLoadingTemplates) {
      return (
        <div className="text-center py-4">
          {t(
            'ctint-mf-cdss.common.templateConfigurationSelector.loadingTemplates'
          )}
        </div>
      );
    }

    if (filteredTemplates.length === 0) {
      return (
        <div className="text-center py-4 text-gray-500">
          {t(
            'ctint-mf-cdss.common.templateConfigurationSelector.noTemplatesForLanguage'
          )}
        </div>
      );
    }

    return filteredTemplates.map((space) => (
      <div
        key={space.id}
        className="mb-2"
      >
        <button
          className={cn(
            'w-full flex items-center justify-between p-2  text-left text-sm font-semibold',
            expandedSpaceId === space.id && 'bg-primary-100'
          )}
          onClick={() => !searchTerm && handleToggleSpace(space.id)}
          disabled={!!searchTerm}
        >
          <span>{space.name}</span>
          <ChevronDown
            className={cn(
              'h-4 w-4 transition-transform',
              expandedSpaceId === space.id && 'rotate-180'
            )}
          />
        </button>

        {expandedSpaceId === space.id && space.items && (
          <div className="">
            {space.items.map((directory) => (
              <div
                key={directory.id}
                className="mb-1"
              >
                <button
                  className={cn(
                    'w-full flex items-center justify-between p-2 pl-4  text-left text-sm',
                    expandedDirectoryId === directory.id && 'bg-primary-100'
                  )}
                  onClick={() =>
                    !searchTerm && handleToggleDirectory(directory.id)
                  }
                  disabled={!!searchTerm}
                >
                  <span>{directory.title}</span>
                  <ChevronDown
                    className={cn(
                      'h-4 w-4 transition-transform',
                      expandedDirectoryId === directory.id && 'rotate-180'
                    )}
                  />
                </button>

                {expandedDirectoryId === directory.id && directory.items && (
                  <div className="">
                    {directory.items.map((template) => (
                      <button
                        key={template.id}
                        className={cn(
                          'w-full text-left p-2 pl-6 text-sm truncate',
                          selectedTemplate?.id === template.id
                            ? 'bg-primary-100 text-primary-600'
                            : 'hover:bg-gray-100'
                        )}
                        onClick={() => handleSelectTemplate(template)}
                      >
                        {template.title}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    ));
  };

  return (
    <div className="mt-4">
      {resolvedTitle && (
        <h3 className="text-sm font-semibold mb-2">{resolvedTitle}</h3>
      )}
      {/* Queue selection */}
      {showQueueInput && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div className="col-span-1">
            <label className="block text-sm font-semibold mb-1">
              {resolvedQueueLabel}
              {isQueueRequired && <span className="text-red-500 ml-1">*</span>}
            </label>
            {renderQueueSelect()}
            {queueValidationError && (
              <p className="mt-1 text-sm text-red-500">
                {queueValidationError}
              </p>
            )}
          </div>
        </div>
      )}
      {/* Template selection */}
      <div className="mb-4">
        <label className="block text-sm font-semibold mb-1">
          {resolvedTemplateLabel}
          {isTemplateRequired && <span className="text-red-500 ml-1">*</span>}
        </label>

        <div className="grid items-center gap-3 grid-cols-2 mb-2">
          <div className="col-span-1">
            <span className="text-sm font-semibold">
              {t(
                'ctint-mf-cdss.common.templateConfigurationSelector.languageLabel'
              )}
            </span>
            <Select
              disabled={!isEditMode}
              value={selectedLanguage || ''}
              onChange={(value) => handleLanguageChange(String(value))}
              options={languageOptions(t)}
              isPagination={false}
              placeholder={t(
                'ctint-mf-cdss.common.templateConfigurationSelector.selectLanguagePlaceholder'
              )}
              labelClassName="h-full"
              triggerClassName="disabled:opacity-100 disabled:bg-grey-100 h-8 disabled:text-black text-sm"
              status={languageValidationErrors ? 'danger' : undefined}
              message={languageValidationErrors}
            />
          </div>
        </div>

        {isEditMode ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 h-full">
            <div className="col-span-1 flex flex-col gap-2">
              {isEditMode && (
                <Input
                  disabled={!isEditMode}
                  size="s"
                  placeholder={t(
                    'ctint-mf-cdss.common.templateConfigurationSelector.searchTemplatesPlaceholder'
                  )}
                  value={searchTerm}
                  onChange={(value) => setSearchTerm(String(value))}
                  allowClear
                  className="disabled:text-black"
                />
              )}
              {/* Template selection UI */}
              <div className="border border-gray-200 rounded-md h-[290px] overflow-y-auto">
                {renderTemplateSelectionUI()}
              </div>
            </div>

            <div>
              <TemplatePreview
                template={templateDetailData}
                onRemove={handleRemoveTemplate}
                isLoading={isLoadingTemplateDetailData}
              />
            </div>
          </div>
        ) : (
          <TemplatePreview
            template={templateDetailData}
            isLoading={isLoadingTemplateDetailData}
          />
        )}

        {templateValidationErrors && (
          <p className="mt-1 text-sm text-red-500">
            {templateValidationErrors}
          </p>
        )}
      </div>
    </div>
  );
};

export default TemplateConfigurationSelector;
