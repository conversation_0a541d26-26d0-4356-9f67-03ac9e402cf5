import { useRole, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import {
  GetAgentRoutingStatus,
  GetAllAgentStatus,
  UpdateAgentRoutingStatus,
} from '@cdss/lib/api';
import { useAgentActivityStore } from '@cdss/store/agentActivityState';
import React, { useEffect, useState } from 'react';

interface TAgentActivityActionBox {
  right?: string;
  top?: string;
}
const AgentActivityActionBox = ({
  right = '1rem',
  top = '1rem',
}: TAgentActivityActionBox) => {
  const { activity, setActivity } = useAgentActivityStore();
  const { toPath, basePath, allRoutes } = useRouteHandler();
  const [isShow, setIsShow] = useState(false);
  const user = useRole();
  useEffect(() => {
    const agentId = user.userConfig?.id || '';

    getRoutingStatus(agentId);

    const unsubscribe = useAgentActivityStore.subscribe((state, prevState) => {
      // console.log('AgentActivityActionBox Previous state:', prevState);
      // console.log('AgentActivityActionBoxCurrent state:', state);
      if ('NOT_RESPONDING' == state.activity) {
        setIsShow(true);
      } else {
        setIsShow(false);
      }
    });

    // 组件卸载时取消订阅
    return () => {
      unsubscribe();
    };
  }, []);

  const getRoutingStatus = async (agentId: string) => {
    const getRoutingStatusRs = await GetAgentRoutingStatus(agentId, basePath);
    const activity = getRoutingStatusRs.data.data.status;
    console.log(activity);
    if ('NOT_RESPONDING' == activity) {
      setIsShow(true);
    } else {
      setIsShow(false);
    }
  };

  const updateRoutingStatus = async () => {
    //7ffa01cd-7866-4ad7-8faa-31e11b42f51f
    const updateRs = await UpdateAgentRoutingStatus(basePath);
    console.log(updateRs);
  };

  return (
    <div
      className="z-50 min-w-96 bg-white rounded-xl shadow-lg p-5 space-y-4 absolute conversationPopup"
      style={{
        right: right ? `${right}` : '0',
        top: top ? `${top}` : '0',
        display: isShow ? 'block' : 'none',
      }}
    >
      <div className="flex w-full items-center space-x-2">
        <span className="text-lg">
          You are currently &quot;Not Responding&quot;
        </span>
      </div>

      <div className="flex gap-4 pt-2">
        <button
          onClick={() => {
            updateRoutingStatus();
          }}
          className="flex-1 px-2 py-2 bg-black text-white rounded-lg text-lg font-medium hover:bg-gray-800 transition-colors"
        >
          Make eligible for interactions
        </button>
      </div>
    </div>
  );
};

export default AgentActivityActionBox;
