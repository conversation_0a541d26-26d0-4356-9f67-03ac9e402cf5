import React, { useEffect, useState } from 'react';
import { ChevronDown, Search } from 'lucide-react';

type FilterType = 'current' | 'history' | 'all';

interface SearchWithDropdownProps {
  onSearch: (term: string) => void;
  onFilterChange: (filter: FilterType) => void;
  setCurrentFilter?: (filter: FilterType) => void;
  currentFilter?: string;
}

const SearchWithDropdown: React.FC<SearchWithDropdownProps> = ({
  onSearch,
  onFilterChange,
  setCurrentFilter,
  currentFilter,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [selectedFilter, setSelectedFilter] = useState('Current');
  const [searchTerm, setSearchTerm] = useState('');

  const filters = [
    { id: 'current' as FilterType, label: 'Current' },
    { id: 'history' as FilterType, label: 'History' },
  ];

  const handleFilterSelect = (filter: (typeof filters)[0]) => {
    setSelectedFilter(filter.label);
    setIsDropdownOpen(false);
    onFilterChange(filter.id);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };
  useEffect(() => {
    setCurrentFilter &&
      setCurrentFilter(
        filters?.find((item) => {
          return item?.label === selectedFilter;
        })?.id as FilterType
      );
  }, []);
  return (
    <div className="relative flex w-full px-4 mb-4 mt-1">
      <div className="flex w-full border border-gray-200 rounded-lg overflow-visible">
        {/* Dropdown Button */}
        <div className="relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            className="flex items-center px-4 py-2 bg-white border-r border-gray-200 hover:bg-gray-50 min-w-[100px] justify-between"
          >
            <span className="text-gray-700">
              {currentFilter || selectedFilter}
            </span>
            <ChevronDown className="w-4 h-4 text-gray-500 ml-2" />
          </button>

          {/* Dropdown Menu */}
          {isDropdownOpen && (
            <div className="absolute top-full left-0 mt-1 w-40 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
              {filters.map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => handleFilterSelect(filter)}
                  className="w-full px-4 py-2 text-left hover:bg-gray-50 text-gray-700"
                >
                  {filter.label}
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Search Input */}
        <div className="flex-1 flex items-center relative">
          <input
            type="text"
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Search"
            className="w-full pl-10 pr-4 py-2 focus:outline-none"
          />
          <div className="absolute left-3">
            <Search className="w-4 h-4 text-gray-400" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SearchWithDropdown;
