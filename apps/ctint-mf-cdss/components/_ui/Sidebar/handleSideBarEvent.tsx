import {
  BaseConversationItem,
  CallbackConvItem,
  EmailConvItem,
  MessageConvItem,
} from '@cdss-modules/design-system/@types/Conversation';
import { extractNumbersFromTel } from './generateSideBarData';
import { TContact } from '@cdss-modules/design-system/@types/index';

export const eventDataFilter = (str: string): string | null => {
  // console.log(str);
  if (!str || typeof str !== 'string') {
    return null;
  }

  // 定义所有可能的路径部分
  const validTypes = ['messages', 'calls', 'callbacks', 'emails'];
  const validStates: { [key: string]: string[] } = {
    messages: [
      'alerting',
      'connected',
      'disconnected',
      'wrapup',
      'messageChanged',
    ],
    calls: ['alerting', 'connected', 'connecting', 'disconnected', 'wrapup'],
    callbacks: ['alerting', 'connected', 'disconnected', 'wrapup'],
    emails: ['alerting', 'connected', 'disconnected', 'wrapup'],
  };

  // 构建完整的正则表达式
  const regex = new RegExp(
    '^conversations\\.' + // 修正拼写：conversations
      '[\\w-]+\\.' + // 允许字母、数字、下划线和连字符的 conversationId
      `(${validTypes.join('|')})` + // 类型部分
      '\\.' +
      `(${Object.values(validStates).flat().join('|')})$` // 状态部分
  );

  // 测试基本格式
  const match = str.match(regex);
  if (!match) {
    return null;
  }

  // 提取类型和状态
  const [, type, state] = match;

  // 验证状态是否对应正确的类型
  if (!validStates[type].includes(state)) {
    return null;
  }

  // 所有检查都通过，返回原始字符串
  return str;
};

export const analyzeEventDataSource = (event: string): any => {
  try {
    console.log('match event', event);
    const regex = /^(\w+)\.[\w-]+\.(\w+)\.(\w+)$/;
    // const str = "conversations.ae8d8a61-7627-4a6a-a097-eb4aac908f57.messages.alerting";
    const match = event.match(regex);
    if (!match) {
      console.error('Invalid event data source format');
      return {};
    }
    // return match as RegExpMatchArray;
    return {
      type: match[1],
      category: match[2],
      eventStatus: match[3],
    };
  } catch (error) {
    console.error('error');
  }
  return {};
};

const getName = (
  eventData: any,
  directoryList: TContact[] | undefined
): any => {
  let isInbound =
    eventData?.originatingDirection === 'inbound' ||
    eventData?.participants?.agent?.[eventData?.participants?.agent?.length - 1]
      ?.direction === 'inbound';
  if (
    eventData?.participants?.customer &&
    eventData?.participants?.customer?.length > 0
  ) {
    const sortedData = eventData?.participants?.customer?.sort(
      (a: any, b: any) => {
        const timeA = new Date(a?.connectedTime || a?.startTime)?.getTime();
        const timeB = new Date(b?.connectedTime || a?.startTime)?.getTime();
        return timeA - timeB;
      }
    );
    const customerData = sortedData?.[0];
    if (
      !eventData?.participants?.agent?.[
        eventData?.participants?.agent?.length - 1
      ]?.direction ||
      eventData?.originatingDirection
    ) {
      isInbound = customerData?.direction === 'inbound';
    }
    return (
      extractNumbersFromTel(customerData?.address) ||
      (isInbound
        ? extractNumbersFromTel(customerData?.ani)
        : extractNumbersFromTel(customerData?.dnis)) ||
      (isInbound
        ? extractNumbersFromTel(
            eventData?.participants?.agent?.[
              eventData?.participants?.agent?.length - 1
            ]?.ani
          )
        : extractNumbersFromTel(
            eventData?.participants?.agent?.[
              eventData?.participants?.agent?.length - 1
            ]?.dnis
          )) ||
      eventData?.participants?.agent?.[
        eventData?.participants?.agent?.length - 1
      ]?.address
    );
  } else if (
    eventData?.participants?.consult &&
    eventData?.participants?.consult?.length > 0
  ) {
    const sortedData = eventData?.participants?.consult?.sort(
      (a: any, b: any) => {
        const timeA = new Date(a?.connectedTime || a?.startTime)?.getTime();
        const timeB = new Date(b?.connectedTime || a?.startTime)?.getTime();
        return timeA - timeB;
      }
    );
    const userId = sortedData?.[sortedData?.length - 1]?.user?.id;
    const user = directoryList?.find((item) => item?.id === userId);
    if (directoryList) {
      return (
        user?.name ||
        extractNumbersFromTel(
          directoryList
            ?.find((item) => item?.id === userId)
            ?.primaryContactInfo?.find(
              (contact) => contact?.mediaType === 'PHONE'
            )?.address || 'N/A'
        ) ||
        (isInbound
          ? extractNumbersFromTel(
              eventData?.participants?.agent?.[
                eventData?.participants?.agent?.length - 1
              ]?.ani
            )
          : extractNumbersFromTel(
              eventData?.participants?.agent?.[
                eventData?.participants?.agent?.length - 1
              ]?.dnis
            ))
      );
    }
  } else {
    if (isInbound) {
      return extractNumbersFromTel(
        eventData?.participants?.agent?.[
          eventData?.participants?.agent?.length - 1
        ]?.ani
      );
    } else {
      return (
        extractNumbersFromTel(
          eventData?.participants?.agent?.[
            eventData?.participants?.agent?.length - 1
          ]?.dnis
        ) ||
        extractNumbersFromTel(
          eventData?.participants?.agent?.[
            eventData?.participants?.agent?.length - 1
          ]?.errorInfo?.messageParams?.destinationAddress
        ) ||
        'unknown'
      );
    }
  }
  // if(eventData?.participants?.agent[eventData?.participants?.agent?.length - 1]?.direction==="outbound"){

  // }
  // if(eventData?.participants?.agent[eventData?.participants?.agent?.length - 1]?.direction==="inbound"){

  // }
};
export const handleEventData = (
  type: string,
  category: string,
  status: string,
  messageEventData: any,
  directoryList?: TContact[]
): any => {
  const icon = 'call';
  // console.log(type,category,status);

  if ('conversations' == type) {
    if ('wrapup' == status) {
      const eventData = messageEventData?.eventData?.data;
      return {
        status: status,
        conversation: {
          // participantId: eventData.currentParticipant.id,
          id: eventData?.id,
          type: 'calls' == category ? 'call' : category,
          icon: icon,
        },
      };
    }

    if ('disconnected' == status) {
      const eventData = messageEventData?.eventData?.data;

      return {
        status,
        conversation: {
          participantId: eventData?.currentParticipant?.id,
          id: eventData?.id,
          type: 'calls' == category ? 'call' : category,
          queueId: eventData?.currentParticipant?.queue?.id,
          disconnectType: eventData?.currentParticipant?.disconnectType,
          wrapupPrompt: eventData?.currentParticipant?.wrapupPrompt,
          wrapupRequired: eventData?.currentParticipant?.wrapupRequired,
          startTime: eventData?.currentParticipant?.connectedTime,
          connectedTime: eventData?.currentParticipant?.connectedTime,
          endTime: eventData?.endTime,
          userName: getName(eventData, directoryList),
          icon: icon,
        },
      };
    }

    if ('messages' == category) {
      const messageHandleRs = handleMessageEventData(status, messageEventData);
      // console.log(messageHandleRs);
      return messageHandleRs;
    }
    if ('callbacks' == category) {
      const callbackHandleRs = handleCallbackEventData(
        status,
        messageEventData
      );
      return callbackHandleRs;
    }

    if ('calls' == category) {
      const callHandleRs = handleCallEventData(
        status,
        messageEventData,
        directoryList
      );
      return callHandleRs;
    }

    if ('emails' == category) {
      //处理emails
      const emailHandleRs = handleEmailEventData(status, messageEventData);
      return emailHandleRs;
    }
  }

  return { status: status, conversation: null };
};

export const handleCallEventData = (
  status: string,
  messageEventData: any,
  directoryList: TContact[] | undefined
) => {
  const eventData = messageEventData?.eventData?.data;
  let icon = 'call';
  if (
    eventData &&
    (eventData?.participants?.agent?.[
      eventData?.participants?.agent?.length - 1
    ]?.direction === 'inbound' ||
      eventData?.originatingDirection === 'inbound')
  ) {
    icon = 'call';
  }
  if (
    eventData &&
    (eventData?.participants?.agent?.[
      eventData?.participants?.agent?.length - 1
    ]?.direction === 'outbound' ||
      eventData?.originatingDirection === 'outbound')
  ) {
    icon = 'outbound';
  }
  if ('alerting' == status) {
    return {
      status: status,
      conversation: {
        participantId: eventData?.currentParticipant?.id,
        id: eventData?.id,
        userName: getName(eventData, directoryList),
        type: 'call',
        queueId: eventData?.currentParticipant?.queue?.id,
        icon: icon,
      },
    };
  }
  if ('connecting' == status) {
    return {
      status: status,
      conversation: {
        participantId: eventData?.currentParticipant?.id,
        id: eventData?.id,
        userName: getName(eventData, directoryList),
        type: 'call',
        queueId: eventData?.currentParticipant?.queue?.id,
        borderProgress: 0,
        bgProgress: 0,
        icon: icon,
      },
    };
  }
  if ('connected' == status) {
    return {
      status: status,
      conversation: {
        participantId: eventData?.currentParticipant?.id,
        id: eventData?.id,
        userName: getName(eventData, directoryList),
        type: 'call',
        queueId: eventData?.currentParticipant?.queue?.id,
        startTime: eventData?.currentParticipant?.connectedTime,
        connectedTime: eventData?.currentParticipant?.connectedTime,
        endTime: '',
        borderProgress: 0,
        bgProgress: 0,
        icon: icon,
      },
    };
  }
  // if ('disconnected' == status) {
  //   return {
  //     status: status,
  //     conversation: {
  //       participantId: eventData?.currentParticipant?.id,
  //       id: eventData?.id,
  //       userName: eventData?.currentParticipant?.address,
  //       type: 'call',
  //       queueId: eventData?.currentParticipant?.queue?.id,
  //       disconnectType: eventData?.currentParticipant?.disconnectType,
  //       startTime: eventData?.currentParticipant?.connectedTime,
  //       endTime: eventData?.endTime,
  //     },
  //   };
  // }

  // return { status, conversation };
};

export const handleCallbackEventData = (
  status: string,
  messageEventData: any
): any => {
  // console.log(status,messageEventData.eventData);
  const eventData = messageEventData?.eventData?.data;
  if ('alerting' == status) {
    let userName = '';
    if (eventData?.address && eventData.address.startsWith('tel:')) {
      userName = eventData.address.substring(4); // 移除 'tel:' 前缀
    } else {
      userName = eventData?.currentParticipant?.callbackNumbers?.[0] || '';
    }

    console.log('callback alerting');
    return {
      status: status,
      conversation: {
        participantId: eventData?.currentParticipant?.id,
        id: eventData.id,
        userName: userName,
        type: 'callback',
        queueId: eventData?.currentParticipant?.queue?.id,
        mediaType: eventData?.mediaType,
      },
    };
  }

  if ('connected' == status) {
    const mediaType = eventData?.mediaType || 'callback';
    const iconType =
      mediaType === 'callback.voicemail' ? 'voicemail' : 'callback';

    // Apply the same logic for connected status
    let userName = '';

    if (eventData?.address && eventData.address.startsWith('tel:')) {
      userName = eventData.address.substring(4);
    } else {
      userName = eventData?.currentParticipant?.callbackNumbers?.[0] || '';
    }

    const conversation: CallbackConvItem = {
      id: eventData?.id,
      userName: userName, // Use the computed userName here
      startTime:
        eventData?.startTime || eventData?.currentParticipant?.connectedTime,
      connectedTime: eventData?.currentParticipant?.connectedTime,
      endTime: '',
      type: 'callback',
      isActive: false,
      borderProgress: 0,
      bgProgress: 0,
      icon: iconType,
      queueId: eventData?.currentParticipant?.queue?.id,
      mediaType: eventData?.mediaType,
    };

    // console.log(status, conversation);
    return { status, conversation };
  }

  // if ('disconnected' == status) {
  //   return {
  //     status,
  //     conversation: {
  //       participantId: eventData.currentParticipant.id,
  //       id: eventData.id,
  //       userName: eventData.currentParticipant.callbackNumbers[0],
  //       type: 'callback',
  //       queueId: eventData.currentParticipant.queue.id,
  //       disconnectType: eventData.currentParticipant.disconnectType,
  //     },
  //   };
  // }

  return { status, conversation: null };
};

export const handleMessageEventData = (
  status: string,
  messageEventData: any
): any => {
  // console.log(status,messageEventData.eventData);
  const eventData = messageEventData?.eventData?.data;
  if ('alerting' == status) {
    return {
      status: status,
      conversation: {
        participantId: eventData?.currentParticipant?.id,
        id: eventData.id,
        userName: eventData?.userName,
        type: 'message',
        queueId: eventData?.currentParticipant?.queue?.id,
      },
    };
  }

  if ('messageChanged' == status) {
    console.log(eventData);
    const newMessageList = eventData;

    const hasInbound = newMessageList.some(
      (item: any) => item.direction === 'inbound'
    );

    const inboundMessages = newMessageList.filter(
      (item: any) => item?.direction === 'inbound' && item?.timestamp
    );

    const lastMessage = inboundMessages.reduce((latest: any, current: any) => {
      const currentTime = new Date(current.timestamp).getTime();
      const latestTime = new Date(latest.timestamp).getTime();
      return currentTime > latestTime ? current : latest;
    }, inboundMessages?.[0]);

    return {
      status: status,
      conversation: {
        id: newMessageList?.[0].conversationId,
        type: 'message',
        newMessage: hasInbound,
        lastMessage:
          lastMessage?.textBody || lastMessage?.medias?.[0]?.filename,
      },
    };
  }

  if ('connected' == status) {
    let conversation: BaseConversationItem = {
      id: eventData.id,
      userName: eventData?.userName,
      startTime: eventData?.startTime,
      connectedTime: eventData?.startTime,
      endTime: '',
      type: 'message',
      isActive: false,
      borderProgress: 0,
      bgProgress: 0,
      icon: '',
      queueId: eventData?.currentParticipant?.queue?.id,
    };

    conversation = {
      ...conversation,
      userAddress: eventData?.userAddress,
      icon: 'whatsapp',
      integrationId: eventData?.integrationId,
      originatingDirection: eventData?.integrationId,
      hasUnread: false,
      latestMessage: '',
      latestMessageTime: '',
    } as MessageConvItem;

    // console.log(status, conversation);
    return { status, conversation };
  }

  // if ('disconnected' == status) {
  //   return {
  //     status,
  //     conversation: {
  //       participantId: eventData.currentParticipant.id,
  //       id: eventData.id,
  //       userName: eventData.userName,
  //       type: 'message',
  //       queueId: eventData.currentParticipant.queue.id,
  //       disconnectType: eventData.currentParticipant.disconnectType,
  //     },
  //   };
  // }

  return { status, conversation: null };
};

export const handleEmailEventData = (
  status: string,
  messageEventData: any
): any => {
  const eventData = messageEventData?.eventData?.data;
  if ('alerting' == status) {
    return {
      status: status,
      conversation: {
        participantId: eventData?.currentParticipant?.id,
        id: eventData.id,
        userName:
          eventData?.sourceEmailAddress ||
          eventData?.userName ||
          eventData?.currentParticipant?.address,
        type: 'email',
        queueId: eventData?.currentParticipant?.queue?.id,
      },
    };
  }

  if ('connected' == status) {
    const conversation: EmailConvItem = {
      id: eventData?.id,
      userName:
        eventData?.sourceEmailAddress ||
        eventData?.userName ||
        eventData?.currentParticipant?.address,
      startTime:
        eventData?.startTime || eventData?.currentParticipant?.connectedTime,
      connectedTime: eventData?.currentParticipant?.connectedTime,
      endTime: '',
      type: 'email',
      isActive: false,
      borderProgress: 0,
      bgProgress: 0,
      icon: 'email',
      queueId: eventData?.currentParticipant?.queue?.id,
    };

    return { status, conversation };
  }

  return { status, conversation: null };
};
