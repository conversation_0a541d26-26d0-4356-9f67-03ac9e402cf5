// import Link from 'next/link';
// import Icon from '../Icon';
import { cn } from '@cdss-modules/design-system/lib/utils';
import ConversationsContainer from './ConversationsContainer';
import NewConversationContainer from './NewConversation';
import IconExpandClose from '@cdss-modules/design-system/components/_ui/Icon/IconExpandClose';
import IconExpandOpen from '@cdss-modules/design-system/components/_ui/Icon/IconExpandOpen';
import { useState, useEffect, useMemo } from 'react';
import { useConversationStore } from '../../../store/conversation';
import { useQueuesStore } from '../../../store/queues';
import SearchWithDropdown from './searchBar';
import {
  useRouteHandler,
  useRole,
  toast,
  Button,
  useToast,
} from '@cdss-modules/design-system';
import { Plus } from 'lucide-react';
import {
  fireGetDirectory,
  GetActiveConversations,
  GetQueues,
  PickUpConversationAction,
} from '@cdss/lib/api';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import { useQueryClient } from '@tanstack/react-query';
import { ManualItemComponent } from '@cdss-modules/design-system/components/_ui/ManualQueueItem';
import { microfrontends } from '@cdss/types/microfrontendsConfig';
import {
  checkDuplicatedConversation,
  getName,
  initConversationData,
  initConversationList,
} from './generateSideBarData';
import {
  analyzeEventDataSource,
  eventDataFilter,
  handleEventData,
} from './handleSideBarEvent';
import ConversationPopup from './conversationPopup';
import { TCallActionProps } from '@cdss/types/conversation';
import { useAgentActivityStore } from '@cdss/store/agentActivityState';
import React from 'react';
import {
  TContact,
  TInteractionParticipant,
} from '@cdss-modules/design-system/@types/Interaction';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';

import { ConversationItem } from '@cdss-modules/design-system/@types/Conversation';
import { useInView } from 'react-intersection-observer';
import { useOpenStationContext } from '@cdss-modules/design-system/context/StationContext';
import { useTbarContext } from '@cdss-modules/design-system/context/TBarContext';

interface SidebarProps {
  onExpand?: (isExpanded: boolean) => void;
}

type FilterType = 'current' | 'history' | 'all';

const Sidebar: React.FC<SidebarProps> = ({ onExpand }) => {
  const { toPath, basePath, allRoutes } = useRouteHandler();
  const { globalConfig } = useRole();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const manualQueues =
    microfrontendsConfig?.['ctint-mf-cdss']?.['manual-queue'] || [];
  const autoAnswerList: string[] =
    microfrontendsConfig?.['ctint-mf-cdss']?.['autoAnswerList'] || [];
  const [isExpanded, setIsExpanded] = useState(true);
  const [directoryList, setDirectoryList] = useState<TContact[] | undefined>(
    undefined
  );
  const { lastMessage, eventData, sendMessage } = useInteractionContext();
  const { getWorkgroupByUserHandle } = useGetworkGroupOrUser();
  const { queues, setQueues } = useQueuesStore();
  const { activity, setActivity } = useAgentActivityStore();
  const [showPickBox, setShowPickBox] = useState(false);
  const [alertingConversation, setAlertingConversation] = useState<{
    id?: any;
    [key: string]: any;
  }>({});
  const [isNewConversation, setIsNewConversation] = useState(false);
  const { open, setOpen, openOption, setOpenOption } = useOpenStationContext();
  const { dismiss } = useToast();
  const user = useRole();
  const {
    // fetchConversations,
    currentConversationId,
    conversations,
    setCurrentActiveConversation,
    messages,
    setConversations,
    addNewMessagesConversations,
    readNewMessagesConversations,
    setLastMessage,
    // setConversationLatest,
  } = useConversationStore();

  const {
    stationContext: { station, stationHandler },
    conversationHistoryListHandle,
    searchConversations,
  } = useTbarContext();
  useEffect(() => {
    const fetchData = async () => {
      try {
        const res = await fireGetDirectory(basePath);
        setDirectoryList(res?.data?.data || undefined);
        fetchActiveConversations(res?.data?.data); // get all active conversations when the page load
        fetchAllqueues();
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    fetchData();
    console.log('当前autoAnswerList:', autoAnswerList);
  }, []);

  // 获取当前对话
  const currentConversation = conversations.find(
    (conv) => conv.id === currentConversationId
  );
  useEffect(() => {
    // console.log('useEffect 触发', conversations);
  }, [conversations]);

  useEffect(() => {
    // start how to handle the websocket event
    if (!lastMessage || lastMessage.data == 'ping') {
      return;
    }
    // console.log(lastMessage?.data);
    let messageEventData: { event?: any; [key: string]: any } = {};
    try {
      messageEventData = JSON.parse(lastMessage?.data);
      //setConversations([newConversation, ...conversations])
    } catch (error) {
      console.log(lastMessage?.data, error);
    }
    const eventString = messageEventData?.event;
    if ('agent.state.changed' == eventString) {
      // for trigger the "make elligiable for interactive popup"
      if (user.userConfig?.id == messageEventData.eventData.data.id) {
        const activityState =
          messageEventData.eventData.data.routingStatus.status;
        console.log(activityState);
        if (activity != activityState) {
          console.log('changed activity');
          setActivity(activityState);
        }
      }
      return;
    }

    const validEventString = eventDataFilter(eventString); // to filter the event string for call, message, voice mail
    console.log('validEventString', validEventString);
    if (validEventString) {
      const eventMetrics = analyzeEventDataSource(validEventString);
      const { type, category, eventStatus } = eventMetrics;
      console.log(type, category, eventStatus); // type -> conversation category-> call,message, eventStatus -> alerting/connected/disc/wrap

      const { status, conversation } = handleEventData(
        // output the different situation data for alerting, connected, disconnected, wrapup
        type,
        category,
        eventStatus,
        messageEventData,
        directoryList
      );
      if ('alerting' == status) {
        console.log(status, conversation);
        if (conversation) {
          handleFilterChange('current');
          setAlertingConversation(conversation);
          setShowPickBox(true);
        }
      }
      if ('connecting' == status) {
        if (conversation) {
          handleFilterChange('current');
          setAlertingConversation(conversation);
        }
      }

      // if connected, will show the corresponding conversation in the sidebar
      if ('connected' == status || 'connecting' == status) {
        if (!conversation) {
          return;
        }
        setShowPickBox(false);
        // to check if the event will be isDuplicated, to avoid the conversation show twice
        const isDuplicated = checkDuplicatedConversation(
          conversation,
          conversations
        );
        if (category === 'calls' && isDuplicated) {
          const newConv = conversations?.map((item) => {
            if (item?.id === conversation?.id) {
              return conversation;
            }
            return item;
          });
          setConversations(newConv);
        }
        if (!isDuplicated) {
          setConversations([conversation, ...conversations]);
        }
        // 检查autoAnswerList 如果存在则不需要切换会话
        console.log('当前会话category:', category);
        if (!autoAnswerList.includes(category)) {
          toPath(`/${conversation.type}?conversationId=${conversation.id}`);
          setCurrentActiveConversation(conversation.id);
        }
        // 为了john 的 message changed event，即使agent 无接conversation , 依然发 message changed event 导致的问题而写的代码
        // const newMessagesConversations =
        //   useConversationStore.getState().newMessagesConversations;

        // if (newMessagesConversations.includes(conversation.id)) {
        //   readNewMessagesConversations(conversation.id);
        // }
      }

      // when wrapup, will remove the corresponding conversation in the sidebar
      if ('wrapup' == status) {
        // console.log(status);
        //conversation
        removeConversationAction(conversation);
      }
      if ('disconnected' == status) {
        const isDuplicated = checkDuplicatedConversation(
          conversation,
          conversations
        );
        if (category === 'calls' && isDuplicated) {
          const newConv = conversations?.map((item) => {
            if (item?.id === conversation?.id) {
              return conversation;
            }
            return item;
          });
          setConversations(newConv);
        }
        const wrapupRequired = conversation.wrapupRequired;
        setShowPickBox(false);
        if (!conversation) return;
        const disconnectType = conversation.disconnectType;
        if ('client' == disconnectType) {
          // for when the user click the reject conversation
          console.log(disconnectType);
          //   setShowPickBox(false);
          const wrapupPrompt = conversation.wrapupPrompt;

          if ('agentRequested' == wrapupPrompt && !wrapupRequired) {
            removeConversationAction(conversation);
          }
        }
        if (!wrapupRequired) {
          removeConversationAction(conversation);
        }
        if ('system' == disconnectType) {
          // for the user ignore or no aware the new conversation come in, then hide the pick up box
          console.log(disconnectType);
          if (conversation.id == alertingConversation?.id) {
            // setShowPickBox(false);
          }
        }

        if ('transfer' == disconnectType) {
          /* empty */
        }
      }

      if ('messageChanged' == status) {
        console.log('messageChanged conversation: ', conversations);
        if (conversation) {
          const conversationId = conversation.id;
          const isNewMessage = conversation.newMessage;
          setLastMessage(conversationId, conversation.lastMessage);
          if (isNewMessage) {
            // if user in the current conversation, also when a new message in, there is no red dot show.
            if (conversationId != currentConversationId) {
              addNewMessagesConversations(conversationId);
            }
          }
        }
      }
    }
  }, [lastMessage, directoryList]);

  // 获取当前对话的消息列表
  // const currentMessages = currentConversationId
  //   ? messages.find(
  //       (message) => (message.conversationId = currentConversationId)
  //     ) || []
  //   : [];
  const [searchTerm, setSearchTerm] = useState('');
  const [currentFilter, setCurrentFilter] = useState<FilterType>('current');
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    searchConversations(term);
  };

  const queryClient = useQueryClient();
  const handleFilterChange = (filter: FilterType) => {
    setCurrentFilter(filter);
    if (filter == 'history') {
      queryClient?.resetQueries({ queryKey: ['get-conversation-history'] });
    }
  };
  const filterConvList = useMemo(() => {
    const historyData = conversationHistoryListHandle?.data?.pages.flatMap(
      (page: any) => page?.data
    );
    if (conversationHistoryListHandle?.data) {
      return initConversationList(historyData, directoryList)?.filter(
        (v: ConversationItem) => {
          return v?.userName
            ?.toLowerCase()
            ?.includes(searchTerm?.toLowerCase());
        }
      );
    }
    return [];
  }, [conversationHistoryListHandle.data, searchTerm]);

  // Filter conversations based on search term and current filter
  const filteredConversations = conversations
    .filter((conversation) => {
      // console.log('conversation.userName', conversation.userName);
      // console.log('filter..........');
      const matchesSearch = conversation?.userName
        ?.toLowerCase()
        ?.includes(searchTerm?.toLowerCase());

      // Apply additional filtering based on currentFilter if needed
      // console.log('matchesSearch', matchesSearch);
      switch (currentFilter) {
        case 'current':
          return matchesSearch;
        case 'history':
          return false;
        case 'all':
        default:
          return false;
      }
    })
    .sort((a, b) => {
      // 假设对话对象有一个 timestamp 属性
      if (!a?.connectedTime || !b?.connectedTime) {
        return 0;
      }
      if (a?.connectedTime < b?.connectedTime) return 1;
      if (a?.connectedTime > b?.connectedTime) return -1;
      return 0;
    });
  // console.log('searchTerm:', searchTerm);
  // 监听 isExpanded 的变化，并调用 onExpand 回调函数
  useEffect(() => {
    onExpand && onExpand(isExpanded);
  }, [isExpanded, onExpand]);

  const fetchActiveConversations = async (directory?: TContact[]) => {
    const deviceId = localStorage.getItem('deviceId');
    let activeConversationListRs = await GetActiveConversations(
      basePath,
      user.userConfig?.id,
      deviceId
    );
    activeConversationListRs = activeConversationListRs.data.data;

    // for handle the active list contain a missing alerting conversation, then show the pick up box again.
    const alertingConversationData = findAlertingInitData(
      activeConversationListRs,
      user.userConfig?.id,
      directory
    );

    if (alertingConversationData) {
      setAlertingConversation(alertingConversationData);
      setShowPickBox(true);
    }

    // set init active conversations show on the sidebar, but filter the alerting conversation.
    setConversations(
      initConversationData(
        activeConversationListRs,
        alertingConversationData,
        directory
      )
    );
  };

  const getUsernameFromAPI = (foundEvent: any) => {
    try {
      // Check if foundEvent and required properties exist
      if (
        !foundEvent ||
        !foundEvent.eventData ||
        !foundEvent.eventData.data ||
        !foundEvent.eventData.data.customer
      ) {
        return '';
      }

      // Get the customer array
      const customerArr = foundEvent.eventData.data.customer;
      if (!customerArr || customerArr.length === 0) {
        return '';
      }

      // If there's only one customer, set it as the most recent customer
      let mostRecentCustomer = null;
      if (customerArr.length === 1) {
        mostRecentCustomer = customerArr[0];
      } else {
        // Find the most recent customer entry (based on messageTime)
        let mostRecentTime = new Date(0);

        for (const customer of customerArr) {
          if (customer.messages && customer.messages.length > 0) {
            for (const message of customer.messages) {
              if (message.messageTime) {
                const messageTime = new Date(message.messageTime);
                if (messageTime > mostRecentTime) {
                  mostRecentTime = messageTime;
                  mostRecentCustomer = customer;
                }
              }
            }
          }
        }
      }

      // Extract username from the most recent customer
      if (
        mostRecentCustomer &&
        mostRecentCustomer.fromAddress &&
        mostRecentCustomer.fromAddress.name
      ) {
        console.log(
          'mostRecentCustomer.fromAddress.name',
          mostRecentCustomer.fromAddress.name
        );
        return mostRecentCustomer.fromAddress.name;
      } else if (
        mostRecentCustomer &&
        mostRecentCustomer.fromAddress &&
        mostRecentCustomer.fromAddress.addressRaw
      ) {
        console.log(
          'mostRecentCustomer.fromAddress.addressRaw',
          mostRecentCustomer.fromAddress.addressRaw
        );
        return mostRecentCustomer.fromAddress.addressRaw;
      }

      return '';
    } catch (error) {
      console.error('Error extracting username:', error);
      return '';
    }
  };

  const findAlertingInitData = (
    jsonData: any,
    userId?: string,
    directory?: TContact[]
  ): any | null => {
    try {
      const dataArray = jsonData;
      if (!dataArray || !Array.isArray(dataArray)) {
        return null;
      }

      const foundEvent = dataArray.find((item) => {
        const agents = item?.eventData?.data?.agent;
        if (!agents || !Array.isArray(agents)) {
          return false;
        }

        return agents.some(
          (agent) =>
            agent.user?.id === userId &&
            (agent.state === 'alerting' || agent.state === 'connecting') &&
            !agent.endTime
        );
      });

      if (!foundEvent) {
        return null;
      }

      // 找到符合条件的 agent
      const alertingAgent = foundEvent.eventData.data.agent.find(
        (agent: TInteractionParticipant) =>
          agent.user?.id === userId &&
          (agent.state === 'alerting' || agent.state === 'connecting') &&
          !agent.endTime
      );
      let userName = '';

      if ('callback' == foundEvent.type) {
        console.log('首先检查是否有 customer 类型的参与者并获取其 address');
        // 首先检查是否有 customer 类型的参与者并获取其 address
        if (
          foundEvent.eventData?.data?.customer &&
          Array.isArray(foundEvent.eventData.data.customer) &&
          foundEvent.eventData.data.customer.length > 0
        ) {
          const customer = foundEvent.eventData.data.customer[0];
          if (customer.address && typeof customer.address === 'string') {
            userName = customer.address.startsWith('tel:')
              ? customer.address.substring(4)
              : customer.address;
          }
        }
        // 其次检查 address 字段
        else if (
          foundEvent.eventData?.data?.address &&
          foundEvent.eventData.data.address.startsWith('tel:')
        ) {
          userName = foundEvent.eventData.data.address.substring(4);
        } else {
          userName =
            alertingAgent.callbackNumbers &&
            Array.isArray(alertingAgent.callbackNumbers) &&
            alertingAgent.callbackNumbers.length > 0
              ? alertingAgent.callbackNumbers[0]
              : 'Unknown Caller';

          // 如果号码以 "tel:" 开头，去掉前缀
          if (userName.startsWith('tel:')) {
            userName = userName.substring(4);
          }
        }
      }

      if ('message' == foundEvent.type) {
        userName = getUsernameFromAPI(foundEvent);
      }

      if ('call' == foundEvent.type) {
        userName = getName(foundEvent, directory);
      }
      if ('email' == foundEvent.type) {
        userName = getName(foundEvent, directory);
      }

      //TODO call -> if ('call' == foundEvent.type) {}

      return {
        participantId: alertingAgent.id,
        id: foundEvent.id,
        userName: userName,
        type: foundEvent.type,
        queueId: alertingAgent.queue?.id || '',
      };
    } catch (error) {
      console.error('Error finding event details:', error);
      return null;
    }
  };

  const fetchAllqueues = async () => {
    const queuesRS = await GetQueues(basePath);
    // console.log(queuesRS.data.data);
    //console.log('queueInfo:', getWorkgroupByUserHandle?.data);
    setQueues(queuesRS.data.data || []);
  };

  const pickUpConversationAction = async (
    conversationsId: string,
    participantId: string,
    action: TCallActionProps
  ) => {
    const actionRs = await PickUpConversationAction(
      conversationsId,
      participantId,
      action,
      basePath
    );
    if (actionRs.status === 200) {
      setShowPickBox(false);
    }
  };

  const removeConversationAction = (conversation: any) => {
    setConversations(
      conversations.filter((conv) => conv.id !== conversation.id)
    );

    // wrapup the current conversation, then navigate to the first conversation
    if (useConversationStore.getState().conversations.length > 0) {
      const firstConversation =
        useConversationStore.getState().conversations[0];

      toPath(
        `/${firstConversation.type}?conversationId=${firstConversation.id}`
      );
      setCurrentActiveConversation(firstConversation.id);
    } else {
      toPath('/');
    }
  };

  const renderItem = () => {
    const renderList =
      currentFilter == 'current' ? filteredConversations : filterConvList;
    return renderList;
  };

  const handleToastClose = () => {
    dismiss();
  };
  const selectStationTip = () => {
    return toast({
      variant: 'error',
      title: 'Warning',
      description: (
        <div className="flex flex-col w-full gap-2">
          <div>You have no phone selected and will not receive calls.</div>
          <div className="flex w-full gap-2">
            <Button
              variant={'primary'}
              size={'s'}
              onClick={() => {
                setOpenOption(true);
                setOpen(true);
                handleToastClose();
                stationHandler?.refetch();
              }}
            >
              Select Phone
            </Button>
            <Button
              variant={'blank'}
              size={'s'}
              onClick={() => {
                handleToastClose();
              }}
            >
              Close
            </Button>
          </div>
        </div>
      ),
    });
  };
  const { ref, inView } = useInView();
  useEffect(() => {
    if (inView && currentFilter == 'history') {
      conversationHistoryListHandle?.fetchNextPage();
    }
  }, [inView, currentFilter]);
  return (
    <>
      <nav
        className={cn(
          'relative h-[calc(100vh_-_64px)] transition-width shrink-0',
          isExpanded ? 'w-80' : 'w-24'
        )}
      >
        {isNewConversation && (
          <NewConversationContainer
            returnEvent={setIsNewConversation}
            queues={getWorkgroupByUserHandle?.data || []}
          />
        )}
        {!isNewConversation && (
          <section className="flex flex-col bg-common-white h-full w-full shadow-contact-window">
            {/* search bar */}
            <div className={cn(isExpanded ? 'visible' : 'hidden')}>
              <SearchWithDropdown
                onSearch={handleSearch}
                onFilterChange={handleFilterChange}
                setCurrentFilter={setCurrentFilter}
                currentFilter={currentFilter}
              />
            </div>
            {/* chat list */}
            <div className="overflow-auto flex-1">
              <ConversationsContainer
                currentFilter={currentFilter}
                isExpanded={isExpanded}
                items={renderItem()}
                currentConversationId={currentConversationId}
                onItemClick={(item) => {
                  toPath(
                    `/${item?.type == 'outbound' ? 'call' : item.type}?conversationId=${item.id}`
                  );
                  // setCurrentConversationId(item.id);
                  setCurrentActiveConversation(item.id);
                  readNewMessagesConversations(item.id);
                  // console.log('all conversations', conversations);
                }}
              />
              <div
                ref={currentFilter == 'history' ? ref : undefined}
                style={{ height: '1px' }}
              />
            </div>
            <div className="flex-1 flex flex-col">
              {!isExpanded && (
                <div className="w-full aspect-square p-1 grid place-items-center cursor-pointer">
                  {/* <img
                    src={iconBase.sidebarCollapseAdd}
                    alt="collapse icon"
                  /> */}
                  <Plus
                    onClick={() => {
                      /* Handle new conversation */
                      setIsNewConversation(true);
                      setIsExpanded(true);
                    }}
                  />
                </div>
              )}
              {/* menu */}
              <div
                className={`items-center justify-center border-t-2 p-2 w-11/12 ml-1 flex}`}
              >
                {manualQueues.map((queue, index) => (
                  <ManualItemComponent
                    key={index}
                    type={queue.type}
                    queueName={queue.queueName}
                    isExpanded={isExpanded}
                    onClick={() => {
                      toPath(`/manualQueue?type=${queue.type}`);
                    }}
                    //queueId={''}
                  />
                ))}
              </div>

              {isExpanded && (
                <div
                  className="w-full h-full px-4 py-2 flex flex-col mb-10"
                  style={{ height: 'calc(100% - 40px)' }}
                >
                  <button
                    className="mt-auto flex items-center justify-center gap-2 w-full py-3 border-2 border-dashed border-gray-300 hover:border-gray-400 text-gray-600 hover:text-gray-700 rounded-lg transition-colors"
                    onClick={() => {
                      /* Handle new conversation */
                      setIsNewConversation(true);
                    }}
                  >
                    <Plus className="w-4 h-4" />
                    <span>New conversation</span>
                  </button>
                </div>
              )}
            </div>
            {/* float footer */}
            <div
              className={`h-auto flex items-center p-2 absolute bottom-0 ${isExpanded ? 'justify-end right-5' : 'justify-center w-full'}`}
            >
              {isExpanded ? (
                <IconExpandClose
                  size="24"
                  alt="expand"
                  color="#949494"
                  className="cursor-pointer"
                  onClick={() => setIsExpanded(!isExpanded)}
                />
              ) : (
                <IconExpandOpen
                  size="24"
                  alt="expand"
                  color="#949494"
                  className="cursor-pointer"
                  onClick={() => setIsExpanded(!isExpanded)}
                />
              )}
            </div>
          </section>
        )}

        {showPickBox && (
          <ConversationPopup
            declineAction={() => {
              console.log('declineAction');
              pickUpConversationAction(
                alertingConversation.id,
                alertingConversation.participantId,
                'disconnect'
              );
            }}
            pickUpAction={() => {
              if (alertingConversation?.type == 'call') {
                if (!station || station?.id === '') {
                  selectStationTip();
                  return;
                }
                if (station?.type === 'inin_webrtc_softphone') {
                  const payload = {
                    type: 'updateInteractionState',
                    data: {
                      action: 'pickup',
                      id: alertingConversation.id,
                    },
                  };
                  const softphoneElem = document.getElementById(
                    'softphone'
                  ) as any;
                  if (softphoneElem) {
                    softphoneElem.contentWindow.postMessage(
                      JSON.stringify(payload),
                      '*'
                    );
                  }
                  return;
                }
              }
              pickUpConversationAction(
                alertingConversation.id,
                alertingConversation.participantId,
                'pickup'
              );
            }}
            type={alertingConversation.type}
            name={alertingConversation.userName}
            queueId={alertingConversation.queueId}
            conversation={alertingConversation}
            mediaType={alertingConversation.mediaType}
          />
        )}
      </nav>
    </>
  );
};

// Create a client
const InteractionSidebar = () => {
  return <Sidebar />;
};

export default InteractionSidebar;
