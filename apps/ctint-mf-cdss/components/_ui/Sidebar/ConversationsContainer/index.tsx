import { ConversationItem } from '@cdss-modules/design-system/@types/Conversation';
import { ConversationItemComponent } from '@cdss-modules/design-system/components/_ui/ConversationItem';
import React, { useState } from 'react';
import { useEffect } from 'react';
import { useConversationStore } from '../../../../store/conversation';
import { MessageData } from '@cdss-modules/design-system/@types/Message';

interface ConversationsContainerProps {
  isExpanded: boolean;
  items: ConversationItem[];
  currentConversationId: string | null;
  onItemClick?: (item: ConversationItem) => void;
  currentFilter?: string;
}

const ConversationsContainer: React.FC<ConversationsContainerProps> = ({
  isExpanded,
  items,
  currentConversationId,
  onItemClick, //这个函数会被绑定到每个对话项的点击事件上
  currentFilter,
}) => {
  const [newMessageConversations, setNewMessageConversations] = useState<
    string[]
  >([]);
  const { setConversations } = useConversationStore();
  useEffect(() => {
    // const unsubscribe = useConversationStore.subscribe((state, prevState) => {
    //   // console.log('useConversationStore', state.newMessagesConversations);
    //   setNewMessageConversations(state.newMessagesConversations);

    //   const messages = state.messages;
    //   const lastMessages = extractLastMessages(messages);
    //   // console.log('lastMessages', lastMessages);
    //   // console.log('conversations', state.conversations);
    //   const updateLastMessageConversations =
    //     updateConversationsWithLatestMessages(
    //       state.conversations,
    //       lastMessages
    //     );
    //   console.log(
    //     'updateLastMessageConversations',
    //     updateLastMessageConversations
    //   );
    //   setConversations(updateLastMessageConversations);
    // });
    const unsubscribeRedDot = useConversationStore.subscribe(
      (state) => state.redothints,
      (redothint) => {
        // console.log('only subscribe red dot');
        setNewMessageConversations(redothint);
      }
    );

    const unsubscribeNewMessage = useConversationStore.subscribe(
      (state) => state.messages,
      (messages) => {
        console.log('only subscribe newMessages');
        console.log('messages', messages);
        const lastMessages = extractLastMessages(messages);
        const updateLastMessageConversations =
          updateConversationsWithLatestMessages(
            useConversationStore.getState().conversations,
            lastMessages
          );
        // console.log(
        //   'updateLastMessageConversations',
        //   updateLastMessageConversations
        // );
        setConversations(updateLastMessageConversations);
      }
    );

    // 组件卸载时取消订阅
    return () => {
      unsubscribeRedDot();
      unsubscribeNewMessage();
    };
  }, []);

  // useEffect(() => {}, [items]);
  // const renderItem = (item: ConversationItem, index: number) => {
  //   // 创建 props 时不包含 key
  //   const props = {
  //     isExpanded: isExpanded,
  //     onClick: () => onItemClick?.(item),
  //   };
  //   return (
  //     <ConversationItemComponent
  //       key={item.id} // key 直接作为属性传递
  //       {...props}
  //       conversation={item}
  //     />
  //   );
  // };
  const extractLastMessages = (messages: MessageData[]) => {
    const result: { [key: string]: string } = {};

    messages.forEach((message) => {
      const conversationId = message.conversationId;

      if (message.messages && message.messages.length > 0) {
        const lastMessage = message.messages[message.messages.length - 1];

        result[conversationId] =
          lastMessage.textBody || lastMessage.medias?.[0]?.filename || '';
        if (lastMessage.type == 'whatsappTemplate') {
          result[conversationId] = 'Sent out a template message...';
        }
      }
    });

    return result;
  };

  /**
   * 根据最新消息更新会话数据
   * @param conversations 原始会话数据数组
   * @param latestMessages 最新消息对象，key为会话ID，value为最新消息内容
   * @returns 更新后的会话数据数组
   */
  const updateConversationsWithLatestMessages = (
    conversations: ConversationItem[],
    latestMessages: { [key: string]: string }
  ): ConversationItem[] => {
    // 创建一个新数组来存储更新后的会话数据
    const updatedConversations = conversations.map((conversation: any) => {
      // 检查当前会话ID是否在需要更新的消息中
      if (latestMessages[conversation.id]) {
        // 获取当前时间作为最新消息时间
        const now = new Date().toISOString();

        // 返回更新后的会话对象

        console.log('lastMessageFull', {
          ...conversation,
          latestMessage: latestMessages[conversation.id],
          latestMessageTime: now,
        });

        return {
          ...conversation,
          latestMessage: latestMessages[conversation.id],
          latestMessageTime: now,
        };
      }
      // 如果不在更新列表中，则返回原始会话对象

      return conversation;
    });

    // 返回整个更新后的会话数组
    return updatedConversations;
  };

  return (
    <div className="flex flex-col w-full overflow-y-auto">
      {items.map((item, index) => {
        return (
          // <div>123</div>
          <ConversationItemComponent
            key={index} // key 直接作为属性传递
            isExpanded={isExpanded}
            onClick={() => onItemClick?.(item)}
            conversation={item}
            currentFilter={currentFilter}
            hasNewMessage={newMessageConversations.includes(item.id)} // 直接计算布尔值，而不是传递函数
          />
        );
      })}
    </div>
  );
};

export default ConversationsContainer;
