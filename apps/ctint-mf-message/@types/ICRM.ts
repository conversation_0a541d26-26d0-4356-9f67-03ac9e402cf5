// 方向类型
type Direction = 'inbound' | 'outbound';

// 阻止规则接口
interface BlockingRule {
  isBlocked: boolean;
  description: string;
  direction: Direction;
}

// 客户联系方式接口
interface CustomerContact {
  id: string;
  customerId: string;
  contactType: string;
  contactValue: string;
  blockingRules: BlockingRule[];
}

// 客户接口
interface Customer {
  id: string;
  chineseName: string;
  englishName: string;
  gender: string;
  language: string;
  referenceId: string;
  customerContacts: CustomerContact[];
}

// API响应接口
interface ICRMResponse {
  nextPageLink?: string;
  customers: Customer[];
}
