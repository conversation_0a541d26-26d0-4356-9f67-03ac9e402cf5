import { Component } from './waTemplate';

export interface Parameter {
  type: 'image' | 'text' | 'payload';
  image?: {
    id?: string;
    link: string;
  };
  text?: string;
  payload?: string;
}

export interface TemplateComponent {
  type: 'header' | 'body' | 'button';
  parameters: Parameter[];
  sub_type?: 'quick_reply' | 'url';
  index?: number;
}

interface TemplatePayload {
  type: 'template';
  template: {
    name: string;
    language: {
      code: string;
    };
    components: TemplateComponent[];
  };
}
export interface PreviewTemplate {
  components: Component[];
}

export interface TMPayload {
  type: 'whatsappTemplate';
  whatsappTemplate: {
    payload: {
      type: 'template';
      template: {
        name: string;
        language: {
          code: string;
        };
        components: TemplateComponent[];
      };
    };
    preview: PreviewTemplate;
    metaData: Record<string, unknown>;
  };
}
