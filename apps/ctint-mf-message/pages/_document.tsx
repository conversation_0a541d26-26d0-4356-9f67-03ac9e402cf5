import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import { Html, Head, Main, NextScript } from 'next/document';
import Script from 'next/script';
declare global {
  interface Window {
    GLOBAL_ENV_VARS: any;
    GLOBAL_BASE_PATH: any;
    GLOBAL_MF_NAME: any;
    $pageSpy: any;
  }
}

export default function Document() {
  const mfName = process.env.mfName || '';
  const globalConfig = loadGlobalConfig(mfName) as any;
  const basePath = globalConfig?.microfrontends?.[mfName]?.basepath || '';
  const baseUrl = `${typeof window !== 'undefined' ? window.location.protocol : 'http:'}//ctint-cdss3-uat-aks.eastasia.cloudapp.azure.com`;

  return (
    <Html lang="en">
      <Head>
        <link
          rel="icon"
          href={`${basePath}/favicon.ico`}
        />
        <script
          id="GLOBAL_ENV"
          dangerouslySetInnerHTML={{
            __html: `window.GLOBAL_BASE_PATH = "${basePath}";window.GLOBAL_MF_NAME = "${mfName}";`,
          }}
        />
        <Script
          src={`${baseUrl}/tracking/plugin/data-harbor/index.min.js`}
          strategy="beforeInteractive"
        />
        <Script
          src={`${baseUrl}/tracking/plugin/rrweb/index.min.js`}
          strategy="beforeInteractive"
        />
      </Head>
      <body>
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}
