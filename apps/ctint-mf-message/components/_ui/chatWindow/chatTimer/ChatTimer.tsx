// import React from 'react';
//
// interface ChatTimerProps {
//   timestamp: number | string | Date;
//   className?: string;
// }
//
// const ChatTimer: React.FC<ChatTimerProps> = ({ timestamp, className }) => {
//   const formatTime = (date: Date) => {
//     return date.toLocaleTimeString('en-US', {
//       hour: '2-digit',
//       minute: '2-digit',
//       hour12: false,
//     });
//   };
//
//   const getDisplayTime = (timestamp: number | string | Date) => {
//     const messageDate = new Date(timestamp);
//     const now = new Date();
//     const time = formatTime(messageDate);
//
//     // 判断是否是同一天
//     const isToday = messageDate.toDateString() === now.toDateString();
//     if (isToday) {
//       return time;
//     }
//
//     // 判断是否是昨天
//     const yesterday = new Date(now);
//     yesterday.setDate(now.getDate() - 1);
//     const isYesterday = messageDate.toDateString() === yesterday.toDateString();
//     if (isYesterday) {
//       return `Yesterday ${time}`;
//     }
//
//     // 判断是否是本周
//     const startOfWeek = new Date(now);
//     startOfWeek.setDate(now.getDate() - now.getDay());
//     const isThisWeek = messageDate >= startOfWeek;
//     if (isThisWeek) {
//       return `${messageDate.toLocaleString('en-US', { weekday: 'long' })} ${time}`;
//     }
//
//     // 其他情况显示完整日期
//     return messageDate
//       .toLocaleString('en-US', {
//         year: 'numeric',
//         month: '2-digit',
//         day: '2-digit',
//         hour: '2-digit',
//         minute: '2-digit',
//         hour12: false,
//       })
//       .replace(',', '');
//   };
//
//   return <span className={className}>{getDisplayTime(timestamp)}</span>;
// };
//
// export default ChatTimer;
