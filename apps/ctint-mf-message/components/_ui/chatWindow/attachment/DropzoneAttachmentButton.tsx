import React from 'react';
import { cn } from '@cdss-modules/design-system/lib/utils';

interface DropzoneAttachmentButtonProps {
  onToggleAttachmentPage: () => void; // Function to open the attachment page
  disabled?: boolean;

  // 自定义样式类
  className?: string;
  buttonClassName?: string;
  iconClassName?: string;
}

const DropzoneAttachmentButton: React.FC<DropzoneAttachmentButtonProps> = ({
  onToggleAttachmentPage,
  disabled = false,
  className,
  buttonClassName,
  iconClassName,
}) => {
  // Handle button click to open attachment page
  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();

    if (!disabled) {
      onToggleAttachmentPage();
    }
  };

  return (
    <div
      className={cn(
        'relative',
        disabled ? 'opacity-50 cursor-not-allowed' : 'hover:cursor-pointer',
        className
      )}
    >
      <button
        className={cn('flex items-center justify-center', buttonClassName)}
        disabled={disabled}
        type="button"
        onClick={handleButtonClick}
      >
        <svg
          width="36"
          height="36"
          viewBox="0 0 36 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className={cn(iconClassName)}
        >
          <path
            d="M23.6253 10.983C22.6457 10.0037 21.0558 10.0037 20.0762 10.983L12.6889 18.3677C10.9986 20.0574 10.9986 22.7946 12.6889 24.4842C14.3791 26.1739 17.1172 26.1739 18.8075 24.4842L24.91 18.3838C25.3477 17.9463 26.0623 17.9463 26.4999 18.3838C26.9375 18.8213 26.9375 19.5356 26.4999 19.9731L20.3974 26.0735C17.8279 28.6422 13.6685 28.6422 11.099 26.0735C8.5295 23.5049 8.5295 19.347 11.099 16.7784L18.4863 9.39367C20.3452 7.53544 23.3563 7.53544 25.2152 9.39367C27.074 11.2519 27.074 14.262 25.2152 16.1202L18.1491 23.1839C17.0008 24.3317 15.1379 24.3317 13.9897 23.1839C12.8414 22.036 12.8414 20.1738 13.9897 19.0259L19.771 13.2466C20.2087 12.8091 20.9233 12.8091 21.3609 13.2466C21.7985 13.684 21.7985 14.3984 21.3609 14.8359L15.5796 20.6153C15.3106 20.8842 15.3106 21.3256 15.5796 21.5945C15.8485 21.8634 16.2902 21.8634 16.5592 21.5945L23.6253 14.5309C24.6049 13.5516 24.6049 11.9623 23.6253 10.983Z"
            fill="black"
          />
        </svg>
      </button>
    </div>
  );
};

export default DropzoneAttachmentButton;
