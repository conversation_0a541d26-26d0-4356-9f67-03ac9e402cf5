import React from 'react';
import { Loader2 } from 'lucide-react';

interface EndMessageButtonProps {
  onClick: () => void;
  disabled: boolean;
  isEnding?: boolean;
}

const EndMessageButton: React.FC<EndMessageButtonProps> = ({
  onClick,
  disabled,
  isEnding = false,
}) => {
  return (
    <button
      onClick={onClick}
      className={`p-2 rounded-lg transition-colors ${
        disabled || isEnding
          ? 'cursor-not-allowed opacity-50'
          : 'hover:bg-gray-100'
      }`}
      aria-label={isEnding ? 'Ending Message' : 'End Message'}
      disabled={disabled || isEnding}
    >
      {isEnding ? (
        <Loader2 className="w-6 h-6 animate-spin" />
      ) : (
        <svg
          width="25"
          height="25"
          viewBox="0 0 25 25"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M18 15V8.33333C18 7.97971 17.8595 7.64057 17.6095 7.39052C17.3594 7.14048 17.0203 7 16.6667 7H10"
            stroke="black"
            strokeWidth="1.33333"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M5.33301 6.33398L18.6663 19.6673"
            stroke="black"
            strokeWidth="1.33333"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M6.4 7.40039C6.13333 7.60039 6 7.93372 6 8.33372V19.0004L8.66667 16.3337H15.3333"
            stroke="black"
            strokeWidth="1.33333"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}
    </button>
  );
};

export default EndMessageButton;
