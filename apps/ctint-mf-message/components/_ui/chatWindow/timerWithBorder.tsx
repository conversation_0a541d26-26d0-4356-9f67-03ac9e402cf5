import React, { useState, useEffect } from 'react';

const TimerWithBorder: React.FC<{
  startTime: string | undefined;
  endTime: string | undefined;
  isConnected: boolean | undefined;
}> = ({ startTime, endTime, isConnected = false }) => {
  const [duration, setDuration] = useState('00:00:00');
  const [isLive, setIsLive] = useState(false);
  const [effectiveStartTime, setEffectiveStartTime] = useState<
    string | undefined
  >(undefined);

  useEffect(() => {
    // Initialize effectiveStartTime when component mounts or when startTime changes
    setEffectiveStartTime(startTime || new Date().toISOString());
  }, [startTime]);

  useEffect(() => {
    let intervalId: NodeJS.Timer;

    const calculateDuration = () => {
      if (!effectiveStartTime) return;

      const start = new Date(effectiveStartTime).getTime();
      const end = endTime ? new Date(endTime).getTime() : Date.now();

      let diff = Math.floor((end - start) / 1000);

      const hours = Math.floor(diff / 3600);
      diff %= 3600;
      const minutes = Math.floor(diff / 60);
      const seconds = diff % 60;

      const formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;

      setDuration(formattedTime);
    };

    if (effectiveStartTime) {
      if (!endTime && isConnected) {
        setIsLive(true);
        calculateDuration();
        intervalId = setInterval(calculateDuration, 1000);
      } else {
        setIsLive(false);
        calculateDuration();
      }
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [effectiveStartTime, endTime, isConnected]);

  return (
    <div className="relative inline-flex items-center">
      <div className="border border-[#DEDEDE] px-3 py-1 h-[23px] min-w-[58px] flex items-center justify-center">
        <span className="font-mono text-[14px] text-black">{duration}</span>
      </div>
    </div>
  );
};

export default TimerWithBorder;
