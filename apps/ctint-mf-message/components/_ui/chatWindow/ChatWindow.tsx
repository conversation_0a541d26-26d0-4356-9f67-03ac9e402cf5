/* eslint-disable @next/next/no-img-element */
import React, { useState, useRef, useEffect, useMemo } from 'react';
import { Loader2, MessageSquare } from 'lucide-react';
import ChatTool from './ChatTool';
import TemplatePage from './template/TemplatePage';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-expect-error
import { useConversationStore } from 'cdss/store/conversation';
import {
  CDSSMessage,
  MessageData,
  MediaItem,
  ApiResponse,
} from '@cdss-modules/design-system/@types/Message';
import {
  BroadcastApiResponse,
  TemplateParameter,
} from '@cdss-modules/design-system/@types/broadcastTemplate';
import {
  BodyComponent,
  Component,
  FooterComponent,
  Button,
  HeaderComponent,
  ButtonsComponent,
} from '../../../@types/waTemplate';
import { useMessageLoadingStore } from '../store/MessageLoadingStore';
import {
  getMessages,
  uploadMedia,
  msgTransfer,
  msgTransferCancel,
  getICRMCustomers,
  getSupportedContent,
  markRead,
  GetBroadcastMessage,
} from '../../../lib/api';
import {
  Popup,
  PopupContent,
  Toaster,
  useRole,
  useRouteHandler,
  useToast,
} from '@cdss-modules/design-system';
import { v4 as uuidv4 } from 'uuid';
import ChatWindowHeader from './ChatWindowHeader';
import {
  microfrontends,
  TransferReq,
} from '../../../@types/microFrontendConfigs';
import { useInteractionContext } from '@cdss-modules/design-system/context/InteractionContext';
import { User } from '../../../@types/microFrontendConfigs';
import { wrapUp } from '../../../lib/api';
import WrapUp from '@cdss-modules/design-system/components/_ui/WrapUp';
import {
  TWrapUpCodeQueueData,
  TWrapUpFormState,
  TWrapUpOption,
} from '@cdss-modules/design-system/@types/Interaction';
import { fireGetWrapupCategory } from '@cdss/lib/api';
import { FileRejection } from 'react-dropzone';
import DropzoneAttachmentPage from '@cdss-modules/design-system/components/_ui/DropzoneAttachmentPage';
import { FileItem } from '@cdss-modules/design-system/@types/file';
import MessagePage from '@cdss-modules/design-system/components/_ui/MessagePage/MessagePage';
import PopupInline from '@cdss-modules/design-system/components/_ui/PopupInline';
import { useMessageSender } from '../../../hooks/useMessageSender';
import { SAAContentSection } from '@cdss-modules/design-system/@types/SAA';
import { useCustomerHistory } from '@cdss-modules/design-system/lib/hooks/useCustomerInfo';
import { PreviewTemplate } from '../../../@types/waTemplateRequest';

type ActivePage = 'message' | 'template' | 'record';
const EmptyStateDisplay = () => {
  return (
    <div className="flex flex-col items-center justify-center h-full text-gray-500 p-8">
      <MessageSquare className="w-16 h-16 mb-4 text-gray-300" />
      <h3 className="text-xl font-semibold mb-2 text-gray-700">
        No Conversation Selected
      </h3>
      <p className="text-center text-gray-500 max-w-sm">
        Select a conversation from the sidebar to start chatting or view message
        history
      </p>
    </div>
  );
};
interface SupportedContentResponse {
  data: {
    id: string;
    name: string;
    mediaTypes?: {
      allow?: {
        inbound?: { type: string }[];
        outbound?: { type: string }[];
      };
    };
  };
  isSuccess: boolean;
}
interface ChatWindowProps {
  conversationId: string;
  ICRMCustomer?: Customer;
  phoneNumber: string;
  setPhoneNumber: (phoneNumber: string) => void;
  setMessageMetadata?: (metadata: string | null) => void;
}
const ChatWindow: React.FC<ChatWindowProps> = ({
  conversationId = '',
  phoneNumber,
  setPhoneNumber,
  setMessageMetadata,
}) => {
  const { getSAAMatchDirectly } = useCustomerHistory();
  const {
    messages,
    handleSingleMessageData,
    handleRealTimeMessage,
    getSAAResult,
    setSAAResult,
    setSAAManualSearchTerm,
    setReferenceMessage,
    updateMessageReference,
  } = useConversationStore();
  const { lastMessage, sendMessage } = useInteractionContext();
  const { activePath } = useRouteHandler();
  const currentPath = activePath?.split('/').pop() || '';
  const [activePage, setActivePage] = useState<ActivePage>('message');
  const [showAttachmentPage, setShowAttachmentPage] = useState(false);
  //存放附件的List
  const [chatMediaList, setChatMediaList] = useState<MediaItem[]>([]);
  const MAX_FILE_SIZE = 25 * 1024 * 1024; // 25MB
  const [isExpanded, setIsExpanded] = useState(false);
  const chatToolRef = useRef<HTMLDivElement>(null);
  const [chatToolHeight, setChatToolHeight] = useState(0);
  const { isFetchLoading, setIsFetchLoading } = useMessageLoadingStore();
  const pendingGreetingInfoRef = useRef<{
    conversationId: string;
    phoneNumber: string;
  } | null>(null);
  const { basePath } = useRouteHandler();
  const { sendTextMessage, sendMediaMessages } = useMessageSender(basePath);
  const { toast } = useToast();
  const [isTransferring, setIsTransferring] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [isEnding, setIsEnding] = useState(false);
  const [showWrapUp, setShowWrapUp] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [convET, setConvET] = useState<Date | undefined>();
  const [
    whatsappConversationWindowStartTime,
    setWhatsappConversationWindowStartTime,
  ] = useState<Date>();
  const { userConfig, globalConfig } = useRole();
  const [wrapUpFormState, setWrapUpFormState] = useState<TWrapUpFormState>({
    selectedItems: [],
    notes: '',
    expandedItems: [],
    itemRemarks: [],
    activeRemarkItems: [],
  });

  const [wrapUpData, setWrapUpData] = useState<TWrapUpCodeQueueData[]>([]);
  const [wrapUpLoading, setWrapUpLoading] = useState(false);
  const [wrapUpError, setWrapUpError] = useState<string | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [showRecordModal, setShowRecordModal] = useState(false);
  const [whatsAppCustomer, setWhatsAppCustomer] = useState<Customer>();
  const [isWindowExpired, setIsWindowExpired] = useState<boolean>(false);
  const [countdownTime, setCountdownTime] = useState<string>('');
  const [isDNC, setIsDNC] = useState<boolean>(false);
  const [curParticipantQueueId, setCurParticipantQueueId] =
    useState<string>('');
  const [highlightedMessageId, setHighlightedMessageId] = useState<string>('');
  const [GCOBMimeLimits, setGCOBMimeLimits] =
    useState<Record<string, number>>();
  const microfrontendsConfig: microfrontends = globalConfig?.microfrontends;
  const GCMessageSupportedId: string | undefined =
    microfrontendsConfig['ctint-mf-message']['GCMessageSupportedId'];
  const GCOBlimits = 25 * 1024 * 1024;
  // WhatsApp Official Business MIME type size limits in bytes
  const WAOBMimeLimits: Record<string, number> = {
    // Audio files - 16 MB
    'audio/aac': 16 * 1024 * 1024,
    'audio/amr': 16 * 1024 * 1024,
    'audio/mpeg': 16 * 1024 * 1024,
    'audio/mp4': 16 * 1024 * 1024,
    'audio/ogg': 16 * 1024 * 1024, // OPUS codecs only; base audio/ogg not supported

    // Document files - 100 MB
    'text/plain': 100 * 1024 * 1024,
    'application/vnd.ms-excel': 100 * 1024 * 1024,
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      100 * 1024 * 1024,
    'application/msword': 100 * 1024 * 1024,
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      100 * 1024 * 1024,
    'application/vnd.ms-powerpoint': 100 * 1024 * 1024,
    'application/vnd.openxmlformats-officedocument.presentationml.presentation':
      100 * 1024 * 1024,
    'application/pdf': 100 * 1024 * 1024,

    // Image files
    'image/jpeg': 5 * 1024 * 1024, // 5 MB
    'image/png': 5 * 1024 * 1024, // 5 MB
    'image/webp': 500 * 1024, // 500 KB (sticker uses 100 KB variant)

    // Video files - 16 MB
    'video/3gpp': 16 * 1024 * 1024,
    'video/mp4': 16 * 1024 * 1024,
    //尝试开放csv
    'text/csv': 25 * 1024 * 1024, // 25 MB
    'application/csv': 25 * 1024 * 1024, // 25 MB
  };
  // 获取 store 的清除引用消息方法
  const { clearReferenceMessage } = useConversationStore();
  const referenceMessageRef = useRef<CDSSMessage | undefined>();
  // const GCOBMimeLimits: Record<string, number> = {
  //   // Document files
  //   'text/csv': 25 * 1024 * 1024, // 25 MB
  //   'application/csv': 25 * 1024 * 1024, // 25 MB
  //   'application/pdf': 25 * 1024 * 1024, // 25 MB
  //   'application/vnd.openxmlformats-officedocument.presentationml.presentation':
  //     25 * 1024 * 1024, // 25 MB
  //   'text/plain': 25 * 1024 * 1024, // 25 MB
  //   'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
  //     25 * 1024 * 1024, // 25 MB
  //   'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
  //     25 * 1024 * 1024, // 25 MB
  //
  //   // Image files
  //   'image/png': 25 * 1024 * 1024, // 25 MB
  //   'image/jpeg': 25 * 1024 * 1024, // 25 MB
  //
  //   // Video files
  //   'video/mp4': 25 * 1024 * 1024, // 25 MB
  //
  //   // Audio files
  //   'audio/mpeg': 25 * 1024 * 1024, // 25 MB
  // };
  // 获取支持的内容类型和限制
  useEffect(() => {
    const fetchSupportedContent = async () => {
      try {
        const response = await getSupportedContent(
          basePath,
          GCMessageSupportedId
        );
        console.log('获取的支持内容类型:', response.data);

        // 创建新的MIME类型限制映射
        const newGCOBMimeLimits: Record<string, number> = {};

        // 从响应中提取 outbound 媒体类型
        const responseData = response.data as SupportedContentResponse;
        const outboundMediaTypes =
          responseData.data.mediaTypes?.allow?.outbound || [];

        // 处理每个媒体类型
        outboundMediaTypes.forEach((mediaTypeObj) => {
          if (mediaTypeObj.type && mediaTypeObj.type !== '*/*') {
            // 为所有类型使用统一的 GCOBlimits 限制
            newGCOBMimeLimits[mediaTypeObj.type] = GCOBlimits;
          }
        });

        // 如果 API 没有返回数据或处理后的映射为空，设置一些默认值
        if (Object.keys(newGCOBMimeLimits).length === 0) {
          // 设置常见类型的默认限制
          newGCOBMimeLimits['text/csv'] = GCOBlimits;
          newGCOBMimeLimits['application/csv'] = GCOBlimits;
          newGCOBMimeLimits['application/pdf'] = GCOBlimits;
          newGCOBMimeLimits['image/png'] = GCOBlimits;
          newGCOBMimeLimits['image/jpeg'] = GCOBlimits;
          newGCOBMimeLimits['video/mp4'] = GCOBlimits;
          newGCOBMimeLimits['audio/mpeg'] = GCOBlimits;
          newGCOBMimeLimits['text/plain'] = GCOBlimits;
          newGCOBMimeLimits[
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          ] = GCOBlimits;
          newGCOBMimeLimits[
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          ] = GCOBlimits;
          newGCOBMimeLimits[
            'application/vnd.openxmlformats-officedocument.presentationml.presentation'
          ] = GCOBlimits;
        }

        console.log('设置的GCOBMimeLimits:', newGCOBMimeLimits);
        // 更新状态
        setGCOBMimeLimits(newGCOBMimeLimits);
      } catch (error) {
        console.error('fetch supported content failed:', error);

        // 如果API调用失败，设置默认的MIME类型限制
        const defaultMimeLimits: Record<string, number> = {
          'text/csv': GCOBlimits,
          'application/csv': GCOBlimits,
          'application/pdf': GCOBlimits,
          'application/vnd.openxmlformats-officedocument.presentationml.presentation':
            GCOBlimits,
          'text/plain': GCOBlimits,
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
            GCOBlimits,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
            GCOBlimits,
          'image/png': GCOBlimits,
          'image/jpeg': GCOBlimits,
          'video/mp4': GCOBlimits,
          'audio/mpeg': GCOBlimits,
        };

        setGCOBMimeLimits(defaultMimeLimits);

        toast({
          title: 'error',
          description:
            'fetch supported content failed, it will use default content types',
          variant: 'error',
        });
      }
    };

    fetchSupportedContent();
  }, []);
  function getMimeIntersectionWithMinValues(
    record1: Record<string, number>,
    record2: Record<string, number>
  ): Record<string, number> {
    const result: Record<string, number> = {};

    // Iterate through keys in the first record
    Object.keys(record1).forEach((key) => {
      // Check if the key also exists in the second record
      if (key in record2) {
        // Add the key to the result with the minimum of the two values
        result[key] = Math.min(record1[key], record2[key]);
      }
    });

    return result;
  }
  const GC2WALimits = useMemo(() => {
    if (!GCOBMimeLimits) {
      return {}; // 返回空对象作为默认值
    }

    return getMimeIntersectionWithMinValues(GCOBMimeLimits, WAOBMimeLimits);
  }, [GCOBMimeLimits]);
  //订阅referenceMessage
  useEffect(() => {
    // 订阅referenceMessage的变化
    const unsubscribeReference = useConversationStore.subscribe(
      (state: any) => state.referenceMessage,
      (newReferenceMessage: any) => {
        if (newReferenceMessage && newReferenceMessage.id && !isWindowExpired) {
          referenceMessageRef.current = newReferenceMessage;
        } else if (!newReferenceMessage || !newReferenceMessage.id) {
          // 当 store 中的 referenceMessage 被清除时，也清除 ref
          referenceMessageRef.current = undefined;
        }
      }
    );
    // 返回清理函数
    return unsubscribeReference;
  }, [isWindowExpired]);

  const fetchICRMCustomer = async (): Promise<Customer | null> => {
    console.log('fetchICRMCustomer conversationId', conversationId);
    console.log('fetchICRMCustomer phoneNumber', phoneNumber);
    if (!conversationId) return null;
    let customer: Customer | null = null;
    try {
      // Get the phone number from the current message data
      if (phoneNumber) {
        // Use simplified payload format
        const iCRMPayload = { contactValue: phoneNumber };
        // Call ICRM API to get user information
        const response = await getICRMCustomers(basePath, iCRMPayload);
        const responseData: ICRMResponse = response.data.data;
        const customers = responseData.customers || [];

        // Filter for a customer with whatsAppMessage contact type
        for (const c of customers) {
          const whatsAppContact = c.customerContacts?.find(
            (contact: CustomerContact) =>
              contact.contactType === 'whatsAppMessage' &&
              contact.contactValue === phoneNumber
          );

          if (whatsAppContact) {
            customer = c;
            break;
          }
        }

        console.log('fetchICRMCustomer whatsAppContact', customer);
        // Store the customer in state - convert null to undefined for state
        setWhatsAppCustomer(customer || undefined);

        return customer;
      }
      return null;
    } catch (error) {
      console.error('获取ICRM客户信息失败:', error);
      return null;
    } finally {
      // 检查并使用 ref 值
      // if (
      //   pendingGreetingInfoRef.current &&
      //   pendingGreetingInfoRef.current.conversationId === conversationId
      // ) {
      //   await sendGreetingMessage(
      //     pendingGreetingInfoRef.current.conversationId,
      //     pendingGreetingInfoRef.current.phoneNumber,
      //     customer
      //   );
      //   // 重置
      //   pendingGreetingInfoRef.current = null;
      // }
    }
  };
  const sendGreetingMessage = async (
    eventConvId: string,
    eventPhoneNumber: string,
    customer: Customer | null
  ) => {
    try {
      console.log('进入sendGreetingMessage，此时的whatsAppCustomer', customer);
      console.log(
        '进入sendGreetingMessage，此时的eventConvId和convId',
        eventConvId,
        conversationId
      );
      console.log(
        '进入sendGreetingMessage，此时的eventPhoneNumber和phoneNumber',
        eventConvId,
        conversationId
      );
      if (
        conversationId === eventConvId &&
        eventPhoneNumber === phoneNumber &&
        customer
      ) {
        const customerLanguage = customer.language || '';
        //不需要再发默认问候语
        if (
          !userConfig?.authenticationInfo?.greeting &&
          !userConfig?.authenticationInfo?.greetingEn
        ) {
          // sendTextMessage(
          //   eventConvId,
          //   'userInfo does not set greeting message yet, there is a default greeting!'
          // );
          return;
        }
        // 根据语言发送问候语
        if (
          customerLanguage === 'Chinese' &&
          userConfig?.authenticationInfo?.greeting
        ) {
          sendTextMessage(eventConvId, userConfig.authenticationInfo.greeting);
        } else if (
          (customerLanguage === 'English' || !customerLanguage) &&
          userConfig?.authenticationInfo?.greetingEn
        ) {
          sendTextMessage(
            eventConvId,
            userConfig.authenticationInfo.greetingEn
          );
        } else if (userConfig?.authenticationInfo?.greetingEn) {
          // 默认问候语（如果没有匹配的语言）
          sendTextMessage(
            eventConvId,
            userConfig.authenticationInfo.greetingEn
          );
        }
      } else {
        // 如果没有获取到客户信息，使用默认问候语
        if (userConfig?.authenticationInfo?.greetingEn) {
          sendTextMessage(
            eventConvId,
            userConfig.authenticationInfo.greetingEn
          );
        } else if (userConfig?.authenticationInfo?.greeting) {
          sendTextMessage(eventConvId, userConfig.authenticationInfo.greeting);
        }
      }
    } catch (error) {
      console.error('发送问候语时出错:', error);
      // 出错时使用默认问候语
      if (userConfig?.authenticationInfo?.greetingEn) {
        sendTextMessage(eventConvId, userConfig.authenticationInfo.greetingEn);
      } else if (userConfig?.authenticationInfo?.greeting) {
        sendTextMessage(eventConvId, userConfig.authenticationInfo.greeting);
      }
    }
  };
  /**
   * 用实际参数值替换模板变量
   * @param text 包含占位符的文本
   * @param parameters 参数数组
   * @returns 替换后的文本
   */
  function replaceTemplateVariables(
    text: string,
    parameters: TemplateParameter[]
  ): string {
    let processedText = text;

    parameters.forEach((param, index) => {
      const placeholder = `{{${index + 1}}}`;
      const regex = new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g');
      processedText = processedText.replace(
        regex,
        param.text ? param.text : ''
      );
    });

    return processedText;
  }
  /**
   * 将 SendPayload 和 TemplateJson 结合成PreviewTemplate格式
   * @param apiResponse API响应数据
   * @returns PreviewTemplate对象
   */
  function combineTemplate(
    apiResponse: BroadcastApiResponse
  ): PreviewTemplate | null {
    if (!apiResponse.isSuccess || !apiResponse.data) {
      console.error('API响应失败或数据为空:', apiResponse.error);
      return null;
    }
    console.log('combine里面的apiResponse', apiResponse);

    const { sendPlayLoad, templateJson } = apiResponse.data;

    try {
      // 创建参数映射，用于填充模板变量
      const parametersMap = new Map<string, TemplateParameter[]>();

      // 安全获取模板组件，处理有占位符和无占位符两种情况
      const templateComponents = sendPlayLoad.template?.components || [];
      const hasParameters = templateComponents.length > 0;

      console.log('模板是否有参数:', hasParameters);
      console.log('模板组件:', templateComponents);

      if (hasParameters) {
        templateComponents.forEach((comp) => {
          if (comp.parameters && comp.parameters.length > 0) {
            parametersMap.set(comp.type, comp.parameters);
          }
        });
      }

      // 转换组件为PreviewTemplate格式
      const previewComponents: Component[] = templateJson.components.map(
        (jsonComp) => {
          const componentType = jsonComp.type;
          const params = parametersMap.get(componentType.toLowerCase());

          switch (componentType) {
            case 'HEADER': {
              const headerComp: HeaderComponent = {
                type: 'HEADER',
                format: (jsonComp.format as 'TEXT' | 'IMAGE') || 'TEXT',
                text: jsonComp.text,
                example: jsonComp.example,
              };

              // 只有在有参数的情况下才处理参数替换
              if (hasParameters && params && params.length > 0) {
                params.forEach((param) => {
                  if (param.type === 'image' && param.image) {
                    headerComp.image = param.image;
                    headerComp.format = 'IMAGE';
                  } else if (param.type === 'text' && headerComp.text) {
                    headerComp.text = replaceTemplateVariables(
                      headerComp.text,
                      [param]
                    );
                  }
                });
              }

              return headerComp;
            }

            case 'BODY': {
              const bodyComp: BodyComponent = {
                type: 'BODY',
                text: jsonComp.text,
                example: jsonComp.example,
              };

              // 只有在有参数的情况下才进行参数替换
              if (hasParameters && params && bodyComp.text) {
                bodyComp.text = replaceTemplateVariables(bodyComp.text, params);
              }

              return bodyComp;
            }

            case 'FOOTER': {
              const footerComp: FooterComponent = {
                type: 'FOOTER',
                text: jsonComp.text,
              };
              return footerComp;
            }

            case 'BUTTONS': {
              if (!jsonComp.buttons) {
                throw new Error('BUTTONS组件缺少buttons数组');
              }

              // 获取按钮参数（只有在有参数的情况下）
              const buttonParams = hasParameters
                ? templateComponents.filter((comp) => comp.type === 'button')
                : [];

              // 转换按钮格式
              const wabaButtons: Button[] = jsonComp.buttons.map(
                (btn, index) => {
                  const baseButton = {
                    text: btn.text,
                    type: btn.type as any,
                  };

                  // 查找对应的按钮参数（只有在有参数的情况下）
                  const btnParam = hasParameters
                    ? buttonParams.find((param) => param.index === index)
                    : null;

                  switch (btn.type) {
                    case 'URL': {
                      let processedUrl = btn.url || '';

                      // 只有在有参数且找到对应按钮参数时才替换占位符
                      if (
                        hasParameters &&
                        btnParam &&
                        btnParam.parameters &&
                        btnParam.sub_type === 'url'
                      ) {
                        processedUrl = replaceTemplateVariables(
                          processedUrl,
                          btnParam.parameters
                        );
                      }

                      return {
                        ...baseButton,
                        type: 'URL' as const,
                        url: processedUrl,
                        example: btn.example,
                      };
                    }

                    case 'PHONE_NUMBER':
                      return {
                        ...baseButton,
                        type: 'PHONE_NUMBER' as const,
                        phone_number: btn.phone_number || '',
                      };

                    case 'QUICK_REPLY':
                      return {
                        ...baseButton,
                        type: 'QUICK_REPLY' as const,
                      };

                    default:
                      return {
                        ...baseButton,
                        type: 'QUICK_REPLY' as const,
                      };
                  }
                }
              );

              const buttonsComp: ButtonsComponent = {
                type: 'BUTTONS',
                buttons: wabaButtons,
              };
              return buttonsComp;
            }

            default:
              throw new Error(`不支持的组件类型: ${componentType}`);
          }
        }
      );

      // 构建PreviewTemplate
      const previewTemplate: PreviewTemplate = {
        components: previewComponents,
      };

      console.log('生成的预览模板:', previewTemplate);
      return previewTemplate;
    } catch (error) {
      console.error('合并模板时发生错误:', error);
      return null;
    }
  }

  //用户首先看到的数据 from store
  const currentMessageData: MessageData = conversationId
    ? messages.find((m: MessageData) => m.conversationId === conversationId)
    : {};
  // 获取当前对话的消息列表 如果数据结构发生变化 从这里修改获取的方式
  const currentMessages: CDSSMessage[] = currentMessageData?.messages ?? [];
  // 首先创建一个延迟函数(测试用)
  const delay = (ms: number) =>
    new Promise((resolve) => setTimeout(resolve, ms));
  /**
   * 处理广播消息查询和存储更新
   * @param broadcastMessages 广播消息数组
   * @param conversationId 对话ID
   * @param basePath API基础路径
   * @returns Promise<void>
   */
  async function processBroadcastMessages(
    broadcastMessages: CDSSMessage[], // 你需要根据实际类型调整
    conversationId: string,
    basePath?: string
  ): Promise<void> {
    if (broadcastMessages.length === 0) {
      return;
    }

    // 异步查询每个 broadcast message
    const broadcastQueries = broadcastMessages.map(async (msg) => {
      try {
        const response = await GetBroadcastMessage(
          msg.externalReferenceId,
          basePath
        );
        return {
          messageId: msg.id,
          externalReferenceId: msg.externalReferenceId,
          broadcastData: response.data,
          success: true,
        };
      } catch (error) {
        console.log(
          `查询 broadcast message 失败, messageId : ${msg.id}, externalReferenceId: ${msg.externalReferenceId}`,
          error
        );
        return {
          messageId: msg.id,
          externalReferenceId: msg.externalReferenceId,
          broadcastData: null,
          success: false,
        };
      }
    });

    // 等待所有查询完成
    const results = await Promise.allSettled(broadcastQueries);

    results.forEach((result) => {
      if (result.status === 'fulfilled' && result.value.success) {
        // 查询成功，更新 store
        const { messageId, broadcastData } = result.value;
        console.log('成功查询的broadcastData:', broadcastData);

        // 转换成ApiResponse格式
        const apiResponse: BroadcastApiResponse = {
          data: broadcastData.data,
          error: '',
          isSuccess: true,
        };

        // 合并模板
        const previewTemplate = combineTemplate(apiResponse);
        console.log('previewTemplate', previewTemplate);
        const templateJson = JSON.stringify(previewTemplate);

        // 更新store
        const cdssMessage = {
          id: 'msg_' + Date.now(),
          conversationId: '',
          participantId: '',
          originalPlatform: '',
          platform: '',
          platformMessageId: '',
          externalMessageId: '',
          channelId: '',
          direction: 'outbound' as const,
          messengerType: '',
          category: '',
          type: 'whatsappTemplate' as const,
          userName: 'broadcast campaign',
          userId: '',
          fromAddress: '',
          fromName: '',
          toAddress: '',
          toName: '',
          timestamp: undefined,
          textBody: templateJson,
          status: 'sent' as const,
          metadata: '',
          tenant: '',
        };

        updateMessageReference(conversationId, messageId, cdssMessage);
      }
    });
  }
  const fetchMessages = async () => {
    try {
      console.log('看顺序 fetch message');
      setIsFetchLoading(true);
      const response = await getMessages(`${basePath}`, conversationId);
      const responseData: MessageData = response.data.data;
      const responseMessages: CDSSMessage[] = responseData.messages ?? [];
      // 在更新 store 前标记未读消息 当前窗口的会话才mark read
      const unreadMessages = responseMessages.filter(
        (msg) => msg.direction === 'inbound' && msg.status !== 'read'
      );

      if (unreadMessages.length > 0) {
        const unreadIds = unreadMessages.map((msg) => msg.id);
        await (async () => {
          try {
            await markRead(basePath, responseData.conversationId, {
              status: 'read',
              ids: unreadIds,
              integrationId: responseData.channel?.integrationId,
            });

            console.log('已将未读消息标记为已读:', unreadIds);
          } catch (error) {
            console.error('标记消息为已读时出错:', error);
          }
        })();
      }
      handleSingleMessageData(responseData);

      //查询 broadcast message 放入 store
      //收集需要查询broadcast的id
      const broadcastMessages = responseMessages.filter(
        (msg) => !msg.referenceId && !msg.reference && msg.externalReferenceId
      );
      console.log('broadcastMessages', broadcastMessages);
      if (broadcastMessages.length > 0) {
        await processBroadcastMessages(
          broadcastMessages,
          conversationId,
          basePath
        );
      }

      if (responseData?.whatsappConversationWindow?.serviceWindowStartTime) {
        setWhatsappConversationWindowStartTime(
          new Date(
            responseData.whatsappConversationWindow.serviceWindowStartTime
          )
        );
      }
      // Extract phone number and update state and call the callback if provided
      const extractedPhoneNumber = responseData?.customer?.from || '';
      if (extractedPhoneNumber) {
        setPhoneNumber(extractedPhoneNumber);
      }
      //如果仍然在转接中，则更新状态，否则初始化
      console.log('responseData', responseData);
      if (
        responseData.activeConsultParticipant &&
        responseData.activeConsultParticipant.state === 'alerting'
      ) {
        setIsTransferring(true);
        setSelectedUser({
          id: responseData.activeConsultParticipant.id,
          name: responseData.activeConsultParticipant.name,
        });
      } else {
        setIsTransferring(false);
        setSelectedUser(null);
      }
      setIsConnected(!responseData.curParticipant?.endTime);
      setConvET(
        responseData?.curParticipant?.endTime !== undefined
          ? new Date(responseData.curParticipant.endTime)
          : undefined
      );
      setCurParticipantQueueId(responseData.curParticipant?.queue.id || '');
      setShowWrapUp(responseData.curParticipant?.state === 'disconnected');
      setIsEnding(false);
      setIsSubmitting(false);
    } catch (error) {
      console.error('获取消息失败:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch messages.',
        variant: 'error',
      });
      return (
        <div className="flex flex-col h-full bg-white shadow-lg rounded-lg">
          <EmptyStateDisplay />
        </div>
      );
    } finally {
      setIsFetchLoading(false);
    }
  };
  // 第二步获取最新消息 react的渲染机制保证加载顺序
  useEffect(() => {
    if (conversationId && currentPath === 'message') {
      console.log('初始化chatWindow');
      //初始化
      setChatMediaList([]);
      setShowAttachmentPage(false);
      setShowRecordModal(false);
      setActivePage('message');
      setWhatsappConversationWindowStartTime(undefined);
      setPhoneNumber('');
      // 清理引用消息状态
      referenceMessageRef.current = undefined;
      clearReferenceMessage();
      fetchMessages();
      console.log('初始化chatWindow结束');
    }
  }, [conversationId, currentPath]);
  useEffect(() => {
    if (phoneNumber) {
      console.log('Phone number updated, fetching ICRM customer:', phoneNumber);
      fetchICRMCustomer();
    }
  }, [phoneNumber]);
  useEffect(() => {
    console.log('greeting的useEffect');
    console.log('messages', messages);
    console.log('whatsAppCustomer', whatsAppCustomer);
    console.log(
      'pendingGreetingInfoRef.current',
      pendingGreetingInfoRef.current
    );
    const currentMessages = messages.find(
      (m: any) => m.conversationId === conversationId
    )?.messages;
    if (
      currentMessages &&
      currentMessages.length > 0 &&
      whatsAppCustomer &&
      !isFetchLoading
    ) {
      // 检查是否有待发送的问候信息
      if (
        pendingGreetingInfoRef.current &&
        pendingGreetingInfoRef.current.conversationId === conversationId &&
        pendingGreetingInfoRef.current.phoneNumber === phoneNumber
      ) {
        console.log('需要发送问候消息', pendingGreetingInfoRef.current);

        // 发送问候消息
        sendGreetingMessage(
          pendingGreetingInfoRef.current.conversationId,
          pendingGreetingInfoRef.current.phoneNumber,
          whatsAppCustomer
        );

        // 重置状态
        pendingGreetingInfoRef.current = null;
      } else {
        console.log('不需要发送问候消息', pendingGreetingInfoRef.current);
      }
    }
  }, [messages, whatsAppCustomer]);
  useEffect(() => {
    // 如果没有开始时间，确认窗口已过期
    if (!whatsappConversationWindowStartTime) {
      setCountdownTime('');
      setIsWindowExpired(true);
      return;
    }

    // 窗口有开始时间，检查是否过期并计算倒计时
    const calculateTimeLeft = () => {
      const startTime = whatsappConversationWindowStartTime.getTime();
      // WhatsApp 服务窗口持续 24 小时 24 * 60 * 60 * 1000
      const endTime = startTime + 24 * 60 * 60 * 1000;
      const now = new Date().getTime();
      const difference = endTime - now;

      // 检查窗口是否过期
      if (difference <= 0) {
        setCountdownTime('00:00:00');
        setIsWindowExpired(true);
        return;
      }

      // 窗口处于活动状态
      setIsWindowExpired(false);

      // 计算小时、分钟、秒
      const hours = Math.floor(difference / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      setCountdownTime(
        `${hours.toString().padStart(2, '0')}:${minutes
          .toString()
          .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      );
    };

    // 立即计算并设置间隔
    calculateTimeLeft();
    const timer = setInterval(calculateTimeLeft, 1000);

    // 在卸载或依赖项变化时清理间隔
    return () => clearInterval(timer);
  }, [whatsappConversationWindowStartTime]); // 只在窗口时间变化时触发

  useEffect(() => {
    const getCustomerOutboundBlocking = (): boolean => {
      if (whatsAppCustomer) {
        const blockingRules =
          whatsAppCustomer.customerContacts[0].blockingRules;
        //在blockingRules中 筛选出 direction==='outbound'的数据
        if (blockingRules) {
          const outboundBlockingRules = blockingRules.filter(
            (rule: any) => rule.direction === 'outbound'
          );
          if (outboundBlockingRules.length > 0) {
            return outboundBlockingRules[0].isBlocked;
          }
        }
      }
      return false;
    };
    const customerOutboundBlocking: boolean = getCustomerOutboundBlocking();
    if (isWindowExpired && customerOutboundBlocking) {
      setIsDNC(true);
    } else {
      setIsDNC(false);
    }
    console.log('DNC', isDNC);
  }, [whatsAppCustomer, isWindowExpired, isDNC]);

  //处理websocket
  useEffect(() => {
    const data = lastMessage?.data;
    if (!data || data === 'ping') return;

    try {
      const formatedEvent = JSON.parse(data);
      // 检验事件格式并解析事件类型
      const eventParts = formatedEvent.event?.split('.');
      if (!eventParts || eventParts.length < 4) {
        return;
      }

      const eventConvId = eventParts[1];
      const eventType = eventParts[3];
      //发送问候语 不再看是否之前有说过话，只要是inbound的connected就发greeting 0516, 如果没有设置greeting,则不发送
      if (
        eventType === 'connected' &&
        formatedEvent.eventData.data.currentParticipant.direction === 'inbound'
      ) {
        const eventPhoneNumber = formatedEvent.eventData.data.userAddress;
        if (eventPhoneNumber) {
          console.log('需要greeting');
          console.log('eventConvId', eventConvId);
          console.log('eventPhoneNumber', eventPhoneNumber);
          // 同步更新 ref 只有ws才知道本次fetchMessages 是否需要greeting
          pendingGreetingInfoRef.current = {
            conversationId: eventConvId,
            phoneNumber: eventPhoneNumber,
          };
        }
      }

      // 处理消息变更事件
      if (eventType === 'messageChanged') {
        // 处理消息数据
        let processedMessages: CDSSMessage[] = formatedEvent.eventData.data.map(
          (messageData: CDSSMessage): CDSSMessage => {
            setMessageMetadata &&
              setMessageMetadata(messageData.metadata || null);
            return {
              id: messageData.id,
              conversationId: messageData.conversationId,
              participantId: messageData.participantId,
              originalPlatform: messageData.originalPlatform,
              platform: messageData.platform,
              platformMessageId: messageData.platformMessageId,
              externalMessageId: messageData.externalMessageId,
              externalReferenceId: messageData.externalReferenceId,
              channelId: messageData.channelId,
              direction: messageData.direction,
              messengerType: messageData.messengerType,
              category: messageData.category,
              type: messageData.type,
              userName: messageData.userName,
              userId: messageData.userId,
              fromAddress: messageData.fromAddress,
              fromName: messageData.fromName,
              toAddress: messageData.toAddress,
              toName: messageData.toName,
              timestamp: new Date(messageData.timestamp),
              textBody: messageData.textBody,
              status: messageData.status,
              metadata: messageData.metadata,
              tenant: '',
              medias: messageData.medias
                ? messageData.medias.map(
                    (media: MediaItem): MediaItem => ({
                      id: media.id,
                      tenant: media.tenant,
                      platform: media.platform,
                      platformMediaId: media.platformMediaId,
                      host: media.host,
                      hostType: media.hostType,
                      mediaType: media.mediaType,
                      url: media.url,
                      mime: media.mime,
                      filename: media.filename,
                      contentSizeBytes: media.contentSizeBytes,
                      createTime: new Date(media.createTime),
                      createBy: media.createBy,
                      updateTime: new Date(media.updateTime),
                      updateBy: media.updateBy,
                      scanResult: media.scanResult,
                    })
                  )
                : [],
              sttJobResult: messageData.sttJobResult,
              virusScanJobResult: messageData.virusScanJobResult,
              saaResult: messageData.saaResult,
              piiJobResult: messageData.piiJobResult,
              reference: messageData.reference,
              referenceId: messageData.referenceId,
            };
          }
        );
        // 在更新 store 前标记当前聊天窗口未读消息

        const newInboundMessages = processedMessages.filter(
          (msg) =>
            conversationId === eventConvId &&
            msg.direction === 'inbound' &&
            msg.status !== 'read'
        );

        if (newInboundMessages.length > 0) {
          const newInboundIds = newInboundMessages.map((msg) => msg.id);
          const integrationId = currentMessageData?.channel?.integrationId;

          try {
            markRead(basePath, eventConvId, {
              status: 'read',
              ids: newInboundIds,
              integrationId,
            });

            // 更新消息数组...
            processedMessages = processedMessages.map((msg) => {
              if (newInboundIds.includes(msg.id)) {
                return { ...msg, status: 'read' };
              }
              return msg;
            });

            console.log('已将新消息标记为已读:', newInboundIds);
          } catch (error) {
            console.error('标记新消息为已读时出错:', error);
          }
        }

        // 处理 SAA 结果
        const processSAAResults = async () => {
          // 筛选出包含 saaResult 且 matchedTags 不为空的消息
          const messagesWithSAA = processedMessages.filter(
            (msg) => msg.saaResult && msg.saaResult.matchedTags
          );

          if (messagesWithSAA.length > 0) {
            // 为每条包含 SAA 的消息处理 SAA 结果
            for (const msg of messagesWithSAA) {
              if (msg.saaResult?.matchedTags) {
                try {
                  // 解析 matchedTags
                  let keywords: string[] = [];
                  try {
                    keywords = JSON.parse(msg.saaResult.matchedTags);
                  } catch (e) {
                    console.error('解析 matchedTags 失败:', e);
                    continue;
                  }

                  // 将关键词数组转为空格分隔的字符串
                  const searchTerm = keywords.join(' ');

                  // 调用 API 获取 SAA 内容
                  const response = await getSAAMatchDirectly({
                    content: searchTerm,
                  });
                  console.log('auto saa 调用接口 返回数据', response.data);
                  if (response && response.data && response.data.contents) {
                    // 构建 SAAContentSection
                    const saaContentSection: SAAContentSection = {
                      timestamp: msg.timestamp,
                      rawTextBody: msg.textBody,
                      keywords: keywords,
                      messageId: msg.id,
                      SAAContentList: response.data.contents.map(
                        (content: any) => ({
                          title: content.title || '',
                          content: content.content || '',
                          editable: content.editable || false,
                        })
                      ),
                    };
                    console.log('构建的SAAContentSection ', saaContentSection);
                    // 获取已有的 SAA 结果
                    const existingSAAResults = getSAAResult(eventConvId) || [];
                    // 检查是否已存在相同messageId的条目
                    const messageIdExists = existingSAAResults.some(
                      (section: SAAContentSection) =>
                        section.messageId === msg.id
                    );

                    if (!messageIdExists) {
                      // 添加新条目并按时间戳倒序排序
                      const updatedResults = [
                        ...existingSAAResults,
                        saaContentSection,
                      ].sort((a, b) => {
                        // 处理没有时间戳的条目
                        if (!a.timestamp) return 1;
                        if (!b.timestamp) return -1;
                        // 最新的排在前面
                        return b.timestamp.getTime() - a.timestamp.getTime();
                      });

                      // 更新上下文，保存排序并去重后的结果
                      setSAAResult(eventConvId, updatedResults);
                      console.log(
                        '已更新SAA结果（已去重）:',
                        eventConvId,
                        updatedResults
                      );
                    } else {
                      console.log('跳过重复的SAA结果，messageId:', msg.id);
                    }
                  }
                } catch (error) {
                  console.error('处理 SAA 结果时出错:', error);
                }
              }
            }
          }
        };

        // 异步处理 SAA 结果
        processSAAResults();
        if (eventConvId === conversationId) {
          //按时间排序 倒序
          processedMessages.sort(
            (a, b) => a.timestamp.getTime() - b.timestamp.getTime()
          );
          // 将处理好的消息传递给 store
          handleRealTimeMessage(eventConvId, processedMessages);
          //更新service window的时间
          const latestInboundMessage = processedMessages
            .filter((msg) => msg.direction === 'inbound')
            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

          if (latestInboundMessage) {
            //更新service window的时间
            setWhatsappConversationWindowStartTime(
              latestInboundMessage.timestamp
            );
          }
          const handleBroadcastMessages = async () => {
            console.log('websocket handleBroadcastMessages');
            // 处理broadcast内容
            const broadcastMessages = processedMessages.filter(
              (msg) =>
                !msg.referenceId && !msg.reference && msg.externalReferenceId
            );
            console.log('broadcastMessages list ', broadcastMessages);

            if (broadcastMessages.length > 0) {
              await processBroadcastMessages(
                broadcastMessages,
                conversationId,
                basePath
              );
            }
          };

          handleBroadcastMessages();
        }
      }
      // 处理连接状态变更事件 transfer wrap-up
      else if (eventConvId === conversationId) {
        // 处理转接状态
        if (eventType === 'connected') {
          // 对方正在响铃（转接中）
          if (
            formatedEvent.eventData.data.activeConsultParticipant?.state ===
            'alerting'
          ) {
            setIsTransferring(true);
            setSelectedUser({
              id: formatedEvent.eventData.data.activeConsultParticipant.id,
              name: formatedEvent.eventData.data.activeConsultParticipant.name,
            });
          }
          // 当前参与者连接成功（包括转接中，被拒绝或主动取消）
          else if (
            formatedEvent.eventData.data.currentParticipant.state ===
            'connected'
          ) {
            setChatMediaList([]);
            setShowAttachmentPage(false);
            setActivePage('message');
            setWhatsappConversationWindowStartTime(undefined);
            fetchMessages();
            // setIsTransferring(false);
            // setIsConnected(true);
            // setSelectedUser(null);
            // setShowWrapUp(false);
          }
        }
        // 处理断开连接事件
        else if (
          eventType === 'disconnected' &&
          formatedEvent.eventData.data.currentParticipant.state ===
            'disconnected'
        ) {
          // 会话结束
          setIsTransferring(false);
          setIsConnected(false);
          setIsEnding(false);
          setShowWrapUp(true);
          setConvET(
            formatedEvent.eventData.data.currentParticipant?.endTime !==
              undefined
              ? new Date(
                  formatedEvent.eventData.data.currentParticipant.endTime
                )
              : undefined
          );
          setSelectedUser(null);
        }
        // 处理总结事件
        else if (eventType === 'wrapup') {
          setIsSubmitting(false);
        }
      }
    } catch (error) {
      console.error('处理websocket消息时出错:', error);
    }
  }, [lastMessage]);

  //查WrapUpCode
  useEffect(() => {
    if (
      (!showWrapUp && isConnected) ||
      !currentMessageData?.curParticipant?.user?.id
    ) {
      return;
    }
    setWrapUpLoading(true);
    fireGetWrapupCategory(currentMessageData.curParticipant?.user?.id, basePath)
      .then((response) => {
        const data = response.data.data;
        const filteredData = data.find(
          (queue: { queueId: string }) =>
            queue.queueId === currentMessageData.curParticipant?.queue.id
        );
        // console.log('queueId', currentMessageData.curParticipant?.queue.id);
        // console.log('WrapUpCode:', filteredData);
        setWrapUpData(filteredData ? [filteredData] : []);
        setWrapUpFormState({
          selectedItems: [],
          notes: '',
          expandedItems: [],
          itemRemarks: [],
          activeRemarkItems: [],
        });
      })
      .catch((err) => {
        setWrapUpError('Failed to fetch wrap-up data');
        toast({
          title: 'Error',
          description: 'Failed to fetch wrap-up data',
          variant: 'error',
        });
      })
      .finally(() => {
        setWrapUpLoading(false);
      });
  }, [currentMessageData?.curParticipant?.user?.id, conversationId]);

  // 用于监听 ChatTool 高度变化
  useEffect(() => {
    if (chatToolRef.current) {
      const observer = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // console.log('height:', entry.contentRect.height);
          setChatToolHeight(entry.contentRect.height);
        }
      });

      observer.observe(chatToolRef.current);
      return () => observer.disconnect();
    }
  }, [conversationId]);
  const convertMediaListToFileItems = (mediaItems: MediaItem[]): FileItem[] => {
    return mediaItems.map((item) => ({
      id: item.id,
      mime: item.mime,
      filename: item.filename,
      contentSizeBytes: item.contentSizeBytes,
    }));
  };
  const handleExpandChange = (expanded: boolean) => {
    setIsExpanded(expanded);
  };
  const handleWrapUpFormStateChange = (newState: TWrapUpFormState) => {
    setWrapUpFormState(newState);
  };

  //提交wrapup表单
  const handleWrapUpSubmit = async () => {
    if (!currentMessageData || !currentMessageData.curParticipant) return;
    try {
      // setLoading(true);
      setIsSubmitting(true);
      const findItemById = (
        items: TWrapUpOption[],
        targetId?: string
      ): TWrapUpOption | undefined => {
        if (!targetId) return undefined;
        for (const item of items) {
          if (item.id === targetId) return item;
          if (item.items) {
            const found = findItemById(item.items, targetId);
            if (found) return found;
          }
        }
        return undefined;
      };
      const wrapUpList = wrapUpFormState.selectedItems
        .map((itemId) => {
          const item = findItemById(wrapUpData[0]?.items || [], itemId);
          // 删除这里的 type 判断，所有被选中的项目都会被包含
          if (!item) return null;
          const itemRemark = wrapUpFormState.itemRemarks.find(
            (r) => r.itemId === itemId
          );
          return {
            wrapUpCode: item.code,
            wrapUpName: item.name,
            remark: itemRemark?.remark || '',
          };
        })
        .filter(Boolean);

      const payload = {
        wrapUpList,
        remark: wrapUpFormState.notes,
        participantId: currentMessageData.curParticipant?.id,
        userId: currentMessageData.curParticipant?.user.id,
        conversationId: conversationId,
        queueId: currentMessageData.curParticipant?.queue.id,
        state: 'disconnected',
      };
      await wrapUp(basePath, payload);
      // setShowWrapUp(false);
      toast({
        title: 'Success',
        description: 'Wrap-up submitted successfully',
        variant: 'success',
      });
    } catch (error) {
      const errorMessage = 'Operation failed';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'error',
      });
      setIsSubmitting(false);
    } finally {
      // setLoading(false);
      // setIsSubmitting(false);
    }
  };

  const handleWrapUpRevert = () => {
    // 添加返回处理逻辑
    setShowWrapUp(false);
  };

  const toggleAttachmentPage = () => {
    setShowAttachmentPage(!showAttachmentPage);
  };

  const handleFileSelect = async (files: File[]) => {
    // If no files were selected, just return
    if (files.length === 0) return;

    try {
      // Show upload progress notification
      toast({
        title: 'Upload in progress',
        description: `Uploading ${files.length} file(s)...`,
        variant: 'loading',
      });

      let successCount = 0;
      let failCount = 0;
      const failedFiles: string[] = [];
      const successfulUploads: MediaItem[] = [];

      // Upload files one by one to avoid parallel processing issues
      for (const file of files) {
        try {
          const uploadResponse = await uploadMedia(
            basePath,
            conversationId,
            file
          );

          const response = uploadResponse.data.data;
          successCount++;

          const uploadedItem: MediaItem = {
            id: response.id,
            tenant: '',
            platform: '',
            platformMediaId: response.id,
            host: '',
            hostType: '',
            mediaType: 'Image',
            url: response.url,
            mime: file.type,
            filename: file.name,
            contentSizeBytes: response.contentLengthBytes,
            createTime: new Date(),
            createBy: '',
            updateTime: new Date(),
            updateBy: '',
            scanResult: '',
          };

          successfulUploads.push(uploadedItem);
        } catch (error) {
          failCount++;
          failedFiles.push(file.name);
          console.error(`Upload failed for file ${file.name}:`, error);
        }
      }

      // Show failed upload notifications
      if (failedFiles.length > 0) {
        failedFiles.forEach((fileName) => {
          toast({
            title: 'Upload Failed',
            description: `Failed to upload "${fileName}".`,
            variant: 'error',
          });
        });
      }

      if (successfulUploads.length > 0) {
        // 直接添加成功上传的媒体项到媒体列表
        setChatMediaList((prevMediaList) => [
          ...prevMediaList,
          ...successfulUploads,
        ]);

        // Show the attachment page if it's not already visible
        if (!showAttachmentPage) {
          setShowAttachmentPage(true);
        }

        // Show success notification
        toast({
          title: 'Upload Complete',
          description: `Successfully uploaded ${successCount} file(s).`,
          variant: 'success',
        });
      } else if (files.length > 0) {
        // Case where all valid files failed to upload
        toast({
          title: 'Upload Failed',
          description: 'All files failed to upload.',
          variant: 'error',
        });
      }
    } catch (error) {
      // Catch any other errors during the upload process
      console.error('Error occurred during upload:', error);

      toast({
        title: 'Error',
        description: 'An unexpected error occurred during upload.',
        variant: 'error',
      });
    }
  };

  // Handle rejected files
  const handleFileRejected = (fileRejections: FileRejection[]) => {
    // Process each rejected file
    fileRejections.forEach((rejection) => {
      const file = rejection.file;
      const errors = rejection.errors;
      //errors中有可能包含dropzone自带的message 取最后一个就自定义的message了
      if (errors.length > 0) {
        // Get the first error (usually the most important one)
        const error = errors[errors.length - 1];

        // Customize toast message based on error code
        let toastTitle = 'File Error';
        let toastDescription = error.message;

        switch (error.code) {
          case 'file-invalid-type':
            toastTitle = 'File Type Not Supported';
            break;
          case 'file-too-large':
            toastTitle = 'File Size Exceeded';
            break;
          case 'too-many-files':
            toastTitle = 'Too Many Files';
            toastDescription = 'Please select fewer files at once.';
            break;
          default:
            // Use the default error message
            break;
        }

        toast({
          title: toastTitle,
          description: toastDescription,
          variant: 'error',
        });
      } else {
        // Fallback for unexpected errors without specific code
        toast({
          title: 'File Error',
          description: `File "${file.name}" could not be accepted.`,
          variant: 'error',
        });
      }
    });

    // Open the attachment page even if all files were rejected
    if (!showAttachmentPage) {
      setShowAttachmentPage(true);
    }
  };

  const handleRemoveFile = (id: string) => {
    setChatMediaList((prevMediaList) =>
      prevMediaList.filter((item) => item.id !== id)
    );
  };

  const handleSendAttachments = () => {
    // 使用 ref 获取最新的 referenceMessage
    const currentReferenceMessage = referenceMessageRef.current;

    // 打印日志用于调试
    console.log('发送附件时的引用消息:', currentReferenceMessage);

    // 使用新的 sendMediaMessages 函数发送所有媒体项，传递引用消息
    sendMediaMessages(conversationId, chatMediaList, currentReferenceMessage);

    // 发送完成后，清空附件列表并关闭附件页面
    setShowAttachmentPage(false);
    setChatMediaList([]);

    // 清除引用消息状态（ref 和 store 都清除）
    referenceMessageRef.current = undefined;
    clearReferenceMessage();
  };
  const toggleTemplateModal = () => {
    setShowTemplateModal(!showTemplateModal);
  };

  const toggleRecordModal = () => {
    setShowRecordModal(!showRecordModal);
  };

  const handleCancelAttachments = () => {
    // 仅关闭附件页面，不清空已上传的文件列表
    setShowAttachmentPage(false);
  };

  const renderAttachmentPage = () => {
    if (!showAttachmentPage) return null;

    return (
      <div
        style={{
          position: 'absolute',
          bottom: `${chatToolHeight + 16}px`,
          left: '50%',
          transform: 'translateX(-50%)',
          zIndex: 20,
        }}
      >
        <DropzoneAttachmentPage
          attachmentList={convertMediaListToFileItems(chatMediaList)}
          onRemoveFile={handleRemoveFile}
          onAddFiles={handleFileSelect}
          onFileRejected={handleFileRejected}
          onSubmit={handleSendAttachments}
          onCancel={handleCancelAttachments}
          maxSizeByType={GC2WALimits}
          title="Attach File"
          submitLabel="Send"
          cancelLabel="Cancel"
        />
      </div>
    );
  };

  const renderLoadingOverlay = () => {
    if (!isFetchLoading) return null;

    return (
      <div className="absolute inset-0 bg-white/30 flex items-center justify-center z-50">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
          <span className="text-sm text-gray-600 font-medium">
            Loading messages...
          </span>
        </div>
      </div>
    );
  };
  //调起end message api 进入ing状态 等待websocket返回
  const handleEndMessage = () => {
    setIsEnding(true);
    const payload = {
      participantId: currentMessageData.curParticipant?.id,
      state: 'disconnected',
      conversationId: conversationId,
    };
    wrapUp(basePath, payload).catch((err) => {
      setIsConnected(true);
      const errorMessage = 'Operation failed';
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'error',
      });
    });
  };
  const handleWrapUP = () => {
    setShowWrapUp(!showWrapUp);
  };
  const handleTransfer = async (
    type: string,
    id: string,
    transferType?: string
  ) => {
    try {
      if (isTransferring) return;
      setIsTransferring(true);

      const currentMessageData: MessageData = conversationId
        ? messages.find((m: MessageData) => m.conversationId === conversationId)
        : {};
      const participantId: string | undefined =
        currentMessageData?.curParticipant?.id;

      // 根据类型构建不同的 payload
      const transferReq: TransferReq =
        type === 'workgroup' ? { queueId: id } : { userId: id, transferType };

      await msgTransfer(basePath, conversationId, participantId, transferReq);
      toast({
        title: 'Success',
        description: 'Transfer request has been sent successfully',
        variant: 'success',
      });
    } catch (err: any) {
      toast({
        title: 'Error',
        description: 'Transfer failed',
        variant: 'error',
      });
      setIsTransferring(false);
    }
  };

  const handleCancelTransfer = async () => {
    // call cancel api
    const result = await msgTransferCancel(
      basePath,
      conversationId,
      selectedUser?.id
    )
      .then((response) => {
        setSelectedUser(null);
        toast({
          title: 'Success',
          description: 'Transfer request has been successfully canceled',
          variant: 'success',
        });
      })
      .catch((error) => {
        toast({
          title: 'error',
          description: 'cancel failed',
          variant: 'error',
        });
      });
  };
  const renderChatTool = () => {
    return (
      <div
        ref={chatToolRef}
        className="sticky bottom-0 bg-white z-10"
      >
        <ChatTool
          onToggleTemplate={toggleTemplateModal}
          onToggleRecord={toggleRecordModal}
          onToggleAttachmentPage={toggleAttachmentPage}
          isExpanded={isExpanded}
          onExpandChange={handleExpandChange}
          conversationId={conversationId}
          isConnected={isConnected}
          isTransferring={isTransferring}
          showAttachmentPage={showAttachmentPage}
          whatsAppCustomer={whatsAppCustomer}
          isWindowExpired={isWindowExpired}
          countdownTime={countdownTime}
          setHighlightedMessageId={setHighlightedMessageId}
          currentMessages={currentMessages}
        />
      </div>
    );
  };
  const handleTemplateSendSuccess = () => {
    setShowTemplateModal(false);
    // 可以添加其他成功后的操作，如显示通知等
  };

  const handleSendError = (error: Error) => {
    setShowTemplateModal(false);
    console.error('发送模板消息失败:', error);
    // 可以添加其他错误处理，如记录错误等
  };

  return (
    <div className="flex flex-col h-full relative">
      {renderLoadingOverlay()}
      <ChatWindowHeader
        username={
          currentMessageData?.curParticipant?.toAddress.name ??
          currentMessageData?.curParticipant?.toAddress?.addressNormalized ??
          ''
        }
        convST={
          currentMessageData?.curParticipant?.connectedTime !== undefined
            ? new Date(currentMessageData.curParticipant.connectedTime)
            : undefined
        }
        convET={convET}
        onTransfer={handleTransfer}
        onWrapUp={handleWrapUP}
        showWrapUp={showWrapUp}
        isConnected={isConnected}
        isTransferring={isTransferring}
        onCancelTransfer={handleCancelTransfer}
        selectedUser={selectedUser}
        onEndMessage={handleEndMessage}
        isEnding={isEnding}
        isWrapUpping={isSubmitting}
        conversationId={conversationId}
        conversationQueueId={curParticipantQueueId}
      />
      <Popup
        open={showTemplateModal}
        onOpenChange={(open) => {
          if (!open) setShowTemplateModal(false);
        }}
      >
        <PopupContent
          title="Send Template Message"
          className="max-w-[520px] max-h-[90vh] w-full"
        >
          <TemplatePage
            conversationId={conversationId}
            waba={currentMessageData?.channel?.account || ''}
            className="h-[70vh]"
            onSendSuccess={handleTemplateSendSuccess}
            onSendError={handleSendError}
            isDNC={isDNC}
          />
        </PopupContent>
      </Popup>

      {/* 使用 Popup 包装 SessionRecordPage */}
      {/* <Popup
        open={showRecordModal}
        onOpenChange={(open) => {
          if (!open) setShowRecordModal(false);
        }}
      >
        <PopupContent
          title="Session Record"
          className="max-w-[40%] max-h-[90vh] w-full"
        >
          <SessionRecordPage
            currentMessages={currentMessages}
            className="h-[70vh]"
            containerClassName="flex flex-col h-full overflow-hidden border-2"
            contentContainerClassName="flex-1 overflow-y-auto"
            onItemClick={(messageId) => {
              // 处理点击消息时的跳转逻辑
              console.log(`定位到消息: ${messageId}`);
              setShowRecordModal(false);
            }}
          />
        </PopupContent>
      </Popup> */}

      <PopupInline
        title="Session Record"
        messages={currentMessages}
        open={showRecordModal}
        onOpenChange={setShowRecordModal}
        setHighlightedMessageId={setHighlightedMessageId}
      />
      {/* 根据 showWrapUp 状态来条件渲染内容 */}
      {showWrapUp ? (
        <WrapUp
          onRevert={handleWrapUpRevert}
          onSubmit={handleWrapUpSubmit}
          formState={wrapUpFormState}
          onFormStateChange={handleWrapUpFormStateChange}
          wrapUpData={wrapUpData}
          loading={wrapUpLoading}
          error={wrapUpError}
          isSubmit={!isConnected}
          isSubmitting={isSubmitting}
          className="flex-1"
        />
      ) : (
        <>
          {/* Main Content Area */}
          <div className="flex-1 overflow-hidden relative">
            <MessagePage
              currentMessages={currentMessages}
              highlightedMessageId={highlightedMessageId}
              setSAAManualSearchTerm={setSAAManualSearchTerm}
              setReferenceMessage={setReferenceMessage}
              setHighlightedMessageId={setHighlightedMessageId}
            />
          </div>

          {renderAttachmentPage()}

          {/* Chat Tool */}
          {renderChatTool()}
        </>
      )}
      <Toaster />
    </div>
  );
};

export default ChatWindow;
