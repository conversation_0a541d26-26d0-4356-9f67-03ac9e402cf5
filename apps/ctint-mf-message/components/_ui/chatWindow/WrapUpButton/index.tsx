import React from 'react';
import { Loader2 } from 'lucide-react';

interface WrapUpButtonProps {
  isActive: boolean;
  onClick: () => void;
  disabled?: boolean;
  isWrapUpping?: boolean;
}

const WrapUpButton: React.FC<WrapUpButtonProps> = ({
  isActive,
  onClick,
  disabled,
  isWrapUpping = false,
}) => {
  const strokeColor = isActive ? '#FFAC4A' : 'currentColor';

  return (
    <button
      onClick={onClick}
      className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
      aria-label="Wrap Up"
      disabled={isWrapUpping}
    >
      {isWrapUpping ? (
        <Loader2 className="w-6 h-6 animate-spin text-gray-600" />
      ) : (
        <svg
          width="25"
          height="25"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M13.3332 5.3335H9.33317C8.96498 5.3335 8.6665 5.63197 8.6665 6.00016V7.3335C8.6665 7.70169 8.96498 8.00016 9.33317 8.00016H13.3332C13.7014 8.00016 13.9998 7.70169 13.9998 7.3335V6.00016C13.9998 5.63197 13.7014 5.3335 13.3332 5.3335Z"
            stroke={strokeColor}
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M8.66716 6.6665H7.33382C6.9802 6.6665 6.64106 6.80698 6.39101 7.05703C6.14096 7.30708 6.00049 7.64622 6.00049 7.99984V17.3332C6.00049 17.6868 6.14096 18.0259 6.39101 18.276C6.64106 18.526 6.9802 18.6665 7.33382 18.6665H15.3338C15.6874 18.6665 16.0266 18.526 16.2766 18.276C16.5267 18.0259 16.6672 17.6868 16.6672 17.3332V16.9998"
            stroke={strokeColor}
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M14.0005 6.6665H15.3338C15.5676 6.66674 15.7973 6.72847 15.9997 6.84548C16.2021 6.96249 16.3702 7.13068 16.4872 7.33317"
            stroke={strokeColor}
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M8.6665 16H9.33317"
            stroke={strokeColor}
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M17.5852 12.4174C17.8507 12.1519 17.9999 11.7917 17.9999 11.4161C17.9999 11.0405 17.8507 10.6803 17.5852 10.4148C17.3196 10.1492 16.9594 10 16.5838 10C16.2082 10 15.8481 10.1492 15.5825 10.4148L12.9092 13.0894C12.7507 13.2478 12.6346 13.4437 12.5718 13.6588L12.0138 15.5721C11.9971 15.6295 11.9961 15.6903 12.0109 15.7482C12.0257 15.806 12.0559 15.8589 12.0981 15.9011C12.1404 15.9434 12.1932 15.9735 12.2511 15.9883C12.309 16.0032 12.3698 16.0022 12.4272 15.9854L14.3405 15.4274C14.5556 15.3646 14.7514 15.2486 14.9098 15.0901L17.5852 12.4174Z"
            stroke={strokeColor}
            strokeWidth="1.3"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}
    </button>
  );
};

export default WrapUpButton;
