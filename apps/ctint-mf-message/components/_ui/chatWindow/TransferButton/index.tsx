import React, { useRef, useState, useEffect, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Search, Loader2 } from 'lucide-react';
import { UserSearchReq, User } from '../../../../@types/microFrontendConfigs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
import { useRole, useRouteHandler } from '@cdss-modules/design-system';
import { AGENT_STATUS_COLOR_MAP } from '@cdss-modules/design-system/lib/constants/status';
import { getQueueInfo, queueMember, userSearch } from '../../../../lib/api';
import { TWorkgroup } from '@cdss-modules/design-system/@types/Interaction';
import { useGetworkGroupOrUser } from '@cdss-modules/design-system/lib/hooks/useGetworkGroupOrUser';

type TransferTag = 'user' | 'workgroup';

interface TransferButtonProps {
  onTransfer?: (type: string, id: string, transferType?: string) => void;
  onCancelTransfer?: () => void;
  disabled?: boolean;
  transferring?: boolean;
  selectedUser?: User | null;
  conversationId?: string;
  conversationQueueId?: string;
}

const TransferButton: React.FC<TransferButtonProps> = ({
  onTransfer,
  onCancelTransfer,
  disabled,
  transferring,
  selectedUser,
  conversationId,
  conversationQueueId,
}) => {
  const { t } = useTranslation();
  const { basePath } = useRouteHandler();
  const [showSearchPanel, setShowSearchPanel] = useState(false);
  const [showTransferringPanel, setShowTransferringPanel] = useState(false);
  const [transferType, setTransferType] = useState<TransferTag>('user');

  // 缓存状态管理
  const [dataCache, setDataCache] = useState<{
    users: {
      data: User[];
      loaded: boolean;
      loading: boolean;
      error?: string;
    };
    workgroups: {
      data: TWorkgroup[];
      loaded: boolean;
      loading: boolean;
      error?: string;
    };
  }>({
    users: {
      data: [],
      loaded: false,
      loading: false,
    },
    workgroups: {
      data: [],
      loaded: false,
      loading: false,
    },
  });

  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [filteredWorkgroups, setFilteredWorkgroups] = useState<TWorkgroup[]>(
    []
  );
  const [searchQuery, setSearchQuery] = useState('');

  const buttonRef = useRef<HTMLDivElement>(null);
  const strokeColor = showSearchPanel ? '#FFAC4A' : 'currentColor';
  const { getAllWorkgroupHandler } = useGetworkGroupOrUser();
  const { userConfig } = useRole();

  const [panelPosition, setPanelPosition] = useState({
    top: 0,
    left: 0,
  });

  const SEARCH_PANEL_ID = 'transfer-search-panel';
  const TRANSFERRING_PANEL_ID = 'transfer-transferring-panel';

  const updatePanelPosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setPanelPosition({
        top: rect.bottom + window.scrollY + 5,
        left: rect.left + window.scrollX,
      });
    }
  };

  useEffect(() => {
    const handleResize = () => {
      if (showSearchPanel || showTransferringPanel) {
        updatePanelPosition();
      }
    };

    const handleScroll = () => {
      if (showSearchPanel || showTransferringPanel) {
        updatePanelPosition();
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('scroll', handleScroll, true);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [showSearchPanel, showTransferringPanel]);

  useEffect(() => {
    return () => {
      const searchPanel = document.getElementById(SEARCH_PANEL_ID);
      if (searchPanel && searchPanel.parentElement) {
        document.body.removeChild(searchPanel.parentElement);
      }

      const transferringPanel = document.getElementById(TRANSFERRING_PANEL_ID);
      if (transferringPanel && transferringPanel.parentElement) {
        document.body.removeChild(transferringPanel.parentElement);
      }
    };
  }, []);

  useEffect(() => {
    setShowTransferringPanel(transferring || false);
  }, [transferring, selectedUser]);

  //  conversationId变化时的处理
  useEffect(() => {
    if (conversationId) {
      console.log(
        '🔄 Conversation changed, resetting cache for:',
        conversationId
      );
      setDataCache({
        users: { data: [], loaded: false, loading: false },
        workgroups: { data: [], loaded: false, loading: false },
      });
      setFilteredUsers([]);
      setFilteredWorkgroups([]);
      setSearchQuery('');
      setShowSearchPanel(false);
    }
  }, [conversationId, conversationQueueId]);

  useEffect(() => {
    if (disabled) {
      setShowSearchPanel(false);
    }
    updatePanelPosition();
  }, [disabled]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current &&
        buttonRef.current.contains(event.target as Node)
      ) {
        return;
      }

      const searchPanelElement = document.getElementById(SEARCH_PANEL_ID);
      const transferringPanelElement = document.getElementById(
        TRANSFERRING_PANEL_ID
      );

      const clickedInSearchPanel =
        searchPanelElement && searchPanelElement.contains(event.target as Node);
      const clickedInTransferringPanel =
        transferringPanelElement &&
        transferringPanelElement.contains(event.target as Node);

      if (!clickedInSearchPanel && !clickedInTransferringPanel) {
        setShowSearchPanel(false);
        if (showTransferringPanel) {
          setShowTransferringPanel(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTransferringPanel]);

  // 队列成员加载逻辑
  const loadQueueMembers = useCallback(async () => {
    if (!conversationQueueId) {
      console.log(' No conversationQueueId provided');
      return;
    }

    // 防止重复加载
    if (dataCache.users.loading) {
      console.log('⏳ Already loading queue members');
      return;
    }

    console.log('🔍 Loading queue members for:', conversationQueueId);

    setDataCache((prev) => ({
      ...prev,
      users: { ...prev.users, loading: true, error: undefined },
    }));

    try {
      const response = await queueMember(basePath, conversationQueueId);
      const result = response.data?.data;

      console.log('Queue members API response:', result);
      console.log(
        ' Response type:',
        typeof result,
        'Array:',
        Array.isArray(result)
      );

      let usersData: User[] = [];
      if (Array.isArray(result) && result.length > 0) {
        const queueData = result.find(
          (item) => item.queueId === conversationQueueId
        );
        console.log(' Found queue data:', queueData);

        if (queueData && Array.isArray(queueData.items)) {
          usersData = queueData.items.map((item: any) => ({
            id: item.id,
            name: item.name,
            email: item.email,
            phone: item.phone || '',
            presence: {
              presenceDefinition: {
                systemPresence: item.presenceStatus || item.routingStatus,
              },
            },
          }));
          console.log(' Processed users data:', usersData.length, usersData);
        } else {
          console.log(' No items found in queue data or items is not array');
        }
      } else {
        console.log('No queue data found or result is not array');
      }

      setDataCache((prev) => ({
        ...prev,
        users: {
          data: usersData,
          loaded: true,
          loading: false,
        },
      }));

      // 只在当前选中用户类型时更新过滤结果
      if (transferType === 'user') {
        setFilteredUsers(usersData);
        console.log(' Updated filtered users:', usersData.length);
      }
    } catch (error) {
      console.error(' Error loading queue members:', error);
      setDataCache((prev) => ({
        ...prev,
        users: {
          data: [],
          loaded: true,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to load queue members',
        },
      }));

      if (transferType === 'user') {
        setFilteredUsers([]);
      }
    }
  }, [conversationQueueId, basePath, transferType, dataCache.users.loading]);

  // queue加载逻辑
  const loadAllWorkgroups = useCallback(async () => {
    if (dataCache.workgroups.loading) {
      console.log('Already loading workgroups');
      return;
    }

    console.log(' Loading workgroups');

    setDataCache((prev) => ({
      ...prev,
      workgroups: { ...prev.workgroups, loading: true, error: undefined },
    }));

    try {
      const response = await getQueueInfo(`${basePath}`, 'message');
      const result = response.data?.data;
      const workgroupsData = Array.isArray(result) ? result : [];

      console.log(
        ' Workgroups API response:',
        workgroupsData.length,
        workgroupsData
      );

      setDataCache((prev) => ({
        ...prev,
        workgroups: {
          data: workgroupsData,
          loaded: true,
          loading: false,
        },
      }));

      if (transferType === 'workgroup') {
        setFilteredWorkgroups(workgroupsData);
        console.log(' Updated filtered workgroups:', workgroupsData.length);
      }
    } catch (error) {
      console.error('Error loading workgroups:', error);
      setDataCache((prev) => ({
        ...prev,
        workgroups: {
          data: [],
          loaded: true,
          loading: false,
          error:
            error instanceof Error
              ? error.message
              : 'Failed to load workgroups',
        },
      }));

      if (transferType === 'workgroup') {
        setFilteredWorkgroups([]);
      }
    }
  }, [basePath, transferType, dataCache.workgroups.loading]);

  // 修复5: 优化搜索逻辑
  const filterUsers = useCallback(
    (query: string) => {
      const sourceData = dataCache.users.data;
      console.log(
        'Filtering users with query:',
        query,
        'from data:',
        sourceData.length
      );

      if (!query.trim()) {
        setFilteredUsers(sourceData);
        return;
      }

      const filtered = sourceData.filter(
        (user) =>
          user.name.toLowerCase().includes(query.toLowerCase()) ||
          (user.phone && user.phone.includes(query))
      );
      console.log('Filtered users result:', filtered.length);
      setFilteredUsers(filtered);
    },
    [dataCache.users.data]
  );

  const searchWorkgroups = useCallback(
    (query: string) => {
      const sourceData = dataCache.workgroups.data;
      console.log(
        'Filtering workgroups with query:',
        query,
        'from data:',
        sourceData.length
      );

      const filtered = query
        ? sourceData.filter((workgroup) =>
            workgroup.name.toLowerCase().includes(query.toLowerCase())
          )
        : sourceData;
      console.log('Filtered workgroups result:', filtered.length);
      setFilteredWorkgroups(filtered);
    },
    [dataCache.workgroups.data]
  );

  const handleSearch = useCallback(
    async (query: string) => {
      if (transferring) return;

      if (transferType === 'user') {
        filterUsers(query);
      } else {
        searchWorkgroups(query);
      }
    },
    [transferType, transferring, filterUsers, searchWorkgroups]
  );

  // 修复6: 改进类型切换逻辑
  const handleTypeChange = (type: TransferTag) => {
    if (type === transferType) return;

    console.log('Switching transfer type from', transferType, 'to', type);
    setTransferType(type);
    setSearchQuery('');

    if (type === 'user') {
      const userData = dataCache.users.data;
      if (dataCache.users.loaded) {
        console.log('Using cached user data:', userData.length);
        setFilteredUsers(userData);
      } else {
        console.log(' No cached user data, will load');
        setFilteredUsers([]);
        if (!dataCache.users.loading) {
          loadQueueMembers();
        }
      }
    } else {
      const workgroupData = dataCache.workgroups.data;
      if (dataCache.workgroups.loaded) {
        console.log('Using cached workgroup data:', workgroupData.length);
        setFilteredWorkgroups(workgroupData);
      } else {
        console.log('No cached workgroup data, will load');
        setFilteredWorkgroups([]);
        if (!dataCache.workgroups.loading) {
          loadAllWorkgroups();
        }
      }
    }
  };

  const handleSearchInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
  };

  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (!transferring) {
        handleSearch(searchQuery);
      }
    }, 300);

    return () => clearTimeout(debounceTimer);
  }, [searchQuery, transferType, transferring, handleSearch]);

  useEffect(() => {
    if (showSearchPanel && !transferring) {
      updatePanelPosition();
    }
  }, [showSearchPanel, transferring]);

  const handleTransfer = (type: string, id: string, transferType?: string) => {
    onTransfer?.(type, id, transferType);
    setShowSearchPanel(false);
  };

  const handleCancelTransfer = () => {
    onCancelTransfer?.();
  };

  // 修复7: 改进面板打开逻辑
  const handleButtonClick = () => {
    if (!disabled) {
      if (transferring) {
        setShowTransferringPanel(!showTransferringPanel);
      } else {
        const newShowSearchPanel = !showSearchPanel;
        setShowSearchPanel(newShowSearchPanel);

        if (newShowSearchPanel) {
          setSearchQuery('');

          // 根据当前类型处理数据
          if (transferType === 'user') {
            const userData = dataCache.users.data;
            if (dataCache.users.loaded && userData.length > 0) {
              console.log('Using existing user cache:', userData.length);
              setFilteredUsers(userData);
            } else {
              console.log('Loading fresh user data');
              setFilteredUsers([]);
              if (!dataCache.users.loading) {
                loadQueueMembers();
              }
            }
          } else {
            const workgroupData = dataCache.workgroups.data;
            if (dataCache.workgroups.loaded && workgroupData.length > 0) {
              console.log(
                'Using existing workgroup cache:',
                workgroupData.length
              );
              setFilteredWorkgroups(workgroupData);
            } else {
              console.log('Loading fresh workgroup data');
              setFilteredWorkgroups([]);
              if (!dataCache.workgroups.loading) {
                loadAllWorkgroups();
              }
            }
          }
        }
      }

      setTimeout(() => {
        updatePanelPosition();
      }, 0);
    }
  };

  const getStatusColor = (user: User) => {
    const systemPresence =
      user.presence?.presenceDefinition?.systemPresence?.toLowerCase();
    return systemPresence !== undefined && systemPresence !== null
      ? AGENT_STATUS_COLOR_MAP[systemPresence]
      : AGENT_STATUS_COLOR_MAP.default;
  };

  const renderTransferringPanel = () => {
    if (!showTransferringPanel || !buttonRef.current || !selectedUser) {
      return null;
    }

    return createPortal(
      <div
        id={TRANSFERRING_PANEL_ID}
        className="bg-white rounded-lg shadow-lg border border-gray-200 w-80 fixed"
        style={{
          top: `${panelPosition.top}px`,
          left: `${panelPosition.left}px`,
          zIndex: 9999,
        }}
      >
        <div className="p-4">
          <div className="text-base font-medium mb-4">Transferring Call</div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
              <div>
                <div className="text-sm font-medium">{selectedUser.name}</div>
                {selectedUser.phone && (
                  <div className="text-xs text-gray-500">
                    {selectedUser.phone}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleCancelTransfer();
                setShowTransferringPanel(false);
              }}
              className="px-3 py-1 text-sm text-red-500 hover:bg-red-50 rounded-md transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>,
      document.body
    );
  };

  // 修复8: 改进用户列表渲染逻辑
  const renderUserList = () => {
    const { loading, loaded, data, error } = dataCache.users;
    const hasData = data.length > 0;
    const hasFilteredData = filteredUsers.length > 0;

    console.log('Rendering user list:', {
      loading,
      loaded,
      dataLength: data.length,
      filteredLength: filteredUsers.length,
      searchQuery,
      error,
    });

    // 显示错误
    if (error) {
      return (
        <div className="p-4 text-center text-red-500">
          <div className="text-sm">{error}</div>
          <button
            onClick={() => {
              console.log('🔄 Retrying load queue members');
              setDataCache((prev) => ({
                ...prev,
                users: { data: [], loaded: false, loading: false },
              }));
              loadQueueMembers();
            }}
            className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-600 rounded hover:bg-red-200"
          >
            Retry
          </button>
        </div>
      );
    }

    // 正在加载
    if (loading) {
      return (
        <div className="p-4 text-center text-gray-500">
          <Loader2 className="w-4 h-4 animate-spin mx-auto mb-2" />
          {t('Loading...')}
        </div>
      );
    }

    // 已加载完成
    if (loaded) {
      // 有搜索条件
      if (searchQuery.trim()) {
        return hasFilteredData ? (
          filteredUsers.map((user) => renderUserItem(user))
        ) : (
          <div className="p-4 text-center text-gray-500">
            {t('No users found')}
          </div>
        );
      }

      // 无搜索条件，显示所有数据
      return hasData ? (
        filteredUsers.map((user) => renderUserItem(user))
      ) : (
        <div className="p-4 text-center text-gray-500">
          {t('No queue members available')}
        </div>
      );
    }

    // 未开始加载
    return (
      <div className="p-4 text-center text-gray-500">
        {t('Loading queue members...')}
      </div>
    );
  };

  // 修复9: 改进工作组列表渲染逻辑
  const renderWorkgroupList = () => {
    const { loading, loaded, data, error } = dataCache.workgroups;
    const hasData = data.length > 0;
    const hasFilteredData = filteredWorkgroups.length > 0;

    console.log('🎨 Rendering workgroup list:', {
      loading,
      loaded,
      dataLength: data.length,
      filteredLength: filteredWorkgroups.length,
      searchQuery,
      error,
    });

    if (error) {
      return (
        <div className="p-4 text-center text-red-500">
          <div className="text-sm">{error}</div>
          <button
            onClick={() => {
              console.log('Retrying load workgroups');
              setDataCache((prev) => ({
                ...prev,
                workgroups: { data: [], loaded: false, loading: false },
              }));
              loadAllWorkgroups();
            }}
            className="mt-2 px-3 py-1 text-xs bg-red-100 text-red-600 rounded hover:bg-red-200"
          >
            Retry
          </button>
        </div>
      );
    }

    if (loading) {
      return (
        <div className="p-4 text-center text-gray-500">
          <Loader2 className="w-4 h-4 animate-spin mx-auto mb-2" />
          {t('Loading...')}
        </div>
      );
    }

    if (loaded) {
      if (searchQuery.trim()) {
        return hasFilteredData ? (
          filteredWorkgroups.map((workgroup) => renderWorkgroupItem(workgroup))
        ) : (
          <div className="p-4 text-center text-gray-500">
            {t('No workgroups found')}
          </div>
        );
      }

      return hasData ? (
        filteredWorkgroups.map((workgroup) => renderWorkgroupItem(workgroup))
      ) : (
        <div className="p-4 text-center text-gray-500">
          {t('No workgroups available')}
        </div>
      );
    }

    return (
      <div className="p-4 text-center text-gray-500">
        {t('Loading workgroups...')}
      </div>
    );
  };

  const renderSearchPanel = () => {
    if (!showSearchPanel || !buttonRef.current) return null;

    return createPortal(
      <div
        id={SEARCH_PANEL_ID}
        className="bg-white rounded-lg shadow-lg border border-gray-200 w-80 fixed"
        style={{
          top: `${panelPosition.top}px`,
          left: `${panelPosition.left}px`,
          zIndex: 9999,
        }}
      >
        <div className="p-3 border-b border-gray-100">
          <div className="text-base font-medium mb-2">Transfer</div>
          <div className="relative">
            <input
              type="text"
              value={searchQuery}
              onChange={handleSearchInput}
              placeholder="Search"
              className="w-full pl-8 pr-3 py-1.5 text-sm border border-gray-200 rounded-lg"
            />
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          </div>
        </div>

        <div className="flex border-b border-gray-200">
          <button
            className={`flex-1 h-9 text-sm ${
              transferType === 'user'
                ? 'border-b-2 border-blue-500 text-blue-500 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => handleTypeChange('user')}
          >
            User
          </button>
          <button
            className={`flex-1 h-9 text-sm ${
              transferType === 'workgroup'
                ? 'border-b-2 border-blue-500 text-blue-500 font-medium'
                : 'text-gray-500'
            }`}
            onClick={() => handleTypeChange('workgroup')}
          >
            Workgroup
          </button>
        </div>

        <div className="max-h-80 overflow-y-auto">
          {transferType === 'user' ? renderUserList() : renderWorkgroupList()}
        </div>
      </div>,
      document.body
    );
  };

  const TransferIcon = () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <circle
        cx="12"
        cy="12"
        r="12"
        fill="#2A69FF"
      />
      <path
        d="M14.2123 7.18349C14.441 6.94771 14.8229 6.93789 15.0642 7.16139L18.625 10.4672C19.125 10.9314 19.125 11.7125 18.625 12.1767L15.0642 15.4825C14.8229 15.706 14.441 15.6962 14.2123 15.4604C13.9836 15.2246 13.9937 14.8513 14.2349 14.6278L17.7957 11.322L14.2349 8.0161C13.9937 7.7926 13.9836 7.41927 14.2123 7.18349ZM10.6289 7.78523C10.6289 7.47576 10.8149 7.19332 11.1063 7.06806C11.3978 6.9428 11.7346 6.99437 11.9708 7.20068L15.9914 10.7374C16.1598 10.8872 16.2578 11.0985 16.2578 11.322C16.2578 11.5455 16.1598 11.7567 15.9914 11.9065L11.9708 15.4432C11.7346 15.652 11.3953 15.7036 11.1063 15.5759C10.8174 15.4481 10.6289 15.1682 10.6289 14.8587V13.2868H9.82478C8.49294 13.2868 7.41239 14.3429 7.41239 15.6446C7.41239 16.3913 7.73404 16.8211 7.97025 17.0372C8.10845 17.1625 8.21651 17.3319 8.21651 17.5161C8.21651 17.7838 7.99537 18 7.72147 18C7.65111 18 7.58075 17.9852 7.51793 17.9533C7.04801 17.7053 5 16.4625 5 13.6798C5 11.2925 6.98017 9.35711 9.42271 9.35711H10.6289V7.78523Z"
        fill="white"
      />
    </svg>
  );

  const renderUserActionButtons = (user: User) => {
    const isOnQueue =
      user.presence?.presenceDefinition?.systemPresence?.toLowerCase() ===
      'on queue';
    const isNotCurrentUser = user.id !== userConfig?.id;

    if (isOnQueue && isNotCurrentUser) {
      return (
        <button
          className="p-1 hover:bg-gray-100 rounded cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleTransfer('user', user.id, 'Attended');
          }}
        >
          <TransferIcon />
        </button>
      );
    }
    return null;
  };

  const renderWorkgroupActionButtons = (workgroup: TWorkgroup) => {
    return (
      <button
        className="p-1 hover:bg-gray-100 rounded"
        onClick={(e) => {
          e.stopPropagation();
          handleTransfer('workgroup', workgroup.id);
        }}
      >
        <TransferIcon />
      </button>
    );
  };

  const renderUserItem = (user: User) => (
    <div
      key={user.id}
      className="px-3 py-2 flex items-center justify-between hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
    >
      <div className="flex items-center gap-2">
        <div
          className="w-2 h-2 rounded-full"
          style={{ backgroundColor: getStatusColor(user) }}
        />
        <span className="text-sm">{user.name}</span>
      </div>
      <div className="flex items-center gap-1">
        {user.phone && (
          <span className="text-xs text-gray-500">{user.phone}</span>
        )}
        {renderUserActionButtons(user)}
      </div>
    </div>
  );

  const renderWorkgroupItem = (workgroup: TWorkgroup) => (
    <div
      key={workgroup.id}
      className="px-3 py-2 flex items-center justify-between hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
    >
      <div className="flex items-center gap-2">
        <span className="text-sm">{workgroup.name}</span>
      </div>
      <div>{renderWorkgroupActionButtons(workgroup)}</div>
    </div>
  );

  // 调试用的 useEffect
  useEffect(() => {
    console.log('=== Debug Info ===');
    console.log('Transfer Type:', transferType);
    console.log('Search Query:', searchQuery);
    console.log('Users Cache:', dataCache.users);
    console.log('Workgroups Cache:', dataCache.workgroups);
    console.log('Filtered Users:', filteredUsers.length);
    console.log('Filtered Workgroups:', filteredWorkgroups.length);
    console.log('==================');
  }, [transferType, searchQuery, dataCache, filteredUsers, filteredWorkgroups]);

  return (
    <div
      ref={buttonRef}
      className="relative"
    >
      <button
        onClick={handleButtonClick}
        className={`p-2 rounded-lg transition-colors relative ${
          disabled ? 'cursor-not-allowed opacity-50' : 'hover:bg-gray-100'
        }`}
        disabled={disabled}
      >
        {transferring ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-t-transparent border-yellow-500 rounded-full animate-spin" />
          </div>
        ) : (
          <svg
            width="25"
            height="25"
            viewBox="0 0 16 16"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={`${disabled ? 'opacity-50' : ''}`}
            stroke={strokeColor}
          >
            <path
              d="M4.66667 4.66667H11.3333V11.3333"
              stroke={strokeColor}
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M4.66667 11.3333L11.3333 4.66667"
              stroke={strokeColor}
              strokeWidth="1.33333"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        )}
      </button>
      {!disabled && (
        <>
          {showSearchPanel && renderSearchPanel()}
          {showTransferringPanel && renderTransferringPanel()}
        </>
      )}
    </div>
  );
};

export default TransferButton;
