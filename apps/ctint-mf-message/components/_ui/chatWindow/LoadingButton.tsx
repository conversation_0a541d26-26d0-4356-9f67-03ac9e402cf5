import React from 'react';
import { Loader } from 'lucide-react';

interface LoadingButtonProps {
  /** 点击事件处理函数 */
  onClick?: () => void;
  /** 是否禁用按钮 */
  disabled?: boolean;
  /** 是否显示加载状态 */
  isLoading?: boolean;
  /** 按钮内容 */
  children: React.ReactNode;
  /** 自定义类名 */
  className?: string;
  /** 加载图标尺寸 */
  loaderSize?: number;
  /** 加载图标颜色 */
  loaderColor?: string;
  /** button 元素的类型 */
  type?: 'button' | 'submit' | 'reset';
}

const LoadingButton: React.FC<LoadingButtonProps> = ({
  onClick,
  disabled = false,
  isLoading = false,
  children,
  className = '',
  loaderSize = 20,
  loaderColor = 'text-gray-600',
  type = 'button',
}) => {
  return (
    <button
      type={type}
      className={`relative flex items-center justify-center transition-opacity
        ${disabled || isLoading ? 'opacity-50 cursor-not-allowed' : 'hover:cursor-pointer'}
        ${className}`}
      onClick={onClick}
      disabled={disabled || isLoading}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Loader
            className={`animate-spin ${loaderColor}`}
            size={loaderSize}
          />
        </div>
      )}
      <div className={isLoading ? 'invisible' : ''}>{children}</div>
    </button>
  );
};

export default LoadingButton;
