import React, { useState, KeyboardEvent, useRef, useEffect } from 'react';
import IconBase from '../../../public/iconsConfig.json';
import EmojiPicker from './EmojiPicker';
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
import { useRouteHandler } from '@cdss-modules/design-system';
import SendButton from './SendButton';
import DropzoneAttachmentButton from '@cdss-modules/design-system/components/_ui/DropzoneAttachmentButton';
import { useMessageSender } from '../../../hooks/useMessageSender';
import IconCRT from '@cdss-modules/design-system/components/_ui/Icon/IconCRT';
import TemplateSelector from '@cdss-modules/design-system/components/_ui/CannedResponses';
// @ts-ignore
import { useConversationStore } from 'cdss/store/conversation';
import { CDSSMessage } from '@cdss-modules/design-system/@types/Message';
import MessageReply from '@cdss-modules/design-system/components/_ui/MessagePage/MessageReply';

interface ChatToolProps {
  onToggleTemplate: () => void;
  onToggleRecord: () => void;
  onExpandChange: (isExpanded: boolean) => void;
  isExpanded: boolean;
  conversationId: string;
  isConnected: boolean;
  isTransferring: boolean;
  showAttachmentPage?: boolean;
  onToggleAttachmentPage: () => void;
  whatsAppCustomer?: Customer;
  isWindowExpired: boolean;
  countdownTime: string;
  setHighlightedMessageId?: (messageId: string) => void;
  currentMessages: CDSSMessage[];
}

const ChatTool: React.FC<ChatToolProps> = ({
  onToggleTemplate,
  onToggleRecord,
  onExpandChange,
  isExpanded,
  conversationId,
  isConnected,
  isTransferring,
  onToggleAttachmentPage,
  whatsAppCustomer,
  isWindowExpired,
  countdownTime,
  setHighlightedMessageId,
  currentMessages,
}) => {
  const [message, setMessage] = useState('');
  const [referenceMessage, setReferenceMessage] = useState<
    CDSSMessage | undefined
  >();
  const [isButtonLoading, setIsButtonLoading] = useState(false);
  const [cooldownTime, setCooldownTime] = useState(0);
  const { basePath } = useRouteHandler();
  const { sendTextMessage } = useMessageSender(basePath);
  const lastProcessTime = useRef<number>(0);
  const cooldownTimer = useRef<NodeJS.Timeout | null>(null);
  const [isDNC, setDNC] = useState<boolean>(false);
  // canned response Template
  const [CRTActive, setCRTActive] = useState<boolean>(false);
  //获取store的值 每次变化都要触发页面渲染 不符合要求(有条件过滤) 要用订阅模式
  const saaContent = useConversationStore((state: any) => state.SAAContent);

  // 获取 store 的清除引用消息方法
  const { clearReferenceMessage } = useConversationStore();

  // Calculate placeholder text
  const getPlaceholder = () => {
    if (isWindowExpired) {
      return 'The conversation window is closed';
    } else if (isTransferring || isConnected) {
      return 'Please enter your content press enter to complete the line';
    } else {
      return 'Please Wrap Up';
    }
  };

  const placeholder = getPlaceholder();

  // 监听复制内容的变化
  useEffect(() => {
    // 专门订阅 SAAContent 的变化
    const unsubscribe = useConversationStore.subscribe(
      (state: any) => state.SAAContent,
      (newSAAContent: any) => {
        if (newSAAContent && !isWindowExpired) {
          setMessage(newSAAContent);
          console.log('SAAContent 更新:', newSAAContent);
        }
      }
    );
    // 返回清理函数
    return unsubscribe;
  }, [isWindowExpired]);

  useEffect(() => {
    // 订阅referenceMessage的变化
    const unsubscribeReference = useConversationStore.subscribe(
      (state: any) => state.referenceMessage,
      (newReferenceMessage: any) => {
        if (newReferenceMessage && newReferenceMessage.id && !isWindowExpired) {
          setReferenceMessage(newReferenceMessage);
        } else if (!newReferenceMessage || !newReferenceMessage.id) {
          // 当 store 中的 referenceMessage 被清除时，也清除本地状态
          setReferenceMessage(undefined);
        }
      }
    );
    // 返回清理函数
    return unsubscribeReference;
  }, [isWindowExpired]);

  //检测referenceMessage的变化
  useEffect(() => {
    console.log('referenceMessage 更新:', referenceMessage);
  }, [referenceMessage]);

  // 使用 useEffect 强制处理会话切换
  useEffect(() => {
    // 重置消息
    setMessage('');
    // 重置引用消息
    setReferenceMessage(undefined);
    clearReferenceMessage();

    // 重置计时器
    if (cooldownTimer.current) {
      clearTimeout(cooldownTimer.current);
    }
  }, [conversationId, clearReferenceMessage]);
  //从store中获取最新的message 通过id
  const getLatestReferenceMessage = () => {
    if (!referenceMessage || !referenceMessage.id) {
      return undefined;
    }

    const latestMessage = currentMessages?.find(
      (msg: CDSSMessage) => msg.id === referenceMessage.id
    );

    const messageToUse = latestMessage || referenceMessage;
    return convertToReplyFormat(messageToUse);
  };
  // 处理关闭引用消息
  const handleCloseReply = (messageId: string) => {
    console.log(`Closing reply for message: ${messageId}`);
    // 清除本地状态
    setReferenceMessage(undefined);
    // 清除 store 中的引用消息
    clearReferenceMessage();
  };

  // 将 CDSSMessage 转换为 MessageReply 需要的格式
  const convertToReplyFormat = (cdssMessage: CDSSMessage) => {
    // 处理模板消息
    if (cdssMessage.type === 'whatsappTemplate') {
      try {
        const templateData = JSON.parse(cdssMessage.textBody);
        let templateHeader;
        let templateBody;

        // 处理 header
        const headerComponent = templateData.components?.find(
          (comp: any) => comp.type === 'HEADER'
        );
        if (headerComponent) {
          if (headerComponent.format === 'TEXT') {
            templateHeader = {
              type: 'text' as const,
              content: headerComponent.text || '',
            };
          } else if (headerComponent.format === 'IMAGE') {
            const imageUrl =
              headerComponent.image?.link ||
              cdssMessage.medias?.find((item) => item.mediaType === 'Image')
                ?.url;
            templateHeader = {
              type: 'image' as const,
              content: headerComponent.text || '',
              imageUrl: imageUrl,
            };
          }
        }

        // 处理 body
        const bodyComponent = templateData.components?.find(
          (comp: any) => comp.type === 'BODY'
        );
        if (bodyComponent) {
          templateBody = bodyComponent.text;
        }

        return {
          ...cdssMessage,
          type: 'whatsappTemplate',
          templateHeader,
          templateBody,
        };
      } catch (error) {
        console.error('Failed to parse template data:', error);
        return cdssMessage;
      }
    }

    // 处理媒体消息
    if (cdssMessage.medias && cdssMessage.medias.length > 0) {
      const media = cdssMessage.medias[0];
      if (media.mediaType === 'Image' || media.mime?.startsWith('image/')) {
        return {
          ...cdssMessage,
          type: 'image',
        };
      } else {
        return {
          ...cdssMessage,
          type: 'file',
        };
      }
    }

    // 默认为文本消息
    return {
      ...cdssMessage,
      type: 'text',
    };
  };

  const startCooldown = (waitTime: number) => {
    setIsButtonLoading(true);
    setCooldownTime(waitTime);

    const startTime = Date.now();
    const updateCooldown = () => {
      const elapsed = Date.now() - startTime;
      const remaining = waitTime - elapsed;

      if (remaining <= 0) {
        setIsButtonLoading(false);
        setCooldownTime(0);
        if (cooldownTimer.current) {
          clearTimeout(cooldownTimer.current);
        }
      } else {
        setCooldownTime(remaining);
        cooldownTimer.current = setTimeout(updateCooldown, 100);
      }
    };

    cooldownTimer.current = setTimeout(updateCooldown, 100);
  };

  const calculateWaitTime = (currentTime: number): number => {
    const timeSinceLastProcess = currentTime - lastProcessTime.current;

    if (timeSinceLastProcess >= 3000) {
      return 0;
    } else {
      return 3000 - timeSinceLastProcess;
    }
  };

  const handleExpandToggle = () => {
    onExpandChange(!isExpanded);
  };

  const handleSendMessage = async (message: string) => {
    // Prevent sending message if the window is expired
    if (isWindowExpired) {
      return;
    }

    const currentTime = Date.now();
    const waitTime = calculateWaitTime(currentTime);

    if (waitTime > 0) {
      startCooldown(waitTime);
      return;
    }

    // Only clean whitespace at the beginning and end, preserve line breaks
    const re_textbody = message.replace(/^\s+|\s+$/g, '');
    if (re_textbody) {
      try {
        // 先记录处理时间并清空输入框
        lastProcessTime.current = Date.now();
        setMessage(''); // Clear input immediately after sending

        // 发送消息，如果有引用消息则传递
        await sendTextMessage(conversationId, re_textbody, referenceMessage);

        // 清除引用消息（发送消息后自动清除回复引用）
        setReferenceMessage(undefined);
        clearReferenceMessage();
      } catch (error) {
        console.error('Failed to send message:', error);
      }
    }
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    // Prevent input if window is expired
    if (isWindowExpired) {
      e.preventDefault();
      return;
    }

    if (e.key === 'Enter') {
      if (e.shiftKey) {
        // Shift+Enter 发送消息
        e.preventDefault();
        handleSendMessage(message);
      } else {
        // Enter 换行 (允许默认行为)
        return;
      }
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    // Prevent adding emoji if window is expired
    if (isWindowExpired) {
      return;
    }
    setMessage((prevMessage) => prevMessage + emoji);
  };

  const toggleCRT = () => {
    setCRTActive(!CRTActive);
  };

  const handleCRTSelect = (content: string, templateId: string) => {
    // Prevent setting template content if window is expired
    if (isWindowExpired) {
      return;
    }

    // Set the template content as the message
    setMessage(content);

    // Close the template selector
    setCRTActive(false);
  };

  // Conditionally render UI elements based on window status
  const renderActionButtons = () => {
    return (
      <>
        <EmojiPicker onSelect={handleEmojiSelect} />
        <DropzoneAttachmentButton
          onToggleAttachmentPage={onToggleAttachmentPage}
          disabled={isWindowExpired}
        />
        <TemplateSelector
          isOpen={CRTActive}
          onSelectTemplate={handleCRTSelect}
          onClose={() => {
            setCRTActive(false);
          }}
          spaceType={'messageCannedResponse'}
          contentType={'text'}
          showNotes={false}
        ></TemplateSelector>
        {isExpanded && (
          <>
            <IconCRT
              size={'24px'}
              className="hover:opacity-80 transition-opacity"
              isActive={CRTActive}
              onClick={() => toggleCRT()}
              isDisable={isWindowExpired}
            ></IconCRT>
            <button
              className="flex items-center justify-center hover:cursor-pointer hover:bg-gray-200 rounded-lg"
              onClick={onToggleTemplate}
            >
              <img
                src={IconBase.templateMessage}
                alt="template"
                className="w-8 h-8"
              />
            </button>
            <button
              className="flex items-center justify-center hover:cursor-pointer hover:bg-gray-200 rounded-lg"
              onClick={onToggleRecord}
            >
              <img
                src={IconBase.sessionRecord}
                alt="record"
                className="w-8 h-8"
              />
            </button>
          </>
        )}
        <button
          className="flex items-center justify-center hover:cursor-pointer"
          onClick={handleExpandToggle}
        >
          <img
            src={IconBase.chatToolMore}
            alt="more"
            className="w-8 h-8"
          />
        </button>
        <SendButton
          onClick={() => handleSendMessage(message)}
          isButtonLoading={isButtonLoading}
        />
      </>
    );
  };

  return !isConnected ? (
    <></>
  ) : (
    <div className="p-4 relative">
      {/* 添加 relative 作为定位基准 */}

      {/* 悬浮的引用消息显示区域 - 左下角对齐父容器左上角 */}
      {referenceMessage && referenceMessage.id && (
        <div
          className="absolute bottom-full left-0 max-w-[50%] z-50 ml-2
                   animate-in slide-in-from-top-2 duration-200"
        >
          <div
            className="bg-white rounded-lg shadow-lg border border-gray-200
                        ring-1 ring-black/5"
          >
            <MessageReply
              quotedMessage={getLatestReferenceMessage()}
              onClose={handleCloseReply}
              hasCloseButton={true}
              setHighlightedMessageId={setHighlightedMessageId}
            />
          </div>
        </div>
      )}
      {/* 服务窗口倒计时 */}
      <div className="bg-gray-100 rounded-md p-2 mb-2 flex items-center justify-center gap-2">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 text-gray-600"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          />
        </svg>
        <span className="text-sm text-gray-700">
          {isWindowExpired
            ? 'The conversation window is closed'
            : `The conversation window will be closed in: ${countdownTime || '00:00:00'}`}
        </span>
      </div>

      {/* 输入框区域 */}
      <div className="flex flex-col">
        {isExpanded ? (
          <div
            className={`bg-white border border-gray-200 rounded-lg ${isWindowExpired ? 'opacity-70' : ''}`}
          >
            <div className="flex-1 p-4">
              <textarea
                className="bg-transparent outline-none w-full resize-none break-words break-all whitespace-pre-wrap"
                placeholder={placeholder}
                style={{ fieldSizing: 'content' } as React.CSSProperties}
                value={message}
                onChange={(e) => !isWindowExpired && setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                aria-label="Message input"
                disabled={isWindowExpired}
              />
            </div>
            <div className="border-gray-200 p-2">
              <div className="flex justify-end items-center space-x-2">
                {renderActionButtons()}
              </div>
            </div>
          </div>
        ) : (
          <div
            className={`bg-white border border-gray-200 rounded-lg p-2 flex items-end ${isWindowExpired ? 'opacity-70' : ''}`}
          >
            <div className="flex-1">
              <textarea
                className="bg-transparent outline-none w-full resize-none field break-words break-all whitespace-pre-wrap"
                style={{ fieldSizing: 'content' } as React.CSSProperties}
                placeholder={placeholder}
                value={message}
                onChange={(e) => !isWindowExpired && setMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                aria-label="Message input"
                disabled={isWindowExpired}
              />
            </div>
            <div className="flex items-center space-x-2 ml-2">
              {/* <div className="renderActionButtonContainer"> */}
              {renderActionButtons()}
              {/* </div> */}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatTool;
