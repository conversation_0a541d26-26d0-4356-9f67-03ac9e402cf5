import React from 'react';
import {
  WABATemplate,
  Component,
  HeaderComponent,
  Button,
} from '../../../../@types/waTemplate';
import { TemplateComponent } from '../../../../@types/waTemplateRequest';
import HeaderImageUpload from './HeaderImageUpload';

interface TemplatePreviewProps {
  headerComponent: TemplateComponent | null;
  bodyComponent: TemplateComponent | null;
  buttonComponents: TemplateComponent[] | null;
  selectedTemplate?: WABATemplate;
  onImageClick?: (e: React.MouseEvent) => void;
  onReselect?: (e: React.MouseEvent) => void;
  mode?: 'edit' | 'chat';
  className?: string;
}

const TemplatePreview: React.FC<TemplatePreviewProps> = ({
  headerComponent,
  bodyComponent,
  buttonComponents,
  selectedTemplate,
  onImageClick,
  onReselect,
  mode = 'edit',
  className = '',
}) => {
  // 处理占位符替换
  const processTemplateText = (
    originalText = '',
    parameters: TemplateComponent['parameters'] = []
  ) => {
    let processedText = originalText;
    parameters.forEach((param, index) => {
      if (param.type === 'text' && param.text) {
        processedText = processedText.replace(`{{${index + 1}}}`, param.text);
      }
    });
    return processedText;
  };

  // 处理头部组件渲染
  const renderHeaderComponent = (component: HeaderComponent) => {
    if (component.format === 'TEXT') {
      const headerText = headerComponent
        ? processTemplateText(component.text, headerComponent.parameters)
        : component.text;
      return <div className="font-medium">{headerText}</div>;
    }

    if (component.format === 'IMAGE') {
      const imageUrl =
        headerComponent?.parameters[0]?.image?.link || component.text;
      return (
        <HeaderImageUpload
          imagePreview={imageUrl}
          onImageClick={onImageClick}
          onReselect={onReselect}
          isEditable={mode === 'edit'}
        />
      );
    }

    return null;
  };

  // 渲染按钮
  const renderButton = (button: Button, buttonIndex: number) => {
    let buttonAction: ((e: React.MouseEvent) => void) | undefined;

    if (mode === 'chat') {
      switch (button.type) {
        case 'URL':
          buttonAction = () => window.open(button.url, '_blank');
          break;
        case 'PHONE_NUMBER':
          buttonAction = () => window.open(`tel:${button.phone_number}`);
          break;
        case 'COPY_CODE':
          buttonAction = () => navigator.clipboard.writeText(button.text);
          break;
      }
    }

    return (
      <button
        key={buttonIndex}
        className={`w-full p-3 text-center text-blue-600 transition-colors border-b border-gray-200 last:border-b-0 ${
          mode === 'edit' ? 'hover:bg-gray-50' : 'hover:bg-blue-50'
        }`}
        onClick={mode === 'chat' ? buttonAction : (e) => e.preventDefault()}
      >
        {button.text}
      </button>
    );
  };

  // 根据组件类型渲染不同内容
  const renderComponent = (
    component: Component,
    dynamicComponent: TemplateComponent | null = null
  ) => {
    switch (component.type) {
      case 'HEADER':
        return (
          <div className="p-3 bg-gray-50 border-b border-gray-200">
            {renderHeaderComponent(component as HeaderComponent)}
          </div>
        );

      case 'BODY': {
        const bodyText = dynamicComponent
          ? processTemplateText(component.text, dynamicComponent.parameters)
          : component.text;
        return bodyText ? (
          <div className="p-3 whitespace-pre-wrap">{bodyText}</div>
        ) : null;
      }

      case 'FOOTER':
        return component.text ? (
          <div className="p-3 text-sm text-gray-500 border-t border-gray-200">
            {component.text}
          </div>
        ) : null;

      case 'BUTTONS': {
        return component.buttons ? (
          <div className="border-t border-gray-200">
            {component.buttons.map((button, buttonIndex) => {
              let processedPreviewText = '';
              if (button.type === 'URL') {
                processedPreviewText = button.url || '';
                const dynamicButton = buttonComponents?.find(
                  (comp) => comp.index === buttonIndex
                );

                if (dynamicButton?.parameters) {
                  dynamicButton.parameters.forEach((param, paramIndex) => {
                    if (param.type === 'text' && param.text) {
                      processedPreviewText = processedPreviewText.replace(
                        `{{${paramIndex + 1}}}`,
                        param.text
                      );
                    }
                  });
                }
              }

              return (
                <div
                  key={buttonIndex}
                  className="flex flex-col"
                >
                  {button.type === 'URL' && (
                    <div className="px-3 pt-2 text-sm text-red-500">
                      Preview URL: {processedPreviewText}
                    </div>
                  )}
                  {renderButton(button, buttonIndex)}
                </div>
              );
            })}
          </div>
        ) : null;
      }

      default:
        return null;
    }
  };

  if (!selectedTemplate) return null;

  const baseClassName =
    mode === 'edit'
      ? 'bg-white rounded border border-gray-200 overflow-hidden'
      : 'bg-white rounded-lg shadow-sm overflow-hidden max-w-sm';

  return (
    <div className={`space-y-4 ${className}`}>
      {mode === 'edit' && (
        <div className="space-y-2">
          <div className="text-xs text-gray-500">Category</div>
          <div className="text-sm font-medium">{selectedTemplate.category}</div>
        </div>
      )}

      <div className={baseClassName}>
        {/* Header */}
        {selectedTemplate.components.find((comp) => comp.type === 'HEADER') &&
          renderComponent(
            selectedTemplate.components.find((comp) => comp.type === 'HEADER')!,
            headerComponent
          )}

        {/* Body */}
        {selectedTemplate.components.find((comp) => comp.type === 'BODY') &&
          renderComponent(
            selectedTemplate.components.find((comp) => comp.type === 'BODY')!,
            bodyComponent
          )}

        {/* Footer */}
        {selectedTemplate.components.find((comp) => comp.type === 'FOOTER') &&
          renderComponent(
            selectedTemplate.components.find((comp) => comp.type === 'FOOTER')!,
            null
          )}

        {/* Buttons */}
        {selectedTemplate.components.find((comp) => comp.type === 'BUTTONS') &&
          renderComponent(
            selectedTemplate.components.find(
              (comp) => comp.type === 'BUTTONS'
            )!,
            buttonComponents?.[0]
          )}
      </div>
    </div>
  );
};

export default TemplatePreview;
