import React from 'react';
import { Loader, Send } from 'lucide-react';

interface SendButtonProps {
  isButtonLoading: boolean;
  onClick?: () => void;
}

const SendButton: React.FC<SendButtonProps> = ({
  onClick,
  isButtonLoading,
}) => {
  return (
    <button
      className="p-1 rounded-lg rounded-l-none border-l cursor-pointer"
      onClick={onClick}
    >
      <div className="w-10 h-10 flex items-center justify-center text-gray-900">
        {isButtonLoading ? (
          <Loader className="w-6 h-6 animate-spin" />
        ) : (
          <Send className="w-6 h-6" />
        )}
      </div>
    </button>
  );
};

export default SendButton;
