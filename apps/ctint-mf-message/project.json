{"name": "ctint-mf-message", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/ctint-mf-message", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": "apps/ctint-mf-message", "outputPath": "dist/apps/ctint-mf-message"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ctint-mf-message:build", "dev": true, "port": 4200, "host": "0.0.0.0"}, "configurations": {"development": {"buildTarget": "ctint-mf-message:build:development", "dev": true}, "production": {"buildTarget": "ctint-mf-message:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "ctint-mf-message:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["coverage/apps/ctint-mf-message"], "options": {"jestConfig": "apps/ctint-mf-message/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/ctint-mf-message/**/*.{ts,tsx,js,jsx}"]}}}}