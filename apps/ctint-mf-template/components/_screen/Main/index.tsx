import {
  <PERSON>th<PERSON><PERSON><PERSON>,
  <PERSON>,
  useRouteHand<PERSON>,
} from '@cdss-modules/design-system';
import TemplateDemo from '../../_ui/TemplateDemo';
export function Main() {
  const { toPath } = useRouteHandler();
  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-template'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-template.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      <TemplateDemo
        testId="home"
        titleI18n="ctint-mf-template.templateHome.title"
        descI18n="ctint-mf-template.templateHome.desc"
        btnLabelI18n="ctint-mf-template.templateHome.btnLabel"
        onClickButton={() => toPath('/ctint-mf-template/detail')}
      />
    </AuthChecker>
  );
}

export default Main;
