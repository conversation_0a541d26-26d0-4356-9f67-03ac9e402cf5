import loadGlobalConfig from '@cdss-modules/design-system/lib/globalConfig';
import Path from 'path';

export type Props = {
  conversationId?: string;
  customerParticipantId?: string;
};

const Component = (context: Props) => {
  const base = 'http://workflow.tdc.org.hk/directoryCTI/search/result2new.asp';
  const mf = 'ctint-mf-tdc';

  const yaml: any = loadGlobalConfig('ctint-mf-cdss');
  const yamlService: any = yaml?.services?.['ctint-call-control'];
  const baseAPI = Path.join(yamlService?.host, yamlService?.basepath);

  const cdssAuthorization = encodeURIComponent(
    localStorage.getItem('cdss-auth-token') || ''
  );
  const tenant = encodeURIComponent('tdc');
  const api = encodeURIComponent(`${baseAPI}/calls/consult`);
  const sourceId = encodeURIComponent(mf);
  const previousId = encodeURIComponent(mf);
  const conversationId = encodeURIComponent(context.conversationId || '');
  const customerParticipantId = encodeURIComponent(
    context.customerParticipantId || ''
  );

  const link = `${base}?CRMSERVER=${api}&cdssAuthorization=${cdssAuthorization}&tenant=${tenant}&previousId=${previousId}&sourceId=${sourceId}&conversationId=${conversationId}&customerParticipantId=${customerParticipantId}`;

  return (
    <>
      {/* Link: {link} */}
      <iframe
        src={link}
        className="h-screen w-screen"
      />
    </>
  );
};

export default Component;
