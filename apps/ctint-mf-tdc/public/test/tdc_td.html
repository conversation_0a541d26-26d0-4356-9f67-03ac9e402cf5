<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TDC TD</title>
</head>

<body>
    TDC TD
    <div id="output"></div>

    <script>
        // Create a new URLSearchParams object from the query string
        const urlParams = new URLSearchParams(window.location.search);

        // Get the value of a specific query parameter
        const paramValue = urlParams.toString();

        // Display the value in the HTML
        document.getElementById('output').textContent = paramValue;
    </script>
</body>

</html>