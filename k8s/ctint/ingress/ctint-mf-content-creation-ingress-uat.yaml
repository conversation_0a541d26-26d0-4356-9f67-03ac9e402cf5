apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-content-creation-ingress
  namespace: cdss-frontend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /ctint/mf-content-creation(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-content-creation-service
                port:
                  number: 4000
