apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-campaign-ingress
  namespace: cdss-frontend
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /ctint/mf-campaign(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-campaign-service
                port:
                  number: 4000
