apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-tdc-deployment
  namespace: ctint-tdc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-tdc
  template:
    metadata:
      labels:
        app: ctint-mf-tdc
    spec:
      containers:
      - name: ctint-mf-tdc
        image: cdss3projectdevacr.azurecr.io/tdc-mf-tdc:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: tdc-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-tdc/public/config
      volumes:
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: tdc/ctint-cdss-globalconfig
          readOnly: false
