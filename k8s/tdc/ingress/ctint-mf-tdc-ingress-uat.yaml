apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-tdc-ingress
  namespace: ctint-tdc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /tdc/mf-tdc(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-tdc-service
                port:
                  number: 4000
