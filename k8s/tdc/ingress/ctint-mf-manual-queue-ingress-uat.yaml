apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-manual-queue-ingress
  namespace: ctint-tdc
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /tdc/mf-manual-queue(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-manual-queue-service
                port:
                  number: 4000
