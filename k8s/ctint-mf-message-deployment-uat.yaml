apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-message-deployment
  namespace: cdss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-message
  template:
    metadata:
      labels:
        app: ctint-mf-message
    spec:
      containers:
      - name: ctint-mf-message
        image: cdss3uatacr.azurecr.io/ctint-mf-message:1
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: ctint-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-message/public/config
      volumes:  
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3uatakssa-share
          shareName: ctint-cdss-globalconfig
          readOnly: false