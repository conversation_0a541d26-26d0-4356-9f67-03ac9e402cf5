apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-interaction-ingress
  namespace: ctint-qhms-phase2
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /qhms2/mf-interaction(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-interaction-service
                port:
                  number: 4000
