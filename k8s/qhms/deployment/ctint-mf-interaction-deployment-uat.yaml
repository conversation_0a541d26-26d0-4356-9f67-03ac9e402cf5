apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-mf-interaction-deployment
  namespace: ctint-qhms
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-mf-interaction
  template:
    metadata:
      labels:
        app: ctint-mf-interaction
    spec:
      containers:
      - name: ctint-mf-interaction
        image: cdss3projectdevacr.azurecr.io/ctint-mf-interaction:1.0.2359
        ports:
          - name: http
            containerPort: 3000
            protocol: TCP
        env:
        - name: CDSS_PUBLIC_ENVIRONMENT
          value: uat
        - name: GLOBAL_CONFIG_FILE
          value: qhms-global-config-uat.yaml
        volumeMounts:
        - name: ctint-global-config
          mountPath: /app/orign_dist/apps/ctint-mf-interaction/public/config
      volumes:
      - name: ctint-global-config
        azureFile:
          secretName: ctintcdss3projectakssa-share
          shareName: qhms/ctint-cdss-globalconfig
          readOnly: false
