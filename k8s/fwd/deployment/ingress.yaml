apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-mf-super-dashboard-ingress
  namespace: ctint-fwd
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /fwd/mf-super-dashboard(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ctint-mf-super-dashboard-service
                port:
                  number: 4000
